---
description: 
globs: 
alwaysApply: true
---
# SweatMint Flutter 前端开发核心规则 (.cursorrules)
figma的json文件在 /Users/<USER>/Documents/worker/sweatmint/running-web/figma-json文件夹内，每一个画板我都保存为json文件在这个文件夹内了
可以使用MCP工具来访问figma，但是由于完整调用figma，代码太长，所以，每次跳用figma MCP工具前，必须使用nodes值来查看对应的画板！
页面必须有自适应！适应不同的屏幕尺寸！
assets素材文件夹内都有2.0x、3.0x的文件夹，里面分别放置2、3倍的图片素材，如果需要必须先检查清楚！做的页面根据不同的屏幕做到自适应！

**布局要思考清楚**处理好figma上的布局Row、Column，要思考清楚，做到弹性布局！！！
**制定者：项目技术总监**

**目标：** 确保 SweatMint Flutter 应用的高质量、可维护性、可扩展性，并与后端系统和设计规范保持高度一致。所有前端开发人员必须严格遵守以下规则。

---

## 1. 核心原则与架构约束

*   **[必须] 遵循既定架构：** 严格遵守 [flutter_development_rules.md](mdc:running-web/flutter_development_rules.md) 中定义的 **表现层(Presentation)-业务逻辑层(Business Logic)-数据访问层(Data Access)-基础设施层(Infrastructure)** 的分层架构。**禁止**跨层直接调用或在错误的层级实现功能。
*   **[必须] 遵循文件夹结构：** 所有新代码和文件必须放置在 [flutter_development_rules.md](mdc:running-web/flutter_development_rules.md) 2.3 节定义的对应目录下 (`lib/features`, `lib/presentation`, `lib/domain`, `lib/data`, `lib/core` 等)。**禁止**随意创建目录或将文件放在不相关的模块下。
*   **[必须] 移动优先：** 所有 UI/UX 设计和实现必须优先考虑移动端体验。
*   **[必须] API 契约是唯一依据：** 前端开发**必须**严格按照**已提供**的后端 API 规范（如相关文档、代码定义或用户的明确指示）来实现。**禁止** AI 自行修改 API 路径、请求/响应格式，或假设 API 存在未定义的功能。如果 AI 在分析需求或现有代码时，发现所需的 API 功能/数据似乎与已提供的规范不符、缺失或不明确，**必须**向用户指出此问题，并请求提供准确的 API 定义或澄清，**不得**基于猜测进行 API 调用或数据处理。
*   **[推荐] 渐进式开发：** 优先完成核心功能，逐步迭代。避免大规模、破坏性的重构。
*   **[绝对禁止] 猜测性开发：** AI 必须**始终**基于项目中的现有代码、[flutter_development_rules.md](mdc:running-web/flutter_development_rules.md) 文档、后端 API 规范以及提供的 Figma JSON 数据进行分析和开发。**严禁**猜测变量名、方法名、类结构、业务逻辑或 API 行为。在不确定的情况下，**必须**优先通过阅读相关代码文件或询问来获取明确信息，而不是自行创造或假设。所有实现都应力求与现有代码风格和模式保持一致。

## 2. 状态管理 (Provider + ChangeNotifier)

*   **[必须] 统一方案与Mixin使用:**
    *   项目统一使用 `Provider` + `ChangeNotifier` 作为主要状态管理方案。**禁止**擅自引入其他全局状态管理库 (如 Bloc, Riverpod)，除非经过架构评审批准用于特定复杂场景。
    *   所有处理异步操作（如 API 调用、耗时计算）并需要管理通用加载 (`isLoading`) 和错误 (`errorMessage`) 状态的 `ChangeNotifier` (或 ViewModel)，**必须**混入 (mixin) `core/mixins/view_model_mixin.dart`。**禁止**在 Provider 内部手动管理通用的 `isLoading` 和 `errorMessage` 状态。
    *   异步操作的执行**必须**优先使用 `ViewModelMixin` 提供的 `executeAsyncAction` 辅助方法。此方法会自动处理加载状态切换、通用错误状态设置以及**自动清除上一次的错误状态**，确保 UI 反馈的及时性和一致性。
*   **[必须] 分离状态与 UI:**
    *   业务逻辑和**特定于页面**的状态（如表单字段错误、密码可见性、定时器状态等）必须封装在 `ChangeNotifier` (或 ViewModel) 中。
    *   通用的加载 (`isLoading`) 和错误消息 (`errorMessage`) 状态由 `ViewModelMixin` 管理。
    *   UI Widget 只负责展示 Provider/Mixin 提供的状态，并通过调用 Provider 的方法来触发事件。
    *   **禁止**在 Widget 构建方法中直接处理复杂业务逻辑或 API 调用。
*   **[必须] 优化监听范围:** 使用 `Consumer` 或 `Selector` 优化监听粒度，避免不必要的 Widget 重建。**禁止**在顶层 Widget 中监听整个 Provider 导致大范围刷新。监听 `isLoading` 或 `errorMessage` 时尤其要注意使用 `Selector` 或 `Consumer` 精确绑定到需要反馈状态的 UI 部分。
*   **[推荐] 状态反馈:**
    *   Mixin 自动管理 `isLoading` 和 `errorMessage` 状态。
    *   UI 应根据 `isLoading` 显示加载指示器（如 `CircularProgressIndicator`），并根据 `errorMessage` 是否为 `null` 来显示或隐藏错误提示。
*   **[必须] 及时释放:** 对于非全局 Provider，确保在 Widget 销毁时正确 `dispose`，防止内存泄漏。

## 3. API 集成规范

*   **[必须] 使用 Service 层:** 所有与特定模块相关的 API 调用必须封装在对应的 Service 类中 (位于 `lib/domain/services` 或根据模块划分在 `features/*/domain/services`)。**禁止**在 Provider 或 Widget 中直接使用 Dio/ApiClient 进行 API 调用。
*   **[必须] 使用 Repository 模式:** Service 层通过调用 Repository 接口 (定义在 `lib/domain/repositories`) 获取数据，Repository 实现 (位于 `lib/data/repositories`) 负责与 DataSource (API 或本地缓存) 交互。
*   **[必须] 使用 ApiClient:** DataSource 实现 (位于 `lib/data/datasources`) **必须** 使用统一的 `ApiClient` (`lib/core/network/api_client.dart`) 来执行实际的网络请求。`ApiClient` 负责：
    *   底层的 HTTP 请求发送。
    *   **全局网络连接检查**: 在发起请求前检查网络状态，无网络则直接抛出 `NetworkException`。
    *   **统一错误转换**: 通过拦截器捕获 `DioException`，并将其转换为具体的 `ApiException` 子类 (`NetworkException`, `AuthenticationException`, `ClientException`, `ServerException`, `UnexpectedException`)。
    *   添加通用的请求头（如 Token, 语言, 设备信息）。
*   **[必须] 统一错误处理流程:**
    *   **ApiClient 层:** 负责捕获 `DioException`，执行全局网络检查，并将错误统一转换为 `ApiException` 子类抛出。
    *   **DataSource/Repository/Service 层:** 通常直接将 `ApiClient` 抛出的 `ApiException` 继续向上抛出。
    *   **Provider/ViewModel 层 (`ViewModelMixin`):**
        *   **必须** 使用 `ViewModelMixin` 的 `executeAsyncAction` 方法来包裹调用 Service 层方法的代码块。
        *   Mixin 会自动捕获所有异常。如果是 `ApiException`，Mixin 会将其 `message` 赋值给 `errorMessage` 状态变量；如果是其他异常，则将 `errorMessage` 设为通用的 `ErrorMessages.unexpectedError`。
        *   可以使用 `executeAsyncAction` 的 `onApiError` 回调来响应特定的 `ApiException` 类型，以更新**特定于 UI 的状态**（例如，设置字段高亮）。
        *   UI 层通过监听 Mixin 提供的 `errorMessage` 来显示通用的错误提示。
    *   **禁止** 在 Provider/ViewModel 层直接 `try-catch DioException` 或手动管理通用的 `errorMessage` 状态变量。
*   **[必须] DTO 与 Model 分离:** API 返回的原始数据结构应映射为 DTO (Data Transfer Object, 位于 `lib/data/dto`)，在 Repository 层或 Service 层将其转换为业务领域所需的 Model (位于 `lib/data/models` 或 `lib/domain/entities`)。**推荐**在 Repository 实现层 (`lib/data/repositories`) 完成 DTO 到 Model 的转换，**禁止**将原始 DTO 直接传递到 UI 层。
*   **[必须] 遵循请求规范:** 严格按照 `@flutter_development_rules.md` 3.1 节定义的请求头、参数传递方式（Query/Body）执行 API 请求。

## 4. UI 实现规范 - 弹性布局与自适应

*   **[必须] Figma 设计驱动：** 遵循 [flutter_development_rules.md](mdc:running-web/flutter_development_rules.md) 5.6 节定义的 **Figma 设计驱动开发流程**。开发新 UI 前，必须先分析对应的画板 JSON 文件 (`/running-web/figma-json/`)，提取布局、样式、资源信息。

*   **[必须] 弹性布局 + flutter_screenutil 完美结合：**
    *   **绝对禁止使用 Positioned** - 除非明确在做 `Stack` 叠加布局，否则完全避免使用 `Positioned`
    *   **核心布局构建原则：**
      - 使用 `Row` 和 `Column` 搭建主体结构
      - 对于需要填充剩余空间的部分，**果断使用 `Expanded`**
      - 对于需要固定比例尺寸或间距的部分，**放心使用 `flutter_screenutil` 的 `.w`, `.h`, `.sp`**
      - 当需要自动换行时，记得使用 `Wrap`
    *   **尺寸和间距规范：**
      - **必须** 使用 `flutter_screenutil` 库来处理所有基于 Figma 设计稿（基准尺寸 375x812）的尺寸（使用 `.w`, `.h`）、间距（使用 `.w`, `.h`）和字体大小（使用 `.sp`）的等比例缩放
      - **禁止** 在代码中硬编码未经 `flutter_screenutil`（如 `.w`, `.h`, `.sp`）处理的像素值
      - 所有尺寸都按 Figma 原始比例自适应，确保不会溢出、不会变形

*   **[必须] 响应式布局策略：**
    *   使用 `MediaQuery`, `LayoutBuilder`, `Flex`, `OrientationBuilder` 等 Flutter 内置工具实现布局结构的响应式调整（如不同屏幕尺寸下改变列数或组件排布）
    *   内容区必须用 `SingleChildScrollView` 包裹，防止内容超出时溢出
    *   控件宽度、padding、字体等都按比例缩放，保证不同屏幕下都高度还原 Figma

*   **[必须] 使用主题：** 所有颜色、字体样式、间距等视觉元素**必须**优先使用 `config/themes.dart` 中定义的 `AppTheme` 值。**禁止**在 Widget 中硬编码颜色值或字体样式。

*   **[必须] 组件复用：** 优先使用 `lib/presentation/widgets/common/` 下的公共组件。开发过程中发现可复用的 UI 部分，**必须**抽取为公共 Widget。**禁止**复制代码片段来实现相似的 UI。

*   **[必须] 方法与逻辑复用：**
    *   **减少代码冗余：** 能复用的方法必须复用，避免重复编写相同逻辑
    *   **提取公共方法：** 发现重复代码片段时，必须提取为公共方法或工具类
    *   **使用继承和Mixin：** 合理使用继承、Mixin等方式复用代码逻辑
    *   **工具类集中管理：** 通用工具方法放在 `lib/core/utils/` 目录下统一管理
    *   **业务逻辑复用：** 相似的业务逻辑通过 Service 层或 Repository 层统一处理
    *   **删除冗余文件：** 该删除的文件要删除，保持代码库整洁

*   **[推荐] 交互反馈：** 为按钮、列表项等可交互元素提供清晰的触摸反馈（如水波纹、高亮）。

*   **[必须] 资源管理：**
    *   所有图片、SVG 等资源必须放置在 `assets/` 目录下，并在 `pubspec.yaml` 中声明
    *   assets 素材文件夹内都有 2.0x、3.0x 的文件夹，里面分别放置 2、3 倍的图片素材，开发前必须先检查清楚
    *   使用 `Image.asset` 加载位图，使用 `flutter_svg` 包 (`SvgPicture.asset`) 加载 SVG 图标
    *   **禁止**使用网络 URL 直接加载应用内的固定图标或背景图

## 5. Figma 集成与设计理解特别规则

*   **[必须] 深度理解 Figma 设计结构：**
    *   **仔细检查并理解 Figma 图层命名：** 所有 Figma 图层、组的 "name" 都经过精心编写，包括分组都非常清晰，AI 必须仔细阅读和理解这些命名规则和分组逻辑
    *   **分析设计分组逻辑：** 理解 Figma 中组件的层级结构、命名约定和分组意图，这些都是实现正确布局的重要线索
    *   **提取布局信息：** 从 Figma JSON 中准确提取 `x`, `y`, `width`, `height` 等位置和尺寸信息，理解父子关系和约束条件

*   **[必须] 使用画板 JSON 作为蓝图：** UI 布局和样式的实现**必须**以对应画板 JSON 文件为主要依据。
    *   Figma JSON 文件位于 `/Users/<USER>/Documents/worker/sweatmint/running-web/figma-json/` 文件夹内
    *   每一个画板都保存为独立的 JSON 文件

*   **[必须] MCP 工具使用规范：**
    *   可以使用 MCP 工具来访问 Figma，但由于完整调用 Figma 代码太长，每次调用 Figma MCP 工具前，**必须使用 nodes 值来查看对应的画板**
    *   **必须** 通过 `mcp_FigmaMCP_download_figma_images` 工具下载 Figma 中的图片和 SVG 资源到本地 `assets` 目录后使用。**禁止**直接截屏或手动导出设计稿中的资源

*   **[必须] 规范资源命名：** 下载资源时，在 MCP 工具参数中指定有意义的文件名 (`fileName`)，与资源内容或用途保持一致（例如 `icon_home_active.svg`, `bg_vip_card.png`）

*   **[必须] 验证多倍图：** 使用 MCP 下载位图后，**必须**检查 `assets` 目录下的文件，确认是否按预期生成了所需倍数 (@1x, @2x, @3x) 的图片。如未生成，需检查 Figma 中的导出设置，或与技术负责人讨论替代方案

*   **[注意] JSON 是快照：** 牢记画板 JSON 是静态的。如果 Figma 设计有更新，需要重新获取最新的 JSON 文件，并对比更新 Flutter 代码

**布局实现关键原则：**
- **思考清楚布局结构：** 处理好 Figma 上的布局 Row、Column，要仔细思考，做到真正的弹性布局
- **自适应设计：** 页面必须有自适应，适应不同的屏幕尺寸
- **完美还原：** 所有尺寸都按 Figma 原始比例自适应，不会溢出、不会变形
- **防溢出处理：** 内容区用 `SingleChildScrollView` 包裹，防止内容超出时溢出
- **比例缩放：** 控件宽度、padding、字体等都按比例缩放，保证不同屏幕下都高度还原 Figma

## 6. 代码质量与流程

*   **[必须] 代码格式化与 Lint：** 提交代码前**必须**运行 `dart format .` 和 `flutter analyze`，确保所有代码格式规范且无 Lint 错误/警告。
*   **[必须] 注释规范：** 遵循 [flutter_development_rules.md](mdc:running-web/flutter_development_rules.md) 5.1.3 节的注释规范，为主要类和方法添加必要的文档注释。
*   **[必须] 编写测试：** 核心业务逻辑 (Services, Repositories) **必须**编写单元测试；关键 UI 组件和页面交互**推荐**编写 Widget 测试。
*   **[必须] Code Review：** 所有 `feature/*` 和 `bugfix/*` 分支合并到 `develop` 前**必须**经过 Code Review。
*   **[必须] 版本控制：** 遵循 [flutter_development_rules.md](mdc:running-web/flutter_development_rules.md) 7.2 节定义的分支管理和提交规范。**禁止**直接向 `develop` 或 `main` 分支提交代码。
*   **[必须] 遵循标准化状态管理:** **必须** 遵循由 `ViewModelMixin` 提供的标准化异步操作状态管理模式，确保所有涉及异步操作的 UI 反馈（加载指示、错误提示、重试逻辑）在整个应用中保持一致性和可靠性。

## 7. 性能与安全

*   **[必须] 避免阻塞主线程：** 耗时操作（网络请求、复杂计算、文件读写）**必须**使用 `async/await` 在异步任务中执行。
*   **[推荐] 优化列表性能：** 使用 `ListView.builder`，并考虑使用 `const` Widget 和列表项缓存策略。
*   **[必须] 安全存储：** 用户 Token 等敏感信息**必须**使用 `flutter_secure_storage` 存储。
*   **[推荐] 输入验证：** 对用户输入进行必要的格式和逻辑验证。

## 8. Do's and Don'ts (简要总结)

*   **Do:**
    *   遵循架构分层和文件夹结构
    *   减少代码冗余，能复用的方法和组件要复用
    *   使用 Provider 进行状态管理
    *   通过 Service 和 Repository 访问 API
    *   优先使用 AppTheme 和公共 Widget
    *   利用 Figma JSON 指导 UI 开发，仔细理解图层命名和分组
    *   使用 Row/Column + Expanded 构建弹性布局
    *   使用 flutter_screenutil 处理所有尺寸和字体
    *   提取公共方法和工具类，保持代码整洁
    *   使用 MCP 工具下载 Figma 资源
    *   编写单元测试和 Widget 测试
    *   遵循代码规范和提交流程

*   **Don't:**
    *   跨层调用或在错误层级写逻辑
    *   该删除的文件要删除，减少冗余
    *   随意创建目录或放置文件
    *   引入未经批准的状态管理库
    *   在 Widget 中直接调用 API
    *   硬编码颜色、字体等样式
    *   使用 Positioned（除非明确的 Stack 叠加布局）
    *   硬编码未经 flutter_screenutil 处理的像素值
    *   复制代码片段代替组件复用
    *   手动导出或截屏 Figma 资源
    *   忽略 API 错误处理
    *   提交未格式化或有 Lint 错误的代码
    *   直接向 `develop` / `main` 分支提交代码

---
