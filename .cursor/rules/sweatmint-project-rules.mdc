---
description: 
globs: 
alwaysApply: false
---
# SweatMint项目开发总规则

**制定者：项目技术总监**
**适用范围：SweatMint后端(Django)和前端(Flutter)开发**

## 核心开发原则

### 基本要求
- 在代码修改或逻辑思考时，必须严谨阅读所有相关代码，不能靠猜测
- 不能出现模棱两可的回答或解决方案，必须严谨，以事实说话
- 每次回答前，请说"你好，BOSS"作为开头
- 文件、文件夹请分类清楚

### 项目路径规范
- **后端开发：** 所有代码都在 `/Users/<USER>/Documents/worker/sweatmint/running/` 文件夹
- **前端开发：** 所有代码都在 `/Users/<USER>/Documents/worker/sweatmint/running-web/` 文件夹
- **后端命令：** 确保在 `/Users/<USER>/Documents/worker/sweatmint/running/.venv/bin/activate` 虚拟环境内运行

### 技术栈约束
- **后端：** Django + SimpleUI模版（必须使用SimpleUI组件）
- **前端：** Flutter + Provider状态管理 + Figma设计驱动开发

## 开发模式约束

### 系统稳定性第一
- **禁止** 修改现有功能逻辑，仅进行优化和扩展
- 任何修改都必须向后兼容，不能破坏现有功能
- 功能更新必须渐进式实施，避免系统中断
- 高风险修改必须有回滚方案

### 严谨的开发流程
1. **读取-理解-实现流程**
   - 先读取相关代码文件了解上下文
   - 分析当前实现和目标要求的差距
   - 考虑多种实现方案并选择最合适的
   - 基于完整理解后实现新功能

2. **绝对禁止猜测性开发**
   - AI必须始终基于现有代码、文档和明确的API规范进行开发
   - 严禁猜测变量名、方法名、类结构、业务逻辑或API行为
   - 不确定时必须通过阅读相关代码文件或询问来获取明确信息

### 代码质量要求
- 所有新代码必须包含中文注释
- 遵循项目既有的代码风格和模式
- 错误处理必须完善，区分业务异常和系统异常
- 关键操作添加详细日志记录

## 前后端协作规范

### API契约管理
- 前端开发必须严格按照已提供的后端API规范实现
- 禁止AI自行修改API路径、请求/响应格式
- API功能/数据不明确时，必须向用户指出并请求澄清
- 不得基于猜测进行API调用或数据处理

### 数据交互标准
- 前后端交互统一使用JSON格式
- 日期时间使用ISO 8601格式
- 分页参数统一为page（页码）和size（每页条数）
- 使用标准HTTP状态码表示请求结果

### 标准响应结构
```json
{
  "code": 200,
  "message": "操作成功",
  "data": { /* 业务数据 */ },
  "timestamp": 1646041438
}
```

### Figma 设计理解与实现规范
- **深度理解设计结构：** AI必须仔细检查并理解Figma图层、组的"name"命名和分组逻辑，这些都经过精心编写且分组清晰
- **分析设计意图：** 理解Figma中组件的层级结构、命名约定和分组意图，作为实现正确布局的重要线索
- **Figma JSON数据源：** 所有设计文件保存在 `/Users/<USER>/Documents/worker/sweatmint/running-web/figma-json/` 文件夹内，每个画板保存为独立JSON文件

### Flutter 弹性布局核心约束
- **绝对禁止 Positioned：** 除非明确在做Stack叠加布局，否则完全避免使用Positioned
- **弹性布局 + flutter_screenutil 完美结合：**
  - 用 Row 和 Column 搭建结构
  - 需要填充剩余空间的部分，果断使用 Expanded
  - 需要固定比例尺寸或间距的部分，放心使用 flutter_screenutil 的 .w, .h, .sp
  - 需要自动换行时，记得用 Wrap
- **自适应设计要求：** 页面必须适应不同屏幕尺寸，所有尺寸按Figma原始比例自适应
- **防溢出处理：** 内容区必须用SingleChildScrollView包裹，防止内容超出时溢出

## 项目特有业务约束

### SweatMint业务逻辑
- 任务完成数据处理必须验证数据真实性
- 健康数据接口必须考虑不同平台差异
- 代币奖励计算需考虑各种加成和倍率
- 所有资金相关操作必须有详细日志和审计记录

### 安全性要求
- 所有用户输入必须经过验证和消毒
- 敏感数据必须加密存储，传输过程加密
- 用户Token等敏感信息使用安全存储
- 防范常见安全威胁：XSS、CSRF、SQL注入等

## 测试与质量保障

### 测试要求
- 核心业务逻辑必须编写单元测试
- 集成测试验证模块间交互
- 端到端测试确保主流程正常
- 测试必须站在管理员角度思考

### 性能要求
- 避免阻塞主线程的耗时操作
- 大数据量操作使用异步处理或分批处理
- 优化数据库查询，合理使用索引
- 关注API响应时间，设定性能基准线

## 版本控制规范

### 提交规范
- 每次提交专注于单一功能或修复
- 提交信息清晰描述变更内容和目的
- 提交前完成测试，确保代码可运行
- 禁止直接在保护分支提交代码

### 分支管理
- 功能开发在独立分支进行
- 定期从主分支合并更新
- 遵循Git Flow工作流程
- 所有分支合并前必须经过Code Review

---

**注意：** 详细的后端开发规则请参考 [backend-rules.mdc](mdc:.cursor/rules/backend-rules.mdc)，详细的前端开发规则请参考 [sweatmint-flutter-frontend-rules.mdc](mdc:.cursor/rules/sweatmint-flutter-frontend-rules.mdc)。
