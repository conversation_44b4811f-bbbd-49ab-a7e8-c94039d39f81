---
description: 后端的rules
globs: 
alwaysApply: true
---
# SweatMint项目AI后端开发规则

在代码修改或者逻辑思考时，必须严谨阅读所有相关代码，不能靠猜的！不能出现模凌两可的回答或者解决方案，必须严谨，阅读相关文档或者代码，给出最优解，原则就是以事实说话，而不是靠猜想！
文件、文件夹请分类清楚，
每次回答前，请说"你好，BOSS"作为开头
确保开发后端，所有代码都在 /Users/<USER>/Documents/worker/sweatmint/running/文件夹，分类清楚
确保每次都在/Users/<USER>/Documents/worker/sweatmint/running/.venv/bin/activate 虚拟环境内运行命令
后台用的是SimpleUI模版，在开发的时候，必须使用SimpleUI的组件

## 1. 系统稳定性保障

1.1. **保持系统稳定性**
   - 不得修改现有功能逻辑，仅进行优化和扩展
   - 任何修改都必须向后兼容，不能破坏现有功能
   - 功能更新必须渐进式实施，避免系统中断
   - 高风险修改必须有回滚方案

1.2. **兼容性要求**
   - 新代码必须与现有代码完全兼容
   - 采用兼容层设计模式处理新旧数据格式
   - 保持API接口一致性，不得随意变更接口参数和返回值
   - 处理所有可能的版本差异和边缘情况

1.3. **代码隔离**
   - 新功能应封装在独立模块中，减少对现有代码的修改
   - 使用策略模式和工厂模式实现功能扩展
   - 避免对现有核心逻辑的侵入式修改
   - 关注点分离，业务逻辑与数据访问分离

1.4. **错误处理规范**
   - 所有公共方法必须包含完善的错误处理
   - 异常必须被捕获并记录，附带足够的上下文信息
   - 关键操作应有事务保护，确保数据一致性
   - 区分业务异常和系统异常，提供用户友好的错误提示

1.5. **注释要求**
   - 所有新代码必须包含中文注释
   - 类注释说明该类的功能、职责和用法
   - 方法注释说明功能实现、参数含义和返回值
   - 复杂逻辑必须有详细说明，便于其他开发者理解

## 2. 开发模式与流程

2.1. **渐进式开发模式**
   - 按照小功能点迭代开发，避免大规模重构
   - 每个功能点完成后进行测试，确保质量
   - 功能开发顺序应遵循优先级排序
   - 优先修复高优先级问题和核心功能缺陷

2.2. **读取-理解-实现流程**
   - 先读取相关代码文件了解上下文
   - 分析当前实现和目标要求的差距
   - 考虑多种实现方案并选择最合适的
   - 基于完整理解后实现新功能

2.3. **测试驱动开发**
   - 为每个功能点编写测试用例
   - 测试覆盖正常路径和异常路径
   - 编写单元测试、集成测试和端到端测试
   - 确保测试可重复执行，结果稳定可靠

## 3. 项目分析与代码实现

3.1. **项目理解与结构分析**
   - 理解整体项目结构，包括目录组织、配置文件和依赖关系
   - 熟悉核心模块功能和交互关系
   - 理解数据流转过程和业务逻辑
   - 识别系统的技术栈和架构设计

3.2. **明确需求与目标**
   - 精确理解任务要求和业务目标
   - 分析用户场景和使用流程
   - 明确功能边界和技术约束
   - 制定清晰的实现计划和验收标准

3.3. **代码设计与生成**
   - 遵循SOLID原则和设计模式
   - 确保代码简洁、可读、可维护
   - 避免过度设计和不必要的复杂性
   - 保持代码风格一致，遵循项目的编码规范

## 4. API设计与实现规范

4.1. **API命名与版本控制**
   - 使用RESTful风格设计API，资源名使用复数形式
   - API命名应具有描述性，遵循一致的命名规范
   - 实现API版本控制机制，支持向前兼容
   - 在URL或Header中明确API版本

4.2. **请求与响应规范**
   - 使用标准HTTP方法（GET/POST/PUT/DELETE）表示操作语义
   - 请求参数应有明确的类型、格式和校验规则
   - 响应统一使用JSON格式，包含状态码、数据和错误信息
   - 分页接口必须包含总数、页码和每页条数信息

4.3. **API文档与自测**
   - 每个API必须有完整文档，包括接口说明、参数描述和响应示例
   - 文档应包含常见错误码和处理方法
   - 提供API自测工具或示例代码
   - 接口变更必须同步更新文档

4.4. **标准响应结构**
   ```python
   # 成功响应格式
   {
       "code": 200,              # 业务状态码
       "message": "操作成功",     # 友好提示信息
       "data": {                 # 业务数据
           "字段1": "值1",
           "字段2": "值2"
       },
       "timestamp": 1646041438   # 时间戳，方便调试
   }
   
   # 失败响应格式
   {
       "code": 400,              # 业务状态码
       "message": "参数错误",     # 用户友好错误信息
       "details": "缺少必填参数x", # 详细错误信息
       "timestamp": 1646041438   # 时间戳
   }
   ```

## 5. 调试、测试与质量保障

5.1. **调试思维流程**
   - 先复现问题，确保理解问题本质
   - 根据日志和错误信息定位根因
   - 提出针对性解决方案，避免临时补丁
   - 验证修复效果，避免引入新问题

5.2. **测试策略与覆盖**
   - 单元测试覆盖核心业务逻辑
   - 集成测试验证模块间交互
   - 端到端测试确保主流程正常
   - 性能测试评估系统承载能力

5.3. **代码审查与质量保障**
   - 遵循代码审查清单，关注安全性、性能和可维护性
   - 使用静态代码分析工具检查代码质量
   - 关注代码复杂度和重复度指标
   - 定期重构技术债务，提升代码质量

## 6. 安全性与性能优化

6.1. **数据安全与认证授权**
   - 所有用户输入必须经过验证和消毒
   - 敏感数据必须加密存储，传输过程加密
   - 实现完善的认证和授权机制
   - 防范常见安全威胁：XSS、CSRF、SQL注入等

6.2. **性能优化措施**
   - 优化数据库查询，合理使用索引
   - 实现缓存机制，减少重复计算
   - 大数据量操作使用异步处理或分批处理
   - 关注API响应时间，设定性能基准线

6.3. **资源管理与异常处理**
   - 正确管理数据库连接、文件句柄等资源
   - 使用上下文管理器确保资源释放
   - 实现限流和熔断机制，防止系统过载
   - 关键操作添加监控和告警机制

## 7. 前后端分离规范

7.1. **数据交互规范**
   - 前后端交互统一使用JSON格式
   - 日期时间使用ISO 8601格式
   - 分页参数统一为page（页码）和size（每页条数）
   - 搜索排序参数使用标准化命名

7.2. **状态码与错误处理**
   - 使用标准HTTP状态码表示请求结果
   - 业务错误码体系应清晰明确
   - 错误信息应包含用户友好提示和技术详情
   - 前端应能根据错误码做出相应处理

7.3. **接口版本与兼容性**
   - API版本变更要有明确的升级路径
   - 接口参数变更应保持向后兼容
   - 弃用接口需有预警和过渡期
   - 重大变更应通过API版本升级实现

## 8. 数据库操作规范

8.1. **数据库访问原则**
   - 使用ORM层统一数据库访问
   - 复杂查询使用原生SQL时必须注释说明
   - 大批量数据操作应使用批处理或存储过程
   - 避免业务代码中直接拼接SQL语句

8.2. **事务与并发控制**
   - 涉及多表操作使用事务保证一致性
   - 处理并发修改时使用乐观锁或悲观锁
   - 避免长事务，减少锁竞争
   - 考虑分布式事务场景的数据一致性

8.3. **索引与性能优化**
   - 为常用查询条件创建适当索引
   - 避免过度索引，影响写入性能
   - 定期分析慢查询，优化SQL语句
   - 合理使用数据库连接池

## 9. 监控、日志与可观测性

9.1. **日志规范**
   - 使用统一的日志框架和格式
   - 区分不同级别日志：ERROR、WARN、INFO、DEBUG
   - 关键操作必须记录日志，包含足够上下文
   - 敏感信息脱敏处理，避免泄露

9.2. **监控与告警**
   - 实现系统关键指标监控
   - 设置合理的告警阈值和规则
   - 监控API响应时间、错误率和业务指标
   - 实现健康检查接口，便于系统巡检

9.3. **可观测性设计**
   - 实现分布式追踪，便于问题排查
   - 关键业务流程添加事件溯源
   - 构建系统指标仪表盘
   - 支持动态调整日志级别

## 10. 代码提交与版本控制

10.1. **提交规范**
    - 每次提交专注于单一功能或修复
    - 提交信息清晰描述变更内容和目的
    - 大功能分多次小提交，保持提交历史清晰
    - 提交前完成测试，确保代码可运行

10.2. **分支管理**
    - 遵循Git Flow或类似工作流
    - 功能开发在独立分支进行
    - 定期从主分支合并更新
    - 禁止直接在保护分支提交代码

10.3. **版本发布与标记**
    - 遵循语义化版本规范
    - 每次发布有明确的变更日志
    - 关键版本添加Git标签
    - 建立正式环境部署前的验证流程

## 11. SweatMint项目特有规范

11.1. **SweatMint业务逻辑约束**
    - 任务完成数据处理必须验证数据真实性
    - 健康数据接口必须考虑不同平台差异
    - 代币奖励计算需考虑各种加成和倍率
    - 所有资金相关操作必须有详细日志和审计记录

11.2. **项目结构规范**
    - 所有任务相关逻辑集中在tasks模块
    - 用户相关功能集中在users模块
    - 服务层代码放置在services目录
    - 工具函数放置在utils目录

11.3. **特殊测试要求**
    - 模拟健康数据测试必须覆盖异常场景
    - 任务奖励计算必须有精度测试
    - 高并发场景下的数据一致性测试
    - 完整的端到端流程测试 
    