import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:logger/logger.dart';

/// 🔥 BOSS关键修复：健康数据同步进度指示器
/// 符合SweatMint设计规范的进度指示器组件
/// 显示当前执行步骤、进度百分比、预计剩余时间
class SyncProgressIndicator extends StatefulWidget {
  /// 当前步骤名称
  final String currentStep;
  
  /// 进度百分比 (0.0 - 1.0)
  final double progress;
  
  /// 预计剩余时间（秒）
  final int? estimatedRemainingSeconds;
  
  /// 是否显示取消按钮
  final bool showCancelButton;
  
  /// 是否显示重试按钮
  final bool showRetryButton;
  
  /// 取消回调
  final VoidCallback? onCancel;
  
  /// 重试回调
  final VoidCallback? onRetry;
  
  /// 详细状态信息
  final String? statusMessage;
  
  /// 是否处于错误状态
  final bool isError;
  
  /// 是否处于成功状态
  final bool isSuccess;
  
  /// 自定义主题颜色
  final Color? primaryColor;
  
  /// 自定义背景颜色
  final Color? backgroundColor;

  const SyncProgressIndicator({
    Key? key,
    required this.currentStep,
    required this.progress,
    this.estimatedRemainingSeconds,
    this.showCancelButton = false,
    this.showRetryButton = false,
    this.onCancel,
    this.onRetry,
    this.statusMessage,
    this.isError = false,
    this.isSuccess = false,
    this.primaryColor,
    this.backgroundColor,
  }) : super(key: key);

  @override
  State<SyncProgressIndicator> createState() => _SyncProgressIndicatorState();
}

class _SyncProgressIndicatorState extends State<SyncProgressIndicator>
    with TickerProviderStateMixin {
  static final Logger _logger = Logger();
  
  late AnimationController _progressAnimationController;
  late AnimationController _pulseAnimationController;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;
  
  @override
  void initState() {
    super.initState();
    
    // 初始化进度动画控制器
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    // 初始化脉冲动画控制器
    _pulseAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    // 创建进度动画
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _progressAnimationController,
      curve: Curves.easeInOut,
    ));
    
    // 创建脉冲动画
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseAnimationController,
      curve: Curves.easeInOut,
    ));
    
    // 启动动画
    _progressAnimationController.forward();
    if (!widget.isError && !widget.isSuccess) {
      _pulseAnimationController.repeat(reverse: true);
    }
    
    _logger.d('🎨 SyncProgressIndicator: 初始化完成');
  }
  
  @override
  void didUpdateWidget(SyncProgressIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 更新进度动画
    if (oldWidget.progress != widget.progress) {
      _progressAnimation = Tween<double>(
        begin: oldWidget.progress,
        end: widget.progress,
      ).animate(CurvedAnimation(
        parent: _progressAnimationController,
        curve: Curves.easeInOut,
      ));
      
      _progressAnimationController.reset();
      _progressAnimationController.forward();
    }
    
    // 根据状态控制脉冲动画
    if (widget.isError || widget.isSuccess) {
      _pulseAnimationController.stop();
    } else if (!_pulseAnimationController.isAnimating) {
      _pulseAnimationController.repeat(reverse: true);
    }
  }
  
  @override
  void dispose() {
    _progressAnimationController.dispose();
    _pulseAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(24.w),
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10.r,
            offset: Offset(0, 4.h),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 步骤标题和状态图标
          _buildStepHeader(),
          
          SizedBox(height: 16.h),
          
          // 进度条
          _buildProgressBar(),
          
          SizedBox(height: 12.h),
          
          // 进度信息
          _buildProgressInfo(),
          
          if (widget.statusMessage != null) ...[
            SizedBox(height: 8.h),
            _buildStatusMessage(),
          ],
          
          if (widget.showCancelButton || widget.showRetryButton) ...[
            SizedBox(height: 16.h),
            _buildActionButtons(),
          ],
        ],
      ),
    );
  }
  
  /// 构建步骤标题和状态图标
  Widget _buildStepHeader() {
    return Row(
      children: [
        // 状态图标
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: widget.isError || widget.isSuccess ? 1.0 : _pulseAnimation.value,
              child: Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: _getStatusColor(),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getStatusIcon(),
                  color: Colors.white,
                  size: 20.sp,
                ),
              ),
            );
          },
        ),
        
        SizedBox(width: 12.w),
        
        // 步骤标题
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.currentStep,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).textTheme.titleLarge?.color,
                ),
              ),
              if (widget.estimatedRemainingSeconds != null)
                Text(
                  _formatRemainingTime(widget.estimatedRemainingSeconds!),
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                ),
            ],
          ),
        ),
        
        // 进度百分比
        Text(
          '${(widget.progress * 100).toInt()}%',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: _getStatusColor(),
          ),
        ),
      ],
    );
  }
  
  /// 构建进度条
  Widget _buildProgressBar() {
    return Container(
      height: 8.h,
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.2),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: AnimatedBuilder(
        animation: _progressAnimation,
        builder: (context, child) {
          return LinearProgressIndicator(
            value: _progressAnimation.value,
            backgroundColor: Colors.transparent,
            valueColor: AlwaysStoppedAnimation<Color>(_getStatusColor()),
            minHeight: 8.h,
          );
        },
      ),
    );
  }
  
  /// 构建进度信息
  Widget _buildProgressInfo() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Progress',
          style: TextStyle(
            fontSize: 12.sp,
            color: Theme.of(context).textTheme.bodySmall?.color,
          ),
        ),
        Text(
          widget.isSuccess 
            ? 'Completed' 
            : widget.isError 
              ? 'Failed' 
              : 'In Progress',
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.w500,
            color: _getStatusColor(),
          ),
        ),
      ],
    );
  }
  
  /// 构建状态消息
  Widget _buildStatusMessage() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: _getStatusColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Text(
        widget.statusMessage!,
        style: TextStyle(
          fontSize: 12.sp,
          color: _getStatusColor(),
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
  
  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        if (widget.showRetryButton && widget.onRetry != null)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: widget.onRetry,
              icon: Icon(Icons.refresh, size: 16.sp),
              label: Text(
                'Retry',
                style: TextStyle(fontSize: 14.sp),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: _getStatusColor(),
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ),
        
        if (widget.showRetryButton && widget.showCancelButton)
          SizedBox(width: 12.w),
        
        if (widget.showCancelButton && widget.onCancel != null)
          Expanded(
            child: OutlinedButton.icon(
              onPressed: widget.onCancel,
              icon: Icon(Icons.close, size: 16.sp),
              label: Text(
                'Cancel',
                style: TextStyle(fontSize: 14.sp),
              ),
              style: OutlinedButton.styleFrom(
                foregroundColor: Theme.of(context).textTheme.bodyLarge?.color,
                padding: EdgeInsets.symmetric(vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ),
      ],
    );
  }
  
  /// 获取状态颜色
  Color _getStatusColor() {
    if (widget.primaryColor != null) {
      return widget.primaryColor!;
    }
    
    if (widget.isError) {
      return Colors.red;
    } else if (widget.isSuccess) {
      return Colors.green;
    } else {
      return Theme.of(context).primaryColor;
    }
  }
  
  /// 获取状态图标
  IconData _getStatusIcon() {
    if (widget.isError) {
      return Icons.error;
    } else if (widget.isSuccess) {
      return Icons.check;
    } else {
      return Icons.sync;
    }
  }
  
  /// 格式化剩余时间
  String _formatRemainingTime(int seconds) {
    if (seconds < 60) {
      return 'About $seconds seconds remaining';
    } else {
      final minutes = (seconds / 60).ceil();
      return 'About $minutes minute${minutes > 1 ? 's' : ''} remaining';
    }
  }
}
