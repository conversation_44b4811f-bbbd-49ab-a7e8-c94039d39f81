import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:convert';
import 'token_debug_tool.dart';
import '../features/auth/presentation/providers/auth_provider.dart';

/// 🔥 BOSS调试页面：Token和用户状态调试界面
class TokenDebugScreen extends StatefulWidget {
  const TokenDebugScreen({super.key});

  @override
  State<TokenDebugScreen> createState() => _TokenDebugScreenState();
}

class _TokenDebugScreenState extends State<TokenDebugScreen> {
  Map<String, dynamic>? _debugResult;
  bool _isLoading = false;
  String? _debugReport;

  @override
  void initState() {
    super.initState();
    _performDebugAnalysis();
  }

  Future<void> _performDebugAnalysis() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 🚨 紧急调试：用户认证不匹配问题
      final result = await TokenDebugTool.debugUserAuthMismatch();
      setState(() {
        _debugResult = result;
        _debugReport = result['debug_report'] as String?;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('调试分析失败: $e')),
        );
      }
    }
  }

  Future<void> _clearAllData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认清除'),
        content: const Text('这将清除所有Token和用户相关数据，需要重新登录。确定继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await TokenDebugTool.clearAllUserData();
      
      // 重置AuthProvider状态
      if (mounted) {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        await authProvider.forceLogout();
      }

      if (!mounted) return;

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('所有用户数据已清除')),
        );
      }

      // 重新分析
      _performDebugAnalysis();
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('已复制到剪贴板')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Token & User Debug'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _performDebugAnalysis,
          ),
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: _clearAllData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 当前AuthProvider状态
                  _buildAuthProviderStatus(),
                  const SizedBox(height: 20),
                  
                  // 调试报告
                  if (_debugReport != null) _buildDebugReport(),
                  const SizedBox(height: 20),
                  
                  // 详细数据
                  if (_debugResult != null) _buildDetailedData(),
                ],
              ),
            ),
    );
  }

  Widget _buildAuthProviderStatus() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '📱 当前AuthProvider状态',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                Text('认证状态: ${authProvider.authStatus}'),
                Text('是否已认证: ${authProvider.isAuthenticated}'),
                Text('Access Token: ${authProvider.accessToken?.substring(0, 20) ?? 'null'}...'),
                Text('用户邮箱: ${authProvider.user?.email ?? 'null'}'),
                Text('用户ID: ${authProvider.user?.userId ?? 'null'}'),
                Text('用户名: ${authProvider.user?.username ?? 'null'}'),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDebugReport() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  '📊 调试报告',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.copy),
                  onPressed: () => _copyToClipboard(_debugReport!),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _debugReport!,
                style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedData() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  '🔍 详细调试数据',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.copy),
                  onPressed: () => _copyToClipboard(
                    const JsonEncoder.withIndent('  ').convert(_debugResult!),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Container(
              width: double.infinity,
              height: 300,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: SingleChildScrollView(
                child: Text(
                  const JsonEncoder.withIndent('  ').convert(_debugResult!),
                  style: const TextStyle(fontFamily: 'monospace', fontSize: 10),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
