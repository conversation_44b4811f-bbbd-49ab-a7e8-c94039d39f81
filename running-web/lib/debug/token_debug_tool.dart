import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:logger/logger.dart';

/// 🔥 BOSS调试工具：Token和用户状态调试器
/// 用于深入分析token内容和用户状态不匹配问题
class TokenDebugTool {
  static const _storage = FlutterSecureStorage();
  static final _logger = Logger();

  /// 🚨 紧急调试：用户认证不匹配问题分析
  static Future<Map<String, dynamic>> debugUserAuthMismatch() async {
    _logger.e('🚨 TokenDebugTool: 紧急调试 - 用户认证不匹配问题分析');

    final debugResult = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'analysis_type': 'user_auth_mismatch_debug',
      'issue': 'User logged <NAME_EMAIL> but <NAME_EMAIL>',
    };

    try {
      // 1. 获取当前存储的Token
      final accessToken = await _storage.read(key: 'access_token');
      final refreshToken = await _storage.read(key: 'refresh_token');

      debugResult['tokens'] = {
        'access_token_exists': accessToken != null,
        'refresh_token_exists': refreshToken != null,
        'access_token_length': accessToken?.length ?? 0,
      };

      // 2. 解码Access Token
      if (accessToken != null) {
        try {
          final decodedToken = JwtDecoder.decode(accessToken);
          debugResult['access_token_payload'] = {
            'user_id': decodedToken['user_id'],
            'email': decodedToken['email'],
            'exp': decodedToken['exp'],
            'iat': decodedToken['iat'],
          };

          // 检查Token是否过期
          final exp = decodedToken['exp'] as int;
          final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
          debugResult['token_status'] = {
            'is_expired': exp <= now,
            'expires_at': DateTime.fromMillisecondsSinceEpoch(exp * 1000).toIso8601String(),
            'time_until_expiry_seconds': exp - now,
          };
        } catch (e) {
          debugResult['access_token_decode_error'] = e.toString();
        }
      }

    } catch (e) {
      debugResult['debug_error'] = e.toString();
    }

    return debugResult;
  }

  /// 🔍 完整的Token和用户状态调试分析
  static Future<Map<String, dynamic>> performCompleteDebugAnalysis() async {
    _logger.i('🔍 TokenDebugTool: 开始完整的Token和用户状态调试分析');

    final debugResult = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'analysis_type': 'complete_token_user_debug',
    };

    try {
      // 1. 读取存储的Token
      final accessToken = await _storage.read(key: 'access_token');
      final refreshToken = await _storage.read(key: 'refresh_token');
      final tokenExpiry = await _storage.read(key: 'token_expiry_exp');

      debugResult['stored_tokens'] = {
        'access_token_exists': accessToken != null,
        'access_token_length': accessToken?.length ?? 0,
        'access_token_prefix': accessToken?.substring(0, 20) ?? 'null',
        'refresh_token_exists': refreshToken != null,
        'refresh_token_length': refreshToken?.length ?? 0,
        'refresh_token_prefix': refreshToken?.substring(0, 20) ?? 'null',
        'token_expiry': tokenExpiry,
      };

      // 2. 解码Access Token
      if (accessToken != null) {
        try {
          final decodedToken = JwtDecoder.decode(accessToken);
          final isExpired = JwtDecoder.isExpired(accessToken);
          final expiryDate = JwtDecoder.getExpirationDate(accessToken);
          
          debugResult['access_token_decoded'] = {
            'is_expired': isExpired,
            'expiry_date': expiryDate.toIso8601String(),
            'user_id': decodedToken['user_id'],
            'exp': decodedToken['exp'],
            'iat': decodedToken['iat'],
            'token_type': decodedToken['token_type'],
            'jti': decodedToken['jti'],
            'full_payload': decodedToken,
          };

          _logger.i('✅ Access Token解码成功');
          _logger.i('  用户ID: ${decodedToken['user_id']}');
          _logger.i('  过期时间: ${expiryDate.toIso8601String()}');
          _logger.i('  是否过期: $isExpired');
          
        } catch (e) {
          debugResult['access_token_decode_error'] = e.toString();
          _logger.e('❌ Access Token解码失败: $e');
        }
      }

      // 3. 解码Refresh Token
      if (refreshToken != null) {
        try {
          final decodedRefreshToken = JwtDecoder.decode(refreshToken);
          final isRefreshExpired = JwtDecoder.isExpired(refreshToken);
          final refreshExpiryDate = JwtDecoder.getExpirationDate(refreshToken);
          
          debugResult['refresh_token_decoded'] = {
            'is_expired': isRefreshExpired,
            'expiry_date': refreshExpiryDate.toIso8601String(),
            'user_id': decodedRefreshToken['user_id'],
            'exp': decodedRefreshToken['exp'],
            'iat': decodedRefreshToken['iat'],
            'token_type': decodedRefreshToken['token_type'],
            'jti': decodedRefreshToken['jti'],
            'full_payload': decodedRefreshToken,
          };

          _logger.i('✅ Refresh Token解码成功');
          _logger.i('  用户ID: ${decodedRefreshToken['user_id']}');
          _logger.i('  过期时间: ${refreshExpiryDate.toIso8601String()}');
          _logger.i('  是否过期: $isRefreshExpired');
          
        } catch (e) {
          debugResult['refresh_token_decode_error'] = e.toString();
          _logger.e('❌ Refresh Token解码失败: $e');
        }
      }

      // 4. 检查Token一致性
      if (accessToken != null && refreshToken != null) {
        try {
          final accessDecoded = JwtDecoder.decode(accessToken);
          final refreshDecoded = JwtDecoder.decode(refreshToken);
          
          final accessUserId = accessDecoded['user_id'];
          final refreshUserId = refreshDecoded['user_id'];
          
          debugResult['token_consistency'] = {
            'access_user_id': accessUserId,
            'refresh_user_id': refreshUserId,
            'user_ids_match': accessUserId == refreshUserId,
            'potential_mismatch': accessUserId != refreshUserId,
          };

          if (accessUserId != refreshUserId) {
            _logger.e('🚨 严重问题：Access Token和Refresh Token的用户ID不匹配！');
            _logger.e('  Access Token用户ID: $accessUserId');
            _logger.e('  Refresh Token用户ID: $refreshUserId');
          } else {
            _logger.i('✅ Token用户ID一致性检查通过');
          }
          
        } catch (e) {
          debugResult['token_consistency_error'] = e.toString();
          _logger.e('❌ Token一致性检查失败: $e');
        }
      }

      // 5. 检查其他存储的用户相关数据
      final allKeys = await _storage.readAll();
      final userRelatedKeys = allKeys.keys.where((key) => 
        key.contains('user') || 
        key.contains('auth') || 
        key.contains('session') ||
        key.contains('device')
      ).toList();

      debugResult['other_stored_data'] = {};
      for (final key in userRelatedKeys) {
        debugResult['other_stored_data'][key] = allKeys[key];
      }

      // 6. 生成调试报告
      final report = _generateDebugReport(debugResult);
      debugResult['debug_report'] = report;

      _logger.i('🎯 TokenDebugTool: 调试分析完成');
      _logger.i('📊 调试报告:\n$report');

      return debugResult;

    } catch (e, stackTrace) {
      _logger.e('❌ TokenDebugTool: 调试分析失败', error: e, stackTrace: stackTrace);
      debugResult['error'] = e.toString();
      debugResult['stack_trace'] = stackTrace.toString();
      return debugResult;
    }
  }

  /// 生成人类可读的调试报告
  static String _generateDebugReport(Map<String, dynamic> debugResult) {
    final buffer = StringBuffer();
    buffer.writeln('🔍 SweatMint Token & User Debug Report');
    buffer.writeln('=' * 50);
    buffer.writeln('时间: ${debugResult['timestamp']}');
    buffer.writeln();

    // Token存储状态
    final storedTokens = debugResult['stored_tokens'] as Map<String, dynamic>?;
    if (storedTokens != null) {
      buffer.writeln('📱 Token存储状态:');
      buffer.writeln('  Access Token: ${storedTokens['access_token_exists'] ? '✅ 存在' : '❌ 不存在'}');
      buffer.writeln('  Refresh Token: ${storedTokens['refresh_token_exists'] ? '✅ 存在' : '❌ 不存在'}');
      buffer.writeln('  Token过期时间: ${storedTokens['token_expiry'] ?? '未设置'}');
      buffer.writeln();
    }

    // Access Token分析
    final accessDecoded = debugResult['access_token_decoded'] as Map<String, dynamic>?;
    if (accessDecoded != null) {
      buffer.writeln('🔑 Access Token分析:');
      buffer.writeln('  用户ID: ${accessDecoded['user_id']}');
      buffer.writeln('  是否过期: ${accessDecoded['is_expired'] ? '❌ 已过期' : '✅ 有效'}');
      buffer.writeln('  过期时间: ${accessDecoded['expiry_date']}');
      buffer.writeln();
    }

    // Token一致性检查
    final consistency = debugResult['token_consistency'] as Map<String, dynamic>?;
    if (consistency != null) {
      buffer.writeln('🔄 Token一致性检查:');
      buffer.writeln('  Access Token用户ID: ${consistency['access_user_id']}');
      buffer.writeln('  Refresh Token用户ID: ${consistency['refresh_user_id']}');
      buffer.writeln('  用户ID匹配: ${consistency['user_ids_match'] ? '✅ 匹配' : '❌ 不匹配'}');
      
      if (consistency['potential_mismatch'] == true) {
        buffer.writeln('  🚨 警告: 检测到Token用户ID不匹配！这可能是设备冲突的原因！');
      }
      buffer.writeln();
    }

    // 错误信息
    if (debugResult.containsKey('error')) {
      buffer.writeln('❌ 错误信息:');
      buffer.writeln('  ${debugResult['error']}');
      buffer.writeln();
    }

    buffer.writeln('=' * 50);
    return buffer.toString();
  }

  /// 🔧 清除所有Token和用户相关数据
  static Future<void> clearAllUserData() async {
    _logger.i('🧹 TokenDebugTool: 清除所有用户相关数据');
    
    try {
      final allKeys = await _storage.readAll();
      final userRelatedKeys = allKeys.keys.where((key) => 
        key.contains('user') || 
        key.contains('auth') || 
        key.contains('token') ||
        key.contains('session') ||
        key.contains('device')
      ).toList();

      for (final key in userRelatedKeys) {
        await _storage.delete(key: key);
        _logger.d('🗑️ 已删除: $key');
      }

      _logger.i('✅ TokenDebugTool: 用户数据清除完成');
    } catch (e) {
      _logger.e('❌ TokenDebugTool: 清除用户数据失败', error: e);
    }
  }
}
