import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:logger/logger.dart';
import 'package:provider/provider.dart';

import '../../../../config/themes.dart';
import '../../../../core/services/health_authorization_dialog_manager.dart';
import '../../../../l10n/app_localizations.dart';
import '../providers/health_permission_provider.dart';

/// 🔥 SweatMint智能健康数据授权弹窗
/// 
/// 特性：
/// 1. 动态权限状态显示，实时反映权限变化
/// 2. 用户友好的授权引导界面
/// 3. 支持权限状态实时更新，无需重新弹出
/// 4. 符合SweatMint设计风格
class HealthAuthorizationDialog extends StatefulWidget {
  final Map<String, String> initialPermissions;
  final VoidCallback? onDismiss;
  
  const HealthAuthorizationDialog({
    Key? key,
    required this.initialPermissions,
    this.onDismiss,
  }) : super(key: key);

  @override
  State<HealthAuthorizationDialog> createState() => _HealthAuthorizationDialogState();
}

class _HealthAuthorizationDialogState extends State<HealthAuthorizationDialog> {
  final Logger _logger = Logger();
  late Map<String, String> _currentPermissions;
  StreamSubscription<Map<String, String>>? _permissionUpdateSubscription;
  bool _isRequestingPermissions = false;
  
  @override
  void initState() {
    super.initState();
    _currentPermissions = Map.from(widget.initialPermissions);
    
    // 注册权限更新监听
    _permissionUpdateSubscription = HealthAuthorizationDialogManager
        .instance.permissionUpdateStream.listen(_onPermissionUpdate);
    
    _logger.i('🎨 HealthAuthorizationDialog初始化，初始权限状态: $_currentPermissions');
  }
  
  @override
  void dispose() {
    _permissionUpdateSubscription?.cancel();
    super.dispose();
  }
  
  /// 🔥 权限状态更新回调
  void _onPermissionUpdate(Map<String, String> newPermissions) {
    if (mounted) {
      setState(() {
        _currentPermissions = Map.from(newPermissions);
      });
      _logger.i('🔄 弹窗权限状态已更新: $newPermissions');
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 320.w,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20.r,
              offset: Offset(0, 10.h),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            _buildContent(),
            ..._buildActions(),
          ],
        ),
      ),
    );
  }
  
  /// 🔥 构建弹窗头部
  Widget _buildHeader() {
    final theme = AppTheme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final authorizedCount = _currentPermissions.values
        .where((status) => status == 'authorized').length;
    final totalCount = _currentPermissions.length;

    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colors.primary,
            theme.colors.primary.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        children: [
          // 图标和标题
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(
                  Icons.health_and_safety,
                  color: Colors.white,
                  size: 24.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Text(
                  l10n.healthAuthorizationTitle,
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          // 进度指示器
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  authorizedCount == totalCount ? Icons.check_circle : Icons.pending,
                  color: Colors.white,
                  size: 16.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  '$authorizedCount/$totalCount ${AppLocalizations.of(context)!.healthAuthPermissionsGranted}',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// 🔥 构建弹窗内容
  Widget _buildContent() {
    return SizedBox(
      width: 280.w,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDescription(),
          SizedBox(height: 16.h),
          _buildPermissionList(),
          SizedBox(height: 16.h),
          _buildTips(),
        ],
      ),
    );
  }
  
  /// 🔥 构建描述文本
  Widget _buildDescription() {
    final theme = AppTheme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final authorizedCount = _currentPermissions.values
        .where((status) => status == 'authorized').length;
    final totalCount = _currentPermissions.length;

    String description;
    if (authorizedCount == 0) {
      description = l10n.healthAuthDescriptionNone;
    } else if (authorizedCount < totalCount) {
      description = l10n.healthAuthDescriptionPartial;
    } else {
      description = l10n.healthAuthDescriptionComplete;
    }

    return Text(
      description,
      style: TextStyle(
        fontSize: 14.sp,
        color: theme.colors.neutral,
        height: 1.4,
      ),
    );
  }
  
  /// 🔥 构建权限列表
  Widget _buildPermissionList() {
    final theme = AppTheme.of(context);
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.colors.background,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: theme.colors.outline,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          _buildPermissionItem('steps', AppLocalizations.of(context)!.healthStepsLabel, Icons.directions_walk),
          SizedBox(height: 8.h),
          _buildPermissionItem('distance', AppLocalizations.of(context)!.healthDistanceLabel, Icons.straighten),
          SizedBox(height: 8.h),
          _buildPermissionItem('calories', AppLocalizations.of(context)!.healthCaloriesLabel, Icons.local_fire_department),
        ],
      ),
    );
  }
  
  /// 🔥 构建单个权限项
  Widget _buildPermissionItem(String key, String name, IconData icon) {
    final theme = AppTheme.of(context);
    final status = _currentPermissions[key] ?? 'notDetermined';
    final isAuthorized = status == 'authorized';

    return Row(
      children: [
        Icon(
          icon,
          size: 20.sp,
          color: isAuthorized ? Colors.green : theme.colors.neutral,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            name,
            style: TextStyle(
              fontSize: 14.sp,
              color: theme.colors.onSurface,
            ),
          ),
        ),
        _buildPermissionStatus(status),
      ],
    );
  }
  
  /// 🔥 构建权限状态标识（现代化设计）
  Widget _buildPermissionStatus(String status) {
    final theme = AppTheme.of(context);
    final l10n = AppLocalizations.of(context)!;

    IconData icon;
    Color color;
    String text;

    switch (status) {
      case 'authorized':
        icon = Icons.check_circle;
        color = Colors.green;
        text = l10n.healthAuthStatusAuthorized;
        break;
      case 'denied':
        icon = Icons.cancel;
        color = theme.colors.error;
        text = l10n.healthAuthStatusDenied;
        break;
      default:
        icon = Icons.help_outline;
        color = Colors.orange;
        text = l10n.healthAuthStatusNotDetermined;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14.sp,
            color: color,
          ),
          SizedBox(width: 4.w),
          Text(
            text,
            style: TextStyle(
              fontSize: 12.sp,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 🔥 构建提示信息（现代化设计）
  Widget _buildTips() {
    final theme = AppTheme.of(context);
    final l10n = AppLocalizations.of(context)!;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colors.primary.withOpacity(0.05),
            theme.colors.primary.withOpacity(0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colors.primary.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: theme.colors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6.r),
                ),
                child: Icon(
                  Icons.lightbulb_outline,
                  size: 16.sp,
                  color: theme.colors.primary,
                ),
              ),
              SizedBox(width: 8.w),
              Text(
                l10n.healthAuthTipTitle,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: theme.colors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            l10n.healthAuthTipContent,
            style: TextStyle(
              fontSize: 13.sp,
              color: theme.colors.onSurface.withOpacity(0.8),
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 🔥 构建操作按钮
  List<Widget> _buildActions() {
    final theme = AppTheme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final allAuthorized = _currentPermissions.values
        .every((status) => status == 'authorized');

    if (allAuthorized) {
      // 所有权限都已授权，只显示确定按钮
      return [
        TextButton(
          onPressed: _handleDismiss,
          child: Text(
            l10n.healthAuthButtonOk,
            style: TextStyle(
              fontSize: 16.sp,
              color: theme.colors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ];
    }

    // 仍有未授权权限，显示跳过和授权按钮
    return [
      TextButton(
        onPressed: _handleDismiss,
        child: Text(
          l10n.healthAuthButtonLater,
          style: TextStyle(
            fontSize: 16.sp,
            color: theme.colors.neutral,
          ),
        ),
      ),
      SizedBox(width: 8.w),
      ElevatedButton(
        onPressed: _isRequestingPermissions ? null : _handleRequestPermissions,
        style: ElevatedButton.styleFrom(
          backgroundColor: theme.colors.primary,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        ),
        child: _isRequestingPermissions
            ? SizedBox(
                width: 16.w,
                height: 16.h,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                l10n.healthAuthButtonGrant,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
      ),
    ];
  }
  
  /// 🔥 处理权限请求
  Future<void> _handleRequestPermissions() async {
    if (_isRequestingPermissions) return;
    
    setState(() {
      _isRequestingPermissions = true;
    });
    
    try {
      _logger.i('🔍 用户点击授权按钮，开始请求健康权限');
      
      final healthPermissionProvider = context.read<HealthPermissionProvider>();
      final success = await healthPermissionProvider.requestPermissions();
      
      if (success) {
        _logger.i('✅ 权限请求成功');
        // 权限状态会通过Stream自动更新，无需手动处理
      } else {
        _logger.w('⚠️ 权限请求失败或被用户拒绝');
      }
      
    } catch (e) {
      _logger.e('❌ 权限请求过程中发生错误', error: e);
    } finally {
      if (mounted) {
        setState(() {
          _isRequestingPermissions = false;
        });
      }
    }
  }
  
  /// 🔥 处理弹窗关闭
  void _handleDismiss() {
    _logger.i('🔄 用户关闭健康授权弹窗');
    widget.onDismiss?.call();
    Navigator.of(context).pop();
  }
}
