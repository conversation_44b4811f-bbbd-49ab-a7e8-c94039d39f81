import 'package:logger/logger.dart';

/// 🔥 BOSS关键修复：错误消息管理器
/// 统一管理所有错误消息，将技术错误转换为用户友好的提示
/// 提供具体的错误处理指导信息
class ErrorMessageManager {
  static final Logger _logger = Logger();
  
  // 单例模式
  static ErrorMessageManager? _instance;
  static ErrorMessageManager get instance {
    _instance ??= ErrorMessageManager._internal();
    return _instance!;
  }
  
  ErrorMessageManager._internal();
  
  /// 🔥 BOSS核心：获取用户友好的错误消息
  /// 
  /// [errorCode] 错误代码
  /// [technicalError] 技术错误信息（可选）
  /// [context] 错误上下文（可选）
  /// 
  /// 返回用户友好的错误消息和指导信息
  UserFriendlyError getUserFriendlyError({
    required String errorCode,
    String? technicalError,
    Map<String, dynamic>? context,
  }) {
    _logger.d('🔍 ErrorMessageManager: 处理错误代码: $errorCode');
    
    try {
      // 根据错误代码获取对应的用户友好消息
      final errorInfo = _getErrorInfo(errorCode);
      
      // 添加上下文信息
      final contextualMessage = _addContextToMessage(
        errorInfo.message,
        context,
      );
      
      // 添加技术错误信息（如果需要）
      final detailedMessage = technicalError != null 
        ? '$contextualMessage\n\nTechnical details: $technicalError'
        : contextualMessage;
      
      return UserFriendlyError(
        title: errorInfo.title,
        message: detailedMessage,
        actionGuide: errorInfo.actionGuide,
        severity: errorInfo.severity,
        canRetry: errorInfo.canRetry,
        retryDelay: errorInfo.retryDelay,
        helpUrl: errorInfo.helpUrl,
        errorCode: errorCode,
        timestamp: DateTime.now(),
      );
      
    } catch (e) {
      _logger.e('❌ ErrorMessageManager: 处理错误消息异常: $e');
      
      // 降级处理：返回通用错误消息
      return _getGenericError(errorCode, technicalError);
    }
  }
  
  /// 🔥 BOSS功能：获取错误信息
  ErrorInfo _getErrorInfo(String errorCode) {
    switch (errorCode.toLowerCase()) {
      // 网络相关错误
      case 'network_offline':
      case 'no_internet':
        return ErrorInfo(
          title: 'No Internet Connection',
          message: 'Your device is not connected to the internet. Health data will be saved locally and synced when connection is restored.',
          actionGuide: 'Please check your WiFi or mobile data connection and try again.',
          severity: ErrorSeverity.WARNING,
          canRetry: true,
          retryDelay: Duration(seconds: 5),
          helpUrl: 'https://sweatmint.com/help/network-issues',
        );
        
      case 'network_timeout':
        return ErrorInfo(
          title: 'Connection Timeout',
          message: 'The request took too long to complete. This might be due to a slow internet connection.',
          actionGuide: 'Please check your internet connection and try again. If the problem persists, try again later.',
          severity: ErrorSeverity.WARNING,
          canRetry: true,
          retryDelay: Duration(seconds: 10),
          helpUrl: 'https://sweatmint.com/help/network-issues',
        );
        
      case 'server_error':
      case 'internal_server_error':
        return ErrorInfo(
          title: 'Server Error',
          message: 'Our servers are experiencing issues. Your data is safe and will be synced once the issue is resolved.',
          actionGuide: 'Please try again in a few minutes. If the problem continues, contact our support team.',
          severity: ErrorSeverity.ERROR,
          canRetry: true,
          retryDelay: Duration(minutes: 2),
          helpUrl: 'https://sweatmint.com/help/server-issues',
        );
        
      // 认证相关错误
      case 'authentication_failed':
      case 'invalid_token':
        return ErrorInfo(
          title: 'Authentication Required',
          message: 'Your session has expired. Please log in again to continue syncing your health data.',
          actionGuide: 'Tap "Login" to sign in to your account again.',
          severity: ErrorSeverity.ERROR,
          canRetry: false,
          helpUrl: 'https://sweatmint.com/help/login-issues',
        );
        
      case 'account_suspended':
        return ErrorInfo(
          title: 'Account Suspended',
          message: 'Your account has been temporarily suspended. Please contact our support team for assistance.',
          actionGuide: 'Contact <NAME_EMAIL> for help with your account.',
          severity: ErrorSeverity.CRITICAL,
          canRetry: false,
          helpUrl: 'https://sweatmint.com/help/account-issues',
        );
        
      // 权限相关错误
      case 'health_permission_denied':
      case 'permission_denied':
        return ErrorInfo(
          title: 'Health Data Permission Required',
          message: 'SweatMint needs access to your health data to track your fitness progress and calculate rewards.',
          actionGuide: 'Go to Settings > Privacy & Security > Health and enable access for SweatMint.',
          severity: ErrorSeverity.WARNING,
          canRetry: true,
          helpUrl: 'https://sweatmint.com/help/health-permissions',
        );
        
      case 'location_permission_denied':
        return ErrorInfo(
          title: 'Location Permission Required',
          message: 'Location access is needed to accurately track your outdoor activities and distance.',
          actionGuide: 'Go to Settings > Privacy & Security > Location Services and enable access for SweatMint.',
          severity: ErrorSeverity.WARNING,
          canRetry: true,
          helpUrl: 'https://sweatmint.com/help/location-permissions',
        );
        
      // 数据相关错误
      case 'data_conflict':
      case 'sync_conflict':
        return ErrorInfo(
          title: 'Data Sync Conflict',
          message: 'There\'s a conflict between your local data and server data. We\'ve automatically resolved it using the most recent information.',
          actionGuide: 'Your data has been safely merged. If you notice any discrepancies, please contact support.',
          severity: ErrorSeverity.INFO,
          canRetry: false,
          helpUrl: 'https://sweatmint.com/help/data-sync',
        );
        
      case 'invalid_health_data':
        return ErrorInfo(
          title: 'Invalid Health Data',
          message: 'Some of your health data appears to be corrupted or invalid.',
          actionGuide: 'Please restart the app and try syncing again. If the issue persists, contact support.',
          severity: ErrorSeverity.WARNING,
          canRetry: true,
          retryDelay: Duration(seconds: 30),
          helpUrl: 'https://sweatmint.com/help/data-issues',
        );
        
      // 应用相关错误
      case 'app_update_required':
        return ErrorInfo(
          title: 'App Update Required',
          message: 'A newer version of SweatMint is required to continue. Please update the app from the App Store.',
          actionGuide: 'Go to the App Store and update SweatMint to the latest version.',
          severity: ErrorSeverity.CRITICAL,
          canRetry: false,
          helpUrl: 'https://sweatmint.com/help/app-updates',
        );
        
      case 'maintenance_mode':
        return ErrorInfo(
          title: 'Maintenance in Progress',
          message: 'SweatMint is currently undergoing maintenance to improve your experience. We\'ll be back shortly.',
          actionGuide: 'Please try again in a few minutes. Your data is safe and will sync automatically when maintenance is complete.',
          severity: ErrorSeverity.INFO,
          canRetry: true,
          retryDelay: Duration(minutes: 5),
          helpUrl: 'https://sweatmint.com/help/maintenance',
        );
        
      // 设备相关错误
      case 'device_not_supported':
        return ErrorInfo(
          title: 'Device Not Supported',
          message: 'Your device doesn\'t support all health tracking features. Some functionality may be limited.',
          actionGuide: 'You can still use basic features. Consider upgrading to a newer device for full functionality.',
          severity: ErrorSeverity.WARNING,
          canRetry: false,
          helpUrl: 'https://sweatmint.com/help/device-compatibility',
        );
        
      case 'storage_full':
        return ErrorInfo(
          title: 'Storage Space Low',
          message: 'Your device is running low on storage space, which may affect app performance.',
          actionGuide: 'Please free up some storage space by deleting unused apps or files.',
          severity: ErrorSeverity.WARNING,
          canRetry: true,
          helpUrl: 'https://sweatmint.com/help/storage-issues',
        );
        
      // 任务和奖励相关错误
      case 'task_completion_failed':
        return ErrorInfo(
          title: 'Task Completion Error',
          message: 'We couldn\'t complete your task due to a technical issue. Your progress has been saved.',
          actionGuide: 'Please try completing the task again. If the issue persists, contact support.',
          severity: ErrorSeverity.WARNING,
          canRetry: true,
          retryDelay: Duration(seconds: 30),
          helpUrl: 'https://sweatmint.com/help/task-issues',
        );
        
      case 'reward_claim_failed':
        return ErrorInfo(
          title: 'Reward Claim Failed',
          message: 'We couldn\'t process your reward claim at this time. Your rewards are safe and will be available shortly.',
          actionGuide: 'Please try claiming your reward again in a few minutes.',
          severity: ErrorSeverity.WARNING,
          canRetry: true,
          retryDelay: Duration(minutes: 1),
          helpUrl: 'https://sweatmint.com/help/reward-issues',
        );
        
      // 默认错误
      default:
        return ErrorInfo(
          title: 'Something Went Wrong',
          message: 'An unexpected error occurred. Don\'t worry, your data is safe.',
          actionGuide: 'Please try again. If the problem continues, restart the app or contact our support team.',
          severity: ErrorSeverity.ERROR,
          canRetry: true,
          retryDelay: Duration(seconds: 30),
          helpUrl: 'https://sweatmint.com/help/general-issues',
        );
    }
  }
  
  /// 添加上下文信息到错误消息
  String _addContextToMessage(String baseMessage, Map<String, dynamic>? context) {
    if (context == null || context.isEmpty) {
      return baseMessage;
    }
    
    String contextualMessage = baseMessage;
    
    // 添加特定上下文信息
    if (context.containsKey('retry_count')) {
      final retryCount = context['retry_count'];
      if (retryCount > 1) {
        contextualMessage += '\n\nThis is attempt #$retryCount.';
      }
    }
    
    if (context.containsKey('last_success_time')) {
      final lastSuccess = context['last_success_time'];
      if (lastSuccess != null) {
        contextualMessage += '\n\nLast successful sync: $lastSuccess';
      }
    }
    
    if (context.containsKey('affected_features')) {
      final features = context['affected_features'] as List?;
      if (features != null && features.isNotEmpty) {
        contextualMessage += '\n\nAffected features: ${features.join(', ')}';
      }
    }
    
    return contextualMessage;
  }
  
  /// 获取通用错误消息
  UserFriendlyError _getGenericError(String errorCode, String? technicalError) {
    return UserFriendlyError(
      title: 'Unexpected Error',
      message: 'An unexpected error occurred. Your data is safe and we\'re working to resolve the issue.',
      actionGuide: 'Please try again later. If the problem persists, contact our support team.',
      severity: ErrorSeverity.ERROR,
      canRetry: true,
      retryDelay: Duration(minutes: 1),
      helpUrl: 'https://sweatmint.com/help/general-issues',
      errorCode: errorCode,
      timestamp: DateTime.now(),
      technicalDetails: technicalError,
    );
  }
  
  /// 🔥 BOSS功能：获取快速错误消息（用于简单场景）
  String getQuickErrorMessage(String errorCode) {
    final errorInfo = _getErrorInfo(errorCode);
    return errorInfo.message;
  }
  
  /// 🔥 BOSS功能：检查错误是否可以重试
  bool canRetryError(String errorCode) {
    final errorInfo = _getErrorInfo(errorCode);
    return errorInfo.canRetry;
  }
  
  /// 🔥 BOSS功能：获取重试延迟时间
  Duration? getRetryDelay(String errorCode) {
    final errorInfo = _getErrorInfo(errorCode);
    return errorInfo.retryDelay;
  }
}

// ========== 数据模型和枚举 ==========

/// 错误严重程度枚举
enum ErrorSeverity {
  INFO,      // 信息提示
  WARNING,   // 警告
  ERROR,     // 错误
  CRITICAL,  // 严重错误
}

/// 🔥 BOSS数据模型：错误信息
class ErrorInfo {
  final String title;
  final String message;
  final String actionGuide;
  final ErrorSeverity severity;
  final bool canRetry;
  final Duration? retryDelay;
  final String? helpUrl;

  ErrorInfo({
    required this.title,
    required this.message,
    required this.actionGuide,
    required this.severity,
    required this.canRetry,
    this.retryDelay,
    this.helpUrl,
  });
}

/// 🔥 BOSS数据模型：用户友好错误
class UserFriendlyError {
  final String title;
  final String message;
  final String actionGuide;
  final ErrorSeverity severity;
  final bool canRetry;
  final Duration? retryDelay;
  final String? helpUrl;
  final String errorCode;
  final DateTime timestamp;
  final String? technicalDetails;

  UserFriendlyError({
    required this.title,
    required this.message,
    required this.actionGuide,
    required this.severity,
    required this.canRetry,
    this.retryDelay,
    this.helpUrl,
    required this.errorCode,
    required this.timestamp,
    this.technicalDetails,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'message': message,
      'action_guide': actionGuide,
      'severity': severity.name,
      'can_retry': canRetry,
      'retry_delay_seconds': retryDelay?.inSeconds,
      'help_url': helpUrl,
      'error_code': errorCode,
      'timestamp': timestamp.toIso8601String(),
      'technical_details': technicalDetails,
    };
  }

  /// 获取错误严重程度的颜色
  String getSeverityColor() {
    switch (severity) {
      case ErrorSeverity.INFO:
        return '#2196F3'; // 蓝色
      case ErrorSeverity.WARNING:
        return '#FF9800'; // 橙色
      case ErrorSeverity.ERROR:
        return '#F44336'; // 红色
      case ErrorSeverity.CRITICAL:
        return '#D32F2F'; // 深红色
    }
  }

  /// 获取错误严重程度的图标
  String getSeverityIcon() {
    switch (severity) {
      case ErrorSeverity.INFO:
        return 'info';
      case ErrorSeverity.WARNING:
        return 'warning';
      case ErrorSeverity.ERROR:
        return 'error';
      case ErrorSeverity.CRITICAL:
        return 'error_outline';
    }
  }
}
