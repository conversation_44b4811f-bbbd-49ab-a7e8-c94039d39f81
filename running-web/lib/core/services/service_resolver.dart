/// SweatMint 统一服务解析器
/// 
/// 🔥 解决Provider和GetIt架构冲突的统一解决方案
/// 提供统一的服务获取接口，支持多种获取策略：
/// 1. Provider获取（优先）
/// 2. GetIt获取（备选）
/// 3. 直接创建（兜底）

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';

import 'health_service.dart';
import 'health_service_impl.dart';
import '../network/api_client.dart';
import '../di/service_locator.dart';

class ServiceResolver {
  static final Logger _logger = Logger();
  
  /// 🔥 统一获取HealthService的方法
  /// 支持多重获取策略，确保在任何情况下都能获取到服务实例
  static Future<HealthService> getHealthService({
    BuildContext? context,
    ApiClient? apiClient,
  }) async {
    _logger.d('🔍 ServiceResolver: 开始获取HealthService');
    
    // 策略1：从Provider获取（如果有context）
    if (context != null) {
      try {
        final healthService = Provider.of<HealthService>(context, listen: false);
        _logger.d('✅ ServiceResolver: 从Provider获取HealthService成功');
        return healthService;
      } catch (e) {
        _logger.w('⚠️ ServiceResolver: Provider获取HealthService失败: $e');
      }
    }
    
    // 策略2：从GetIt获取（如果已注册且有apiClient）
    if (apiClient != null) {
      try {
        if (GetIt.instance.isRegistered<HealthService>()) {
          final healthService = ServiceLocator.getHealthService(apiClient);
          _logger.d('✅ ServiceResolver: 从GetIt获取HealthService成功');
          return healthService;
        }
      } catch (e) {
        _logger.w('⚠️ ServiceResolver: GetIt获取HealthService失败: $e');
      }
    }
    
    // 策略3：直接创建实例（兜底方案）
    try {
      final resolvedApiClient = apiClient ?? await _getApiClientFallback();
      final healthService = HealthServiceImpl(apiClient: resolvedApiClient);
      _logger.d('✅ ServiceResolver: 直接创建HealthService成功');
      return healthService;
    } catch (e) {
      _logger.e('❌ ServiceResolver: 所有策略都失败: $e');
      throw HealthServiceUnavailableException('无法获取HealthService: $e');
    }
  }
  
  /// 🔥 获取ApiClient的兜底方法
  static Future<ApiClient> _getApiClientFallback() async {
    // 尝试从GetIt获取
    try {
      return GetIt.instance<ApiClient>();
    } catch (e) {
      _logger.w('GetIt获取ApiClient失败，创建临时实例: $e');
      // 创建临时ApiClient实例
      return ApiClient.createTemporary();
    }
  }
}

/// 🔥 HealthService不可用异常
class HealthServiceUnavailableException implements Exception {
  final String message;
  HealthServiceUnavailableException(this.message);
  
  @override
  String toString() => 'HealthServiceUnavailableException: $message';
}
