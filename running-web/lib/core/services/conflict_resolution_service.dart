import 'package:logger/logger.dart';
import 'data_conflict_detector.dart';

/// 🔥 BOSS关键修复：冲突解决策略服务
/// 实现健康数据同步冲突的自动解决策略
/// 确保数据一致性和完整性
class ConflictResolutionService {
  static final Logger _logger = Logger();
  
  // 单例模式
  static ConflictResolutionService? _instance;
  static ConflictResolutionService get instance {
    _instance ??= ConflictResolutionService._internal();
    return _instance!;
  }
  
  ConflictResolutionService._internal();
  
  /// 🔥 BOSS核心：解决健康数据冲突
  /// 
  /// [conflictResult] 冲突检测结果
  /// [resolutionStrategy] 解决策略（可选，默认使用推荐策略）
  /// 
  /// 返回解决后的数据和解决结果
  Future<ConflictResolutionResult> resolveHealthDataConflict({
    required ConflictDetectionResult conflictResult,
    ConflictResolutionStrategy? resolutionStrategy,
  }) async {
    _logger.i('🔧 ConflictResolutionService: 开始解决健康数据冲突');
    _logger.i('  冲突类型: ${conflictResult.conflictType.name}');
    _logger.i('  冲突严重程度: ${conflictResult.conflictSeverity.name}');
    _logger.i('  推荐操作: ${conflictResult.recommendedAction.name}');
    
    try {
      // 如果没有冲突，直接返回服务器数据
      if (!conflictResult.hasConflict) {
        return ConflictResolutionResult(
          success: true,
          resolvedData: conflictResult.serverData,
          resolutionStrategy: ConflictResolutionStrategy.NO_CONFLICT,
          resolutionDetails: {'message': '无冲突，使用服务器数据'},
          resolutionTime: DateTime.now(),
        );
      }
      
      // 确定解决策略
      final strategy = resolutionStrategy ?? _getDefaultStrategy(conflictResult);
      
      // 根据策略解决冲突
      switch (strategy) {
        case ConflictResolutionStrategy.SERVER_PRIORITY:
          return _resolveWithServerPriority(conflictResult);
          
        case ConflictResolutionStrategy.LOCAL_PRIORITY:
          return _resolveWithLocalPriority(conflictResult);
          
        case ConflictResolutionStrategy.TIMESTAMP_PRIORITY:
          return _resolveWithTimestampPriority(conflictResult);
          
        case ConflictResolutionStrategy.VERSION_PRIORITY:
          return _resolveWithVersionPriority(conflictResult);
          
        case ConflictResolutionStrategy.MERGE_DATA:
          return _resolveWithDataMerge(conflictResult);
          
        case ConflictResolutionStrategy.MANUAL_REVIEW:
          return _requireManualReview(conflictResult);
          
        case ConflictResolutionStrategy.NO_CONFLICT:
          return ConflictResolutionResult(
            success: true,
            resolvedData: conflictResult.serverData,
            resolutionStrategy: strategy,
            resolutionDetails: {'message': '无冲突'},
            resolutionTime: DateTime.now(),
          );
      }
      
    } catch (e) {
      _logger.e('❌ ConflictResolutionService: 冲突解决异常: $e');
      return ConflictResolutionResult(
        success: false,
        resolvedData: conflictResult.serverData, // 降级使用服务器数据
        resolutionStrategy: ConflictResolutionStrategy.SERVER_PRIORITY,
        resolutionDetails: {
          'error': e.toString(),
          'fallback': 'used_server_data',
        },
        resolutionTime: DateTime.now(),
      );
    }
  }
  
  /// 🔥 BOSS策略：服务器优先解决
  ConflictResolutionResult _resolveWithServerPriority(ConflictDetectionResult conflictResult) {
    _logger.i('🌐 使用服务器优先策略解决冲突');
    
    return ConflictResolutionResult(
      success: true,
      resolvedData: conflictResult.serverData,
      resolutionStrategy: ConflictResolutionStrategy.SERVER_PRIORITY,
      resolutionDetails: {
        'strategy': 'server_priority',
        'reason': '服务器数据被认为是权威数据源',
        'conflict_type': conflictResult.conflictType.name,
      },
      resolutionTime: DateTime.now(),
    );
  }
  
  /// 🔥 BOSS策略：本地优先解决
  ConflictResolutionResult _resolveWithLocalPriority(ConflictDetectionResult conflictResult) {
    _logger.i('📱 使用本地优先策略解决冲突');
    
    return ConflictResolutionResult(
      success: true,
      resolvedData: conflictResult.localData,
      resolutionStrategy: ConflictResolutionStrategy.LOCAL_PRIORITY,
      resolutionDetails: {
        'strategy': 'local_priority',
        'reason': '本地数据被认为是最新的',
        'conflict_type': conflictResult.conflictType.name,
      },
      resolutionTime: DateTime.now(),
    );
  }
  
  /// 🔥 BOSS策略：时间戳优先解决
  ConflictResolutionResult _resolveWithTimestampPriority(ConflictDetectionResult conflictResult) {
    _logger.i('⏰ 使用时间戳优先策略解决冲突');
    
    final localTimestamp = _extractTimestamp(conflictResult.localData);
    final serverTimestamp = _extractTimestamp(conflictResult.serverData);
    
    Map<String, dynamic> resolvedData;
    String reason;
    
    if (localTimestamp != null && serverTimestamp != null) {
      if (localTimestamp.isAfter(serverTimestamp)) {
        resolvedData = conflictResult.localData;
        reason = '本地数据时间戳更新 (${localTimestamp.toIso8601String()})';
      } else {
        resolvedData = conflictResult.serverData;
        reason = '服务器数据时间戳更新 (${serverTimestamp.toIso8601String()})';
      }
    } else {
      // 如果无法获取时间戳，默认使用服务器数据
      resolvedData = conflictResult.serverData;
      reason = '无法获取时间戳，默认使用服务器数据';
    }
    
    return ConflictResolutionResult(
      success: true,
      resolvedData: resolvedData,
      resolutionStrategy: ConflictResolutionStrategy.TIMESTAMP_PRIORITY,
      resolutionDetails: {
        'strategy': 'timestamp_priority',
        'reason': reason,
        'local_timestamp': localTimestamp?.toIso8601String(),
        'server_timestamp': serverTimestamp?.toIso8601String(),
      },
      resolutionTime: DateTime.now(),
    );
  }
  
  /// 🔥 BOSS策略：版本优先解决
  ConflictResolutionResult _resolveWithVersionPriority(ConflictDetectionResult conflictResult) {
    _logger.i('🔢 使用版本优先策略解决冲突');
    
    final localVersion = _extractVersion(conflictResult.localData);
    final serverVersion = _extractVersion(conflictResult.serverData);
    
    Map<String, dynamic> resolvedData;
    String reason;
    
    if (localVersion != null && serverVersion != null) {
      if (_compareVersions(localVersion, serverVersion) > 0) {
        resolvedData = conflictResult.localData;
        reason = '本地数据版本更高 ($localVersion)';
      } else {
        resolvedData = conflictResult.serverData;
        reason = '服务器数据版本更高 ($serverVersion)';
      }
    } else {
      // 如果无法获取版本，默认使用服务器数据
      resolvedData = conflictResult.serverData;
      reason = '无法获取版本信息，默认使用服务器数据';
    }
    
    return ConflictResolutionResult(
      success: true,
      resolvedData: resolvedData,
      resolutionStrategy: ConflictResolutionStrategy.VERSION_PRIORITY,
      resolutionDetails: {
        'strategy': 'version_priority',
        'reason': reason,
        'local_version': localVersion,
        'server_version': serverVersion,
      },
      resolutionTime: DateTime.now(),
    );
  }
  
  /// 🔥 BOSS策略：数据合并解决
  ConflictResolutionResult _resolveWithDataMerge(ConflictDetectionResult conflictResult) {
    _logger.i('🔀 使用数据合并策略解决冲突');
    
    try {
      final mergedData = _mergeHealthData(
        conflictResult.localData,
        conflictResult.serverData,
        conflictResult.conflictDetails,
      );
      
      return ConflictResolutionResult(
        success: true,
        resolvedData: mergedData,
        resolutionStrategy: ConflictResolutionStrategy.MERGE_DATA,
        resolutionDetails: {
          'strategy': 'data_merge',
          'reason': '合并本地和服务器数据',
          'merge_rules': _getMergeRules(),
        },
        resolutionTime: DateTime.now(),
      );
      
    } catch (e) {
      _logger.e('❌ 数据合并失败: $e，降级使用服务器数据');
      return _resolveWithServerPriority(conflictResult);
    }
  }
  
  /// 🔥 BOSS策略：需要手动审查
  ConflictResolutionResult _requireManualReview(ConflictDetectionResult conflictResult) {
    _logger.w('👤 冲突需要手动审查');
    
    return ConflictResolutionResult(
      success: false,
      resolvedData: conflictResult.serverData, // 临时使用服务器数据
      resolutionStrategy: ConflictResolutionStrategy.MANUAL_REVIEW,
      resolutionDetails: {
        'strategy': 'manual_review',
        'reason': '冲突复杂，需要手动审查',
        'conflict_severity': conflictResult.conflictSeverity.name,
        'conflict_details': conflictResult.conflictDetails,
      },
      resolutionTime: DateTime.now(),
    );
  }
  
  /// 获取默认解决策略
  ConflictResolutionStrategy _getDefaultStrategy(ConflictDetectionResult conflictResult) {
    switch (conflictResult.recommendedAction) {
      case ConflictAction.USE_SERVER_DATA:
        return ConflictResolutionStrategy.SERVER_PRIORITY;
      case ConflictAction.USE_LOCAL_DATA:
        return ConflictResolutionStrategy.LOCAL_PRIORITY;
      case ConflictAction.USE_LATEST_TIMESTAMP:
        return ConflictResolutionStrategy.TIMESTAMP_PRIORITY;
      case ConflictAction.USE_HIGHER_VERSION:
        return ConflictResolutionStrategy.VERSION_PRIORITY;
      case ConflictAction.MERGE_DATA:
        return ConflictResolutionStrategy.MERGE_DATA;
      case ConflictAction.MANUAL_REVIEW:
        return ConflictResolutionStrategy.MANUAL_REVIEW;
    }
  }
  
  /// 提取时间戳
  DateTime? _extractTimestamp(Map<String, dynamic> data) {
    final timestampFields = ['timestamp', 'updated_at', 'sync_time', 'created_at'];
    
    for (final field in timestampFields) {
      final value = data[field];
      if (value != null) {
        try {
          if (value is String) {
            return DateTime.parse(value);
          } else if (value is int) {
            return DateTime.fromMillisecondsSinceEpoch(value);
          }
        } catch (e) {
          _logger.w('⚠️ 解析时间戳失败: $field = $value');
        }
      }
    }
    
    return null;
  }
  
  /// 提取版本信息
  dynamic _extractVersion(Map<String, dynamic> data) {
    return data['version'] ?? data['data_version'] ?? data['v'];
  }
  
  /// 比较版本号
  int _compareVersions(dynamic version1, dynamic version2) {
    try {
      if (version1 is String && version2 is String) {
        // 简单的版本号比较（如 "1.2.3"）
        final v1Parts = version1.split('.').map(int.parse).toList();
        final v2Parts = version2.split('.').map(int.parse).toList();
        
        final maxLength = v1Parts.length > v2Parts.length ? v1Parts.length : v2Parts.length;
        
        for (int i = 0; i < maxLength; i++) {
          final v1Part = i < v1Parts.length ? v1Parts[i] : 0;
          final v2Part = i < v2Parts.length ? v2Parts[i] : 0;
          
          if (v1Part != v2Part) {
            return v1Part.compareTo(v2Part);
          }
        }
        
        return 0;
      } else if (version1 is num && version2 is num) {
        return version1.compareTo(version2);
      }
    } catch (e) {
      _logger.w('⚠️ 版本比较失败: $e');
    }
    
    return 0; // 无法比较时认为相等
  }
  
  /// 合并健康数据
  Map<String, dynamic> _mergeHealthData(
    Map<String, dynamic> localData,
    Map<String, dynamic> serverData,
    Map<String, dynamic> conflictDetails,
  ) {
    final mergedData = Map<String, dynamic>.from(serverData); // 以服务器数据为基础
    
    // 合并规则：对于数值类型的健康数据，取较大值
    final numericFields = ['steps', 'distance', 'calories', 'active_minutes'];
    
    for (final field in numericFields) {
      final localValue = localData[field];
      final serverValue = serverData[field];
      
      if (localValue is num && serverValue is num) {
        mergedData[field] = localValue > serverValue ? localValue : serverValue;
      }
    }
    
    // 使用最新的时间戳
    final localTimestamp = _extractTimestamp(localData);
    final serverTimestamp = _extractTimestamp(serverData);
    
    if (localTimestamp != null && serverTimestamp != null) {
      if (localTimestamp.isAfter(serverTimestamp)) {
        mergedData['timestamp'] = localTimestamp.toIso8601String();
        mergedData['updated_at'] = localTimestamp.toIso8601String();
      }
    }
    
    // 添加合并标记
    mergedData['_merged'] = true;
    mergedData['_merge_time'] = DateTime.now().toIso8601String();
    
    return mergedData;
  }
  
  /// 获取合并规则
  Map<String, String> _getMergeRules() {
    return {
      'numeric_fields': 'take_maximum_value',
      'timestamp': 'use_latest',
      'text_fields': 'prefer_server',
      'arrays': 'merge_unique',
    };
  }
}

// ========== 数据模型和枚举 ==========

/// 冲突解决策略枚举
enum ConflictResolutionStrategy {
  SERVER_PRIORITY,    // 服务器优先
  LOCAL_PRIORITY,     // 本地优先
  TIMESTAMP_PRIORITY, // 时间戳优先
  VERSION_PRIORITY,   // 版本优先
  MERGE_DATA,         // 合并数据
  MANUAL_REVIEW,      // 手动审查
  NO_CONFLICT,        // 无冲突
}

/// 🔥 BOSS数据模型：冲突解决结果
class ConflictResolutionResult {
  final bool success;
  final Map<String, dynamic> resolvedData;
  final ConflictResolutionStrategy resolutionStrategy;
  final Map<String, dynamic> resolutionDetails;
  final DateTime resolutionTime;

  ConflictResolutionResult({
    required this.success,
    required this.resolvedData,
    required this.resolutionStrategy,
    required this.resolutionDetails,
    required this.resolutionTime,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'resolved_data': resolvedData,
      'resolution_strategy': resolutionStrategy.name,
      'resolution_details': resolutionDetails,
      'resolution_time': resolutionTime.toIso8601String(),
    };
  }
}
