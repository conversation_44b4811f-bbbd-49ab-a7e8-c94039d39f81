import 'package:logger/logger.dart';

/// 🔥 BOSS关键修复：数据冲突检测器
/// 检测本地和服务器健康数据的冲突
/// 基于时间戳和数据版本进行冲突检测
class DataConflictDetector {
  static final Logger _logger = Logger();
  
  // 单例模式
  static DataConflictDetector? _instance;
  static DataConflictDetector get instance {
    _instance ??= DataConflictDetector._internal();
    return _instance!;
  }
  
  DataConflictDetector._internal();
  
  /// 🔥 BOSS核心：检测健康数据冲突
  /// 
  /// [localData] 本地健康数据
  /// [serverData] 服务器健康数据
  /// [dataType] 数据类型（steps, distance, calories等）
  /// 
  /// 返回冲突检测结果
  ConflictDetectionResult detectHealthDataConflict({
    required Map<String, dynamic> localData,
    required Map<String, dynamic> serverData,
    required String dataType,
  }) {
    _logger.i('🔍 DataConflictDetector: 开始检测健康数据冲突');
    _logger.i('  数据类型: $dataType');
    _logger.i('  本地数据: ${_sanitizeDataForLog(localData)}');
    _logger.i('  服务器数据: ${_sanitizeDataForLog(serverData)}');
    
    try {
      // 检查数据完整性
      final localValidation = _validateHealthData(localData, dataType);
      final serverValidation = _validateHealthData(serverData, dataType);
      
      if (!localValidation.isValid || !serverValidation.isValid) {
        return ConflictDetectionResult(
          hasConflict: true,
          conflictType: ConflictType.DATA_INTEGRITY_ERROR,
          conflictSeverity: ConflictSeverity.HIGH,
          localData: localData,
          serverData: serverData,
          conflictDetails: {
            'local_validation': localValidation.toJson(),
            'server_validation': serverValidation.toJson(),
          },
          recommendedAction: ConflictAction.MANUAL_REVIEW,
          detectionTime: DateTime.now(),
        );
      }
      
      // 检查时间戳冲突
      final timestampConflict = _detectTimestampConflict(localData, serverData);
      if (timestampConflict != null) {
        return timestampConflict;
      }
      
      // 检查数据值冲突
      final valueConflict = _detectValueConflict(localData, serverData, dataType);
      if (valueConflict != null) {
        return valueConflict;
      }
      
      // 检查数据版本冲突
      final versionConflict = _detectVersionConflict(localData, serverData);
      if (versionConflict != null) {
        return versionConflict;
      }
      
      // 无冲突
      _logger.i('✅ DataConflictDetector: 未检测到数据冲突');
      return ConflictDetectionResult(
        hasConflict: false,
        conflictType: ConflictType.NO_CONFLICT,
        conflictSeverity: ConflictSeverity.NONE,
        localData: localData,
        serverData: serverData,
        conflictDetails: {},
        recommendedAction: ConflictAction.USE_SERVER_DATA,
        detectionTime: DateTime.now(),
      );
      
    } catch (e) {
      _logger.e('❌ DataConflictDetector: 冲突检测异常: $e');
      return ConflictDetectionResult(
        hasConflict: true,
        conflictType: ConflictType.DETECTION_ERROR,
        conflictSeverity: ConflictSeverity.HIGH,
        localData: localData,
        serverData: serverData,
        conflictDetails: {'error': e.toString()},
        recommendedAction: ConflictAction.MANUAL_REVIEW,
        detectionTime: DateTime.now(),
      );
    }
  }
  
  /// 🔥 BOSS功能：检测时间戳冲突
  ConflictDetectionResult? _detectTimestampConflict(
    Map<String, dynamic> localData,
    Map<String, dynamic> serverData,
  ) {
    final localTimestamp = _extractTimestamp(localData);
    final serverTimestamp = _extractTimestamp(serverData);
    
    if (localTimestamp == null || serverTimestamp == null) {
      _logger.w('⚠️ 时间戳缺失，无法进行时间戳冲突检测');
      return null;
    }
    
    final timeDifference = localTimestamp.difference(serverTimestamp).abs();
    
    // 如果时间差超过5分钟，认为存在时间戳冲突
    if (timeDifference.inMinutes > 5) {
      _logger.w('⚠️ 检测到时间戳冲突: 本地=${localTimestamp.toIso8601String()}, 服务器=${serverTimestamp.toIso8601String()}');
      
      return ConflictDetectionResult(
        hasConflict: true,
        conflictType: ConflictType.TIMESTAMP_CONFLICT,
        conflictSeverity: ConflictSeverity.MEDIUM,
        localData: localData,
        serverData: serverData,
        conflictDetails: {
          'local_timestamp': localTimestamp.toIso8601String(),
          'server_timestamp': serverTimestamp.toIso8601String(),
          'time_difference_minutes': timeDifference.inMinutes,
        },
        recommendedAction: ConflictAction.USE_LATEST_TIMESTAMP,
        detectionTime: DateTime.now(),
      );
    }
    
    return null;
  }
  
  /// 🔥 BOSS功能：检测数据值冲突
  ConflictDetectionResult? _detectValueConflict(
    Map<String, dynamic> localData,
    Map<String, dynamic> serverData,
    String dataType,
  ) {
    final conflictingFields = <String, Map<String, dynamic>>{};
    
    // 根据数据类型检测特定字段的冲突
    final fieldsToCheck = _getFieldsToCheck(dataType);
    
    for (final field in fieldsToCheck) {
      final localValue = localData[field];
      final serverValue = serverData[field];
      
      if (localValue != null && serverValue != null && localValue != serverValue) {
        // 检查数值差异是否超过阈值
        if (_isSignificantDifference(localValue, serverValue, field)) {
          conflictingFields[field] = {
            'local_value': localValue,
            'server_value': serverValue,
            'difference': _calculateDifference(localValue, serverValue),
          };
        }
      }
    }
    
    if (conflictingFields.isNotEmpty) {
      _logger.w('⚠️ 检测到数据值冲突: $conflictingFields');
      
      return ConflictDetectionResult(
        hasConflict: true,
        conflictType: ConflictType.VALUE_CONFLICT,
        conflictSeverity: _calculateConflictSeverity(conflictingFields),
        localData: localData,
        serverData: serverData,
        conflictDetails: {
          'conflicting_fields': conflictingFields,
          'data_type': dataType,
        },
        recommendedAction: _getRecommendedActionForValueConflict(conflictingFields),
        detectionTime: DateTime.now(),
      );
    }
    
    return null;
  }
  
  /// 🔥 BOSS功能：检测数据版本冲突
  ConflictDetectionResult? _detectVersionConflict(
    Map<String, dynamic> localData,
    Map<String, dynamic> serverData,
  ) {
    final localVersion = localData['version'] ?? localData['data_version'];
    final serverVersion = serverData['version'] ?? serverData['data_version'];
    
    if (localVersion != null && serverVersion != null) {
      if (localVersion != serverVersion) {
        _logger.w('⚠️ 检测到版本冲突: 本地=$localVersion, 服务器=$serverVersion');
        
        return ConflictDetectionResult(
          hasConflict: true,
          conflictType: ConflictType.VERSION_CONFLICT,
          conflictSeverity: ConflictSeverity.LOW,
          localData: localData,
          serverData: serverData,
          conflictDetails: {
            'local_version': localVersion,
            'server_version': serverVersion,
          },
          recommendedAction: ConflictAction.USE_HIGHER_VERSION,
          detectionTime: DateTime.now(),
        );
      }
    }
    
    return null;
  }
  
  /// 验证健康数据完整性
  DataValidationResult _validateHealthData(Map<String, dynamic> data, String dataType) {
    final errors = <String>[];
    
    // 检查必需字段
    final requiredFields = _getRequiredFields(dataType);
    for (final field in requiredFields) {
      if (!data.containsKey(field) || data[field] == null) {
        errors.add('缺少必需字段: $field');
      }
    }
    
    // 检查数据类型和范围
    if (data.containsKey('steps')) {
      final steps = data['steps'];
      if (steps is! int || steps < 0 || steps > 100000) {
        errors.add('步数数据无效: $steps');
      }
    }
    
    if (data.containsKey('distance')) {
      final distance = data['distance'];
      if (distance is! num || distance < 0 || distance > 1000) {
        errors.add('距离数据无效: $distance');
      }
    }
    
    if (data.containsKey('calories')) {
      final calories = data['calories'];
      if (calories is! num || calories < 0 || calories > 10000) {
        errors.add('卡路里数据无效: $calories');
      }
    }
    
    return DataValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
  
  /// 提取时间戳
  DateTime? _extractTimestamp(Map<String, dynamic> data) {
    final timestampFields = ['timestamp', 'updated_at', 'sync_time', 'created_at'];
    
    for (final field in timestampFields) {
      final value = data[field];
      if (value != null) {
        try {
          if (value is String) {
            return DateTime.parse(value);
          } else if (value is int) {
            return DateTime.fromMillisecondsSinceEpoch(value);
          }
        } catch (e) {
          _logger.w('⚠️ 解析时间戳失败: $field = $value');
        }
      }
    }
    
    return null;
  }
  
  /// 获取需要检查的字段
  List<String> _getFieldsToCheck(String dataType) {
    switch (dataType.toLowerCase()) {
      case 'steps':
        return ['steps', 'step_count'];
      case 'distance':
        return ['distance', 'distance_meters'];
      case 'calories':
        return ['calories', 'calories_burned', 'active_calories'];
      case 'heart_rate':
        return ['heart_rate', 'bpm', 'avg_heart_rate'];
      default:
        return ['value', 'amount', 'count'];
    }
  }
  
  /// 获取必需字段
  List<String> _getRequiredFields(String dataType) {
    switch (dataType.toLowerCase()) {
      case 'steps':
        return ['steps'];
      case 'distance':
        return ['distance'];
      case 'calories':
        return ['calories'];
      default:
        return [];
    }
  }
  
  /// 检查是否为显著差异
  bool _isSignificantDifference(dynamic localValue, dynamic serverValue, String field) {
    if (localValue is num && serverValue is num) {
      final difference = (localValue - serverValue).abs();
      
      // 根据字段类型设置不同的阈值
      switch (field.toLowerCase()) {
        case 'steps':
        case 'step_count':
          return difference > 100; // 步数差异超过100步
        case 'distance':
        case 'distance_meters':
          return difference > 0.1; // 距离差异超过0.1公里
        case 'calories':
        case 'calories_burned':
          return difference > 50; // 卡路里差异超过50卡
        default:
          return difference > 0; // 其他字段任何差异都认为是显著的
      }
    }
    
    return localValue != serverValue;
  }
  
  /// 计算差异值
  dynamic _calculateDifference(dynamic localValue, dynamic serverValue) {
    if (localValue is num && serverValue is num) {
      return (localValue - serverValue).abs();
    }
    return 'type_mismatch';
  }
  
  /// 计算冲突严重程度
  ConflictSeverity _calculateConflictSeverity(Map<String, Map<String, dynamic>> conflictingFields) {
    if (conflictingFields.length >= 3) {
      return ConflictSeverity.HIGH;
    } else if (conflictingFields.length == 2) {
      return ConflictSeverity.MEDIUM;
    } else {
      return ConflictSeverity.LOW;
    }
  }
  
  /// 获取值冲突的推荐操作
  ConflictAction _getRecommendedActionForValueConflict(Map<String, Map<String, dynamic>> conflictingFields) {
    // 如果冲突字段较多，建议手动审查
    if (conflictingFields.length >= 3) {
      return ConflictAction.MANUAL_REVIEW;
    }
    
    // 否则使用服务器数据
    return ConflictAction.USE_SERVER_DATA;
  }
  
  /// 清理敏感数据用于日志记录
  Map<String, dynamic> _sanitizeDataForLog(Map<String, dynamic> data) {
    final sanitized = Map<String, dynamic>.from(data);
    
    // 移除敏感字段
    sanitized.remove('user_id');
    sanitized.remove('device_id');
    sanitized.remove('access_token');
    
    // 限制数据大小
    if (sanitized.length > 10) {
      return Map.fromEntries(sanitized.entries.take(10));
    }
    
    return sanitized;
  }
}

// ========== 数据模型和枚举 ==========

/// 冲突类型枚举
enum ConflictType {
  NO_CONFLICT,           // 无冲突
  TIMESTAMP_CONFLICT,    // 时间戳冲突
  VALUE_CONFLICT,        // 数据值冲突
  VERSION_CONFLICT,      // 版本冲突
  DATA_INTEGRITY_ERROR,  // 数据完整性错误
  DETECTION_ERROR,       // 检测错误
}

/// 冲突严重程度枚举
enum ConflictSeverity {
  NONE,    // 无冲突
  LOW,     // 低严重程度
  MEDIUM,  // 中等严重程度
  HIGH,    // 高严重程度
}

/// 推荐操作枚举
enum ConflictAction {
  USE_SERVER_DATA,       // 使用服务器数据
  USE_LOCAL_DATA,        // 使用本地数据
  USE_LATEST_TIMESTAMP,  // 使用最新时间戳的数据
  USE_HIGHER_VERSION,    // 使用更高版本的数据
  MERGE_DATA,            // 合并数据
  MANUAL_REVIEW,         // 手动审查
}

/// 🔥 BOSS数据模型：冲突检测结果
class ConflictDetectionResult {
  final bool hasConflict;
  final ConflictType conflictType;
  final ConflictSeverity conflictSeverity;
  final Map<String, dynamic> localData;
  final Map<String, dynamic> serverData;
  final Map<String, dynamic> conflictDetails;
  final ConflictAction recommendedAction;
  final DateTime detectionTime;

  ConflictDetectionResult({
    required this.hasConflict,
    required this.conflictType,
    required this.conflictSeverity,
    required this.localData,
    required this.serverData,
    required this.conflictDetails,
    required this.recommendedAction,
    required this.detectionTime,
  });

  Map<String, dynamic> toJson() {
    return {
      'has_conflict': hasConflict,
      'conflict_type': conflictType.name,
      'conflict_severity': conflictSeverity.name,
      'local_data': localData,
      'server_data': serverData,
      'conflict_details': conflictDetails,
      'recommended_action': recommendedAction.name,
      'detection_time': detectionTime.toIso8601String(),
    };
  }
}

/// 🔥 BOSS数据模型：数据验证结果
class DataValidationResult {
  final bool isValid;
  final List<String> errors;

  DataValidationResult({
    required this.isValid,
    required this.errors,
  });

  Map<String, dynamic> toJson() {
    return {
      'is_valid': isValid,
      'errors': errors,
    };
  }
}
