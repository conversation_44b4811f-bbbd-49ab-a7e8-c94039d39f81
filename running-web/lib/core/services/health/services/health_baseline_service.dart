/// 🔥 v14.1重构：健康数据基线管理服务
/// 
/// 从HealthDataFlowService中提取的基线管理逻辑
/// 专注于基线数据的初始化、重置、跨天处理和会话管理

import 'package:logger/logger.dart';

import '../interfaces/health_service_interface.dart';
import '../../../network/api_client.dart';
import '../../../services/health_service.dart';
import '../../../services/baseline_service.dart';
import '../../../models/health_data.dart';
// 🔥 v14.1重构：新加坡时间工具方法直接在此实现

/// 健康数据基线管理服务实现
class HealthBaselineService implements IHealthBaselineService {
  static final Logger _logger = Logger();
  
  final ApiClient _apiClient;
  final HealthService _healthService;
  
  /// 基线管理性能指标
  final Map<String, int> _baselineMetrics = {
    'baseline_init_count': 0,
    'baseline_reset_count': 0,
    'cross_day_count': 0,
    'session_continuity_count': 0,
  };

  HealthBaselineService({
    required ApiClient apiClient,
    required HealthService healthService,
  }) : _apiClient = apiClient,
       _healthService = healthService;

  @override
  Future<Map<String, dynamic>> handleBaseline(
    String scenario, 
    Map<String, dynamic> permissions
  ) async {
    _logger.i('📊 HealthBaselineService: 处理基线管理 - 场景: $scenario');

    try {
      final hasAnyPermission = permissions.values.any((status) => status == 'authorized');
      if (!hasAnyPermission) {
        _logger.i('⚠️ 所有健康权限都未授权，跳过基线处理');
        return {
          'success': true,
          'baseline_reset': false,
          'reason': 'no_permissions',
          'skipped': true,
        };
      }

      // 根据场景处理基线
      switch (scenario) {
        case 'login':
        case 'restart':
          return await _handleNewSessionBaseline(permissions, scenario);
        case 'resume':
          return await _handleResumeBaseline(permissions);
        default:
          _logger.w('⚠️ 未知场景: $scenario，使用默认处理');
          return await _handleDefaultBaseline(permissions);
      }

    } catch (e) {
      _logger.e('❌ HealthBaselineService: 基线处理异常', error: e);
      return {
        'success': false,
        'error': e.toString(),
        'baseline_reset': false,
      };
    }
  }

  @override
  Future<bool> checkCrossDay() async {
    try {
      _logger.i('🌅 HealthBaselineService: 检查跨天情况');
      
      // 获取当前新加坡时间
      final currentTime = getCurrentSingaporeTime();
      
      // 获取上次会话时间（这里需要从会话管理器获取）
      // TODO: 集成SessionManager获取上次会话时间
      final lastSessionTime = DateTime.now().subtract(const Duration(hours: 1)); // 临时实现
      
      // 检查是否跨天
      final currentDate = _getSingaporeDateStart(currentTime);
      final lastSessionDate = _getSingaporeDateStart(lastSessionTime);
      
      final isCrossDay = !currentDate.isAtSameMomentAs(lastSessionDate);
      
      if (isCrossDay) {
        _logger.i('🌅 检测到跨天: ${lastSessionDate.toIso8601String()} -> ${currentDate.toIso8601String()}');
        _recordMetric('cross_day_count');
      } else {
        _logger.d('✅ 未跨天，继续当前会话');
      }
      
      return isCrossDay;
      
    } catch (e) {
      _logger.e('❌ 跨天检查异常: $e');
      return false;
    }
  }

  @override
  Future<Map<String, dynamic>> resetBaseline(String reason) async {
    _logger.i('🔄 HealthBaselineService: 重置基线 - 原因: $reason');

    try {
      _recordMetric('baseline_reset_count');
      
      // 根据重置原因执行不同的重置策略
      switch (reason) {
        case 'cross_day_reset':
          return await _resetCrossDayBaseline();
        case 'new_session':
          return await _resetNewSessionBaseline();
        case 'permission_change':
          return await _resetPermissionChangeBaseline();
        default:
          return await _resetDefaultBaseline(reason);
      }
      
    } catch (e) {
      _logger.e('❌ 基线重置异常: $e');
      return {
        'success': false,
        'error': e.toString(),
        'reset_type': reason,
      };
    }
  }

  @override
  DateTime getCurrentSingaporeTime() {
    return _getSingaporeNow();
  }

  @override
  Future<Map<String, dynamic>> checkSessionContinuity(DateTime? lastSessionTime) async {
    _logger.i('🔗 HealthBaselineService: 检查会话连续性');

    try {
      _recordMetric('session_continuity_count');
      
      if (lastSessionTime == null) {
        _logger.i('🆕 首次会话，无需连续性检查');
        return {
          'is_continuous': false,
          'session_type': 'first_session',
          'action': 'create_new',
        };
      }

      final currentTime = getCurrentSingaporeTime();
      final timeDifference = currentTime.difference(lastSessionTime);
      
      // 4小时超时规则
      const sessionTimeout = Duration(hours: 4);
      
      if (timeDifference > sessionTimeout) {
        _logger.i('⏰ 会话超时 (${timeDifference.inHours}小时)，创建新会话');
        return {
          'is_continuous': false,
          'session_type': 'timeout_new',
          'action': 'create_new',
          'timeout_hours': timeDifference.inHours,
        };
      }

      // 检查跨天
      final isCrossDay = await checkCrossDay();
      if (isCrossDay) {
        _logger.i('🌅 跨天检测，创建跨天会话');
        return {
          'is_continuous': false,
          'session_type': 'cross_day',
          'action': 'create_cross_day',
          'cross_day_detected': true,
        };
      }

      _logger.i('✅ 会话连续，恢复现有会话');
      return {
        'is_continuous': true,
        'session_type': 'continuous',
        'action': 'resume_existing',
        'session_duration_hours': timeDifference.inHours,
      };
      
    } catch (e) {
      _logger.e('❌ 会话连续性检查异常: $e');
      return {
        'is_continuous': false,
        'session_type': 'error_fallback',
        'action': 'create_new',
        'error': e.toString(),
      };
    }
  }

  /// 处理新会话基线（登录/重启场景）
  Future<Map<String, dynamic>> _handleNewSessionBaseline(
    Map<String, dynamic> permissions, 
    String scenario
  ) async {
    _logger.i('🆕 处理新会话基线 - 场景: $scenario');

    try {
      final sessionStartTime = getCurrentSingaporeTime();
      final todayStart = _getSingaporeDateStart(sessionStartTime);
      
      // 转换权限格式
      final permissionsStatus = _convertPermissions(permissions);
      
      // 获取从今天00:00到会话开始时间的健康数据作为基线
      final baselineData = await _healthService.getHealthDataForPeriod(
        startTime: todayStart,
        endTime: sessionStartTime,
        permissions: _convertPermissionsForAPI(permissions),
      );
      
      // 创建基线数据（即使获取失败也要创建默认基线）
      final actualBaselineData = baselineData ?? HealthData(
        steps: permissionsStatus['steps'] == true ? 0 : null,
        distance: permissionsStatus['distance'] == true ? 0.0 : null,
        calories: permissionsStatus['calories'] == true ? 0 : null,
        date: sessionStartTime,
        source: 'default_baseline'
      );

      // 使用BaselineService初始化基线
      final baselineService = BaselineService(apiClient: _apiClient);
      final initResult = await baselineService.initializeBaseline(
        totals: actualBaselineData,
        permissions: permissionsStatus,
        checkSessionContinuity: false,
        isAppRestart: scenario == 'restart',
        restartReason: '${scenario}_baseline',
      );

      _recordMetric('baseline_init_count');

      if (initResult.success) {
        _logger.i('✅ 新会话基线创建成功');
        return {
          'success': true,
          'baseline_reset': true,
          'baseline_type': 'new_session',
          'baseline_time_range': 'today_00:00_to_session_start',
          'reason': scenario,
          'session_start_time': sessionStartTime.toIso8601String(),
        };
      } else {
        _logger.w('⚠️ 新会话基线创建失败: ${initResult.message}');
        return {
          'success': false,
          'error': initResult.message,
          'baseline_reset': false,
        };
      }
      
    } catch (e) {
      _logger.e('❌ 新会话基线处理异常: $e');
      return {
        'success': false,
        'error': e.toString(),
        'baseline_reset': false,
      };
    }
  }

  /// 处理恢复会话基线
  Future<Map<String, dynamic>> _handleResumeBaseline(Map<String, dynamic> permissions) async {
    _logger.i('🔄 处理恢复会话基线');

    try {
      // 检查是否跨天
      final isCrossDay = await checkCrossDay();
      
      if (isCrossDay) {
        _logger.i('🌅 检测到跨天，执行跨天基线重置');
        return await _handleCrossDayBaseline(permissions);
      } else {
        _logger.i('✅ 未跨天，基线保持不变');
        return {
          'success': true,
          'baseline_reset': false,
          'baseline_type': 'unchanged',
          'reason': 'session_continues',
        };
      }
      
    } catch (e) {
      _logger.e('❌ 恢复会话基线处理异常: $e');
      return {
        'success': false,
        'error': e.toString(),
        'baseline_reset': false,
      };
    }
  }

  /// 处理跨天基线重置
  Future<Map<String, dynamic>> _handleCrossDayBaseline(Map<String, dynamic> permissions) async {
    _logger.i('🌅 处理跨天基线重置');

    try {
      final permissionsStatus = _convertPermissions(permissions);
      
      // 创建零基线数据（跨天重置为0）
      final zeroBaseline = HealthData(
        steps: permissionsStatus['steps'] == true ? 0 : null,
        distance: permissionsStatus['distance'] == true ? 0.0 : null,
        calories: permissionsStatus['calories'] == true ? 0 : null,
        date: _getSingaporeTodayStart(),
        source: 'cross_day_reset'
      );
      
      // 使用BaselineService重置基线
      final baselineService = BaselineService(apiClient: _apiClient);
      final initResult = await baselineService.initializeBaseline(
        totals: zeroBaseline,
        permissions: permissionsStatus,
        checkSessionContinuity: false,
        isAppRestart: false,
        restartReason: 'cross_day_reset',
      );

      if (initResult.success) {
        _logger.i('✅ 跨天基线重置为0成功');
        return {
          'success': true,
          'baseline_reset': true,
          'baseline_type': 'cross_day_reset',
          'baseline_value': 'zero',
          'reason': 'cross_day_detected',
        };
      } else {
        _logger.w('⚠️ 跨天基线重置失败: ${initResult.message}');
        return {
          'success': false,
          'error': initResult.message,
          'baseline_reset': false,
        };
      }
      
    } catch (e) {
      _logger.e('❌ 跨天基线重置异常: $e');
      return {
        'success': false,
        'error': e.toString(),
        'baseline_reset': false,
      };
    }
  }

  /// 处理默认基线
  Future<Map<String, dynamic>> _handleDefaultBaseline(Map<String, dynamic> permissions) async {
    _logger.i('🔧 处理默认基线');

    // 默认情况下不重置基线，保持现有状态
    return {
      'success': true,
      'baseline_reset': false,
      'baseline_type': 'default',
      'reason': 'default_handling',
    };
  }

  /// 重置跨天基线
  Future<Map<String, dynamic>> _resetCrossDayBaseline() async {
    _logger.i('🌅 重置跨天基线为0');

    try {
      // TODO: 获取当前权限状态
      final permissions = <String, dynamic>{}; // 临时实现
      return await _handleCrossDayBaseline(permissions);
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'reset_type': 'cross_day_reset',
      };
    }
  }

  /// 重置新会话基线
  Future<Map<String, dynamic>> _resetNewSessionBaseline() async {
    _logger.i('🆕 重置新会话基线');

    try {
      // TODO: 获取当前权限状态
      final permissions = <String, dynamic>{}; // 临时实现
      return await _handleNewSessionBaseline(permissions, 'new_session');
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'reset_type': 'new_session',
      };
    }
  }

  /// 重置权限变化基线
  Future<Map<String, dynamic>> _resetPermissionChangeBaseline() async {
    _logger.i('🔄 重置权限变化基线');

    try {
      // TODO: 实现权限变化基线重置逻辑
      return {
        'success': true,
        'baseline_reset': true,
        'reset_type': 'permission_change',
        'reason': 'permission_status_changed',
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'reset_type': 'permission_change',
      };
    }
  }

  /// 重置默认基线
  Future<Map<String, dynamic>> _resetDefaultBaseline(String reason) async {
    _logger.i('🔧 重置默认基线 - 原因: $reason');

    return {
      'success': true,
      'baseline_reset': false,
      'reset_type': 'default',
      'reason': reason,
    };
  }

  /// 转换权限格式为布尔值映射
  Map<String, bool> _convertPermissions(Map<String, dynamic> permissions) {
    return {
      'steps': permissions['steps'] == 'authorized',
      'distance': permissions['distance'] == 'authorized',
      'calories': permissions['calories'] == 'authorized',
    };
  }

  /// 转换权限格式为API调用格式
  Map<String, String> _convertPermissionsForAPI(Map<String, dynamic> permissions) {
    return {
      'steps': permissions['steps'] == 'authorized' ? 'authorized' : 'notDetermined',
      'distance': permissions['distance'] == 'authorized' ? 'authorized' : 'notDetermined',
      'calories': permissions['calories'] == 'authorized' ? 'authorized' : 'notDetermined',
    };
  }

  /// 记录性能指标
  void _recordMetric(String metric) {
    _baselineMetrics[metric] = (_baselineMetrics[metric] ?? 0) + 1;
    _logger.d('基线管理指标 $metric: ${_baselineMetrics[metric]}');
  }

  /// 获取基线管理性能报告
  Map<String, dynamic> getPerformanceReport() {
    return {
      'metrics': Map.from(_baselineMetrics),
      'service': 'HealthBaselineService',
      'version': 'v14.1',
    };
  }

  // ========== 新加坡时间工具方法 ==========

  /// 获取当前新加坡时间
  DateTime _getSingaporeNow() {
    // Flutter中使用UTC时间转换为新加坡时间
    final utcNow = DateTime.now().toUtc();
    return utcNow.add(const Duration(hours: 8)); // UTC+8
  }

  /// 获取新加坡时间的今天0:00
  DateTime _getSingaporeTodayStart() {
    final singaporeNow = _getSingaporeNow();
    return DateTime(
      singaporeNow.year,
      singaporeNow.month,
      singaporeNow.day,
      0, 0, 0, 0
    );
  }

  /// 获取新加坡时间的指定日期0:00
  DateTime _getSingaporeDateStart(DateTime date) {
    final singaporeDate = _convertUtcToSingapore(date);
    return DateTime(
      singaporeDate.year,
      singaporeDate.month,
      singaporeDate.day,
      0, 0, 0, 0
    );
  }

  /// 将UTC时间转换为新加坡时间
  DateTime _convertUtcToSingapore(DateTime utcTime) {
    return utcTime.add(const Duration(hours: 8));
  }
}
