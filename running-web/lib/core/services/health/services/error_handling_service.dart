import 'package:logger/logger.dart';
import '../interfaces/error_handling_interface.dart';

/// 🔥 v14.1重构：错误处理服务实现
/// 
/// 统一管理所有错误的分析、处理、恢复和记录
/// 支持网络错误、权限错误、超时错误、认证错误等各种错误类型
class ErrorHandlingService implements IErrorHandlingService {
  static final Logger _logger = Logger();
  
  /// 错误统计
  final Map<String, int> _errorStatistics = {
    'network_errors': 0,
    'permission_errors': 0,
    'timeout_errors': 0,
    'authentication_errors': 0,
    'validation_errors': 0,
    'system_errors': 0,
    'unknown_errors': 0,
  };
  
  /// 错误历史记录
  final List<Map<String, dynamic>> _errorHistory = [];
  
  /// 错误恢复策略
  final Map<ErrorType, ErrorRecoveryStrategy> _recoveryStrategies = {};
  
  /// 性能指标
  final Map<String, int> _performanceMetrics = {
    'total_errors_handled': 0,
    'recovery_attempts': 0,
    'successful_recoveries': 0,
    'failed_recoveries': 0,
  };
  
  @override
  Future<ErrorHandlingResult> handleError({
    required String operation,
    required dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
    bool attemptRecovery = true,
    String? userFriendlyMessage,
  }) async {
    final startTime = DateTime.now();
    _performanceMetrics['total_errors_handled'] = 
        (_performanceMetrics['total_errors_handled'] ?? 0) + 1;
    
    _logger.e('🔧 ErrorHandlingService: 处理错误 - $operation', error: error);
    
    try {
      // 分析错误
      final analysis = analyzeError(error);
      final errorId = generateErrorId();
      
      // 记录错误统计
      recordErrorStatistic(analysis.category);
      
      // 记录错误历史
      recordErrorHistory(errorId, operation, error, analysis, context);
      
      // 记录详细日志
      _logger.e('❌ $operation 失败 [${analysis.category}]',
          error: error, stackTrace: stackTrace);
      
      // 尝试错误恢复
      Map<String, dynamic>? recoveryResult;
      bool recoveryAttempted = false;
      
      if (attemptRecovery && analysis.recoverable) {
        recoveryAttempted = true;
        _performanceMetrics['recovery_attempts'] = 
            (_performanceMetrics['recovery_attempts'] ?? 0) + 1;
        
        recoveryResult = await attemptErrorRecovery(analysis, operation, context);
        
        if (recoveryResult?['success'] == true) {
          _performanceMetrics['successful_recoveries'] = 
              (_performanceMetrics['successful_recoveries'] ?? 0) + 1;
          _logger.i('✅ 错误恢复成功: $operation');
        } else {
          _performanceMetrics['failed_recoveries'] = 
              (_performanceMetrics['failed_recoveries'] ?? 0) + 1;
          _logger.w('⚠️ 错误恢复失败: $operation');
        }
      }
      
      // 生成用户友好的错误信息
      final friendlyMessage = userFriendlyMessage ?? generateUserFriendlyMessage(analysis);
      
      final result = ErrorHandlingResult(
        success: false,
        errorId: errorId,
        analysis: analysis,
        userMessage: friendlyMessage,
        recoveryAttempted: recoveryAttempted,
        recoveryResult: recoveryResult,
        timestamp: startTime,
        operation: operation,
        context: context,
      );
      
      _logger.i('🔧 ErrorHandlingService: 错误处理完成 - $errorId');
      return result;
      
    } catch (e) {
      _logger.e('❌ ErrorHandlingService: 错误处理失败', error: e);
      
      // 返回基础错误结果
      return ErrorHandlingResult(
        success: false,
        errorId: generateErrorId(),
        analysis: ErrorAnalysisResult(
          type: ErrorType.system,
          category: 'system_errors',
          severity: ErrorSeverity.high,
          recoverable: false,
          retryRecommended: false,
          message: '系统错误处理失败',
        ),
        userMessage: '系统遇到未知错误，请稍后重试',
        recoveryAttempted: false,
        timestamp: startTime,
        operation: operation,
        context: context,
      );
    }
  }
  
  @override
  ErrorAnalysisResult analyzeError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // 网络错误分析
    if (errorString.contains('network') || errorString.contains('connection') ||
        errorString.contains('socket') || errorString.contains('timeout')) {
      return ErrorAnalysisResult(
        type: ErrorType.network,
        category: 'network_errors',
        severity: ErrorSeverity.medium,
        recoverable: true,
        retryRecommended: true,
        userAction: 'check_network',
        message: '网络连接异常，请检查网络设置',
        metadata: {'retry_delay': 3000},
      );
    }
    
    // 权限错误分析
    if (errorString.contains('permission') || errorString.contains('authorization') ||
        errorString.contains('denied') || errorString.contains('unauthorized') ||
        errorString.contains('权限')) {
      
      // 权限检查超时
      if (errorString.contains('timeout') || errorString.contains('超时')) {
        return ErrorAnalysisResult(
          type: ErrorType.permission,
          category: 'permission_errors',
          severity: ErrorSeverity.medium,
          recoverable: true,
          retryRecommended: true,
          userAction: 'retry_permission_check',
          message: '权限检查超时，请重试',
          metadata: {'retry_delay': 2000},
        );
      }
      
      // 权限被拒绝
      if (errorString.contains('denied') || errorString.contains('拒绝')) {
        return ErrorAnalysisResult(
          type: ErrorType.permission,
          category: 'permission_errors',
          severity: ErrorSeverity.high,
          recoverable: true,
          retryRecommended: false,
          userAction: 'show_permission_guide',
          message: '健康数据权限被拒绝，请前往设置授权',
          metadata: {'requires_user_action': true},
        );
      }
      
      // 权限未确定
      if (errorString.contains('notdetermined') || errorString.contains('未确定')) {
        return ErrorAnalysisResult(
          type: ErrorType.permission,
          category: 'permission_errors',
          severity: ErrorSeverity.medium,
          recoverable: true,
          retryRecommended: true,
          userAction: 'request_permission',
          message: '健康数据权限未设置，请授权',
          metadata: {'requires_user_action': true},
        );
      }
      
      // 权限检查失败（通用）
      return ErrorAnalysisResult(
        type: ErrorType.permission,
        category: 'permission_errors',
        severity: ErrorSeverity.high,
        recoverable: true,
        retryRecommended: true,
        userAction: 'retry_or_guide',
        message: '权限检查失败，请重试或检查设置',
        metadata: {'retry_delay': 1000},
      );
    }
    
    // 超时错误分析
    if (errorString.contains('timeout') || errorString.contains('超时')) {
      return ErrorAnalysisResult(
        type: ErrorType.timeout,
        category: 'timeout_errors',
        severity: ErrorSeverity.medium,
        recoverable: true,
        retryRecommended: true,
        userAction: 'retry_operation',
        message: '操作超时，请重试',
        metadata: {'retry_delay': 2000},
      );
    }
    
    // 认证错误分析
    if (errorString.contains('auth') || errorString.contains('token') ||
        errorString.contains('login') || errorString.contains('认证')) {
      return ErrorAnalysisResult(
        type: ErrorType.authentication,
        category: 'authentication_errors',
        severity: ErrorSeverity.high,
        recoverable: true,
        retryRecommended: false,
        userAction: 'reauth_required',
        message: '认证失败，请重新登录',
        metadata: {'requires_reauth': true},
      );
    }
    
    // 验证错误分析
    if (errorString.contains('validation') || errorString.contains('invalid') ||
        errorString.contains('验证') || errorString.contains('格式')) {
      return ErrorAnalysisResult(
        type: ErrorType.validation,
        category: 'validation_errors',
        severity: ErrorSeverity.low,
        recoverable: false,
        retryRecommended: false,
        userAction: 'check_input',
        message: '数据格式错误，请检查输入',
        metadata: {'requires_user_fix': true},
      );
    }
    
    // 未知错误
    return ErrorAnalysisResult(
      type: ErrorType.unknown,
      category: 'unknown_errors',
      severity: ErrorSeverity.medium,
      recoverable: false,
      retryRecommended: true,
      userAction: 'retry_or_contact_support',
      message: '遇到未知错误，请重试或联系客服',
      metadata: {'error_details': error.toString()},
    );
  }

  @override
  Future<Map<String, dynamic>?> attemptErrorRecovery(
    ErrorAnalysisResult analysis,
    String operation,
    Map<String, dynamic>? context,
  ) async {
    _logger.i('🔄 ErrorHandlingService: 尝试错误恢复 - ${analysis.type.name}');

    try {
      switch (analysis.type) {
        case ErrorType.network:
          return await _recoverNetworkError(analysis, operation, context);
        case ErrorType.permission:
          return await _recoverPermissionError(analysis, operation, context);
        case ErrorType.timeout:
          return await _recoverTimeoutError(analysis, operation, context);
        case ErrorType.authentication:
          return await _recoverAuthenticationError(analysis, operation, context);
        default:
          _logger.w('⚠️ 无可用的恢复策略: ${analysis.type.name}');
          return null;
      }
    } catch (e) {
      _logger.e('❌ 错误恢复失败', error: e);
      return {
        'success': false,
        'error': e.toString(),
        'recovery_type': analysis.type.name,
      };
    }
  }

  @override
  String generateUserFriendlyMessage(ErrorAnalysisResult analysis) {
    return analysis.message ?? _getDefaultMessage(analysis.type);
  }

  @override
  void recordErrorStatistic(String category) {
    _errorStatistics[category] = (_errorStatistics[category] ?? 0) + 1;
  }

  @override
  void recordErrorHistory(
    String errorId,
    String operation,
    dynamic error,
    ErrorAnalysisResult analysis,
    Map<String, dynamic>? context,
  ) {
    final record = {
      'error_id': errorId,
      'operation': operation,
      'error': error.toString(),
      'analysis': analysis.toJson(),
      'context': context,
      'timestamp': DateTime.now().toIso8601String(),
    };

    _errorHistory.add(record);

    // 保持最近1000条记录
    if (_errorHistory.length > 1000) {
      _errorHistory.removeAt(0);
    }
  }

  @override
  String generateErrorId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = DateTime.now().microsecond;
    return 'ERR_$timestamp\_$random';
  }

  @override
  Map<String, int> getErrorStatistics() {
    return Map.from(_errorStatistics);
  }

  @override
  List<Map<String, dynamic>> getErrorHistory({int limit = 100}) {
    final startIndex = _errorHistory.length > limit ? _errorHistory.length - limit : 0;
    return _errorHistory.sublist(startIndex);
  }

  @override
  void clearErrorHistory() {
    _errorHistory.clear();
    _logger.i('🔄 ErrorHandlingService: 错误历史已清理');
  }

  @override
  void setRecoveryStrategy(ErrorType type, ErrorRecoveryStrategy strategy) {
    _recoveryStrategies[type] = strategy;
    _logger.i('🔧 ErrorHandlingService: 设置恢复策略 - ${type.name}');
  }

  @override
  Map<String, dynamic> getPerformanceReport() {
    return {
      'metrics': Map.from(_performanceMetrics),
      'error_statistics': Map.from(_errorStatistics),
      'error_history_count': _errorHistory.length,
      'recovery_strategies_count': _recoveryStrategies.length,
      'service': 'ErrorHandlingService',
      'version': 'v14.1',
    };
  }

  // ========== 错误恢复实现方法 ==========

  /// 网络错误恢复
  Future<Map<String, dynamic>?> _recoverNetworkError(
    ErrorAnalysisResult analysis,
    String operation,
    Map<String, dynamic>? context,
  ) async {
    _logger.i('🌐 尝试网络错误恢复');

    // 等待网络恢复
    await Future.delayed(const Duration(seconds: 2));

    return {
      'success': true,
      'recovery_type': 'network_retry',
      'message': '网络连接已恢复，可以重试操作',
      'retry_recommended': true,
    };
  }

  /// 权限错误恢复
  Future<Map<String, dynamic>?> _recoverPermissionError(
    ErrorAnalysisResult analysis,
    String operation,
    Map<String, dynamic>? context,
  ) async {
    _logger.i('🔐 尝试权限错误恢复');

    final userAction = analysis.userAction;

    if (userAction == 'retry_permission_check') {
      return {
        'success': true,
        'recovery_type': 'permission_retry',
        'message': '可以重新检查权限状态',
        'retry_recommended': true,
      };
    } else if (userAction == 'show_permission_guide') {
      return {
        'success': false,
        'recovery_type': 'permission_guide_required',
        'message': '需要用户手动授权权限',
        'user_action_required': true,
      };
    }

    return null;
  }

  /// 超时错误恢复
  Future<Map<String, dynamic>?> _recoverTimeoutError(
    ErrorAnalysisResult analysis,
    String operation,
    Map<String, dynamic>? context,
  ) async {
    _logger.i('⏰ 尝试超时错误恢复');

    return {
      'success': true,
      'recovery_type': 'timeout_retry',
      'message': '可以重试操作，建议增加超时时间',
      'retry_recommended': true,
      'suggested_timeout': 10000,
    };
  }

  /// 认证错误恢复
  Future<Map<String, dynamic>?> _recoverAuthenticationError(
    ErrorAnalysisResult analysis,
    String operation,
    Map<String, dynamic>? context,
  ) async {
    _logger.i('🔑 尝试认证错误恢复');

    return {
      'success': false,
      'recovery_type': 'auth_relogin_required',
      'message': '需要重新登录',
      'user_action_required': true,
      'action': 'relogin',
    };
  }

  /// 获取默认错误消息
  String _getDefaultMessage(ErrorType type) {
    switch (type) {
      case ErrorType.network:
        return '网络连接异常，请检查网络设置';
      case ErrorType.permission:
        return '权限不足，请检查应用权限设置';
      case ErrorType.timeout:
        return '操作超时，请重试';
      case ErrorType.authentication:
        return '认证失败，请重新登录';
      case ErrorType.validation:
        return '数据格式错误，请检查输入';
      case ErrorType.system:
        return '系统错误，请稍后重试';
      case ErrorType.unknown:
        return '遇到未知错误，请重试或联系客服';
    }
  }
}
