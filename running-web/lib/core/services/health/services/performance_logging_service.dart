import 'package:logger/logger.dart';
import '../interfaces/performance_logging_interface.dart';

/// 🔥 v14.1重构：性能日志服务实现
/// 
/// 统一管理所有性能监控、日志记录和分析功能
/// 支持操作耗时监控、性能指标统计、日志分级记录等功能
class PerformanceLoggingService implements IPerformanceLoggingService {
  static final Logger _logger = Logger();
  
  /// 正在进行的操作记录
  final Map<String, _OngoingOperation> _ongoingOperations = {};
  
  /// 完成的操作性能记录
  final List<OperationPerformance> _operationHistory = [];
  
  /// 性能指标历史
  final Map<String, List<PerformanceMetric>> _metricHistory = {};
  
  /// 日志记录历史
  final List<LogRecord> _logHistory = [];
  
  /// 性能阈值配置
  final Map<String, Map<String, dynamic>> _performanceThresholds = {};
  
  /// 服务性能指标
  final Map<String, int> _serviceMetrics = {
    'operations_started': 0,
    'operations_completed': 0,
    'metrics_recorded': 0,
    'logs_recorded': 0,
  };
  
  @override
  String startOperation(String operation, {Map<String, dynamic>? context}) {
    final operationId = _generateOperationId();
    final startTime = DateTime.now();
    
    _ongoingOperations[operationId] = _OngoingOperation(
      id: operationId,
      operation: operation,
      startTime: startTime,
      context: context,
    );
    
    _serviceMetrics['operations_started'] = 
        (_serviceMetrics['operations_started'] ?? 0) + 1;
    
    _logger.d('🚀 PerformanceLoggingService: 开始监控操作 - $operation [$operationId]');
    
    return operationId;
  }
  
  @override
  OperationPerformance endOperation(
    String operationId,
    {bool success = true, List<PerformanceMetric>? additionalMetrics}
  ) {
    final endTime = DateTime.now();
    final ongoing = _ongoingOperations.remove(operationId);
    
    if (ongoing == null) {
      _logger.w('⚠️ PerformanceLoggingService: 未找到操作记录 - $operationId');
      throw ArgumentError('Operation not found: $operationId');
    }
    
    final duration = endTime.difference(ongoing.startTime);
    final metrics = <PerformanceMetric>[
      PerformanceMetric(
        name: '${ongoing.operation}_duration',
        type: PerformanceMetricType.duration,
        value: duration.inMilliseconds,
        unit: 'ms',
        timestamp: endTime,
        metadata: {'operation_id': operationId},
      ),
    ];
    
    if (additionalMetrics != null) {
      metrics.addAll(additionalMetrics);
    }
    
    final performance = OperationPerformance(
      operation: ongoing.operation,
      startTime: ongoing.startTime,
      endTime: endTime,
      duration: duration,
      success: success,
      context: ongoing.context,
      metrics: metrics,
    );
    
    _operationHistory.add(performance);
    
    // 记录性能指标
    for (final metric in metrics) {
      recordMetric(metric);
    }
    
    // 检查性能阈值
    _checkPerformanceThresholds(performance);
    
    _serviceMetrics['operations_completed'] = 
        (_serviceMetrics['operations_completed'] ?? 0) + 1;
    
    _logger.d('✅ PerformanceLoggingService: 完成操作监控 - ${ongoing.operation} [${duration.inMilliseconds}ms]');
    
    // 保持最近10000条记录
    if (_operationHistory.length > 10000) {
      _operationHistory.removeAt(0);
    }
    
    return performance;
  }
  
  @override
  void recordMetric(PerformanceMetric metric) {
    _metricHistory[metric.name] ??= [];
    _metricHistory[metric.name]!.add(metric);
    
    // 保持每个指标最近1000条记录
    if (_metricHistory[metric.name]!.length > 1000) {
      _metricHistory[metric.name]!.removeAt(0);
    }
    
    _serviceMetrics['metrics_recorded'] = 
        (_serviceMetrics['metrics_recorded'] ?? 0) + 1;
    
    _logger.d('📊 PerformanceLoggingService: 记录指标 - ${metric.name}: ${metric.value}${metric.unit}');
  }
  
  @override
  void recordMetrics(List<PerformanceMetric> metrics) {
    for (final metric in metrics) {
      recordMetric(metric);
    }
  }
  
  @override
  void log(
    LogLevel level,
    String message,
    {String? operation, Map<String, dynamic>? context, dynamic error, StackTrace? stackTrace}
  ) {
    final record = LogRecord(
      level: level,
      message: message,
      timestamp: DateTime.now(),
      operation: operation,
      context: context,
      error: error,
      stackTrace: stackTrace,
    );
    
    _logHistory.add(record);
    
    // 保持最近5000条日志记录
    if (_logHistory.length > 5000) {
      _logHistory.removeAt(0);
    }
    
    _serviceMetrics['logs_recorded'] = 
        (_serviceMetrics['logs_recorded'] ?? 0) + 1;
    
    // 输出到系统日志
    switch (level) {
      case LogLevel.debug:
        _logger.d(message, error: error, stackTrace: stackTrace);
        break;
      case LogLevel.info:
        _logger.i(message, error: error, stackTrace: stackTrace);
        break;
      case LogLevel.warning:
        _logger.w(message, error: error, stackTrace: stackTrace);
        break;
      case LogLevel.error:
        _logger.e(message, error: error, stackTrace: stackTrace);
        break;
      case LogLevel.critical:
        _logger.f(message, error: error, stackTrace: stackTrace);
        break;
    }
  }
  
  @override
  void logDebug(String message, {String? operation, Map<String, dynamic>? context}) {
    log(LogLevel.debug, message, operation: operation, context: context);
  }
  
  @override
  void logInfo(String message, {String? operation, Map<String, dynamic>? context}) {
    log(LogLevel.info, message, operation: operation, context: context);
  }
  
  @override
  void logWarning(String message, {String? operation, Map<String, dynamic>? context}) {
    log(LogLevel.warning, message, operation: operation, context: context);
  }
  
  @override
  void logError(String message, {String? operation, Map<String, dynamic>? context, dynamic error, StackTrace? stackTrace}) {
    log(LogLevel.error, message, operation: operation, context: context, error: error, stackTrace: stackTrace);
  }
  
  @override
  void logCritical(String message, {String? operation, Map<String, dynamic>? context, dynamic error, StackTrace? stackTrace}) {
    log(LogLevel.critical, message, operation: operation, context: context, error: error, stackTrace: stackTrace);
  }
  
  @override
  Map<String, dynamic> getPerformanceStatistics({String? operation, Duration? timeRange}) {
    final now = DateTime.now();
    final cutoffTime = timeRange != null ? now.subtract(timeRange) : null;
    
    var operations = _operationHistory.where((op) {
      if (operation != null && op.operation != operation) return false;
      if (cutoffTime != null && op.startTime.isBefore(cutoffTime)) return false;
      return true;
    }).toList();
    
    if (operations.isEmpty) {
      return {
        'operation': operation ?? 'all',
        'count': 0,
        'success_rate': 0.0,
        'avg_duration_ms': 0.0,
        'min_duration_ms': 0,
        'max_duration_ms': 0,
        'time_range': timeRange?.inMilliseconds,
      };
    }
    
    final successCount = operations.where((op) => op.success).length;
    final durations = operations.map((op) => op.duration.inMilliseconds).toList();
    durations.sort();
    
    return {
      'operation': operation ?? 'all',
      'count': operations.length,
      'success_count': successCount,
      'failure_count': operations.length - successCount,
      'success_rate': successCount / operations.length,
      'avg_duration_ms': durations.reduce((a, b) => a + b) / durations.length,
      'min_duration_ms': durations.first,
      'max_duration_ms': durations.last,
      'median_duration_ms': durations[durations.length ~/ 2],
      'p95_duration_ms': durations[(durations.length * 0.95).floor()],
      'time_range': timeRange?.inMilliseconds,
    };
  }

  @override
  List<PerformanceMetric> getMetricHistory(String metricName, {Duration? timeRange, int limit = 100}) {
    final metrics = _metricHistory[metricName] ?? [];
    final now = DateTime.now();
    final cutoffTime = timeRange != null ? now.subtract(timeRange) : null;

    var filteredMetrics = metrics.where((metric) {
      if (cutoffTime != null && metric.timestamp.isBefore(cutoffTime)) return false;
      return true;
    }).toList();

    // 返回最近的记录
    if (filteredMetrics.length > limit) {
      filteredMetrics = filteredMetrics.sublist(filteredMetrics.length - limit);
    }

    return filteredMetrics;
  }

  @override
  List<LogRecord> getLogRecords({LogLevel? level, String? operation, Duration? timeRange, int limit = 100}) {
    final now = DateTime.now();
    final cutoffTime = timeRange != null ? now.subtract(timeRange) : null;

    var filteredLogs = _logHistory.where((log) {
      if (level != null && log.level != level) return false;
      if (operation != null && log.operation != operation) return false;
      if (cutoffTime != null && log.timestamp.isBefore(cutoffTime)) return false;
      return true;
    }).toList();

    // 返回最近的记录
    if (filteredLogs.length > limit) {
      filteredLogs = filteredLogs.sublist(filteredLogs.length - limit);
    }

    return filteredLogs;
  }

  @override
  List<OperationPerformance> getSlowOperations({int threshold = 1000, Duration? timeRange}) {
    final now = DateTime.now();
    final cutoffTime = timeRange != null ? now.subtract(timeRange) : null;

    return _operationHistory.where((op) {
      if (cutoffTime != null && op.startTime.isBefore(cutoffTime)) return false;
      return op.duration.inMilliseconds >= threshold;
    }).toList();
  }

  @override
  Map<String, dynamic> getErrorRateStatistics({Duration? timeRange}) {
    final now = DateTime.now();
    final cutoffTime = timeRange != null ? now.subtract(timeRange) : null;

    var operations = _operationHistory.where((op) {
      if (cutoffTime != null && op.startTime.isBefore(cutoffTime)) return false;
      return true;
    }).toList();

    if (operations.isEmpty) {
      return {
        'total_operations': 0,
        'error_count': 0,
        'error_rate': 0.0,
        'time_range': timeRange?.inMilliseconds,
      };
    }

    final errorCount = operations.where((op) => !op.success).length;

    return {
      'total_operations': operations.length,
      'error_count': errorCount,
      'success_count': operations.length - errorCount,
      'error_rate': errorCount / operations.length,
      'success_rate': (operations.length - errorCount) / operations.length,
      'time_range': timeRange?.inMilliseconds,
    };
  }

  @override
  void cleanupHistoryData({Duration olderThan = const Duration(days: 7)}) {
    final cutoffTime = DateTime.now().subtract(olderThan);

    // 清理操作历史
    _operationHistory.removeWhere((op) => op.startTime.isBefore(cutoffTime));

    // 清理指标历史
    for (final metricName in _metricHistory.keys) {
      _metricHistory[metricName]!.removeWhere((metric) => metric.timestamp.isBefore(cutoffTime));
    }

    // 清理日志历史
    _logHistory.removeWhere((log) => log.timestamp.isBefore(cutoffTime));

    _logger.i('🧹 PerformanceLoggingService: 清理历史数据完成');
  }

  @override
  Map<String, dynamic> exportPerformanceReport({Duration? timeRange}) {
    return {
      'performance_statistics': getPerformanceStatistics(timeRange: timeRange),
      'error_rate_statistics': getErrorRateStatistics(timeRange: timeRange),
      'slow_operations': getSlowOperations(timeRange: timeRange).map((op) => op.toJson()).toList(),
      'service_metrics': Map.from(_serviceMetrics),
      'ongoing_operations_count': _ongoingOperations.length,
      'metric_types_count': _metricHistory.length,
      'log_records_count': _logHistory.length,
      'export_timestamp': DateTime.now().toIso8601String(),
      'time_range': timeRange?.inMilliseconds,
    };
  }

  @override
  void setPerformanceThresholds(String operation, Map<String, dynamic> thresholds) {
    _performanceThresholds[operation] = thresholds;
    _logger.i('🎯 PerformanceLoggingService: 设置性能阈值 - $operation');
  }

  @override
  Map<String, dynamic> getServicePerformanceReport() {
    return {
      'service_metrics': Map.from(_serviceMetrics),
      'ongoing_operations_count': _ongoingOperations.length,
      'operation_history_count': _operationHistory.length,
      'metric_history_count': _metricHistory.values.fold(0, (sum, list) => sum + list.length),
      'log_history_count': _logHistory.length,
      'performance_thresholds_count': _performanceThresholds.length,
      'service': 'PerformanceLoggingService',
      'version': 'v14.1',
    };
  }

  // ========== 私有辅助方法 ==========

  /// 生成操作ID
  String _generateOperationId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = DateTime.now().microsecond;
    return 'OP_$timestamp\_$random';
  }

  /// 检查性能阈值
  void _checkPerformanceThresholds(OperationPerformance performance) {
    final thresholds = _performanceThresholds[performance.operation];
    if (thresholds == null) return;

    final durationThreshold = thresholds['max_duration_ms'] as int?;
    if (durationThreshold != null && performance.duration.inMilliseconds > durationThreshold) {
      logWarning(
        '性能阈值告警: ${performance.operation} 耗时 ${performance.duration.inMilliseconds}ms 超过阈值 ${durationThreshold}ms',
        operation: performance.operation,
        context: {
          'actual_duration_ms': performance.duration.inMilliseconds,
          'threshold_ms': durationThreshold,
          'operation_id': performance.operation,
        },
      );
    }
  }
}

/// 正在进行的操作记录
class _OngoingOperation {
  final String id;
  final String operation;
  final DateTime startTime;
  final Map<String, dynamic>? context;

  const _OngoingOperation({
    required this.id,
    required this.operation,
    required this.startTime,
    this.context,
  });
}
