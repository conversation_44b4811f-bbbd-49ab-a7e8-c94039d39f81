/// 🔥 v14.1重构：健康数据同步服务
///
/// 统一健康数据同步逻辑，支持多种同步策略
/// 集成错误处理和性能监控

import 'package:logger/logger.dart';
import 'dart:io';

import '../interfaces/health_data_sync_interface.dart';
import '../interfaces/error_handling_interface.dart';
import '../interfaces/performance_logging_interface.dart';
import '../../../services/health_service.dart';
import '../../../models/health_data.dart';

/// 旧的健康数据同步服务实现（保持兼容性）
class HealthSyncService implements IHealthSyncService {
  static final Logger _logger = Logger();

  final HealthService _healthService;

  /// 性能指标记录
  final Map<String, int> _performanceMetrics = {
    'sync_count': 0,
    'timeout_count': 0,
    'validation_count': 0,
    'api_calls_count': 0,
  };

  HealthSyncService({
    required HealthService healthService,
  }) : _healthService = healthService;

  @override
  Future<Map<String, dynamic>> syncHealthData(Map<String, dynamic> params) async {
    final stepStartTime = DateTime.now();

    _logger.i('📊 HealthSyncService: 开始健康数据同步');

    try {
      // 提取参数
      final permissions = params['permissions'] ?? {};
      final permissionStates = params['permission_states'] ?? {};
      final baselineResult = params['baseline_result'];
      final sessionContext = params['session_context'] ?? {};
      final timeoutDuration = params['timeout_duration'] ?? 10;

      // 基本的权限状态验证
      if (permissions.isEmpty) {
        _performanceMetrics['validation_count'] = (_performanceMetrics['validation_count'] ?? 0) + 1;
        return {
          'success': false,
          'error': '权限信息为空',
          'duration_ms': DateTime.now().difference(stepStartTime).inMilliseconds,
          'skipped': false,
        };
      }

      // 核心同步逻辑：调用HealthService获取健康数据
      _performanceMetrics['api_calls_count'] = (_performanceMetrics['api_calls_count'] ?? 0) + 1;

      final healthDataResult = await _healthService.getHealthData(
        permissions: permissions,
        permissionStates: permissionStates,
        baselineResult: baselineResult,
        sessionContext: sessionContext,
        timeoutDuration: timeoutDuration,
      ).timeout(Duration(seconds: timeoutDuration));

      // 数据验证和处理
      _performanceMetrics['validation_count'] = (_performanceMetrics['validation_count'] ?? 0) + 1;

      if (healthDataResult == null) {
        return {
          'success': false,
          'error': '健康数据获取失败',
          'duration_ms': DateTime.now().difference(stepStartTime).inMilliseconds,
          'skipped': false,
        };
      }

      // 成功返回结果
      _performanceMetrics['sync_count'] = (_performanceMetrics['sync_count'] ?? 0) + 1;

      return {
        'success': true,
        'health_data': healthDataResult,
        'duration_ms': DateTime.now().difference(stepStartTime).inMilliseconds,
        'skipped': false,
        'sync_strategy': 'standard',
        'performance_metrics': Map.from(_performanceMetrics),
      };

    } catch (e, stackTrace) {
      _logger.e('❌ HealthSyncService同步失败', error: e, stackTrace: stackTrace);

      return {
        'success': false,
        'error': e.toString(),
        'duration_ms': DateTime.now().difference(stepStartTime).inMilliseconds,
        'skipped': false,
        'sync_strategy': 'standard',
      };
    }
  }

  @override
  Future<Map<String, dynamic>> performSyncWithConflictResolution({
    required Map<String, bool> permissions,
    dynamic preloadedHealthData,
    String dataType = 'health_data',
  }) async {
    // 简化实现
    return {
      'success': true,
      'sync_success': true,
      'health_data': {},
      'affected_tasks': 0,
      'conflict_resolved': false,
      'conflict_details': null,
    };
  }

  @override
  Map<String, dynamic> calculateIncrement(
    Map<String, dynamic> baseline,
    Map<String, dynamic> current
  ) {
    // 简化实现
    return {};
  }

  @override
  bool validateSyncResult(Map<String, dynamic> syncResult) {
    return syncResult.containsKey('success');
  }
}

/// 新的健康数据同步服务实现
class HealthDataSyncService implements IHealthDataSyncService {
  static final Logger _logger = Logger();

  final HealthService _healthService;
  final IErrorHandlingService _errorHandlingService;
  final IPerformanceLoggingService _performanceLoggingService;

  /// 性能统计
  final Map<String, int> _stats = {
    'total_syncs': 0,
    'successful_syncs': 0,
    'failed_syncs': 0,
    'timeout_syncs': 0,
    'skipped_syncs': 0,
  };

  HealthDataSyncService({
    required HealthService healthService,
    required IErrorHandlingService errorHandlingService,
    required IPerformanceLoggingService performanceLoggingService,
  }) : _healthService = healthService,
       _errorHandlingService = errorHandlingService,
       _performanceLoggingService = performanceLoggingService;

  @override
  Future<HealthDataSyncResult> syncHealthData(HealthSyncParams params) async {
    _stats['total_syncs'] = (_stats['total_syncs'] ?? 0) + 1;

    final operationId = _performanceLoggingService.startOperation(
      'health_data_sync',
      context: {'strategy': params.strategy.name},
    );

    try {
      // 根据策略选择同步方法
      switch (params.strategy) {
        case HealthSyncStrategy.standard:
          return await syncWithStandardStrategy(params);
        case HealthSyncStrategy.optimized:
          return await syncWithOptimizedStrategy(params);
        case HealthSyncStrategy.firstSync:
          return await syncWithFirstSyncStrategy(params);
      }
    } catch (error, stackTrace) {
      _stats['failed_syncs'] = (_stats['failed_syncs'] ?? 0) + 1;

      final result = await _errorHandlingService.handleError(
        operation: 'health_data_sync',
        error: error,
        stackTrace: stackTrace,
        context: params.toMap(),
      );

      _performanceLoggingService.endOperation(operationId, success: false);

      return HealthDataSyncResult(
        success: false,
        error: result.userMessage,
        errorCode: result.errorCategory,
        durationMs: result.duration?.inMilliseconds ?? 0,
        strategy: params.strategy,
        performanceMetrics: const HealthSyncPerformanceMetrics(),
      );
    }
  }

  @override
  Future<HealthDataSyncResult> syncWithStandardStrategy(HealthSyncParams params) async {
    final startTime = DateTime.now();

    try {
      // 跨天风险检查
      if (params.enableCrossDayRiskCheck && isInCrossDayRiskPeriod()) {
        _stats['skipped_syncs'] = (_stats['skipped_syncs'] ?? 0) + 1;
        return HealthDataSyncResult(
          success: true,
          skipped: true,
          skipReason: '跨天风险时段，跳过同步',
          durationMs: DateTime.now().difference(startTime).inMilliseconds,
          strategy: params.strategy,
          performanceMetrics: const HealthSyncPerformanceMetrics(),
        );
      }

      // 执行标准同步逻辑（从原_executeStep4HealthDataSync迁移）
      final healthData = await _performStandardSync(params);

      _stats['successful_syncs'] = (_stats['successful_syncs'] ?? 0) + 1;

      return HealthDataSyncResult(
        success: true,
        healthData: healthData,
        durationMs: DateTime.now().difference(startTime).inMilliseconds,
        strategy: params.strategy,
        performanceMetrics: const HealthSyncPerformanceMetrics(
          apiCallCount: 1,
          validationCount: 1,
        ),
      );
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<HealthDataSyncResult> syncWithOptimizedStrategy(HealthSyncParams params) async {
    final startTime = DateTime.now();

    try {
      // 轻量化同步逻辑（从原_executeOptimizedHealthDataSync迁移）
      final healthData = await _performOptimizedSync(params);

      _stats['successful_syncs'] = (_stats['successful_syncs'] ?? 0) + 1;

      return HealthDataSyncResult(
        success: true,
        healthData: healthData,
        durationMs: DateTime.now().difference(startTime).inMilliseconds,
        strategy: params.strategy,
        performanceMetrics: const HealthSyncPerformanceMetrics(
          apiCallCount: 1,
        ),
      );
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<HealthDataSyncResult> syncWithFirstSyncStrategy(HealthSyncParams params) async {
    final startTime = DateTime.now();

    try {
      // 首次同步逻辑（从原_performTodayFirstHealthDataSync迁移）
      final healthData = await _performFirstSync(params);

      _stats['successful_syncs'] = (_stats['successful_syncs'] ?? 0) + 1;

      return HealthDataSyncResult(
        success: true,
        healthData: healthData,
        durationMs: DateTime.now().difference(startTime).inMilliseconds,
        strategy: params.strategy,
        performanceMetrics: const HealthSyncPerformanceMetrics(
          apiCallCount: 1,
          validationCount: 1,
        ),
      );
    } catch (error) {
      rethrow;
    }
  }

  // 核心同步实现方法
  Future<HealthData?> _performStandardSync(HealthSyncParams params) async {
    return await _healthService.getHealthData(
      permissions: params.permissions,
      permissionStates: params.permissionStates,
      baselineResult: params.baselineResult,
      sessionContext: params.sessionContext.toMap(),
      timeoutDuration: params.timeoutSeconds,
    ).timeout(Duration(seconds: params.timeoutSeconds));
  }

  Future<HealthData?> _performOptimizedSync(HealthSyncParams params) async {
    return await _healthService.getHealthData(
      permissions: params.permissions,
      permissionStates: params.permissionStates,
      sessionContext: params.sessionContext.toMap(),
      timeoutDuration: params.timeoutSeconds,
    ).timeout(Duration(seconds: params.timeoutSeconds));
  }

  Future<HealthData?> _performFirstSync(HealthSyncParams params) async {
    return await _healthService.getHealthData(
      permissions: params.permissions,
      permissionStates: params.permissionStates,
      baselineResult: params.baselineResult,
      sessionContext: params.sessionContext.toMap(),
      timeoutDuration: params.timeoutSeconds,
    ).timeout(Duration(seconds: params.timeoutSeconds));
  }

  @override
  Future<Map<String, dynamic>> validateSyncParams(HealthSyncParams params) async {
    final errors = <String>[];

    if (params.permissions.isEmpty) {
      errors.add('权限信息为空');
    }

    if (params.timeoutSeconds <= 0) {
      errors.add('超时时间必须大于0');
    }

    return {
      'valid': errors.isEmpty,
      'errors': errors,
    };
  }

  @override
  bool isInCrossDayRiskPeriod() {
    final now = DateTime.now();
    final hour = now.hour;
    return (hour == 23 && now.minute >= 30) || (hour == 0 && now.minute <= 30);
  }

  @override
  Map<String, dynamic> getPerformanceStatistics() {
    return Map.from(_stats);
  }

  @override
  void resetPerformanceStatistics() {
    _stats.clear();
    _stats.addAll({
      'total_syncs': 0,
      'successful_syncs': 0,
      'failed_syncs': 0,
      'timeout_syncs': 0,
      'skipped_syncs': 0,
    });
  }

  @override
  Map<String, dynamic> getServiceHealthStatus() {
    final total = _stats['total_syncs'] ?? 0;
    final successful = _stats['successful_syncs'] ?? 0;

    return {
      'service_name': 'HealthDataSyncService',
      'status': total > 0 ? 'active' : 'idle',
      'success_rate': total > 0 ? (successful / total * 100).round() : 100,
      'total_operations': total,
    };
  }
}
