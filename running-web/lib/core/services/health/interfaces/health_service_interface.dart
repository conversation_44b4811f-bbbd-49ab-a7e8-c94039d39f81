/// 🔥 v14.1重构：HealthDataFlowService模块化接口定义
/// 
/// 定义所有健康数据流程服务的抽象接口，确保模块间的松耦合
/// 遵循SOLID原则，支持依赖注入和单元测试

/// 权限管理服务接口
abstract class IHealthPermissionService {
  /// 检查健康权限状态
  /// 
  /// 返回格式：
  /// ```dart
  /// {
  ///   'success': true,
  ///   'permissions': {'steps': 'authorized', 'distance': 'denied', 'calories': 'authorized'},
  ///   'has_all_permissions': false,
  ///   'duration_ms': 150
  /// }
  /// ```
  Future<Map<String, dynamic>> checkPermissions();
  
  /// 获取权限状态映射
  /// 
  /// 返回格式：Map<String, String> - 权限名称到状态的映射
  Future<Map<String, String>> getPermissionStatus();
  
  /// 验证权限结果的有效性
  /// 
  /// [permissions] - 权限检查结果
  /// 返回是否所有必要权限都已获得
  bool validatePermissions(Map<String, dynamic> permissions);
  
  /// 格式化权限状态为API传参格式
  /// 
  /// [permissions] - 原始权限状态
  /// 返回格式化后的权限映射
  Map<String, String> formatPermissionForAPI(Map<String, dynamic> permissions);
}

/// 数据同步服务接口
abstract class IHealthSyncService {
  /// 执行健康数据同步
  /// 
  /// [params] - 同步参数，包含权限、基线等信息
  /// 返回同步结果
  Future<Map<String, dynamic>> syncHealthData(Map<String, dynamic> params);
  
  /// 执行带冲突检测的健康数据同步
  /// 
  /// [permissions] - 权限状态
  /// [preloadedHealthData] - 预加载的健康数据
  /// 返回同步结果，包含冲突解决信息
  Future<Map<String, dynamic>> performSyncWithConflictResolution({
    required Map<String, bool> permissions,
    dynamic preloadedHealthData,
    String dataType = 'health_data',
  });
  
  /// 计算健康数据增量
  /// 
  /// [baseline] - 基线数据
  /// [current] - 当前数据
  /// 返回增量计算结果
  Map<String, dynamic> calculateIncrement(
    Map<String, dynamic> baseline, 
    Map<String, dynamic> current
  );
  
  /// 验证同步结果的有效性
  /// 
  /// [syncResult] - 同步结果
  /// 返回验证是否通过
  bool validateSyncResult(Map<String, dynamic> syncResult);
}

/// 基线管理服务接口
abstract class IHealthBaselineService {
  /// 处理基线管理逻辑
  /// 
  /// [scenario] - 场景类型 ('login', 'restart', 'resume')
  /// [permissions] - 权限状态
  /// 返回基线处理结果
  Future<Map<String, dynamic>> handleBaseline(
    String scenario, 
    Map<String, dynamic> permissions
  );
  
  /// 检查是否跨天
  /// 
  /// 返回跨天检查结果
  Future<bool> checkCrossDay();
  
  /// 重置基线数据
  /// 
  /// [reason] - 重置原因
  /// 返回重置结果
  Future<Map<String, dynamic>> resetBaseline(String reason);
  
  /// 获取当前新加坡时间
  /// 
  /// 返回新加坡时区的当前时间
  DateTime getCurrentSingaporeTime();
  
  /// 检查会话连续性
  /// 
  /// [lastSessionTime] - 上次会话时间
  /// 返回连续性检查结果
  Future<Map<String, dynamic>> checkSessionContinuity(DateTime? lastSessionTime);
}

/// UI数据加载服务接口
abstract class IHealthUIService {
  /// 加载UI数据
  /// 
  /// [permissionResult] - 权限检查结果
  /// [syncResult] - 数据同步结果
  /// 返回UI数据加载结果
  Future<Map<String, dynamic>> loadUIData(
    Map<String, dynamic> permissionResult,
    Map<String, dynamic> syncResult
  );
  
  /// 显示权限引导
  /// 
  /// [permissions] - 权限状态
  /// 返回权限引导结果
  Future<Map<String, dynamic>> showPermissionGuide(Map<String, dynamic> permissions);
  
  /// 等待UI渲染完成
  /// 
  /// 确保UI完全加载后再执行后续操作
  Future<void> waitForUIRenderingComplete();
  
  /// 验证UI加载状态
  /// 
  /// 返回UI是否已完全加载
  bool isUIFullyLoaded();
}

/// 场景处理服务接口
abstract class IHealthScenarioService {
  /// 准备场景执行
  /// 
  /// [scenario] - 场景类型
  /// 返回准备结果
  Future<Map<String, dynamic>> prepareScenario(String scenario);
  
  /// 完成场景处理
  /// 
  /// [scenario] - 场景类型
  /// [result] - 执行结果
  /// 返回完成处理结果
  Future<Map<String, dynamic>> finishScenario(
    String scenario, 
    Map<String, dynamic> result
  );
  
  /// 确定最优执行策略
  /// 
  /// [scenario] - 场景类型
  /// 返回推荐的执行策略
  String determineOptimalStrategy(String scenario);
  
  /// 获取场景配置
  ///
  /// [scenario] - 场景类型
  /// 返回场景特定的配置
  Map<String, dynamic> getScenarioConfig(String scenario);
}

/// 流程状态管理接口
abstract class IHealthFlowStateManager {
  /// 更新步骤状态
  ///
  /// [step] - 步骤编号
  /// [success] - 是否成功
  void updateStepStatus(int step, bool success);

  /// 通知步骤进度
  ///
  /// [step] - 步骤编号
  /// [phase] - 当前阶段
  void notifyStepProgress(int step, String phase);

  /// 记录步骤结果
  ///
  /// [step] - 步骤编号
  /// [result] - 步骤执行结果
  void recordStepResult(int step, Map<String, dynamic> result);

  /// 获取流程状态
  ///
  /// 返回当前流程的完整状态
  Map<String, dynamic> getFlowState();

  /// 重置流程状态
  ///
  /// 清空所有步骤状态，准备新的流程执行
  void resetFlowState();

  /// 获取当前执行步骤
  ///
  /// 返回当前正在执行的步骤编号
  int getCurrentStep();

  /// 检查步骤是否完成
  ///
  /// [step] - 步骤编号
  /// 返回指定步骤是否已完成
  bool isStepCompleted(int step);
}

/// 错误处理接口
abstract class IHealthErrorHandler {
  /// 处理步骤执行错误
  ///
  /// [step] - 出错的步骤编号
  /// [error] - 错误对象
  /// [stackTrace] - 堆栈跟踪
  /// 返回错误处理结果
  Future<Map<String, dynamic>> handleStepError(
    int step,
    dynamic error,
    StackTrace? stackTrace
  );

  /// 记录错误信息
  ///
  /// [operation] - 操作名称
  /// [error] - 错误对象
  void recordError(String operation, dynamic error);

  /// 判断是否应该重试
  ///
  /// [error] - 错误对象
  /// 返回是否建议重试
  bool shouldRetry(dynamic error);

  /// 获取用户友好的错误消息
  ///
  /// [error] - 错误对象
  /// 返回用户可理解的错误描述
  String getUserFriendlyMessage(dynamic error);

  /// 尝试错误恢复
  ///
  /// [error] - 错误对象
  /// [context] - 错误上下文
  /// 返回恢复尝试结果
  Future<Map<String, dynamic>> attemptRecovery(
    dynamic error,
    Map<String, dynamic> context
  );
}
