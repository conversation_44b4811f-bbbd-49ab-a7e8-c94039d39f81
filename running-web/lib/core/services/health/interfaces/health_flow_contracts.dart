/// 🔥 v14.1重构：健康数据流程契约定义
/// 
/// 定义步骤间的数据传递格式和错误处理契约
/// 确保模块间数据交换的标准化和类型安全

/// 步骤执行结果基础契约
class StepExecutionResult {
  final bool success;
  final int step;
  final String phase;
  final Map<String, dynamic> data;
  final String? error;
  final int durationMs;
  final DateTime timestamp;

  const StepExecutionResult({
    required this.success,
    required this.step,
    required this.phase,
    required this.data,
    this.error,
    required this.durationMs,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'step': step,
      'phase': phase,
      'data': data,
      'error': error,
      'duration_ms': durationMs,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory StepExecutionResult.fromJson(Map<String, dynamic> json) {
    return StepExecutionResult(
      success: json['success'] ?? false,
      step: json['step'] ?? 0,
      phase: json['phase'] ?? '',
      data: json['data'] ?? {},
      error: json['error'],
      durationMs: json['duration_ms'] ?? 0,
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

/// 权限检查结果契约
class PermissionCheckResult extends StepExecutionResult {
  final Map<String, String> permissions;
  final bool hasAllPermissions;
  final List<String> deniedPermissions;

  PermissionCheckResult({
    required bool success,
    required this.permissions,
    required this.hasAllPermissions,
    required this.deniedPermissions,
    required int durationMs,
    String? error,
  }) : super(
          success: success,
          step: 2,
          phase: 'permission_check',
          data: {
            'permissions': permissions,
            'has_all_permissions': hasAllPermissions,
            'denied_permissions': deniedPermissions,
          },
          error: error,
          durationMs: durationMs,
          timestamp: DateTime.now(),
        );

  factory PermissionCheckResult.fromStepResult(StepExecutionResult result) {
    final data = result.data;
    return PermissionCheckResult(
      success: result.success,
      permissions: Map<String, String>.from(data['permissions'] ?? {}),
      hasAllPermissions: data['has_all_permissions'] ?? false,
      deniedPermissions: List<String>.from(data['denied_permissions'] ?? []),
      durationMs: result.durationMs,
      error: result.error,
    );
  }
}

/// 基线处理结果契约
class BaselineProcessResult extends StepExecutionResult {
  final bool baselineReset;
  final bool crossDayDetected;
  final String sessionAction;
  final Map<String, dynamic> baselineData;

  BaselineProcessResult({
    required bool success,
    required this.baselineReset,
    required this.crossDayDetected,
    required this.sessionAction,
    required this.baselineData,
    required int durationMs,
    String? error,
  }) : super(
          success: success,
          step: 3,
          phase: 'baseline_process',
          data: {
            'baseline_reset': baselineReset,
            'cross_day_detected': crossDayDetected,
            'session_action': sessionAction,
            'baseline_data': baselineData,
          },
          error: error,
          durationMs: durationMs,
          timestamp: DateTime.now(),
        );

  factory BaselineProcessResult.fromStepResult(StepExecutionResult result) {
    final data = result.data;
    return BaselineProcessResult(
      success: result.success,
      baselineReset: data['baseline_reset'] ?? false,
      crossDayDetected: data['cross_day_detected'] ?? false,
      sessionAction: data['session_action'] ?? 'continue',
      baselineData: data['baseline_data'] ?? {},
      durationMs: result.durationMs,
      error: result.error,
    );
  }
}

/// 健康数据同步结果契约
class HealthSyncResult extends StepExecutionResult {
  final bool syncSuccess;
  final Map<String, dynamic> healthData;
  final int affectedTasks;
  final bool conflictResolved;
  final Map<String, dynamic>? conflictDetails;

  HealthSyncResult({
    required bool success,
    required this.syncSuccess,
    required this.healthData,
    required this.affectedTasks,
    this.conflictResolved = false,
    this.conflictDetails,
    required int durationMs,
    String? error,
  }) : super(
          success: success,
          step: 4,
          phase: 'health_sync',
          data: {
            'sync_success': syncSuccess,
            'health_data': healthData,
            'affected_tasks': affectedTasks,
            'conflict_resolved': conflictResolved,
            'conflict_details': conflictDetails,
          },
          error: error,
          durationMs: durationMs,
          timestamp: DateTime.now(),
        );

  factory HealthSyncResult.fromStepResult(StepExecutionResult result) {
    final data = result.data;
    return HealthSyncResult(
      success: result.success,
      syncSuccess: data['sync_success'] ?? false,
      healthData: data['health_data'] ?? {},
      affectedTasks: data['affected_tasks'] ?? 0,
      conflictResolved: data['conflict_resolved'] ?? false,
      conflictDetails: data['conflict_details'],
      durationMs: result.durationMs,
      error: result.error,
    );
  }
}

/// UI加载结果契约
class UILoadingResult extends StepExecutionResult {
  final bool uiDataLoaded;
  final bool permissionGuideShown;
  final Map<String, dynamic> uiState;

  UILoadingResult({
    required bool success,
    required this.uiDataLoaded,
    required this.permissionGuideShown,
    required this.uiState,
    required int durationMs,
    String? error,
  }) : super(
          success: success,
          step: 5,
          phase: 'ui_loading',
          data: {
            'ui_data_loaded': uiDataLoaded,
            'permission_guide_shown': permissionGuideShown,
            'ui_state': uiState,
          },
          error: error,
          durationMs: durationMs,
          timestamp: DateTime.now(),
        );

  factory UILoadingResult.fromStepResult(StepExecutionResult result) {
    final data = result.data;
    return UILoadingResult(
      success: result.success,
      uiDataLoaded: data['ui_data_loaded'] ?? false,
      permissionGuideShown: data['permission_guide_shown'] ?? false,
      uiState: data['ui_state'] ?? {},
      durationMs: result.durationMs,
      error: result.error,
    );
  }
}

/// 流程执行上下文契约
class FlowExecutionContext {
  final String scenario;
  final DateTime startTime;
  final Map<String, dynamic> metadata;
  final List<StepExecutionResult> stepResults;

  FlowExecutionContext({
    required this.scenario,
    required this.startTime,
    this.metadata = const {},
    this.stepResults = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'scenario': scenario,
      'start_time': startTime.toIso8601String(),
      'metadata': metadata,
      'step_results': stepResults.map((r) => r.toJson()).toList(),
    };
  }

  /// 获取指定步骤的结果
  StepExecutionResult? getStepResult(int step) {
    try {
      return stepResults.firstWhere((r) => r.step == step);
    } catch (e) {
      return null;
    }
  }

  /// 检查所有步骤是否成功
  bool get allStepsSuccessful {
    return stepResults.every((r) => r.success);
  }

  /// 获取总执行时间
  Duration get totalDuration {
    if (stepResults.isEmpty) return Duration.zero;
    return Duration(
      milliseconds: stepResults.map((r) => r.durationMs).reduce((a, b) => a + b),
    );
  }
}
