/// 🔥 v14.1重构：健康数据同步服务接口
/// 
/// 统一健康数据同步的标准化接口，支持多种同步策略
/// 集成错误处理和性能监控，确保高质量的数据同步服务

import '../../../models/health_data.dart';

/// 同步策略枚举
enum HealthSyncStrategy {
  /// 标准同步策略 - 完整的权限检查和数据验证
  standard,
  /// 优化同步策略 - 轻量化流程，跳过部分检查
  optimized,
  /// 首次同步策略 - 跨天后的第一次同步
  firstSync,
}

/// 同步参数配置
class HealthSyncParams {
  /// 权限状态映射
  final Map<String, dynamic> permissions;
  
  /// API权限状态参数
  final Map<String, dynamic> permissionStates;
  
  /// 基线处理结果
  final Map<String, dynamic>? baselineResult;
  
  /// 会话上下文信息
  final HealthSyncSessionContext sessionContext;
  
  /// 同步策略
  final HealthSyncStrategy strategy;
  
  /// 超时配置（秒）
  final int timeoutSeconds;
  
  /// 是否启用跨天风险检查
  final bool enableCrossDayRiskCheck;
  
  /// 自定义配置参数
  final Map<String, dynamic>? customConfig;

  const HealthSyncParams({
    required this.permissions,
    required this.permissionStates,
    this.baselineResult,
    required this.sessionContext,
    this.strategy = HealthSyncStrategy.standard,
    this.timeoutSeconds = 10,
    this.enableCrossDayRiskCheck = true,
    this.customConfig,
  });

  /// 转换为Map格式（兼容现有代码）
  Map<String, dynamic> toMap() {
    return {
      'permissions': permissions,
      'permission_states': permissionStates,
      'baseline_result': baselineResult,
      'session_context': sessionContext.toMap(),
      'strategy': strategy.name,
      'timeout_seconds': timeoutSeconds,
      'enable_cross_day_risk_check': enableCrossDayRiskCheck,
      'custom_config': customConfig,
    };
  }

  /// 从Map创建实例
  factory HealthSyncParams.fromMap(Map<String, dynamic> map) {
    return HealthSyncParams(
      permissions: map['permissions'] ?? {},
      permissionStates: map['permission_states'] ?? {},
      baselineResult: map['baseline_result'],
      sessionContext: HealthSyncSessionContext.fromMap(map['session_context'] ?? {}),
      strategy: HealthSyncStrategy.values.firstWhere(
        (e) => e.name == map['strategy'],
        orElse: () => HealthSyncStrategy.standard,
      ),
      timeoutSeconds: map['timeout_seconds'] ?? 10,
      enableCrossDayRiskCheck: map['enable_cross_day_risk_check'] ?? true,
      customConfig: map['custom_config'],
    );
  }
}

/// 会话上下文信息
class HealthSyncSessionContext {
  /// 执行步骤
  final int step;
  
  /// 场景标识
  final String scenario;
  
  /// 时间戳
  final String timestamp;
  
  /// 用户ID（可选）
  final String? userId;
  
  /// 设备ID（可选）
  final String? deviceId;
  
  /// 额外上下文数据
  final Map<String, dynamic>? extraData;

  const HealthSyncSessionContext({
    required this.step,
    required this.scenario,
    required this.timestamp,
    this.userId,
    this.deviceId,
    this.extraData,
  });

  Map<String, dynamic> toMap() {
    return {
      'step': step,
      'scenario': scenario,
      'timestamp': timestamp,
      'user_id': userId,
      'device_id': deviceId,
      'extra_data': extraData,
    };
  }

  factory HealthSyncSessionContext.fromMap(Map<String, dynamic> map) {
    return HealthSyncSessionContext(
      step: map['step'] ?? 0,
      scenario: map['scenario'] ?? 'unknown',
      timestamp: map['timestamp'] ?? DateTime.now().toIso8601String(),
      userId: map['user_id'],
      deviceId: map['device_id'],
      extraData: map['extra_data'],
    );
  }
}

/// 同步结果
class HealthDataSyncResult {
  /// 同步是否成功
  final bool success;
  
  /// 健康数据
  final HealthData? healthData;
  
  /// 错误信息
  final String? error;
  
  /// 错误代码
  final String? errorCode;
  
  /// 执行时长（毫秒）
  final int durationMs;
  
  /// 是否跳过执行
  final bool skipped;
  
  /// 跳过原因
  final String? skipReason;
  
  /// 使用的同步策略
  final HealthSyncStrategy strategy;

  /// 性能指标
  final HealthSyncPerformanceMetrics performanceMetrics;
  
  /// 影响的任务数量
  final int affectedTasks;
  
  /// 总奖励金额
  final double totalRewards;
  
  /// 额外结果数据
  final Map<String, dynamic>? extraData;

  const HealthDataSyncResult({
    required this.success,
    this.healthData,
    this.error,
    this.errorCode,
    required this.durationMs,
    this.skipped = false,
    this.skipReason,
    required this.strategy,
    required this.performanceMetrics,
    this.affectedTasks = 0,
    this.totalRewards = 0.0,
    this.extraData,
  });

  /// 转换为Map格式（兼容现有代码）
  Map<String, dynamic> toMap() {
    return {
      'success': success,
      'health_data': healthData?.toJson(),
      'error': error,
      'error_code': errorCode,
      'duration_ms': durationMs,
      'skipped': skipped,
      'skip_reason': skipReason,
      'strategy': strategy.name,
      'performance_metrics': performanceMetrics.toMap(),
      'affected_tasks': affectedTasks,
      'total_rewards': totalRewards,
      'extra_data': extraData,
    };
  }
}

/// 性能指标
class HealthSyncPerformanceMetrics {
  /// API调用次数
  final int apiCallCount;
  
  /// 数据验证次数
  final int validationCount;
  
  /// 缓存命中次数
  final int cacheHitCount;
  
  /// 网络请求时长（毫秒）
  final int networkDurationMs;
  
  /// 数据处理时长（毫秒）
  final int processingDurationMs;

  const HealthSyncPerformanceMetrics({
    this.apiCallCount = 0,
    this.validationCount = 0,
    this.cacheHitCount = 0,
    this.networkDurationMs = 0,
    this.processingDurationMs = 0,
  });

  Map<String, dynamic> toMap() {
    return {
      'api_call_count': apiCallCount,
      'validation_count': validationCount,
      'cache_hit_count': cacheHitCount,
      'network_duration_ms': networkDurationMs,
      'processing_duration_ms': processingDurationMs,
    };
  }
}

/// 健康数据同步服务接口
abstract class IHealthDataSyncService {
  /// 执行健康数据同步
  ///
  /// [params] 同步参数配置
  /// 返回标准化的同步结果
  Future<HealthDataSyncResult> syncHealthData(HealthSyncParams params);

  /// 执行标准同步策略
  ///
  /// 完整的权限检查和数据验证流程
  Future<HealthDataSyncResult> syncWithStandardStrategy(HealthSyncParams params);

  /// 执行优化同步策略
  ///
  /// 轻量化流程，跳过部分检查，适用于定时同步
  Future<HealthDataSyncResult> syncWithOptimizedStrategy(HealthSyncParams params);

  /// 执行首次同步策略
  ///
  /// 跨天后的第一次同步，包含基线重置逻辑
  Future<HealthDataSyncResult> syncWithFirstSyncStrategy(HealthSyncParams params);

  /// 验证同步参数
  /// 
  /// [params] 待验证的参数
  /// 返回验证结果和错误信息
  Future<Map<String, dynamic>> validateSyncParams(HealthSyncParams params);

  /// 检查跨天风险时段
  /// 
  /// 返回是否处于跨天风险时段
  bool isInCrossDayRiskPeriod();

  /// 获取服务性能统计
  /// 
  /// 返回服务的性能指标和统计信息
  Map<String, dynamic> getPerformanceStatistics();

  /// 重置性能统计
  /// 
  /// 清空所有性能统计数据
  void resetPerformanceStatistics();

  /// 获取服务健康状态
  /// 
  /// 返回服务的健康状态和诊断信息
  Map<String, dynamic> getServiceHealthStatus();
}
