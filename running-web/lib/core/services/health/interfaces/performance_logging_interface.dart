/// 🔥 v14.1重构：性能日志服务接口
/// 
/// 定义统一的性能监控、日志记录和分析功能
/// 支持操作耗时监控、性能指标统计、日志分级记录等功能

/// 日志级别枚举
enum LogLevel {
  debug,
  info,
  warning,
  error,
  critical,
}

/// 性能指标类型枚举
enum PerformanceMetricType {
  duration,
  count,
  rate,
  memory,
  network,
}

/// 性能指标数据
class PerformanceMetric {
  final String name;
  final PerformanceMetricType type;
  final dynamic value;
  final String unit;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const PerformanceMetric({
    required this.name,
    required this.type,
    required this.value,
    required this.unit,
    required this.timestamp,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type.name,
      'value': value,
      'unit': unit,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }
}

/// 操作性能记录
class OperationPerformance {
  final String operation;
  final DateTime startTime;
  final DateTime endTime;
  final Duration duration;
  final bool success;
  final Map<String, dynamic>? context;
  final List<PerformanceMetric> metrics;

  const OperationPerformance({
    required this.operation,
    required this.startTime,
    required this.endTime,
    required this.duration,
    required this.success,
    this.context,
    required this.metrics,
  });

  Map<String, dynamic> toJson() {
    return {
      'operation': operation,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      'duration_ms': duration.inMilliseconds,
      'success': success,
      'context': context,
      'metrics': metrics.map((m) => m.toJson()).toList(),
    };
  }
}

/// 日志记录
class LogRecord {
  final LogLevel level;
  final String message;
  final DateTime timestamp;
  final String? operation;
  final Map<String, dynamic>? context;
  final dynamic error;
  final StackTrace? stackTrace;

  const LogRecord({
    required this.level,
    required this.message,
    required this.timestamp,
    this.operation,
    this.context,
    this.error,
    this.stackTrace,
  });

  Map<String, dynamic> toJson() {
    return {
      'level': level.name,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'operation': operation,
      'context': context,
      'error': error?.toString(),
      'stack_trace': stackTrace?.toString(),
    };
  }
}

/// 性能日志服务接口
abstract class IPerformanceLoggingService {
  /// 开始操作性能监控
  /// 
  /// [operation] - 操作名称
  /// [context] - 上下文信息
  /// 返回操作ID用于后续跟踪
  String startOperation(String operation, {Map<String, dynamic>? context});
  
  /// 结束操作性能监控
  /// 
  /// [operationId] - 操作ID
  /// [success] - 操作是否成功
  /// [additionalMetrics] - 额外的性能指标
  /// 返回操作性能记录
  OperationPerformance endOperation(
    String operationId,
    {bool success = true, List<PerformanceMetric>? additionalMetrics}
  );
  
  /// 记录性能指标
  /// 
  /// [metric] - 性能指标
  void recordMetric(PerformanceMetric metric);
  
  /// 记录多个性能指标
  /// 
  /// [metrics] - 性能指标列表
  void recordMetrics(List<PerformanceMetric> metrics);
  
  /// 记录日志
  /// 
  /// [level] - 日志级别
  /// [message] - 日志消息
  /// [operation] - 操作名称
  /// [context] - 上下文信息
  /// [error] - 错误对象
  /// [stackTrace] - 堆栈跟踪
  void log(
    LogLevel level,
    String message,
    {String? operation, Map<String, dynamic>? context, dynamic error, StackTrace? stackTrace}
  );
  
  /// 记录调试日志
  void logDebug(String message, {String? operation, Map<String, dynamic>? context});
  
  /// 记录信息日志
  void logInfo(String message, {String? operation, Map<String, dynamic>? context});
  
  /// 记录警告日志
  void logWarning(String message, {String? operation, Map<String, dynamic>? context});
  
  /// 记录错误日志
  void logError(String message, {String? operation, Map<String, dynamic>? context, dynamic error, StackTrace? stackTrace});
  
  /// 记录关键错误日志
  void logCritical(String message, {String? operation, Map<String, dynamic>? context, dynamic error, StackTrace? stackTrace});
  
  /// 获取操作性能统计
  /// 
  /// [operation] - 操作名称（可选，为空则返回所有操作）
  /// [timeRange] - 时间范围（可选）
  /// 返回性能统计数据
  Map<String, dynamic> getPerformanceStatistics({String? operation, Duration? timeRange});
  
  /// 获取性能指标历史
  /// 
  /// [metricName] - 指标名称
  /// [timeRange] - 时间范围
  /// [limit] - 记录数量限制
  /// 返回指标历史数据
  List<PerformanceMetric> getMetricHistory(String metricName, {Duration? timeRange, int limit = 100});
  
  /// 获取日志记录
  /// 
  /// [level] - 日志级别过滤
  /// [operation] - 操作名称过滤
  /// [timeRange] - 时间范围
  /// [limit] - 记录数量限制
  /// 返回日志记录列表
  List<LogRecord> getLogRecords({LogLevel? level, String? operation, Duration? timeRange, int limit = 100});
  
  /// 获取慢操作报告
  /// 
  /// [threshold] - 慢操作阈值（毫秒）
  /// [timeRange] - 时间范围
  /// 返回慢操作列表
  List<OperationPerformance> getSlowOperations({int threshold = 1000, Duration? timeRange});
  
  /// 获取错误率统计
  /// 
  /// [timeRange] - 时间范围
  /// 返回错误率统计数据
  Map<String, dynamic> getErrorRateStatistics({Duration? timeRange});
  
  /// 清理历史数据
  /// 
  /// [olderThan] - 清理多久之前的数据
  void cleanupHistoryData({Duration olderThan = const Duration(days: 7)});
  
  /// 导出性能报告
  /// 
  /// [timeRange] - 时间范围
  /// 返回完整的性能报告
  Map<String, dynamic> exportPerformanceReport({Duration? timeRange});
  
  /// 设置性能阈值
  /// 
  /// [operation] - 操作名称
  /// [thresholds] - 性能阈值配置
  void setPerformanceThresholds(String operation, Map<String, dynamic> thresholds);
  
  /// 获取服务性能报告
  /// 
  /// 返回服务本身的性能统计
  Map<String, dynamic> getServicePerformanceReport();
}
