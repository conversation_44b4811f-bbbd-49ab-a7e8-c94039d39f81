/// 🔥 v14.1重构：场景处理器服务接口
/// 
/// 定义场景处理的标准化接口，支持登录、重启、唤醒、定时同步、日常重置等场景
/// 提供统一的前置处理、后置处理和场景状态管理功能

/// 场景类型枚举
enum ScenarioType {
  login,
  restart,
  resume,
  periodicSync,
  dailyReset,
}

/// 场景处理结果
class ScenarioHandlerResult {
  final bool success;
  final String? message;
  final Map<String, dynamic>? data;
  final String? errorMessage;
  final Duration? duration;

  const ScenarioHandlerResult({
    required this.success,
    this.message,
    this.data,
    this.errorMessage,
    this.duration,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data,
      'error_message': errorMessage,
      'duration_ms': duration?.inMilliseconds,
    };
  }
}

/// 场景上下文信息
class ScenarioContext {
  final ScenarioType type;
  final DateTime startTime;
  final Map<String, dynamic> parameters;
  final String? previousScenario;

  const ScenarioContext({
    required this.type,
    required this.startTime,
    required this.parameters,
    this.previousScenario,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'start_time': startTime.toIso8601String(),
      'parameters': parameters,
      'previous_scenario': previousScenario,
    };
  }
}

/// 场景处理器服务接口
abstract class IScenarioHandlerService {
  /// 执行场景前置处理
  /// 
  /// [context] - 场景上下文信息
  /// 返回前置处理结果
  Future<ScenarioHandlerResult> executePreProcessing(ScenarioContext context);
  
  /// 执行场景后置处理
  /// 
  /// [context] - 场景上下文信息
  /// [flowResult] - 主流程执行结果
  /// 返回后置处理结果
  Future<ScenarioHandlerResult> executePostProcessing(
    ScenarioContext context,
    Map<String, dynamic> flowResult,
  );
  
  /// 获取场景特定的配置
  /// 
  /// [type] - 场景类型
  /// 返回场景配置信息
  Map<String, dynamic> getScenarioConfig(ScenarioType type);
  
  /// 验证场景执行结果
  /// 
  /// [context] - 场景上下文信息
  /// [result] - 执行结果
  /// 返回验证是否通过
  bool validateScenarioResult(
    ScenarioContext context,
    Map<String, dynamic> result,
  );
  
  /// 获取场景处理性能报告
  /// 
  /// 返回性能统计信息
  Map<String, dynamic> getPerformanceReport();
  
  /// 重置场景处理状态
  /// 
  /// 清理所有场景处理状态和缓存
  void resetScenarioState();
}
