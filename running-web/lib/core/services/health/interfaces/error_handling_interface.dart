/// 🔥 v14.1重构：错误处理服务接口
/// 
/// 定义统一的错误处理、分析、恢复和记录功能
/// 支持网络错误、权限错误、超时错误、认证错误等各种错误类型

/// 错误类型枚举
enum ErrorType {
  network,
  permission,
  timeout,
  authentication,
  validation,
  system,
  unknown,
}

/// 错误严重程度枚举
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// 错误分析结果
class ErrorAnalysisResult {
  final ErrorType type;
  final String category;
  final ErrorSeverity severity;
  final bool recoverable;
  final bool retryRecommended;
  final String? userAction;
  final String? message;
  final Map<String, dynamic>? metadata;

  const ErrorAnalysisResult({
    required this.type,
    required this.category,
    required this.severity,
    required this.recoverable,
    required this.retryRecommended,
    this.userAction,
    this.message,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'category': category,
      'severity': severity.name,
      'recoverable': recoverable,
      'retry_recommended': retryRecommended,
      'user_action': userAction,
      'message': message,
      'metadata': metadata,
    };
  }
}

/// 错误处理结果
class ErrorHandlingResult {
  final bool success;
  final String errorId;
  final ErrorAnalysisResult analysis;
  final String userMessage;
  final bool recoveryAttempted;
  final Map<String, dynamic>? recoveryResult;
  final DateTime timestamp;
  final String operation;
  final Map<String, dynamic>? context;

  const ErrorHandlingResult({
    required this.success,
    required this.errorId,
    required this.analysis,
    required this.userMessage,
    required this.recoveryAttempted,
    this.recoveryResult,
    required this.timestamp,
    required this.operation,
    this.context,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'error_id': errorId,
      'analysis': analysis.toJson(),
      'user_message': userMessage,
      'recovery_attempted': recoveryAttempted,
      'recovery_result': recoveryResult,
      'timestamp': timestamp.toIso8601String(),
      'operation': operation,
      'context': context,
    };
  }
}

/// 错误恢复策略
class ErrorRecoveryStrategy {
  final String name;
  final bool enabled;
  final int maxRetries;
  final Duration retryDelay;
  final Map<String, dynamic> parameters;

  const ErrorRecoveryStrategy({
    required this.name,
    required this.enabled,
    required this.maxRetries,
    required this.retryDelay,
    required this.parameters,
  });
}

/// 错误处理服务接口
abstract class IErrorHandlingService {
  /// 处理错误
  /// 
  /// [operation] - 操作名称
  /// [error] - 错误对象
  /// [stackTrace] - 堆栈跟踪
  /// [context] - 上下文信息
  /// [attemptRecovery] - 是否尝试恢复
  /// [userFriendlyMessage] - 用户友好消息
  /// 返回错误处理结果
  Future<ErrorHandlingResult> handleError({
    required String operation,
    required dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
    bool attemptRecovery = true,
    String? userFriendlyMessage,
  });
  
  /// 分析错误
  /// 
  /// [error] - 错误对象
  /// 返回错误分析结果
  ErrorAnalysisResult analyzeError(dynamic error);
  
  /// 尝试错误恢复
  /// 
  /// [analysis] - 错误分析结果
  /// [operation] - 操作名称
  /// [context] - 上下文信息
  /// 返回恢复结果
  Future<Map<String, dynamic>?> attemptErrorRecovery(
    ErrorAnalysisResult analysis,
    String operation,
    Map<String, dynamic>? context,
  );
  
  /// 生成用户友好的错误消息
  /// 
  /// [analysis] - 错误分析结果
  /// 返回用户友好消息
  String generateUserFriendlyMessage(ErrorAnalysisResult analysis);
  
  /// 记录错误统计
  /// 
  /// [category] - 错误类别
  void recordErrorStatistic(String category);
  
  /// 记录错误历史
  /// 
  /// [errorId] - 错误ID
  /// [operation] - 操作名称
  /// [error] - 错误对象
  /// [analysis] - 错误分析结果
  /// [context] - 上下文信息
  void recordErrorHistory(
    String errorId,
    String operation,
    dynamic error,
    ErrorAnalysisResult analysis,
    Map<String, dynamic>? context,
  );
  
  /// 生成错误ID
  /// 
  /// 返回唯一的错误ID
  String generateErrorId();
  
  /// 获取错误统计信息
  /// 
  /// 返回错误统计数据
  Map<String, int> getErrorStatistics();
  
  /// 获取错误历史记录
  /// 
  /// [limit] - 记录数量限制
  /// 返回错误历史列表
  List<Map<String, dynamic>> getErrorHistory({int limit = 100});
  
  /// 清理错误历史
  /// 
  /// 清理旧的错误历史记录
  void clearErrorHistory();
  
  /// 设置错误恢复策略
  /// 
  /// [type] - 错误类型
  /// [strategy] - 恢复策略
  void setRecoveryStrategy(ErrorType type, ErrorRecoveryStrategy strategy);
  
  /// 获取错误处理性能报告
  /// 
  /// 返回性能统计信息
  Map<String, dynamic> getPerformanceReport();
}
