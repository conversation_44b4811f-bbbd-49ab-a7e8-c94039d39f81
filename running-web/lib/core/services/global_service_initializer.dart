import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:logger/logger.dart';

import 'event_triggered_sync_service.dart';
import 'health_authorization_dialog_manager.dart';
import '../../../features/home/<USER>/providers/home_provider.dart';
import '../../../features/home/<USER>/providers/health_provider.dart';
import '../../../features/tasks/presentation/providers/task_provider.dart';

/// 🔥 v14.1架构合规：全局服务初始化器
/// 
/// 负责在应用启动时初始化所有全局服务，确保：
/// 1. EventTriggeredSyncService在全局范围内初始化一次
/// 2. MainLayoutScreen只负责执行步骤5，不再初始化全局服务
/// 3. 遵循v14.1架构约束，各组件职责清晰
/// 4. 提供服务初始化状态查询和错误处理
class GlobalServiceInitializer {
  static final Logger _logger = Logger();
  static bool _isInitialized = false;
  static bool _isInitializing = false;
  static final Map<String, bool> _serviceStatus = {};
  
  /// 检查是否已初始化
  static bool get isInitialized => _isInitialized;
  
  /// 检查是否正在初始化
  static bool get isInitializing => _isInitializing;
  
  /// 获取服务状态
  static Map<String, bool> get serviceStatus => Map.unmodifiable(_serviceStatus);
  
  /// 🔥 核心方法：初始化所有全局服务
  /// 
  /// 应该在应用启动后、用户界面显示前调用
  /// 通常在main.dart的runApp()之后，或在SplashScreen完成后调用
  static Future<void> initializeGlobalServices(BuildContext context) async {
    if (_isInitialized) {
      _logger.i('✅ GlobalServiceInitializer: 全局服务已初始化，跳过重复初始化');
      return;
    }
    
    if (_isInitializing) {
      _logger.w('⚠️ GlobalServiceInitializer: 正在初始化中，等待完成...');
      // 等待初始化完成
      while (_isInitializing) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      return;
    }
    
    _isInitializing = true;
    _logger.i('🚀 GlobalServiceInitializer: 开始初始化全局服务');
    
    try {
      // 1. 初始化EventTriggeredSyncService
      await _initializeEventTriggeredSyncService(context);
      
      // 2. 初始化智能授权弹窗管理器
      _initializeHealthAuthorizationDialogManager();

      // 3. 未来可以在这里添加其他全局服务的初始化
      // await _initializeOtherGlobalServices(context);
      
      _isInitialized = true;
      _logger.i('✅ GlobalServiceInitializer: 所有全局服务初始化完成');
      
    } catch (e) {
      _logger.e('❌ GlobalServiceInitializer: 全局服务初始化失败', error: e);
      rethrow;
    } finally {
      _isInitializing = false;
    }
  }
  
  /// 🔥 初始化EventTriggeredSyncService
  static Future<void> _initializeEventTriggeredSyncService(BuildContext context) async {
    try {
      _logger.i('🔄 开始初始化EventTriggeredSyncService...');
      _serviceStatus['EventTriggeredSyncService'] = false;
      
      // 获取EventTriggeredSyncService实例
      final eventTriggeredSyncService = Provider.of<EventTriggeredSyncService>(context, listen: false);
      
      // 🔥 关键步骤1：初始化生命周期监听器
      eventTriggeredSyncService.initializeLifecycleListener();
      _logger.i('✅ EventTriggeredSyncService生命周期监听已初始化');
      
      // 🔥 关键步骤2：获取必需的Provider依赖
      final homeProvider = Provider.of<HomeProvider>(context, listen: false);
      
      // TaskProvider和HealthProvider可能不存在，安全获取
      TaskProvider? taskProvider;
      try {
        taskProvider = Provider.of<TaskProvider>(context, listen: false);
        _logger.i('✅ TaskProvider已注入');
      } catch (e) {
        _logger.w('⚠️ TaskProvider未找到，将使用null值: $e');
      }
      
      HealthProvider? healthProvider;
      try {
        healthProvider = Provider.of<HealthProvider>(context, listen: false);
        _logger.i('✅ HealthProvider已注入');
      } catch (e) {
        _logger.w('⚠️ HealthProvider未找到，将使用null值: $e');
      }
      
      // 🔥 关键步骤3：调用initialize方法
      eventTriggeredSyncService.initialize(
        homeProvider: homeProvider,
        taskProvider: taskProvider,
        healthProvider: healthProvider,
      );
      
      _serviceStatus['EventTriggeredSyncService'] = true;
      _logger.i('✅ EventTriggeredSyncService初始化完成');
      _logger.i('🎯 定时同步器已启动，每2分钟执行健康数据同步');
      _logger.i('📱 App生命周期监听已启动，支持前后台切换处理');
      
    } catch (e) {
      _serviceStatus['EventTriggeredSyncService'] = false;
      _logger.e('❌ EventTriggeredSyncService初始化失败', error: e);
      rethrow;
    }
  }
  
  /// 🔥 重置初始化状态（用于测试或重新初始化）
  static void reset() {
    _isInitialized = false;
    _isInitializing = false;
    _serviceStatus.clear();
    _logger.i('🔄 GlobalServiceInitializer: 状态已重置');
  }
  
  /// 🔥 获取初始化状态报告
  static Map<String, dynamic> getStatusReport() {
    return {
      'isInitialized': _isInitialized,
      'isInitializing': _isInitializing,
      'serviceStatus': Map.from(_serviceStatus),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
  
  /// 🔥 检查特定服务是否已初始化
  static bool isServiceInitialized(String serviceName) {
    return _serviceStatus[serviceName] ?? false;
  }
  
  /// 🔥 初始化智能授权弹窗管理器
  static void _initializeHealthAuthorizationDialogManager() {
    try {
      _logger.i('🔄 开始初始化智能授权弹窗管理器...');

      // 获取单例实例，确保初始化
      HealthAuthorizationDialogManager.instance;

      _serviceStatus['HealthAuthorizationDialogManager'] = true;
      _logger.i('✅ 智能授权弹窗管理器初始化完成');

    } catch (e) {
      _logger.e('❌ 智能授权弹窗管理器初始化失败', error: e);
      _serviceStatus['HealthAuthorizationDialogManager'] = false;
      rethrow;
    }
  }

  /// 🔥 等待所有服务初始化完成
  static Future<void> waitForInitialization({Duration timeout = const Duration(seconds: 30)}) async {
    final startTime = DateTime.now();
    
    while (!_isInitialized && DateTime.now().difference(startTime) < timeout) {
      await Future.delayed(const Duration(milliseconds: 100));
    }
    
    if (!_isInitialized) {
      throw TimeoutException('全局服务初始化超时', timeout);
    }
  }
}

/// 🔥 超时异常类
class TimeoutException implements Exception {
  final String message;
  final Duration timeout;
  
  const TimeoutException(this.message, this.timeout);
  
  @override
  String toString() => 'TimeoutException: $message (timeout: ${timeout.inSeconds}s)';
}
