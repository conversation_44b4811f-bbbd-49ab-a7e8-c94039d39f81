import 'dart:async';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';

import '../../features/health/presentation/providers/health_permission_provider.dart';
import '../../features/health/presentation/widgets/health_authorization_dialog.dart';
import '../../config/app_routes.dart';
import 'health_data_flow_service.dart';
import '../controllers/phase_gate_controller.dart';

/// 🔥 SweatMint智能健康数据授权弹窗管理器
/// 
/// 核心功能：
/// 1. 全局单例弹窗管理，避免重复弹窗
/// 2. 应用生命周期感知，智能处理权限状态变化
/// 3. 权限状态缓存和实时检测机制
/// 4. 弹窗内容动态更新，无需重新弹出
/// 
/// 使用场景：
/// - UI加载完成后的延迟权限检查
/// - 应用从后台恢复时的权限状态更新
/// - 用户在系统设置中修改权限后的智能处理
class HealthAuthorizationDialogManager {
  static HealthAuthorizationDialogManager? _instance;
  static HealthAuthorizationDialogManager get instance => 
      _instance ??= HealthAuthorizationDialogManager._();
  
  HealthAuthorizationDialogManager._();
  
  final Logger _logger = Logger();
  
  // 🔥 全局弹窗状态管理
  bool _isDialogShowing = false;
  BuildContext? _currentDialogContext;
  
  // 🔥 权限状态缓存和变化检测
  Map<String, String>? _lastKnownPermissions;
  DateTime? _lastPermissionCheckTime;
  
  // 🔥 弹窗内容更新通知机制
  final StreamController<Map<String, String>> _permissionUpdateController = 
      StreamController<Map<String, String>>.broadcast();
  
  Stream<Map<String, String>> get permissionUpdateStream => 
      _permissionUpdateController.stream;
  
  // 🔥 用户体验优化配置
  static const Duration _delayBeforeShow = Duration(milliseconds: 1500);
  static const Duration _permissionCheckCooldown = Duration(seconds: 3);
  
  /// 🔥 核心方法：检查权限并智能显示授权弹窗
  ///
  /// 在UI加载完成后调用，会延迟一定时间以优化用户体验
  /// 确保全局只能存在一个弹窗实例
  ///
  /// [permissionResults] - 可选的权限状态，如果提供则直接使用，避免重复检查
  Future<void> checkAndShowAuthorizationDialog(
    BuildContext context, {
    Map<String, String>? permissionResults,
  }) async {
    try {
      _logger.i('🔍 HealthAuthorizationDialogManager: 开始检查权限并判断是否显示授权弹窗');
      
      // 1. 检查是否已有弹窗显示
      if (_isDialogShowing) {
        _logger.d('⚡ 授权弹窗已显示，跳过重复弹出');
        return;
      }
      
      // 2. 延迟执行，优化用户体验
      _logger.i('⏳ 延迟${_delayBeforeShow.inMilliseconds}ms后检查权限，优化用户体验');
      await Future.delayed(_delayBeforeShow);
      
      // 3. 检查context是否仍然有效
      if (!context.mounted) {
        _logger.w('⚠️ Context已失效，取消权限检查');
        return;
      }
      
      // 4. 获取当前权限状态
      Map<String, String> permissions;
      if (permissionResults != null) {
        // 🔥 v14.1修复：使用传入的权限结果，避免重复检查
        permissions = permissionResults;
        _logger.i('✅ 使用传入的权限结果: $permissions');
      } else {
        // 降级：重新检查权限状态
        _logger.w('⚠️ 未提供权限结果，降级重新检查');
        permissions = await _getLatestPermissions(context);
      }

      // 5. 判断是否需要显示弹窗
      if (_shouldShowDialog(permissions)) {
        await _showAuthorizationDialog(context, permissions);
      } else {
        _logger.i('✅ 权限状态正常，无需显示授权弹窗');
      }
      
    } catch (e) {
      _logger.e('❌ 检查权限并显示授权弹窗失败', error: e);
      await _handlePermissionCheckError(e);
    }
  }
  
  /// 🔥 应用生命周期变化处理
  /// 
  /// 当应用从后台恢复时，智能检测权限状态变化
  /// 如果弹窗正在显示且权限状态有变化，则更新弹窗内容
  void onAppLifecycleChanged(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _logger.i('📱 应用恢复到前台，检查权限状态变化');
      _handleAppResumed();
    }
  }
  
  /// 🔥 更新已显示的弹窗内容
  /// 
  /// 当权限状态发生变化时，动态更新弹窗内容而不是重新弹出
  Future<void> updateDialogIfShowing(Map<String, String> newPermissions) async {
    if (!_isDialogShowing) {
      _logger.d('🔍 没有弹窗显示，跳过更新');
      return;
    }
    
    _logger.i('🔄 权限状态发生变化，更新弹窗内容');
    _lastKnownPermissions = Map.from(newPermissions);
    
    // 检查是否所有权限都已授权
    final allAuthorized = _areAllPermissionsAuthorized(newPermissions);
    
    if (allAuthorized) {
      // 所有权限都已授权，自动关闭弹窗
      await dismissDialog();
      _logger.i('✅ 所有健康权限已授权，自动关闭授权弹窗');
    } else {
      // 仍有未授权权限，通知弹窗更新内容
      _notifyDialogUpdate(newPermissions);
      _logger.i('🔄 弹窗内容已更新，反映最新权限状态');
    }
  }
  
  /// 🔥 关闭授权弹窗
  Future<void> dismissDialog() async {
    if (!_isDialogShowing || _currentDialogContext == null) {
      return;
    }
    
    try {
      Navigator.of(_currentDialogContext!).pop();
      _resetDialogState();
      _logger.i('✅ 授权弹窗已关闭');
    } catch (e) {
      _logger.e('❌ 关闭授权弹窗失败', error: e);
      _resetDialogState();
    }
  }
  
  /// 🔥 严谨修复：获取权限状态，100%使用步骤2结果，绝不进行原生检查
  Future<Map<String, String>> _getLatestPermissions(BuildContext context) async {
    try {
      // 🔥 严谨修复：第一优先级 - PhaseGateController存储的步骤2权限结果
      try {
        final phaseGateController = GetIt.instance<PhaseGateController>();
        final step2Results = phaseGateController.getStep2PermissionResults();

        if (step2Results != null && step2Results.isNotEmpty) {
          _logger.i('✅ 严谨修复: 使用PhaseGateController步骤2权限结果: $step2Results');
          _lastKnownPermissions = Map.from(step2Results);
          _lastPermissionCheckTime = DateTime.now();
          return step2Results;
        }
      } catch (e) {
        _logger.w('⚠️ 严谨修复: PhaseGateController获取失败: $e');
      }

      // 🔥 严谨修复：第二优先级 - HealthDataFlowService的步骤2结果
      try {
        final healthDataFlowService = GetIt.instance<HealthDataFlowService>();
        final step2Result = healthDataFlowService.step2PermissionResult;

        if (step2Result != null && step2Result.isNotEmpty) {
          _logger.i('✅ 严谨修复: 使用HealthDataFlowService步骤2权限结果: $step2Result');
          _lastKnownPermissions = Map.from(step2Result);
          _lastPermissionCheckTime = DateTime.now();
          return step2Result;
        }
      } catch (e) {
        _logger.w('⚠️ 严谨修复: HealthDataFlowService获取失败: $e');
      }

      // 🔥 严谨修复：第三优先级 - 使用缓存状态，绝不进行原生检查
      if (_lastKnownPermissions != null && _lastKnownPermissions!.isNotEmpty) {
        _logger.i('✅ 严谨修复: 使用缓存权限状态，避免原生检查: $_lastKnownPermissions');
        return _lastKnownPermissions!;
      }

      // 🔥 严谨修复：最后降级 - 返回安全的默认状态，绝不触发原生权限检查
      _logger.w('⚠️ 严谨修复: 所有步骤2结果获取失败，使用安全默认状态');
      final defaultPermissions = _getDefaultPermissions();
      _lastKnownPermissions = defaultPermissions;

      // 🔥 严谨修复：记录权限获取失败，但不进行原生检查
      _logger.e('🚫 严谨修复: 权限获取失败，但严格遵循"一次检查，多处使用"原则，不进行原生检查');

      return defaultPermissions;

    } catch (e) {
      _logger.e('❌ 严谨修复: 权限获取异常: $e');

      // 🔥 严谨修复：即使异常也不进行原生权限检查
      final safeDefault = _getDefaultPermissions();
      _lastKnownPermissions = safeDefault;
      return safeDefault;
    }
  }
  
  /// 🔥 判断是否需要显示弹窗
  bool _shouldShowDialog(Map<String, String> permissions) {
    // 🔥 BOSS关键修复：检查是否有未授权的权限（包括notDetermined状态）
    final unauthorizedPermissions = <String>[];
    final authorizedPermissions = <String>[];

    for (final entry in permissions.entries) {
      if (entry.value == 'notDetermined') {
        unauthorizedPermissions.add(entry.key);
      } else if (entry.value == 'authorized') {
        authorizedPermissions.add(entry.key);
      }
      // 忽略'denied'状态，因为用户明确拒绝后不应该重复弹窗
    }

    final hasUnauthorized = unauthorizedPermissions.isNotEmpty;
    final shouldShow = hasUnauthorized;

    _logger.i('🔍 弹窗显示判断:');
    _logger.i('  ✅ 已授权: ${authorizedPermissions.join(", ")}');
    _logger.i('  ❌ 未授权: ${unauthorizedPermissions.join(", ")}');
    _logger.i('  🎯 需要显示弹窗: $shouldShow');

    return shouldShow;
  }
  
  /// 🔥 检查权限状态是否发生变化
  bool _hasPermissionChanged(Map<String, String> newPermissions) {
    if (_lastKnownPermissions == null) return true;
    
    for (final key in newPermissions.keys) {
      if (_lastKnownPermissions![key] != newPermissions[key]) {
        _logger.i('🔄 检测到权限状态变化: $key ${_lastKnownPermissions![key]} -> ${newPermissions[key]}');
        return true;
      }
    }
    return false;
  }
  
  /// 🔥 检查是否所有权限都已授权
  bool _areAllPermissionsAuthorized(Map<String, String> permissions) {
    return permissions.values.every((status) => status == 'authorized');
  }
  
  /// 🔥 严谨修复：获取安全的默认权限状态
  /// 当无法获取步骤2权限结果时，返回安全的默认状态
  Map<String, String> _getDefaultPermissions() {
    // 🔥 严谨修复：返回保守的默认状态，避免误导用户
    _logger.w('🚫 严谨修复: 使用默认权限状态，这表明步骤2权限检查可能未正确执行');

    return {
      'steps': 'notDetermined',
      'distance': 'notDetermined',
      'calories': 'notDetermined',
    };
  }
  
  /// 🔥 通知弹窗更新内容
  void _notifyDialogUpdate(Map<String, String> newPermissions) {
    _permissionUpdateController.add(newPermissions);
  }
  
  /// 🔥 重置弹窗状态
  void _resetDialogState() {
    _isDialogShowing = false;
    _currentDialogContext = null;
  }
  
  /// 🔥 处理应用恢复事件
  Future<void> _handleAppResumed() async {
    try {
      if (_isDialogShowing) {
        // 弹窗正在显示，检查权限是否有变化
        final context = _getGlobalContext();
        if (context != null) {
          final currentPermissions = await _getLatestPermissions(context);
          if (_hasPermissionChanged(currentPermissions)) {
            await updateDialogIfShowing(currentPermissions);
          }
        }
      } else {
        // 没有弹窗显示，检查是否需要弹出
        final context = _getGlobalContext();
        if (context != null) {
          // 延迟一小段时间，确保UI稳定
          await Future.delayed(const Duration(milliseconds: 500));
          await checkAndShowAuthorizationDialog(context);
        }
      }
    } catch (e) {
      _logger.e('❌ 处理应用恢复事件失败', error: e);
    }
  }
  
  /// 🔥 获取全局BuildContext
  BuildContext? _getGlobalContext() {
    return AppRoutes.navigatorKey.currentContext;
  }
  
  /// 🔥 处理权限检查错误
  Future<void> _handlePermissionCheckError(dynamic error) async {
    _logger.w('⚠️ 权限检查失败，使用安全策略: $error');
    
    if (_lastKnownPermissions != null && _isDialogShowing) {
      // 如果有缓存且弹窗正在显示，保持当前状态
      return;
    }
    
    // 如果没有缓存，使用安全的默认状态
    final defaultPermissions = _getDefaultPermissions();
    final context = _getGlobalContext();
    
    if (context != null && _shouldShowDialog(defaultPermissions)) {
      await _showAuthorizationDialog(context, defaultPermissions);
    }
  }
  
  /// 🔥 显示授权弹窗
  Future<void> _showAuthorizationDialog(BuildContext context, Map<String, String> permissions) async {
    try {
      _logger.i('🎯 显示健康授权弹窗，权限状态: $permissions');
      _isDialogShowing = true;
      _lastKnownPermissions = Map.from(permissions);

      // 使用导入的弹窗组件

      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (dialogContext) {
          _currentDialogContext = dialogContext;
          return HealthAuthorizationDialog(
            initialPermissions: permissions,
            onDismiss: () => dismissDialog(),
          );
        },
      );

      // 弹窗关闭后重置状态
      _resetDialogState();

    } catch (e) {
      _logger.e('❌ 显示授权弹窗失败', error: e);
      _resetDialogState();
    }
  }
  
  /// 🔥 清理资源
  void dispose() {
    _permissionUpdateController.close();
    _resetDialogState();
    _lastKnownPermissions = null;
    _lastPermissionCheckTime = null;
    _logger.i('🔄 HealthAuthorizationDialogManager已清理');
  }
}
