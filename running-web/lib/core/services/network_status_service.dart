import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// 🔥 BOSS关键修复：网络状态监听服务
/// 实现网络连接状态的实时监听和管理
/// 支持离线处理机制的基础服务
class NetworkStatusService extends ChangeNotifier {
  static final Logger _logger = Logger();
  
  // 单例模式
  static NetworkStatusService? _instance;
  static NetworkStatusService get instance {
    _instance ??= NetworkStatusService._internal();
    return _instance!;
  }
  
  NetworkStatusService._internal();
  
  // 网络连接状态
  bool _isConnected = true;
  ConnectivityResult _connectionType = ConnectivityResult.wifi;
  
  // 连接监听器
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  final Connectivity _connectivity = Connectivity();
  
  // 网络状态变化回调
  final List<Function(bool isConnected, ConnectivityResult connectionType)> _statusChangeCallbacks = [];
  
  // Getters
  bool get isConnected => _isConnected;
  ConnectivityResult get connectionType => _connectionType;
  bool get isWifi => _connectionType == ConnectivityResult.wifi;
  bool get isMobile => _connectionType == ConnectivityResult.mobile;
  bool get isOffline => !_isConnected;
  
  /// 🔥 BOSS核心：初始化网络状态监听
  Future<void> initialize() async {
    _logger.i('🌐 NetworkStatusService: 开始初始化网络状态监听');
    
    try {
      // 获取当前网络状态
      final initialResult = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(initialResult);
      
      // 开始监听网络状态变化
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        _updateConnectionStatus,
        onError: (error) {
          _logger.e('❌ NetworkStatusService: 网络状态监听异常: $error');
        },
      );
      
      _logger.i('✅ NetworkStatusService: 网络状态监听初始化完成');
      _logger.i('📊 当前网络状态: 连接=${_isConnected}, 类型=${_connectionType.name}');
      
    } catch (e) {
      _logger.e('❌ NetworkStatusService: 初始化失败: $e');
      // 默认假设有网络连接
      _isConnected = true;
      _connectionType = ConnectivityResult.wifi;
    }
  }
  
  /// 🔥 BOSS核心：更新网络连接状态
  Future<void> _updateConnectionStatus(ConnectivityResult result) async {
    _logger.d('🔄 NetworkStatusService: 网络状态变化检测: ${result.name}');
    
    final previouslyConnected = _isConnected;
    final previousConnectionType = _connectionType;
    
    // 更新连接状态
    _connectionType = result;
    _isConnected = result != ConnectivityResult.none;
    
    // 检查状态是否真正发生变化
    if (previouslyConnected != _isConnected || previousConnectionType != _connectionType) {
      _logger.i('📡 NetworkStatusService: 网络状态变化');
      _logger.i('  之前: 连接=${previouslyConnected}, 类型=${previousConnectionType.name}');
      _logger.i('  现在: 连接=${_isConnected}, 类型=${_connectionType.name}');
      
      // 通知监听器
      notifyListeners();
      
      // 调用状态变化回调
      for (final callback in _statusChangeCallbacks) {
        try {
          callback(_isConnected, _connectionType);
        } catch (e) {
          _logger.e('❌ NetworkStatusService: 状态变化回调异常: $e');
        }
      }
      
      // 记录重要的网络状态变化
      if (previouslyConnected && !_isConnected) {
        _logger.w('📴 NetworkStatusService: 网络连接丢失，进入离线模式');
      } else if (!previouslyConnected && _isConnected) {
        _logger.i('📶 NetworkStatusService: 网络连接恢复，退出离线模式');
      }
    }
  }
  
  /// 🔥 BOSS功能：添加网络状态变化监听器
  void addStatusChangeListener(Function(bool isConnected, ConnectivityResult connectionType) callback) {
    _statusChangeCallbacks.add(callback);
    _logger.d('📝 NetworkStatusService: 添加状态变化监听器，当前监听器数量: ${_statusChangeCallbacks.length}');
  }
  
  /// 🔥 BOSS功能：移除网络状态变化监听器
  void removeStatusChangeListener(Function(bool isConnected, ConnectivityResult connectionType) callback) {
    _statusChangeCallbacks.remove(callback);
    _logger.d('📝 NetworkStatusService: 移除状态变化监听器，当前监听器数量: ${_statusChangeCallbacks.length}');
  }
  
  /// 🔥 BOSS功能：手动检查网络状态
  Future<bool> checkNetworkStatus() async {
    try {
      _logger.d('🔍 NetworkStatusService: 手动检查网络状态');
      final result = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(result);
      return _isConnected;
    } catch (e) {
      _logger.e('❌ NetworkStatusService: 手动检查网络状态失败: $e');
      return _isConnected; // 返回当前状态
    }
  }
  
  /// 🔥 BOSS功能：等待网络连接恢复
  Future<bool> waitForConnection({Duration timeout = const Duration(seconds: 30)}) async {
    if (_isConnected) {
      _logger.d('✅ NetworkStatusService: 网络已连接，无需等待');
      return true;
    }
    
    _logger.i('⏳ NetworkStatusService: 等待网络连接恢复，超时时间: ${timeout.inSeconds}秒');
    
    final completer = Completer<bool>();
    Timer? timeoutTimer;
    StreamSubscription<ConnectivityResult>? subscription;
    
    // 设置超时定时器
    timeoutTimer = Timer(timeout, () {
      if (!completer.isCompleted) {
        _logger.w('⏰ NetworkStatusService: 等待网络连接超时');
        completer.complete(false);
      }
    });
    
    // 监听网络状态变化
    subscription = _connectivity.onConnectivityChanged.listen((result) {
      if (result != ConnectivityResult.none && !completer.isCompleted) {
        _logger.i('✅ NetworkStatusService: 网络连接已恢复');
        completer.complete(true);
      }
    });
    
    final result = await completer.future;
    
    // 清理资源
    timeoutTimer?.cancel();
    subscription?.cancel();
    
    return result;
  }
  
  /// 🔥 BOSS功能：获取网络状态信息
  Map<String, dynamic> getNetworkInfo() {
    return {
      'is_connected': _isConnected,
      'connection_type': _connectionType.name,
      'is_wifi': isWifi,
      'is_mobile': isMobile,
      'is_offline': isOffline,
      'listeners_count': _statusChangeCallbacks.length,
    };
  }
  
  /// 🔥 BOSS功能：释放资源
  @override
  void dispose() {
    _logger.i('🔄 NetworkStatusService: 释放网络状态监听资源');
    _connectivitySubscription?.cancel();
    _statusChangeCallbacks.clear();
    super.dispose();
  }
}
