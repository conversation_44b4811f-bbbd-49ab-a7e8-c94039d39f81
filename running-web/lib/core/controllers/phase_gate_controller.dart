import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:mutex/mutex.dart';

/// v14.1流程阶段枚举
/// 定义了完整的5步骤流程和细分阶段
enum V141Phase {
  /// 步骤1: 认证状态检查
  STEP1_AUTH_CHECK,
  
  /// 步骤2: 健康权限检查
  STEP2_PERMISSION_CHECK,
  
  /// 步骤3: 跨天检查和基线重置
  STEP3_CROSS_DAY_BASELINE,
  
  /// 步骤4: 健康数据同步
  STEP4_HEALTH_DATA_SYNC,
  
  /// 步骤5a: UI数据加载（必须在5b之前完成）
  STEP5A_UI_DATA_LOADING,
  
  /// 步骤5b: 权限引导（只能在5a完成后执行）
  STEP5B_PERMISSION_GUIDE,
  
  /// 流程完成
  COMPLETED
}

/// 阶段门状态枚举
enum PhaseGateStatus {
  /// 未开始
  NOT_STARTED,
  
  /// 进行中
  IN_PROGRESS,
  
  /// 已完成
  COMPLETED,
  
  /// 执行失败
  FAILED
}

/// 阶段执行结果
class PhaseExecutionResult {
  final V141Phase phase;
  final PhaseGateStatus status;
  final DateTime? startTime;
  final DateTime? endTime;
  final Duration? duration;
  final Map<String, dynamic>? result;
  final String? error;

  PhaseExecutionResult({
    required this.phase,
    required this.status,
    this.startTime,
    this.endTime,
    this.duration,
    this.result,
    this.error,
  });

  Map<String, dynamic> toJson() {
    return {
      'phase': phase.name,
      'status': status.name,
      'start_time': startTime?.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'duration_ms': duration?.inMilliseconds,
      'result': result,
      'error': error,
    };
  }
}

/// 🔥 BOSS核心组件：阶段门控制器
/// 
/// 实现v14.1流程的严格时序控制，确保：
/// 1. 每个阶段必须完全完成才能进入下一阶段
/// 2. 步骤5a（UI加载）必须在步骤5b（权限引导）之前完成
/// 3. 提供阶段状态查询和等待机制
/// 4. 支持阶段执行的监控和调试
class PhaseGateController extends ChangeNotifier {
  static final Logger _logger = Logger();

  /// 🔥 v14.1架构修复：状态更新互斥锁，防止竞态条件
  final Mutex _stateMutex = Mutex();

  /// 阶段状态映射
  final Map<V141Phase, PhaseGateStatus> _phaseStatus = {};

  /// 阶段执行结果
  final Map<V141Phase, PhaseExecutionResult> _phaseResults = {};

  /// 🔥 v14.1修复：步骤2权限检查结果存储
  /// 用于避免重复权限检查和状态不一致问题
  Map<String, String>? _step2PermissionResults;
  
  /// 阶段完成的Completer，用于等待机制
  final Map<V141Phase, Completer<bool>> _phaseCompleters = {};
  
  /// 当前执行的阶段
  V141Phase? _currentPhase;
  
  /// 流程开始时间
  DateTime? _flowStartTime;
  
  /// 是否启用严格模式（开发时可关闭用于调试）
  bool _strictMode = true;

  /// 上次通知时间，用于减少频繁通知
  DateTime? _lastNotifyTime;

  /// 通知间隔阈值（毫秒）
  static const int _notifyThresholdMs = 100;

  PhaseGateController() {
    _initializePhases();
  }

  /// 初始化所有阶段状态
  void _initializePhases() {
    for (final phase in V141Phase.values) {
      _phaseStatus[phase] = PhaseGateStatus.NOT_STARTED;
      _phaseCompleters[phase] = Completer<bool>();
    }
    _logger.i('🎯 PhaseGateController初始化完成，严格模式: $_strictMode');
  }

  /// 获取当前执行的阶段
  V141Phase? get currentPhase => _currentPhase;
  
  /// 获取流程开始时间
  DateTime? get flowStartTime => _flowStartTime;
  
  /// 获取阶段状态
  PhaseGateStatus getPhaseStatus(V141Phase phase) {
    return _phaseStatus[phase] ?? PhaseGateStatus.NOT_STARTED;
  }
  
  /// 获取阶段执行结果
  PhaseExecutionResult? getPhaseResult(V141Phase phase) {
    return _phaseResults[phase];
  }

  /// 获取所有阶段状态
  Map<V141Phase, PhaseGateStatus> getAllPhaseStatus() {
    return Map.unmodifiable(_phaseStatus);
  }

  /// 🔥 v14.1修复：存储步骤2权限检查结果
  /// 避免重复权限检查和状态不一致问题
  void storeStep2PermissionResults(Map<String, String> results) {
    _step2PermissionResults = Map.from(results);
    _logger.i('💾 步骤2权限结果已存储: $_step2PermissionResults');
  }

  /// 🔥 v14.1修复：获取步骤2权限检查结果
  /// 供后续组件使用，避免重复检查
  Map<String, String>? getStep2PermissionResults() {
    _logger.d('📖 读取步骤2权限结果: $_step2PermissionResults');
    return _step2PermissionResults != null ? Map.from(_step2PermissionResults!) : null;
  }

  /// 🔥 v14.1修复：清除步骤2权限结果
  /// 用于新会话开始时重置状态
  void clearStep2PermissionResults() {
    _step2PermissionResults = null;
    _logger.i('🗑️ 步骤2权限结果已清除');
  }

  /// 检查是否可以进入指定阶段
  bool canEnterPhase(V141Phase phase) {
    if (!_strictMode) return true;
    
    // 获取前置阶段
    final prerequisitePhases = _getPrerequisitePhases(phase);
    
    // 检查所有前置阶段是否已完成
    for (final prerequisite in prerequisitePhases) {
      if (_phaseStatus[prerequisite] != PhaseGateStatus.COMPLETED) {
        _logger.w('⚠️ 无法进入阶段${phase.name}，前置阶段${prerequisite.name}未完成');
        return false;
      }
    }
    
    return true;
  }

  /// 获取指定阶段的前置阶段
  List<V141Phase> _getPrerequisitePhases(V141Phase phase) {
    switch (phase) {
      case V141Phase.STEP1_AUTH_CHECK:
        return [];
      case V141Phase.STEP2_PERMISSION_CHECK:
        return [V141Phase.STEP1_AUTH_CHECK];
      case V141Phase.STEP3_CROSS_DAY_BASELINE:
        return [V141Phase.STEP1_AUTH_CHECK, V141Phase.STEP2_PERMISSION_CHECK];
      case V141Phase.STEP4_HEALTH_DATA_SYNC:
        return [V141Phase.STEP1_AUTH_CHECK, V141Phase.STEP2_PERMISSION_CHECK, V141Phase.STEP3_CROSS_DAY_BASELINE];
      case V141Phase.STEP5A_UI_DATA_LOADING:
        return [V141Phase.STEP1_AUTH_CHECK, V141Phase.STEP2_PERMISSION_CHECK, V141Phase.STEP3_CROSS_DAY_BASELINE, V141Phase.STEP4_HEALTH_DATA_SYNC];
      case V141Phase.STEP5B_PERMISSION_GUIDE:
        return [V141Phase.STEP1_AUTH_CHECK, V141Phase.STEP2_PERMISSION_CHECK, V141Phase.STEP3_CROSS_DAY_BASELINE, V141Phase.STEP4_HEALTH_DATA_SYNC, V141Phase.STEP5A_UI_DATA_LOADING];
      case V141Phase.COMPLETED:
        return V141Phase.values.where((p) => p != V141Phase.COMPLETED).toList();
    }
  }

  /// 标记阶段开始执行
  /// 🔥 v14.1架构修复：添加Mutex保护，确保状态更新原子性
  Future<void> markPhaseInProgress(V141Phase phase) async {
    await _stateMutex.acquire();
    try {
      if (!canEnterPhase(phase)) {
        throw StateError('无法进入阶段${phase.name}，前置条件未满足');
      }

      _currentPhase = phase;
      _phaseStatus[phase] = PhaseGateStatus.IN_PROGRESS;

      // 记录开始时间
      _flowStartTime ??= DateTime.now();

      final startTime = DateTime.now();
      _phaseResults[phase] = PhaseExecutionResult(
        phase: phase,
        status: PhaseGateStatus.IN_PROGRESS,
        startTime: startTime,
      );

      _logger.i('🚀 阶段${phase.name}开始执行');

      // 🔥 优化：只在关键阶段变化时通知监听器，减少Provider依赖更新频率
      _notifyListenersIfNeeded(phase, PhaseGateStatus.IN_PROGRESS);
    } finally {
      _stateMutex.release();
    }
  }

  /// 标记阶段执行完成
  /// 🔥 v14.1架构修复：添加Mutex保护，确保状态更新原子性
  Future<void> markPhaseCompleted(V141Phase phase, {Map<String, dynamic>? result}) async {
    await _stateMutex.acquire();
    try {
      if (_phaseStatus[phase] != PhaseGateStatus.IN_PROGRESS) {
        _logger.w('⚠️ 阶段${phase.name}未在执行中，无法标记完成');
        return;
      }

      final endTime = DateTime.now();
      final startTime = _phaseResults[phase]?.startTime ?? endTime;
      final duration = endTime.difference(startTime);

      _phaseStatus[phase] = PhaseGateStatus.COMPLETED;
      _phaseResults[phase] = PhaseExecutionResult(
        phase: phase,
        status: PhaseGateStatus.COMPLETED,
        startTime: startTime,
        endTime: endTime,
        duration: duration,
        result: result,
      );

      // 完成Completer
      if (!_phaseCompleters[phase]!.isCompleted) {
        _phaseCompleters[phase]!.complete(true);
      }

      _logger.i('✅ 阶段${phase.name}执行完成，耗时: ${duration.inMilliseconds}ms');

      // 🔥 BOSS关键修复：添加详细的状态更新日志
      _logger.i('📊 PhaseGateController状态更新: ${phase.name} -> ${PhaseGateStatus.COMPLETED.name}');
      _logger.i('🔍 当前所有阶段状态: ${_phaseStatus.map((k, v) => MapEntry(k.name, v.name))}');

      // 检查是否所有阶段都完成
      if (_isAllPhasesCompleted()) {
        _currentPhase = V141Phase.COMPLETED;
        _logger.i('🎉 v14.1流程全部完成');
      }

      // 🔥 v14.1架构修复：状态更新确认机制
      await _confirmStateUpdate(phase, PhaseGateStatus.COMPLETED);

      // 🔥 BOSS关键修复：强制状态通知，确保UI及时更新
      await _forceStateNotification(phase, PhaseGateStatus.COMPLETED);

      // 🔥 优化：使用智能通知机制
      _notifyListenersIfNeeded(phase, PhaseGateStatus.COMPLETED);
    } finally {
      _stateMutex.release();
    }
  }

  /// 标记阶段执行失败
  /// 🔥 v14.1架构修复：添加Mutex保护，确保状态更新原子性
  Future<void> markPhaseFailed(V141Phase phase, String error) async {
    await _stateMutex.acquire();
    try {
      final endTime = DateTime.now();
      final startTime = _phaseResults[phase]?.startTime ?? endTime;
      final duration = endTime.difference(startTime);

      _phaseStatus[phase] = PhaseGateStatus.FAILED;
      _phaseResults[phase] = PhaseExecutionResult(
        phase: phase,
        status: PhaseGateStatus.FAILED,
        startTime: startTime,
        endTime: endTime,
        duration: duration,
        error: error,
      );

      // 完成Completer（失败）
      if (!_phaseCompleters[phase]!.isCompleted) {
        _phaseCompleters[phase]!.complete(false);
      }

      _logger.e('❌ 阶段${phase.name}执行失败: $error');

      // 🔥 v14.1架构修复：状态更新确认机制
      await _confirmStateUpdate(phase, PhaseGateStatus.FAILED);

      // 🔥 优化：使用智能通知机制
      _notifyListenersIfNeeded(phase, PhaseGateStatus.FAILED);
    } finally {
      _stateMutex.release();
    }
  }

  /// 等待指定阶段完成
  /// 🔥 P2修复：优化状态检查机制，提高响应速度
  Future<bool> waitForPhaseCompletion(V141Phase phase, {Duration? timeout}) async {
    // 如果已经完成，直接返回
    if (_phaseStatus[phase] == PhaseGateStatus.COMPLETED) {
      return true;
    }

    // 如果已经失败，直接返回
    if (_phaseStatus[phase] == PhaseGateStatus.FAILED) {
      return false;
    }

    // 🔥 P2修复：使用轮询机制提高状态检查频率
    if (timeout != null) {
      final endTime = DateTime.now().add(timeout);
      const checkInterval = Duration(milliseconds: 100); // 100ms检查一次

      while (DateTime.now().isBefore(endTime)) {
        // 检查状态是否已更新
        if (_phaseStatus[phase] == PhaseGateStatus.COMPLETED) {
          _logger.i('✅ 轮询检测到阶段${phase.name}已完成');
          return true;
        }

        if (_phaseStatus[phase] == PhaseGateStatus.FAILED) {
          _logger.w('❌ 轮询检测到阶段${phase.name}已失败');
          return false;
        }

        // 等待一小段时间后再次检查
        await Future.delayed(checkInterval);
      }

      _logger.w('⚠️ 等待阶段${phase.name}完成超时（轮询模式）');
      return false;
    } else {
      // 无超时限制，使用原有机制
      try {
        return await _phaseCompleters[phase]!.future;
      } on TimeoutException {
        _logger.w('⚠️ 等待阶段${phase.name}完成超时');
        return false;
      }
    }
  }

  /// 🔥 新增：强制阶段完成（用于修复状态更新时序问题）
  /// 🔥 v14.1架构修复：添加Mutex保护，确保状态更新原子性
  Future<void> forcePhaseCompletion(V141Phase phase) async {
    await _stateMutex.acquire();
    try {
      _logger.w('🔧 强制标记阶段${phase.name}为完成状态');

      final now = DateTime.now();
      _phaseStatus[phase] = PhaseGateStatus.COMPLETED;
      _phaseResults[phase] = PhaseExecutionResult(
        phase: phase,
        status: PhaseGateStatus.COMPLETED,
        startTime: _phaseResults[phase]?.startTime ?? now,
        endTime: now,
        duration: Duration.zero,
        result: {'forced_completion': true},
      );

      // 完成Completer
      if (!_phaseCompleters[phase]!.isCompleted) {
        _phaseCompleters[phase]!.complete(true);
      }

      _logger.i('✅ 强制完成阶段${phase.name}');

      // 🔥 v14.1架构修复：状态更新确认机制
      await _confirmStateUpdate(phase, PhaseGateStatus.COMPLETED);

      // 🔥 BOSS关键修复：强制通知监听器，确保状态更新及时传播
      _lastNotifyTime = null; // 重置通知时间，强制通知
      notifyListeners();

      // 🔥 额外延迟确保监听器处理完成
      await Future.delayed(const Duration(milliseconds: 10));

      // 🔥 再次通知确保状态传播到所有监听器
      notifyListeners();
    } finally {
      _stateMutex.release();
    }
  }

  /// 🔥 P1.3修复：灵活的阶段完成检查，允许部分步骤失败
  ///
  /// 检查指定阶段是否可以继续执行，即使前置步骤失败
  /// [phase] 要检查的阶段
  /// [allowFailedDependencies] 是否允许依赖步骤失败
  /// [criticalPhases] 关键步骤列表，这些步骤失败时不能继续
  Future<bool> canProceedToPhase(
    V141Phase phase, {
    bool allowFailedDependencies = false,
    List<V141Phase> criticalPhases = const [V141Phase.STEP1_AUTH_CHECK],
  }) async {
    _logger.i('🔍 P1.3检查：阶段${phase.name}是否可以继续执行');

    // 检查关键步骤是否完成
    for (final criticalPhase in criticalPhases) {
      final status = _phaseStatus[criticalPhase];
      if (status == PhaseGateStatus.FAILED) {
        _logger.w('❌ P1.3检查：关键步骤${criticalPhase.name}失败，无法继续');
        return false;
      }
      if (status != PhaseGateStatus.COMPLETED) {
        _logger.w('⏳ P1.3检查：关键步骤${criticalPhase.name}未完成，等待中');
        return false;
      }
    }

    // 如果允许依赖失败，检查是否有足够的成功步骤
    if (allowFailedDependencies) {
      final precedingPhases = _getPrecedingPhases(phase);
      final completedCount = precedingPhases.where((p) =>
        _phaseStatus[p] == PhaseGateStatus.COMPLETED
      ).length;

      // 至少需要50%的前置步骤成功
      const requiredSuccessRate = 0.5;
      final actualSuccessRate = precedingPhases.isEmpty ? 1.0 : completedCount / precedingPhases.length;

      if (actualSuccessRate >= requiredSuccessRate) {
        _logger.i('✅ P1.3检查：成功率${(actualSuccessRate * 100).toInt()}%，允许继续执行${phase.name}');
        return true;
      } else {
        _logger.w('❌ P1.3检查：成功率${(actualSuccessRate * 100).toInt()}%低于要求，无法继续');
        return false;
      }
    }

    // 严格模式：所有前置步骤必须完成
    final precedingPhases = _getPrecedingPhases(phase);
    for (final precedingPhase in precedingPhases) {
      if (_phaseStatus[precedingPhase] != PhaseGateStatus.COMPLETED) {
        _logger.w('❌ P1.3检查：前置步骤${precedingPhase.name}未完成，无法继续');
        return false;
      }
    }

    _logger.i('✅ P1.3检查：所有前置步骤已完成，可以执行${phase.name}');
    return true;
  }

  /// 🔥 P1.3修复：获取指定阶段的前置步骤
  List<V141Phase> _getPrecedingPhases(V141Phase phase) {
    switch (phase) {
      case V141Phase.STEP1_AUTH_CHECK:
        return [];
      case V141Phase.STEP2_PERMISSION_CHECK:
        return [V141Phase.STEP1_AUTH_CHECK];
      case V141Phase.STEP3_CROSS_DAY_BASELINE:
        return [V141Phase.STEP1_AUTH_CHECK, V141Phase.STEP2_PERMISSION_CHECK];
      case V141Phase.STEP4_HEALTH_DATA_SYNC:
        return [V141Phase.STEP1_AUTH_CHECK, V141Phase.STEP2_PERMISSION_CHECK, V141Phase.STEP3_CROSS_DAY_BASELINE];
      case V141Phase.STEP5A_UI_DATA_LOADING:
        return [V141Phase.STEP1_AUTH_CHECK, V141Phase.STEP2_PERMISSION_CHECK, V141Phase.STEP3_CROSS_DAY_BASELINE, V141Phase.STEP4_HEALTH_DATA_SYNC];
      case V141Phase.STEP5B_PERMISSION_GUIDE:
        return [V141Phase.STEP5A_UI_DATA_LOADING];
      case V141Phase.COMPLETED:
        return V141Phase.values.where((p) => p != V141Phase.COMPLETED).toList();
    }
  }

  /// 检查是否所有阶段都完成
  bool _isAllPhasesCompleted() {
    final requiredPhases = V141Phase.values.where((p) => p != V141Phase.COMPLETED);
    return requiredPhases.every((phase) => _phaseStatus[phase] == PhaseGateStatus.COMPLETED);
  }

  /// 重置所有阶段
  void resetAllPhases() {
    _logger.i('🔄 重置所有阶段状态');
    
    for (final phase in V141Phase.values) {
      _phaseStatus[phase] = PhaseGateStatus.NOT_STARTED;
      if (!_phaseCompleters[phase]!.isCompleted) {
        _phaseCompleters[phase]!.complete(false);
      }
      _phaseCompleters[phase] = Completer<bool>();
    }
    
    _phaseResults.clear();
    _currentPhase = null;
    _flowStartTime = null;

    // 🔥 优化：重置时强制通知，因为这是重要的状态变化
    notifyListeners();
  }

  /// 从指定阶段重置
  void resetFromPhase(V141Phase fromPhase) {
    _logger.i('🔄 从阶段${fromPhase.name}开始重置');
    
    final phasesToReset = V141Phase.values.where((phase) => 
      phase.index >= fromPhase.index
    );
    
    for (final phase in phasesToReset) {
      _phaseStatus[phase] = PhaseGateStatus.NOT_STARTED;
      if (!_phaseCompleters[phase]!.isCompleted) {
        _phaseCompleters[phase]!.complete(false);
      }
      _phaseCompleters[phase] = Completer<bool>();
      _phaseResults.remove(phase);
    }

    // 🔥 优化：重置时强制通知，因为这是重要的状态变化
    notifyListeners();
  }

  /// 设置严格模式
  void setStrictMode(bool enabled) {
    _strictMode = enabled;
    _logger.i('🎯 PhaseGateController严格模式: $_strictMode');
  }

  /// 🔥 优化：智能通知机制，减少频繁的Provider依赖更新
  void _notifyListenersIfNeeded(V141Phase phase, PhaseGateStatus status) {
    final now = DateTime.now();

    // 只在关键状态变化时通知
    final isKeyStatusChange = status == PhaseGateStatus.COMPLETED ||
                             status == PhaseGateStatus.FAILED ||
                             (status == PhaseGateStatus.IN_PROGRESS &&
                              (phase == V141Phase.STEP1_AUTH_CHECK ||
                               phase == V141Phase.STEP5A_UI_DATA_LOADING));

    // 检查通知间隔
    final shouldNotify = isKeyStatusChange ||
                        _lastNotifyTime == null ||
                        now.difference(_lastNotifyTime!).inMilliseconds > _notifyThresholdMs;

    if (shouldNotify) {
      _lastNotifyTime = now;
      notifyListeners();
    }
  }

  /// 获取执行报告
  Map<String, dynamic> getExecutionReport() {
    final totalDuration = _flowStartTime != null 
        ? DateTime.now().difference(_flowStartTime!)
        : Duration.zero;
    
    return {
      'flow_start_time': _flowStartTime?.toIso8601String(),
      'current_phase': _currentPhase?.name,
      'total_duration_ms': totalDuration.inMilliseconds,
      'strict_mode': _strictMode,
      'phase_status': _phaseStatus.map((k, v) => MapEntry(k.name, v.name)),
      'phase_results': _phaseResults.map((k, v) => MapEntry(k.name, v.toJson())),
      'completed_phases': _phaseStatus.entries
          .where((entry) => entry.value == PhaseGateStatus.COMPLETED)
          .map((entry) => entry.key.name)
          .toList(),
      'failed_phases': _phaseStatus.entries
          .where((entry) => entry.value == PhaseGateStatus.FAILED)
          .map((entry) => entry.key.name)
          .toList(),
    };
  }

  /// 🔥 v14.1修复：优化状态更新确认机制，减少同步延迟
  /// 确保状态更新的原子性和即时性，消除63ms延迟
  Future<void> _confirmStateUpdate(V141Phase phase, PhaseGateStatus expectedStatus) async {
    _logger.d('🔍 v14.1修复: 开始状态更新确认 - ${phase.name} -> ${expectedStatus.name}');

    // 🔥 v14.1修复：减少重试次数和延迟，提高响应速度
    const maxRetries = 2;
    const retryDelay = Duration(milliseconds: 5); // 从10ms减少到5ms

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      // 🔥 v14.1修复：第一次尝试不延迟，立即检查状态
      if (attempt > 1) {
        await Future.delayed(retryDelay);
      }

      // 验证状态更新是否成功
      final actualStatus = _phaseStatus[phase];

      _logger.d('🔍 v14.1修复: 状态确认第${attempt}次: ${phase.name} 期望=${expectedStatus.name}, 实际=${actualStatus?.name ?? "NULL"}');

      if (actualStatus == expectedStatus) {
        _logger.d('✅ 状态更新确认成功（第${attempt}次尝试）: ${phase.name} = ${expectedStatus.name}');
        return;
      }

      if (attempt < maxRetries) {
        _logger.w('⚠️ 状态更新确认失败，准备第${attempt + 1}次重试');
        // 强制重新设置状态
        _phaseStatus[phase] = expectedStatus;
      } else {
        _logger.e('❌ 状态更新确认最终失败: ${phase.name} 期望=${expectedStatus.name}, 实际=${actualStatus?.name ?? "NULL"}');
        // 最后一次强制设置
        _phaseStatus[phase] = expectedStatus;
      }
    }
  }

  /// 🔥 v14.1修复：优化强制状态通知机制，提高即时性
  /// 确保状态变更通知立即传播，减少延迟
  Future<void> _forceStateNotification(V141Phase phase, PhaseGateStatus status) async {
    _logger.i('🔔 v14.1修复: 强制状态通知: ${phase.name} -> ${status.name}');

    // 重置通知时间，强制通知
    _lastNotifyTime = null;

    // 立即通知监听器
    notifyListeners();

    // 🔥 v14.1修复：减少延迟，从50ms减少到10ms，提高响应速度
    await Future.delayed(const Duration(milliseconds: 10));

    // 再次通知确保状态传播到所有监听器
    notifyListeners();

    _logger.i('✅ 状态通知完成: ${phase.name}');
  }

  /// 🔥 严谨修复：检查步骤1-4是否全部完成（增强版状态验证）
  bool get isSteps1to4Completed {
    final steps1to4 = [
      V141Phase.STEP1_AUTH_CHECK,
      V141Phase.STEP2_PERMISSION_CHECK,
      V141Phase.STEP3_CROSS_DAY_BASELINE,
      V141Phase.STEP4_HEALTH_DATA_SYNC,
    ];

    // 🔥 严谨修复：多层状态验证，确保准确性
    final statusMap = <String, String>{};
    bool allCompleted = true;
    final incompletePhases = <String>[];

    for (final phase in steps1to4) {
      final status = _phaseStatus[phase] ?? PhaseGateStatus.NOT_STARTED;
      statusMap[phase.name] = status.name;

      if (status != PhaseGateStatus.COMPLETED) {
        allCompleted = false;
        incompletePhases.add(phase.name);
      }
    }

    // 🔥 严谨修复：详细状态日志，便于问题排查
    if (!allCompleted) {
      _logger.w('⚠️ 严谨修复: 步骤1-4未完成，缺失阶段: ${incompletePhases.join(", ")}');
      _logger.d('📊 严谨修复: 详细状态 - $statusMap');
    } else {
      _logger.d('✅ 严谨修复: 步骤1-4全部完成');
    }

    return allCompleted;
  }

  /// 🔥 v14.1修复：统一状态检查入口，实现互斥锁机制
  /// 避免多个组件并发状态检查，减少不必要的重试
  static bool _isCheckingSteps1to4 = false;
  static Completer<bool>? _currentCheckCompleter;

  Future<bool> checkSteps1to4CompletedWithRetry({int maxRetries = 2}) async {
    // 🔥 v14.1修复：如果正在检查，等待当前检查结果
    if (_isCheckingSteps1to4 && _currentCheckCompleter != null) {
      _logger.i('🔄 v14.1修复: 状态检查正在进行中，等待结果');
      return await _currentCheckCompleter!.future;
    }

    // 🔥 v14.1修复：设置互斥锁，防止并发检查
    _isCheckingSteps1to4 = true;
    _currentCheckCompleter = Completer<bool>();

    try {
      _logger.i('🔍 v14.1修复: 开始步骤1-4状态检查（最多重试$maxRetries次）');

      // 🔥 v14.1修复：先直接检查一次，大多数情况下无需重试
      bool isCompleted = isSteps1to4Completed;
      if (isCompleted) {
        _logger.i('✅ v14.1修复: 步骤1-4状态检查成功（无需重试）');
        _currentCheckCompleter!.complete(true);
        return true;
      }

      // 🔥 v14.1修复：减少重试次数和延迟
      for (int i = 0; i < maxRetries; i++) {
        final delayMs = 50 * (i + 1); // 从100ms减少到50ms
        _logger.d('⏳ v14.1修复: 等待${delayMs}ms后重试状态检查');
        await Future.delayed(Duration(milliseconds: delayMs));

        isCompleted = isSteps1to4Completed;
        _logger.i('🔍 v14.1修复: 状态检查第${i+1}次: $isCompleted');

        if (isCompleted) {
          _logger.i('✅ v14.1修复: 步骤1-4状态检查成功（第${i+1}次重试）');
          _currentCheckCompleter!.complete(true);
          return true;
        }
      }

      // 最后一次检查
      final finalResult = isSteps1to4Completed;
      _logger.w('⚠️ v14.1修复: 步骤1-4状态检查重试完成，最终结果: $finalResult');
      _currentCheckCompleter!.complete(finalResult);
      return finalResult;
    } finally {
      // 🔥 v14.1修复：释放互斥锁
      _isCheckingSteps1to4 = false;
      _currentCheckCompleter = null;
    }
  }

  // ========== 状态验证和自动修复机制 ==========

  /// 🔥 BOSS关键修复：状态一致性自检机制
  /// 定期检查状态一致性，发现异常时自动修复
  Future<Map<String, dynamic>> performStateConsistencyCheck() async {
    _logger.i('🔍 PhaseGateController: 开始状态一致性自检');

    final checkResult = <String, dynamic>{
      'check_time': DateTime.now().toIso8601String(),
      'inconsistencies': <String>[],
      'auto_fixes_applied': <String>[],
      'overall_status': 'healthy',
    };

    try {
      // 检查1：验证阶段状态的逻辑一致性
      await _checkPhaseLogicalConsistency(checkResult);

      // 检查2：验证Completer状态一致性
      await _checkCompleterConsistency(checkResult);

      // 检查3：验证执行结果一致性
      await _checkExecutionResultConsistency(checkResult);

      // 检查4：验证步骤1-4完成状态
      await _checkSteps1to4CompletionConsistency(checkResult);

      // 总结检查结果
      final inconsistencyCount = (checkResult['inconsistencies'] as List).length;
      final autoFixCount = (checkResult['auto_fixes_applied'] as List).length;

      if (inconsistencyCount == 0) {
        checkResult['overall_status'] = 'healthy';
        _logger.i('✅ 状态一致性自检通过：无异常发现');
      } else if (autoFixCount == inconsistencyCount) {
        checkResult['overall_status'] = 'fixed';
        _logger.i('✅ 状态一致性自检完成：发现${inconsistencyCount}个异常，已全部自动修复');
      } else {
        checkResult['overall_status'] = 'warning';
        _logger.w('⚠️ 状态一致性自检完成：发现${inconsistencyCount}个异常，修复${autoFixCount}个');
      }

    } catch (e) {
      _logger.e('❌ 状态一致性自检异常: $e');
      checkResult['overall_status'] = 'error';
      checkResult['error'] = e.toString();
    }

    return checkResult;
  }

  /// 检查阶段状态的逻辑一致性
  Future<void> _checkPhaseLogicalConsistency(Map<String, dynamic> checkResult) async {
    final inconsistencies = checkResult['inconsistencies'] as List<String>;
    final autoFixes = checkResult['auto_fixes_applied'] as List<String>;

    // 检查前置阶段依赖关系
    for (final phase in V141Phase.values) {
      if (phase == V141Phase.COMPLETED) continue;

      final currentStatus = _phaseStatus[phase];
      final prerequisites = _getPrerequisitePhases(phase);

      // 如果当前阶段已完成，检查前置阶段是否都已完成
      if (currentStatus == PhaseGateStatus.COMPLETED) {
        for (final prerequisite in prerequisites) {
          final prerequisiteStatus = _phaseStatus[prerequisite];

          if (prerequisiteStatus != PhaseGateStatus.COMPLETED) {
            final issue = '阶段${phase.name}已完成，但前置阶段${prerequisite.name}状态为${prerequisiteStatus?.name ?? "NULL"}';
            inconsistencies.add(issue);

            // 自动修复：强制完成前置阶段
            _logger.w('🔧 自动修复: $issue');
            _phaseStatus[prerequisite] = PhaseGateStatus.COMPLETED;
            autoFixes.add('强制完成前置阶段${prerequisite.name}');
          }
        }
      }
    }
  }

  /// 检查Completer状态一致性
  Future<void> _checkCompleterConsistency(Map<String, dynamic> checkResult) async {
    final inconsistencies = checkResult['inconsistencies'] as List<String>;
    final autoFixes = checkResult['auto_fixes_applied'] as List<String>;

    for (final phase in V141Phase.values) {
      final status = _phaseStatus[phase];
      final completer = _phaseCompleters[phase];

      // 检查已完成阶段的Completer是否也已完成
      if (status == PhaseGateStatus.COMPLETED && completer != null && !completer.isCompleted) {
        final issue = '阶段${phase.name}状态为COMPLETED但Completer未完成';
        inconsistencies.add(issue);

        // 自动修复：完成Completer
        _logger.w('🔧 自动修复: $issue');
        completer.complete(true);
        autoFixes.add('完成阶段${phase.name}的Completer');
      }

      // 检查失败阶段的Completer是否正确处理
      if (status == PhaseGateStatus.FAILED && completer != null && !completer.isCompleted) {
        final issue = '阶段${phase.name}状态为FAILED但Completer未完成';
        inconsistencies.add(issue);

        // 自动修复：完成Completer（失败）
        _logger.w('🔧 自动修复: $issue');
        completer.complete(false);
        autoFixes.add('完成阶段${phase.name}的Completer（失败）');
      }
    }
  }

  /// 检查执行结果一致性
  Future<void> _checkExecutionResultConsistency(Map<String, dynamic> checkResult) async {
    final inconsistencies = checkResult['inconsistencies'] as List<String>;
    final autoFixes = checkResult['auto_fixes_applied'] as List<String>;

    for (final phase in V141Phase.values) {
      final status = _phaseStatus[phase];
      final result = _phaseResults[phase];

      // 检查已完成阶段是否有执行结果
      if (status == PhaseGateStatus.COMPLETED && result == null) {
        final issue = '阶段${phase.name}状态为COMPLETED但缺少执行结果';
        inconsistencies.add(issue);

        // 自动修复：创建默认执行结果
        _logger.w('🔧 自动修复: $issue');
        final now = DateTime.now();
        _phaseResults[phase] = PhaseExecutionResult(
          phase: phase,
          status: PhaseGateStatus.COMPLETED,
          startTime: now,
          endTime: now,
          duration: Duration.zero,
          result: {'auto_generated': true, 'reason': 'consistency_check_fix'},
        );
        autoFixes.add('为阶段${phase.name}创建默认执行结果');
      }

      // 检查执行结果状态与阶段状态是否一致
      if (result != null && result.status != status) {
        final issue = '阶段${phase.name}状态为${status?.name}但执行结果状态为${result.status.name}';
        inconsistencies.add(issue);

        // 自动修复：更新执行结果状态
        _logger.w('🔧 自动修复: $issue');
        _phaseResults[phase] = PhaseExecutionResult(
          phase: result.phase,
          status: status!,
          startTime: result.startTime,
          endTime: result.endTime,
          duration: result.duration,
          result: result.result,
          error: result.error,
        );
        autoFixes.add('同步阶段${phase.name}的执行结果状态');
      }
    }
  }

  /// 检查步骤1-4完成状态一致性
  Future<void> _checkSteps1to4CompletionConsistency(Map<String, dynamic> checkResult) async {
    final steps1to4 = [
      V141Phase.STEP1_AUTH_CHECK,
      V141Phase.STEP2_PERMISSION_CHECK,
      V141Phase.STEP3_CROSS_DAY_BASELINE,
      V141Phase.STEP4_HEALTH_DATA_SYNC,
    ];

    final completedSteps = <V141Phase>[];
    final incompleteSteps = <V141Phase>[];

    for (final step in steps1to4) {
      final status = _phaseStatus[step];
      if (status == PhaseGateStatus.COMPLETED) {
        completedSteps.add(step);
      } else {
        incompleteSteps.add(step);
      }
    }

    // 记录步骤1-4的完成状态
    checkResult['steps_1to4_status'] = {
      'completed_count': completedSteps.length,
      'total_count': steps1to4.length,
      'completed_steps': completedSteps.map((s) => s.name).toList(),
      'incomplete_steps': incompleteSteps.map((s) => s.name).toList(),
      'is_all_completed': incompleteSteps.isEmpty,
    };

    _logger.i('📊 步骤1-4状态统计: ${completedSteps.length}/${steps1to4.length}完成');
  }
}
