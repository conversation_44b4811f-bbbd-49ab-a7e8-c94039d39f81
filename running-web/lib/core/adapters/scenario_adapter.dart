import 'package:logger/logger.dart';

/// App状态枚举
enum AppState {
  /// 冷启动：App首次启动
  COLD_START,
  
  /// 热重启：App重启但进程未完全关闭
  HOT_RESTART,
  
  /// 后台恢复：从后台切换到前台
  RESUME_FROM_BACKGROUND,
  
  /// 定时同步：2分钟定时任务触发
  PERIODIC_SYNC,
}

/// v14.1执行策略枚举
enum V141ExecutionStrategy {
  /// 完整流程：执行所有5个步骤
  FULL_FLOW,
  
  /// 重置会话流程：重新验证会话状态
  RESET_SESSION_FLOW,
  
  /// 连续性检查流程：验证会话连续性
  CONTINUITY_CHECK_FLOW,
  
  /// 优化同步流程：轻量化的数据同步
  OPTIMIZED_SYNC_FLOW,
}

/// 执行上下文
class ExecutionContext {
  final AppState appState;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;
  final Duration? lastExecutionInterval;

  ExecutionContext({
    required this.appState,
    required this.timestamp,
    this.metadata = const {},
    this.lastExecutionInterval,
  });

  Map<String, dynamic> toJson() {
    return {
      'app_state': appState.name,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
      'last_execution_interval_ms': lastExecutionInterval?.inMilliseconds,
    };
  }
}

/// 🔥 BOSS核心组件：场景适配器
/// 
/// 根据不同的App状态选择最优的v14.1执行策略：
/// - 冷启动：完整流程，确保所有数据正确初始化
/// - 热重启：重置会话流程，验证会话状态
/// - 后台恢复：连续性检查，快速验证状态
/// - 定时同步：优化同步流程，最小化资源消耗
class ScenarioAdapter {
  static final Logger _logger = Logger();
  
  /// 根据App状态获取最优执行策略
  static V141ExecutionStrategy getStrategy(AppState state, {ExecutionContext? context}) {
    _logger.i('🎯 ScenarioAdapter: 分析App状态 ${state.name}');
    
    switch (state) {
      case AppState.COLD_START:
        _logger.i('❄️ 冷启动策略：执行完整v14.1流程');
        return V141ExecutionStrategy.FULL_FLOW;
        
      case AppState.HOT_RESTART:
        _logger.i('🔥 热重启策略：重置会话流程');
        return V141ExecutionStrategy.RESET_SESSION_FLOW;
        
      case AppState.RESUME_FROM_BACKGROUND:
        // 根据后台时间决定策略
        final strategy = _getResumeStrategy(context);
        _logger.i('📱 后台恢复策略：${strategy.name}');
        return strategy;
        
      case AppState.PERIODIC_SYNC:
        _logger.i('⏰ 定时同步策略：优化同步流程');
        return V141ExecutionStrategy.OPTIMIZED_SYNC_FLOW;
    }
  }
  
  /// 获取后台恢复的具体策略
  static V141ExecutionStrategy _getResumeStrategy(ExecutionContext? context) {
    if (context?.lastExecutionInterval == null) {
      return V141ExecutionStrategy.CONTINUITY_CHECK_FLOW;
    }
    
    final interval = context!.lastExecutionInterval!;
    
    // 超过30分钟，执行完整流程
    if (interval.inMinutes > 30) {
      _logger.i('⏱️ 后台时间超过30分钟，执行完整流程');
      return V141ExecutionStrategy.FULL_FLOW;
    }
    
    // 超过5分钟，重置会话
    if (interval.inMinutes > 5) {
      _logger.i('⏱️ 后台时间超过5分钟，重置会话流程');
      return V141ExecutionStrategy.RESET_SESSION_FLOW;
    }
    
    // 5分钟内，连续性检查
    _logger.i('⏱️ 后台时间较短，连续性检查流程');
    return V141ExecutionStrategy.CONTINUITY_CHECK_FLOW;
  }
  
  /// 获取策略的预期执行时间
  static Duration getExpectedDuration(V141ExecutionStrategy strategy) {
    switch (strategy) {
      case V141ExecutionStrategy.FULL_FLOW:
        return const Duration(milliseconds: 4600); // 完整流程：4.6秒
      case V141ExecutionStrategy.RESET_SESSION_FLOW:
        return const Duration(milliseconds: 2000); // 重置会话：2秒
      case V141ExecutionStrategy.CONTINUITY_CHECK_FLOW:
        return const Duration(milliseconds: 800);  // 连续性检查：0.8秒
      case V141ExecutionStrategy.OPTIMIZED_SYNC_FLOW:
        return const Duration(milliseconds: 1300); // 优化同步：1.3秒
    }
  }
  
  /// 获取策略的资源消耗级别
  static ResourceConsumptionLevel getResourceConsumption(V141ExecutionStrategy strategy) {
    switch (strategy) {
      case V141ExecutionStrategy.FULL_FLOW:
        return ResourceConsumptionLevel.HIGH;
      case V141ExecutionStrategy.RESET_SESSION_FLOW:
        return ResourceConsumptionLevel.MEDIUM;
      case V141ExecutionStrategy.CONTINUITY_CHECK_FLOW:
        return ResourceConsumptionLevel.LOW;
      case V141ExecutionStrategy.OPTIMIZED_SYNC_FLOW:
        return ResourceConsumptionLevel.LOW;
    }
  }
  
  /// 检查策略是否需要网络连接
  static bool requiresNetwork(V141ExecutionStrategy strategy) {
    switch (strategy) {
      case V141ExecutionStrategy.FULL_FLOW:
        return true;  // 需要完整的API调用
      case V141ExecutionStrategy.RESET_SESSION_FLOW:
        return true;  // 需要验证会话
      case V141ExecutionStrategy.CONTINUITY_CHECK_FLOW:
        return false; // 主要是本地检查
      case V141ExecutionStrategy.OPTIMIZED_SYNC_FLOW:
        return true;  // 需要同步数据
    }
  }
  
  /// 检查策略是否可以降级
  static bool canDegrade(V141ExecutionStrategy strategy) {
    switch (strategy) {
      case V141ExecutionStrategy.FULL_FLOW:
        return true;  // 可以降级到其他策略
      case V141ExecutionStrategy.RESET_SESSION_FLOW:
        return true;  // 可以降级到连续性检查
      case V141ExecutionStrategy.CONTINUITY_CHECK_FLOW:
        return false; // 已经是最轻量级
      case V141ExecutionStrategy.OPTIMIZED_SYNC_FLOW:
        return false; // 已经是优化版本
    }
  }
  
  /// 获取降级策略
  static V141ExecutionStrategy? getDegradedStrategy(V141ExecutionStrategy strategy) {
    if (!canDegrade(strategy)) return null;
    
    switch (strategy) {
      case V141ExecutionStrategy.FULL_FLOW:
        return V141ExecutionStrategy.RESET_SESSION_FLOW;
      case V141ExecutionStrategy.RESET_SESSION_FLOW:
        return V141ExecutionStrategy.CONTINUITY_CHECK_FLOW;
      default:
        return null;
    }
  }
  
  /// 创建执行上下文
  static ExecutionContext createContext(
    AppState appState, {
    Map<String, dynamic>? metadata,
    Duration? lastExecutionInterval,
  }) {
    return ExecutionContext(
      appState: appState,
      timestamp: DateTime.now(),
      metadata: metadata ?? {},
      lastExecutionInterval: lastExecutionInterval,
    );
  }
  
  /// 分析策略执行结果
  static StrategyAnalysis analyzeExecution(
    V141ExecutionStrategy strategy,
    Duration actualDuration,
    bool success,
  ) {
    final expectedDuration = getExpectedDuration(strategy);
    final performanceRatio = actualDuration.inMilliseconds / expectedDuration.inMilliseconds;
    
    return StrategyAnalysis(
      strategy: strategy,
      expectedDuration: expectedDuration,
      actualDuration: actualDuration,
      performanceRatio: performanceRatio,
      success: success,
      recommendation: _getRecommendation(strategy, performanceRatio, success),
    );
  }
  
  /// 获取性能建议
  static String _getRecommendation(
    V141ExecutionStrategy strategy,
    double performanceRatio,
    bool success,
  ) {
    if (!success) {
      return '执行失败，建议检查网络连接和权限状态';
    }
    
    if (performanceRatio > 2.0) {
      return '执行时间超标，建议考虑降级策略或优化网络环境';
    }
    
    if (performanceRatio > 1.5) {
      return '执行时间偏长，建议监控网络状况';
    }
    
    if (performanceRatio < 0.5) {
      return '执行效率很高，当前策略表现良好';
    }
    
    return '执行时间正常，策略选择合适';
  }
}

/// 资源消耗级别
enum ResourceConsumptionLevel {
  LOW,    // 低消耗
  MEDIUM, // 中等消耗
  HIGH,   // 高消耗
}

/// 策略执行分析结果
class StrategyAnalysis {
  final V141ExecutionStrategy strategy;
  final Duration expectedDuration;
  final Duration actualDuration;
  final double performanceRatio;
  final bool success;
  final String recommendation;

  StrategyAnalysis({
    required this.strategy,
    required this.expectedDuration,
    required this.actualDuration,
    required this.performanceRatio,
    required this.success,
    required this.recommendation,
  });

  Map<String, dynamic> toJson() {
    return {
      'strategy': strategy.name,
      'expected_duration_ms': expectedDuration.inMilliseconds,
      'actual_duration_ms': actualDuration.inMilliseconds,
      'performance_ratio': performanceRatio,
      'success': success,
      'recommendation': recommendation,
    };
  }
}
