#!/bin/bash

# SweatMint 启动流程修复验证脚本
# 作者: SweatMint技术团队
# 日期: 2025年1月22日

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_PATH="/Users/<USER>/Documents/worker/sweatmint/running-web"

echo -e "${BLUE}🚀 SweatMint 启动流程修复验证脚本${NC}"
echo -e "${BLUE}================================================${NC}"

# 检查项目路径
if [ ! -d "$PROJECT_PATH" ]; then
    echo -e "${RED}❌ 项目路径不存在: $PROJECT_PATH${NC}"
    exit 1
fi

cd "$PROJECT_PATH"

echo -e "\n${YELLOW}📋 开始验证修复效果...${NC}"

# 1. 代码质量检查
echo -e "\n${BLUE}1. 代码质量检查${NC}"
echo "   检查编译错误和代码质量..."

# 运行flutter analyze
echo "   运行 flutter analyze..."
flutter analyze --no-fatal-infos > analyze_result.txt 2>&1

# 检查是否有error级别的错误
ERROR_COUNT=$(grep -c "error •" analyze_result.txt || true)
WARNING_COUNT=$(grep -c "warning •" analyze_result.txt || true)
INFO_COUNT=$(grep -c "info •" analyze_result.txt || true)

echo -e "   📊 分析结果："
echo -e "      - Errors: ${ERROR_COUNT}"
echo -e "      - Warnings: ${WARNING_COUNT}"
echo -e "      - Info: ${INFO_COUNT}"

if [ "$ERROR_COUNT" -eq 0 ]; then
    echo -e "   ✅ 无编译错误，代码质量检查通过"
else
    echo -e "   ❌ 发现 $ERROR_COUNT 个编译错误"
    echo -e "   错误详情："
    grep "error •" analyze_result.txt || true
    exit 1
fi

# 2. 关键修复点验证
echo -e "\n${BLUE}2. 关键修复点验证${NC}"

# 2.1 检查PhaseGateController修复
echo "   2.1 检查PhaseGateController状态检查增强..."
if grep -q "checkSteps1to4CompletedWithRetry" lib/core/controllers/phase_gate_controller.dart; then
    echo -e "   ✅ PhaseGateController重试机制已实现"
else
    echo -e "   ❌ PhaseGateController重试机制未找到"
    exit 1
fi

if grep -q "📊 步骤1-4状态检查详情" lib/core/controllers/phase_gate_controller.dart; then
    echo -e "   ✅ 详细状态日志已添加"
else
    echo -e "   ❌ 详细状态日志未找到"
    exit 1
fi

# 2.2 检查V141FlowStateController修复
echo "   2.2 检查V141FlowStateController状态统一..."
if grep -q "优先从PhaseGateController读取" lib/core/controllers/v141_flow_state_controller.dart; then
    echo -e "   ✅ 状态统一机制已实现"
else
    echo -e "   ❌ 状态统一机制未找到"
    exit 1
fi

if grep -q "状态不一致检测" lib/core/controllers/v141_flow_state_controller.dart; then
    echo -e "   ✅ 状态不一致检测已添加"
else
    echo -e "   ❌ 状态不一致检测未找到"
    exit 1
fi

# 2.3 检查SplashScreen修复
echo "   2.3 检查SplashScreen异步状态检查..."
if grep -q "_checkSteps1to4StatusAsync" lib/features/splash/presentation/screens/splash_screen.dart; then
    echo -e "   ✅ 异步状态检查方法已实现"
else
    echo -e "   ❌ 异步状态检查方法未找到"
    exit 1
fi

if grep -q "_delayedStatusCheck" lib/features/splash/presentation/screens/splash_screen.dart; then
    echo -e "   ✅ 延迟状态检查已实现"
else
    echo -e "   ❌ 延迟状态检查未找到"
    exit 1
fi

# 3. 关键文件完整性检查
echo -e "\n${BLUE}3. 关键文件完整性检查${NC}"

CRITICAL_FILES=(
    "lib/core/controllers/phase_gate_controller.dart"
    "lib/core/controllers/v141_flow_state_controller.dart"
    "lib/features/splash/presentation/screens/splash_screen.dart"
    "lib/features/main_layout/presentation/screens/main_layout_screen.dart"
)

for file in "${CRITICAL_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "   ✅ $file 存在"
    else
        echo -e "   ❌ $file 缺失"
        exit 1
    fi
done

# 4. 生成验证报告
echo -e "\n${BLUE}4. 生成验证报告${NC}"

REPORT_FILE="startup_fix_verification_report.md"
cat > "$REPORT_FILE" << EOF
# SweatMint 启动流程修复验证报告

**验证时间**: $(date)
**验证状态**: ✅ 通过

## 验证结果摘要

### 代码质量检查
- ✅ 编译错误: $ERROR_COUNT 个
- ⚠️ 警告: $WARNING_COUNT 个  
- ℹ️ 信息: $INFO_COUNT 个

### 关键修复点验证
- ✅ PhaseGateController状态检查增强
- ✅ V141FlowStateController状态统一
- ✅ SplashScreen异步状态检查
- ✅ 关键文件完整性

## 下一步行动

1. **真机测试**: 在iPhone 15 Pro Max上测试启动流程
2. **性能监控**: 记录启动时间和状态检查耗时
3. **日志分析**: 验证状态同步日志是否正确输出
4. **用户验收**: 确认用户体验改善效果

## 关键监控指标

- 启动时间: 目标 < 30秒
- 状态检查成功率: 目标 > 95%
- 启动成功率: 目标 > 95%

---
**验证工具**: startup_fix_verification_script
**技术负责人**: SweatMint技术团队
EOF

echo -e "   📄 验证报告已生成: $REPORT_FILE"

# 5. 清理临时文件
rm -f analyze_result.txt build_result.txt

# 6. 总结
echo -e "\n${GREEN}🎉 验证完成！${NC}"
echo -e "${GREEN}================================================${NC}"
echo -e "${GREEN}✅ 所有关键修复点验证通过${NC}"
echo -e "${GREEN}✅ 代码质量检查通过${NC}"
echo -e "${GREEN}✅ 文件完整性检查通过${NC}"
echo -e "\n${YELLOW}📋 下一步建议：${NC}"
echo -e "   1. 在真机上测试启动流程"
echo -e "   2. 监控关键日志输出"
echo -e "   3. 验证状态同步效果"
echo -e "   4. 收集性能数据"
echo -e "\n${BLUE}📄 详细报告: $REPORT_FILE${NC}"

exit 0
