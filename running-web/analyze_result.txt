Analyzing running-web...                                        

   info • The constant name 'COLD_START' isn't a lowerCamelCase identifier • lib/core/adapters/scenario_adapter.dart:6:3 • constant_identifier_names
   info • The constant name 'HOT_RESTART' isn't a lowerCamelCase identifier • lib/core/adapters/scenario_adapter.dart:9:3 • constant_identifier_names
   info • The constant name 'RESUME_FROM_BACKGROUND' isn't a lowerCamelCase identifier • lib/core/adapters/scenario_adapter.dart:12:3 • constant_identifier_names
   info • The constant name 'PERIODIC_SYNC' isn't a lowerCamelCase identifier • lib/core/adapters/scenario_adapter.dart:15:3 • constant_identifier_names
   info • The constant name 'FULL_FLOW' isn't a lowerCamelCase identifier • lib/core/adapters/scenario_adapter.dart:21:3 • constant_identifier_names
   info • The constant name 'RESET_SESSION_FLOW' isn't a lowerCamelCase identifier • lib/core/adapters/scenario_adapter.dart:24:3 • constant_identifier_names
   info • The constant name 'CONTINUITY_CHECK_FLOW' isn't a lowerCamelCase identifier • lib/core/adapters/scenario_adapter.dart:27:3 • constant_identifier_names
   info • The constant name 'OPTIMIZED_SYNC_FLOW' isn't a lowerCamelCase identifier • lib/core/adapters/scenario_adapter.dart:30:3 • constant_identifier_names
   info • The constant name 'LOW' isn't a lowerCamelCase identifier • lib/core/adapters/scenario_adapter.dart:248:3 • constant_identifier_names
   info • The constant name 'MEDIUM' isn't a lowerCamelCase identifier • lib/core/adapters/scenario_adapter.dart:249:3 • constant_identifier_names
   info • The constant name 'HIGH' isn't a lowerCamelCase identifier • lib/core/adapters/scenario_adapter.dart:250:3 • constant_identifier_names
   info • The constant name 'LOGIN' isn't a lowerCamelCase identifier • lib/core/controllers/health_data_flow_coordinator.dart:13:3 • constant_identifier_names
   info • The constant name 'APP_RESTART' isn't a lowerCamelCase identifier • lib/core/controllers/health_data_flow_coordinator.dart:14:3 • constant_identifier_names
   info • The constant name 'APP_RESUME' isn't a lowerCamelCase identifier • lib/core/controllers/health_data_flow_coordinator.dart:15:3 • constant_identifier_names
   info • The constant name 'TIMER_SYNC' isn't a lowerCamelCase identifier • lib/core/controllers/health_data_flow_coordinator.dart:16:3 • constant_identifier_names
   info • The constant name 'STEP1_AUTH_CHECK' isn't a lowerCamelCase identifier • lib/core/controllers/phase_gate_controller.dart:10:3 • constant_identifier_names
   info • The constant name 'STEP2_PERMISSION_CHECK' isn't a lowerCamelCase identifier • lib/core/controllers/phase_gate_controller.dart:13:3 • constant_identifier_names
   info • The constant name 'STEP3_CROSS_DAY_BASELINE' isn't a lowerCamelCase identifier • lib/core/controllers/phase_gate_controller.dart:16:3 • constant_identifier_names
   info • The constant name 'STEP4_HEALTH_DATA_SYNC' isn't a lowerCamelCase identifier • lib/core/controllers/phase_gate_controller.dart:19:3 • constant_identifier_names
   info • The constant name 'STEP5A_UI_DATA_LOADING' isn't a lowerCamelCase identifier • lib/core/controllers/phase_gate_controller.dart:22:3 • constant_identifier_names
   info • The constant name 'STEP5B_PERMISSION_GUIDE' isn't a lowerCamelCase identifier • lib/core/controllers/phase_gate_controller.dart:25:3 • constant_identifier_names
   info • The constant name 'COMPLETED' isn't a lowerCamelCase identifier • lib/core/controllers/phase_gate_controller.dart:28:3 • constant_identifier_names
   info • The constant name 'NOT_STARTED' isn't a lowerCamelCase identifier • lib/core/controllers/phase_gate_controller.dart:34:3 • constant_identifier_names
   info • The constant name 'IN_PROGRESS' isn't a lowerCamelCase identifier • lib/core/controllers/phase_gate_controller.dart:37:3 • constant_identifier_names
   info • The constant name 'COMPLETED' isn't a lowerCamelCase identifier • lib/core/controllers/phase_gate_controller.dart:40:3 • constant_identifier_names
   info • The constant name 'FAILED' isn't a lowerCamelCase identifier • lib/core/controllers/phase_gate_controller.dart:43:3 • constant_identifier_names
   info • Unnecessary braces in a string interpolation • lib/core/controllers/phase_gate_controller.dart:592:26 • unnecessary_brace_in_string_interps
   info • Unnecessary braces in a string interpolation • lib/core/controllers/phase_gate_controller.dart:595:32 • unnecessary_brace_in_string_interps
   info • Unnecessary braces in a string interpolation • lib/core/controllers/phase_gate_controller.dart:721:34 • unnecessary_brace_in_string_interps
   info • Unnecessary braces in a string interpolation • lib/core/controllers/phase_gate_controller.dart:724:35 • unnecessary_brace_in_string_interps
   info • Unnecessary braces in a string interpolation • lib/core/controllers/phase_gate_controller.dart:724:62 • unnecessary_brace_in_string_interps
   info • The constant name 'SERVER_PRIORITY' isn't a lowerCamelCase identifier • lib/core/services/conflict_resolution_service.dart:379:3 • constant_identifier_names
   info • The constant name 'LOCAL_PRIORITY' isn't a lowerCamelCase identifier • lib/core/services/conflict_resolution_service.dart:380:3 • constant_identifier_names
   info • The constant name 'TIMESTAMP_PRIORITY' isn't a lowerCamelCase identifier • lib/core/services/conflict_resolution_service.dart:381:3 • constant_identifier_names
   info • The constant name 'VERSION_PRIORITY' isn't a lowerCamelCase identifier • lib/core/services/conflict_resolution_service.dart:382:3 • constant_identifier_names
   info • The constant name 'MERGE_DATA' isn't a lowerCamelCase identifier • lib/core/services/conflict_resolution_service.dart:383:3 • constant_identifier_names
   info • The constant name 'MANUAL_REVIEW' isn't a lowerCamelCase identifier • lib/core/services/conflict_resolution_service.dart:384:3 • constant_identifier_names
   info • The constant name 'NO_CONFLICT' isn't a lowerCamelCase identifier • lib/core/services/conflict_resolution_service.dart:385:3 • constant_identifier_names
   info • The constant name 'NO_CONFLICT' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:387:3 • constant_identifier_names
   info • The constant name 'TIMESTAMP_CONFLICT' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:388:3 • constant_identifier_names
   info • The constant name 'VALUE_CONFLICT' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:389:3 • constant_identifier_names
   info • The constant name 'VERSION_CONFLICT' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:390:3 • constant_identifier_names
   info • The constant name 'DATA_INTEGRITY_ERROR' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:391:3 • constant_identifier_names
   info • The constant name 'DETECTION_ERROR' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:392:3 • constant_identifier_names
   info • The constant name 'NONE' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:397:3 • constant_identifier_names
   info • The constant name 'LOW' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:398:3 • constant_identifier_names
   info • The constant name 'MEDIUM' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:399:3 • constant_identifier_names
   info • The constant name 'HIGH' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:400:3 • constant_identifier_names
   info • The constant name 'USE_SERVER_DATA' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:405:3 • constant_identifier_names
   info • The constant name 'USE_LOCAL_DATA' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:406:3 • constant_identifier_names
   info • The constant name 'USE_LATEST_TIMESTAMP' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:407:3 • constant_identifier_names
   info • The constant name 'USE_HIGHER_VERSION' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:408:3 • constant_identifier_names
   info • The constant name 'MERGE_DATA' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:409:3 • constant_identifier_names
   info • The constant name 'MANUAL_REVIEW' isn't a lowerCamelCase identifier • lib/core/services/data_conflict_detector.dart:410:3 • constant_identifier_names
   info • Don't use 'BuildContext's across async gaps • lib/core/services/health_authorization_dialog_manager.dart:81:40 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/core/services/health_authorization_dialog_manager.dart:264:49 • use_build_context_synchronously
   info • Use 'const' with the constructor to improve performance • lib/core/services/health_data_flow_service.dart:849:32 • prefer_const_constructors
   info • Unnecessary braces in a string interpolation • lib/core/services/health_data_flow_service.dart:974:37 • unnecessary_brace_in_string_interps
   info • Use 'const' with the constructor to improve performance • lib/core/services/health_data_flow_service.dart:2125:18 • prefer_const_constructors
warning • The declaration '_performLocalSessionContinuityCheck' isn't referenced • lib/core/services/health_data_flow_service.dart:2490:32 • unused_element
warning • The declaration '_handleCrossDaySettlement' isn't referenced • lib/core/services/health_data_flow_service.dart:2538:16 • unused_element
warning • The declaration '_initializeBaselineForLogin' isn't referenced • lib/core/services/health_data_flow_service.dart:2768:16 • unused_element
warning • The declaration '_checkPermissionChanges' isn't referenced • lib/core/services/health_data_flow_service.dart:2815:32 • unused_element
warning • The declaration '_handlePermissionChanges' isn't referenced • lib/core/services/health_data_flow_service.dart:2849:16 • unused_element
warning • The declaration '_validateHealthData' isn't referenced • lib/core/services/health_data_flow_service.dart:2873:8 • unused_element
   info • Use 'const' with the constructor to improve performance • lib/core/services/health_data_flow_service.dart:2932:20 • prefer_const_constructors
   info • Don't use 'BuildContext's across async gaps • lib/core/services/health_data_flow_service.dart:3159:67 • use_build_context_synchronously
warning • The declaration '_preloadHomePageData' isn't referenced • lib/core/services/health_data_flow_service.dart:3644:16 • unused_element
warning • The declaration '_initializeVipService' isn't referenced • lib/core/services/health_data_flow_service.dart:3672:16 • unused_element
warning • The declaration '_initializeEventSyncService' isn't referenced • lib/core/services/health_data_flow_service.dart:3683:16 • unused_element
   info • Use 'const' with the constructor to improve performance • lib/core/services/health_data_flow_service.dart:3836:18 • prefer_const_constructors
   info • Unnecessary braces in a string interpolation • lib/core/services/health_data_flow_service.dart:5181:33 • unnecessary_brace_in_string_interps
warning • The declaration '_showHealthPermissionGuide' isn't referenced • lib/core/services/health_data_flow_service.dart:5628:16 • unused_element
   info • Unnecessary braces in a string interpolation • lib/core/services/health_data_flow_service.dart:6210:25 • unnecessary_brace_in_string_interps
   info • Unnecessary braces in a string interpolation • lib/core/services/network_status_service.dart:57:32 • unnecessary_brace_in_string_interps
   info • Unnecessary braces in a string interpolation • lib/core/services/network_status_service.dart:81:27 • unnecessary_brace_in_string_interps
   info • Unnecessary braces in a string interpolation • lib/core/services/network_status_service.dart:82:27 • unnecessary_brace_in_string_interps
warning • The receiver can't be null, so the null-aware operator '?.' is unnecessary • lib/core/services/network_status_service.dart:162:17 • invalid_null_aware_operator
warning • The receiver can't be null, so the null-aware operator '?.' is unnecessary • lib/core/services/network_status_service.dart:163:17 • invalid_null_aware_operator
   info • Unnecessary braces in a string interpolation • lib/core/services/offline_data_queue_service.dart:176:30 • unnecessary_brace_in_string_interps
   info • Unnecessary braces in a string interpolation • lib/core/services/offline_data_queue_service.dart:176:52 • unnecessary_brace_in_string_interps
   info • Unnecessary braces in a string interpolation • lib/core/services/offline_data_queue_service.dart:176:72 • unnecessary_brace_in_string_interps
   info • Use 'const' with the constructor to improve performance • lib/core/utils/error_message_manager.dart:79:23 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/core/utils/error_message_manager.dart:90:23 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/core/utils/error_message_manager.dart:102:23 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/core/utils/error_message_manager.dart:169:23 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/core/utils/error_message_manager.dart:191:23 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/core/utils/error_message_manager.dart:224:23 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/core/utils/error_message_manager.dart:235:23 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/core/utils/error_message_manager.dart:247:23 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/core/utils/error_message_manager.dart:294:19 • prefer_const_constructors
   info • The constant name 'INFO' isn't a lowerCamelCase identifier • lib/core/utils/error_message_manager.dart:325:3 • constant_identifier_names
   info • The constant name 'WARNING' isn't a lowerCamelCase identifier • lib/core/utils/error_message_manager.dart:326:3 • constant_identifier_names
   info • The constant name 'ERROR' isn't a lowerCamelCase identifier • lib/core/utils/error_message_manager.dart:327:3 • constant_identifier_names
   info • The constant name 'CRITICAL' isn't a lowerCamelCase identifier • lib/core/utils/error_message_manager.dart:328:3 • constant_identifier_names
warning • The declaration '_getLastKnownPermissions' isn't referenced • lib/features/auth/domain/services/auth_service_impl.dart:155:29 • unused_element
warning • The declaration '_getDefaultHealthData' isn't referenced • lib/features/auth/domain/services/auth_service_impl.dart:175:24 • unused_element
   info • Don't use 'BuildContext's across async gaps • lib/features/debug/presentation/widgets/debug_menu.dart:139:41 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/debug/presentation/widgets/debug_menu.dart:141:40 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/debug/presentation/widgets/debug_menu.dart:218:41 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/debug/presentation/widgets/debug_menu.dart:220:40 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/debug/presentation/widgets/debug_menu.dart:239:21 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/debug/presentation/widgets/debug_menu.dart:270:40 • use_build_context_synchronously
warning • The value of the local variable 'theme' isn't used • lib/features/health/presentation/widgets/health_authorization_dialog.dart:69:11 • unused_local_variable
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/health/presentation/widgets/health_authorization_dialog.dart:79:35 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/health/presentation/widgets/health_authorization_dialog.dart:113:34 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/health/presentation/widgets/health_authorization_dialog.dart:129:39 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/health/presentation/widgets/health_authorization_dialog.dart:156:35 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/health/presentation/widgets/health_authorization_dialog.dart:312:22 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/health/presentation/widgets/health_authorization_dialog.dart:315:24 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/health/presentation/widgets/health_authorization_dialog.dart:352:34 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/health/presentation/widgets/health_authorization_dialog.dart:353:34 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/health/presentation/widgets/health_authorization_dialog.dart:358:39 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/health/presentation/widgets/health_authorization_dialog.dart:370:47 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/health/presentation/widgets/health_authorization_dialog.dart:395:45 • deprecated_member_use
   info • Use 'const' with the constructor to improve performance • lib/features/health/presentation/widgets/health_authorization_dialog.dart:455:24 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/health/presentation/widgets/health_authorization_dialog.dart:457:31 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/home/<USER>/widgets/overview_card.dart:590:28 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/home/<USER>/widgets/overview_card.dart:591:29 • prefer_const_constructors
   info • Don't use 'BuildContext's across async gaps • lib/features/home/<USER>/widgets/overview_card.dart:603:36 • use_build_context_synchronously
   info • Use 'const' with the constructor to improve performance • lib/features/home/<USER>/widgets/overview_card.dart:605:28 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/home/<USER>/widgets/overview_card.dart:606:29 • prefer_const_constructors
   info • Don't use 'BuildContext's across async gaps • lib/features/home/<USER>/widgets/overview_card.dart:612:36 • use_build_context_synchronously
   info • Use 'const' with the constructor to improve performance • lib/features/home/<USER>/widgets/overview_card.dart:614:28 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/home/<USER>/widgets/overview_card.dart:615:29 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/home/<USER>/widgets/overview_card.dart:686:34 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/home/<USER>/widgets/overview_card.dart:687:35 • prefer_const_constructors
   info • Don't use 'BuildContext's across async gaps • lib/features/home/<USER>/widgets/overview_card.dart:695:42 • use_build_context_synchronously
   info • Use 'const' with the constructor to improve performance • lib/features/home/<USER>/widgets/overview_card.dart:697:34 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/home/<USER>/widgets/overview_card.dart:698:35 • prefer_const_constructors
   info • Don't use 'BuildContext's across async gaps • lib/features/home/<USER>/widgets/overview_card.dart:703:42 • use_build_context_synchronously
   info • Use 'const' with the constructor to improve performance • lib/features/home/<USER>/widgets/overview_card.dart:705:34 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/home/<USER>/widgets/overview_card.dart:706:35 • prefer_const_constructors
   info • Don't use 'BuildContext's across async gaps • lib/features/main_layout/presentation/screens/main_layout_screen.dart:208:35 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/features/main_layout/presentation/screens/main_layout_screen.dart:253:41 • use_build_context_synchronously
   info • Use 'const' with the constructor to improve performance • lib/features/main_layout/presentation/screens/main_layout_screen.dart:502:28 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/main_layout/presentation/screens/main_layout_screen.dart:511:28 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/settings/presentation/screens/health_settings_screen.dart:438:38 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/settings/presentation/screens/health_settings_screen.dart:439:39 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/settings/presentation/screens/health_settings_screen.dart:453:40 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/settings/presentation/screens/health_settings_screen.dart:454:41 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/settings/presentation/screens/health_settings_screen.dart:463:40 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/settings/presentation/screens/health_settings_screen.dart:464:41 • prefer_const_constructors
   info • Use 'const' with the constructor to improve performance • lib/features/splash/presentation/screens/splash_screen.dart:620:26 • prefer_const_constructors
warning • The declaration '_$AddonTaskItemDtoFromJson' isn't referenced • lib/features/tasks/data/dto/addon_task_list_dto.g.dart:9:18 • unused_element
warning • The declaration '_$AddonTaskListDataDtoFromJson' isn't referenced • lib/features/tasks/data/dto/addon_task_list_dto.g.dart:58:22 • unused_element
warning • The declaration '_$AddonTaskListDtoFromJson' isn't referenced • lib/features/tasks/data/dto/addon_task_list_dto.g.dart:85:18 • unused_element
warning • The declaration '_$UserLevelInfoDtoFromJson' isn't referenced • lib/features/tasks/data/dto/daily_task_list_dto.g.dart:9:18 • unused_element
warning • The declaration '_$UserVipInfoDtoFromJson' isn't referenced • lib/features/tasks/data/dto/daily_task_list_dto.g.dart:21:16 • unused_element
warning • The declaration '_$VipRefundInfoDtoFromJson' isn't referenced • lib/features/tasks/data/dto/daily_task_list_dto.g.dart:35:18 • unused_element
warning • The declaration '_$DailyTaskItemDtoFromJson' isn't referenced • lib/features/tasks/data/dto/daily_task_list_dto.g.dart:65:18 • unused_element
warning • The declaration '_$DailyTaskListDataDtoFromJson' isn't referenced • lib/features/tasks/data/dto/daily_task_list_dto.g.dart:100:22 • unused_element
warning • The declaration '_$DailyTaskListDtoFromJson' isn't referenced • lib/features/tasks/data/dto/daily_task_list_dto.g.dart:131:18 • unused_element
warning • The declaration '_$RewardBreakdownDtoFromJson' isn't referenced • lib/features/tasks/data/dto/task_dto_commons.g.dart:9:20 • unused_element
warning • The declaration '_$RewardsDtoFromJson' isn't referenced • lib/features/tasks/data/dto/task_dto_commons.g.dart:23:12 • unused_element
warning • The declaration '_$RequirementsDtoFromJson' isn't referenced • lib/features/tasks/data/dto/task_dto_commons.g.dart:48:17 • unused_element
warning • The declaration '_$ProgressDtoFromJson' isn't referenced • lib/features/tasks/data/dto/task_dto_commons.g.dart:66:13 • unused_element
warning • The declaration '_$EarnedTodayDtoFromJson' isn't referenced • lib/features/tasks/data/dto/today_summary_dto.g.dart:9:16 • unused_element
warning • The declaration '_$NextTaskDtoFromJson' isn't referenced • lib/features/tasks/data/dto/today_summary_dto.g.dart:21:13 • unused_element
warning • The declaration '_$TodaySummaryDataDtoFromJson' isn't referenced • lib/features/tasks/data/dto/today_summary_dto.g.dart:40:21 • unused_element
warning • The declaration '_$TodaySummaryDtoFromJson' isn't referenced • lib/features/tasks/data/dto/today_summary_dto.g.dart:68:17 • unused_element
   info • Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check • lib/features/tasks/presentation/screens/tasks_page.dart:334:40 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check • lib/features/tasks/presentation/screens/tasks_page.dart:397:40 • use_build_context_synchronously
warning • The declaration '_$UserModelFromJson' isn't referenced • lib/features/users/data/models/user_model.g.dart:9:11 • unused_element
warning • The declaration '_$MemberLevelModelFromJson' isn't referenced • lib/features/users/data/models/user_model.g.dart:50:18 • unused_element
warning • The declaration '_$VipInfoDtoFromJson' isn't referenced • lib/features/vip/data/dto/vip_status_dto.g.dart:37:12 • unused_element
warning • The declaration '_$AdditionalStatsDtoFromJson' isn't referenced • lib/features/vip/data/dto/vip_status_dto.g.dart:97:20 • unused_element
warning • The declaration '_$VipStatusDataDtoFromJson' isn't referenced • lib/features/vip/data/dto/vip_status_dto.g.dart:115:18 • unused_element
warning • The declaration '_$VipRefundPlanDtoFromJson' isn't referenced • lib/features/vip/data/dto/vip_status_dto.g.dart:175:18 • unused_element
warning • The declaration '_$VipLevelDetailDtoFromJson' isn't referenced • lib/features/vip/data/dto/vip_status_dto.g.dart:213:19 • unused_element
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/presentation/widgets/common/sync_progress_indicator.dart:160:33 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/presentation/widgets/common/sync_progress_indicator.dart:268:28 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/presentation/widgets/common/sync_progress_indicator.dart:319:34 • deprecated_member_use
   info • Use 'const' for final variables initialized to a constant value • test/core/controllers/phase_gate_controller_concurrency_test.dart:19:9 • prefer_const_declarations
   info • Use 'const' for final variables initialized to a constant value • test/core/controllers/phase_gate_controller_concurrency_test.dart:47:9 • prefer_const_declarations
   info • Use 'const' for final variables initialized to a constant value • test/core/controllers/phase_gate_controller_concurrency_test.dart:104:9 • prefer_const_declarations
   info • Use 'const' for final variables initialized to a constant value • test/core/controllers/phase_gate_controller_concurrency_test.dart:122:9 • prefer_const_declarations
   info • Use 'const' for final variables initialized to a constant value • test/core/controllers/phase_gate_controller_concurrency_test.dart:139:9 • prefer_const_declarations
   info • Use 'const' for final variables initialized to a constant value • test/core/controllers/phase_gate_controller_concurrency_test.dart:156:9 • prefer_const_declarations
   info • Use 'const' for final variables initialized to a constant value • test/core/controllers/phase_gate_controller_concurrency_test.dart:231:11 • prefer_const_declarations
   info • Use 'const' for final variables initialized to a constant value • test/core/services/error_handling_test.dart:55:9 • prefer_const_declarations
   info • Use 'const' for final variables initialized to a constant value • test/core/services/error_handling_test.dart:59:9 • prefer_const_declarations
   info • Use 'const' for final variables initialized to a constant value • test/core/services/error_handling_test.dart:63:9 • prefer_const_declarations
   info • Use 'const' for final variables initialized to a constant value • test/core/services/error_handling_test.dart:67:9 • prefer_const_declarations
   info • Use 'const' for final variables initialized to a constant value • test/core/services/error_handling_test.dart:144:9 • prefer_const_declarations
warning • The value of the local variable 'context' isn't used • test/core/services/event_triggered_sync_service_test.dart:119:15 • unused_local_variable
warning • The value of the local variable 'context' isn't used • test/core/services/event_triggered_sync_service_test.dart:148:15 • unused_local_variable
warning • The value of the local variable 'mockBaselineService' isn't used • test/core/services/health_data_flow_service_test.dart:32:30 • unused_local_variable
   info • Use 'const' for final variables initialized to a constant value • test/core/services/health_data_performance_test.dart:84:9 • prefer_const_declarations
   info • Use 'const' for final variables initialized to a constant value • test/features/splash/splash_screen_timing_fix_test.dart:176:5 • prefer_const_declarations
   info • Don't invoke 'print' in production code • test/features/splash/splash_screen_timing_fix_test.dart:244:7 • avoid_print
   info • Don't invoke 'print' in production code • test/features/splash/splash_screen_timing_fix_test.dart:245:7 • avoid_print
   info • Don't invoke 'print' in production code • test/features/splash/splash_screen_timing_fix_test.dart:249:9 • avoid_print
   info • Don't invoke 'print' in production code • test/features/splash/splash_screen_timing_fix_test.dart:250:9 • avoid_print
   info • Don't invoke 'print' in production code • test/features/splash/splash_screen_timing_fix_test.dart:255:9 • avoid_print
   info • Don't invoke 'print' in production code • test/features/splash/splash_screen_timing_fix_test.dart:256:9 • avoid_print
   info • Don't invoke 'print' in production code • test/features/splash/splash_screen_timing_fix_test.dart:260:11 • avoid_print
   info • Don't invoke 'print' in production code • test/features/splash/splash_screen_timing_fix_test.dart:262:13 • avoid_print
   info • Don't invoke 'print' in production code • test/features/splash/splash_screen_timing_fix_test.dart:268:11 • avoid_print
   info • Don't invoke 'print' in production code • test/features/splash/splash_screen_timing_fix_test.dart:270:13 • avoid_print
   info • Don't invoke 'print' in production code • test/features/splash/splash_screen_timing_fix_test.dart:275:7 • avoid_print
   info • Don't invoke 'print' in production code • test/features/splash/splash_screen_timing_fix_test.dart:276:7 • avoid_print

202 issues found. (ran in 1.0s)
