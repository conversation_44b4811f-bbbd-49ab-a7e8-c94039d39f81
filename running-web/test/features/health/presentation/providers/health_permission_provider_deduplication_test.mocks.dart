// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in sweatmint/test/features/health/presentation/providers/health_permission_provider_deduplication_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;
import 'package:sweatmint/core/models/health_data.dart' as _i2;
import 'package:sweatmint/core/services/health_service.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeHealthData_0 extends _i1.SmartFake implements _i2.HealthData {
  _FakeHealthData_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeHealthSyncResult_1 extends _i1.SmartFake
    implements _i2.HealthSyncResult {
  _FakeHealthSyncResult_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBaselineInitResult_2 extends _i1.SmartFake
    implements _i2.BaselineInitResult {
  _FakeBaselineInitResult_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBaselineStatusResult_3 extends _i1.SmartFake
    implements _i2.BaselineStatusResult {
  _FakeBaselineStatusResult_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [HealthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockHealthService extends _i1.Mock implements _i3.HealthService {
  MockHealthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i2.HealthData> get healthDataStream => (super.noSuchMethod(
        Invocation.getter(#healthDataStream),
        returnValue: _i4.Stream<_i2.HealthData>.empty(),
      ) as _i4.Stream<_i2.HealthData>);

  @override
  _i4.Future<bool> hasAllRequiredPermissions() => (super.noSuchMethod(
        Invocation.method(
          #hasAllRequiredPermissions,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> hasBasicPermissions() => (super.noSuchMethod(
        Invocation.method(
          #hasBasicPermissions,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<List<String>> getAuthorizedPermissions() => (super.noSuchMethod(
        Invocation.method(
          #getAuthorizedPermissions,
          [],
        ),
        returnValue: _i4.Future<List<String>>.value(<String>[]),
      ) as _i4.Future<List<String>>);

  @override
  _i4.Future<bool> hasPermissions() => (super.noSuchMethod(
        Invocation.method(
          #hasPermissions,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> requestPermissions() => (super.noSuchMethod(
        Invocation.method(
          #requestPermissions,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<_i2.HealthData> getHealthData({
    required DateTime? startTime,
    required DateTime? endTime,
    Map<String, String>? permissions,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getHealthData,
          [],
          {
            #startTime: startTime,
            #endTime: endTime,
            #permissions: permissions,
          },
        ),
        returnValue: _i4.Future<_i2.HealthData>.value(_FakeHealthData_0(
          this,
          Invocation.method(
            #getHealthData,
            [],
            {
              #startTime: startTime,
              #endTime: endTime,
              #permissions: permissions,
            },
          ),
        )),
      ) as _i4.Future<_i2.HealthData>);

  @override
  _i4.Future<Map<String, String>> checkDetailedPermissions() =>
      (super.noSuchMethod(
        Invocation.method(
          #checkDetailedPermissions,
          [],
        ),
        returnValue: _i4.Future<Map<String, String>>.value(<String, String>{}),
      ) as _i4.Future<Map<String, String>>);

  @override
  _i4.Future<Map<String, String>> checkRealPermissions() => (super.noSuchMethod(
        Invocation.method(
          #checkRealPermissions,
          [],
        ),
        returnValue: _i4.Future<Map<String, String>>.value(<String, String>{}),
      ) as _i4.Future<Map<String, String>>);

  @override
  _i4.Future<_i2.HealthData?> getTodayHealthData(
          {Map<String, String>? permissions}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTodayHealthData,
          [],
          {#permissions: permissions},
        ),
        returnValue: _i4.Future<_i2.HealthData?>.value(),
      ) as _i4.Future<_i2.HealthData?>);

  @override
  _i4.Future<_i2.HealthData?> getHealthDataForDate(DateTime? date) =>
      (super.noSuchMethod(
        Invocation.method(
          #getHealthDataForDate,
          [date],
        ),
        returnValue: _i4.Future<_i2.HealthData?>.value(),
      ) as _i4.Future<_i2.HealthData?>);

  @override
  _i4.Future<_i2.HealthData?> getHealthDataForPeriod({
    required DateTime? startTime,
    required DateTime? endTime,
    Map<String, String>? permissions,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getHealthDataForPeriod,
          [],
          {
            #startTime: startTime,
            #endTime: endTime,
            #permissions: permissions,
          },
        ),
        returnValue: _i4.Future<_i2.HealthData?>.value(),
      ) as _i4.Future<_i2.HealthData?>);

  @override
  _i4.Future<List<_i2.HealthData>> getHealthDataForDateRange(
    DateTime? startDate,
    DateTime? endDate,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getHealthDataForDateRange,
          [
            startDate,
            endDate,
          ],
        ),
        returnValue: _i4.Future<List<_i2.HealthData>>.value(<_i2.HealthData>[]),
      ) as _i4.Future<List<_i2.HealthData>>);

  @override
  _i4.Future<_i2.HealthSyncResult> syncHealthData(_i2.HealthData? data) =>
      (super.noSuchMethod(
        Invocation.method(
          #syncHealthData,
          [data],
        ),
        returnValue:
            _i4.Future<_i2.HealthSyncResult>.value(_FakeHealthSyncResult_1(
          this,
          Invocation.method(
            #syncHealthData,
            [data],
          ),
        )),
      ) as _i4.Future<_i2.HealthSyncResult>);

  @override
  _i4.Future<bool> isHealthDataAvailable() => (super.noSuchMethod(
        Invocation.method(
          #isHealthDataAvailable,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<List<String>> getSupportedDataTypes() => (super.noSuchMethod(
        Invocation.method(
          #getSupportedDataTypes,
          [],
        ),
        returnValue: _i4.Future<List<String>>.value(<String>[]),
      ) as _i4.Future<List<String>>);

  @override
  _i4.Future<void> startBackgroundMonitoring() => (super.noSuchMethod(
        Invocation.method(
          #startBackgroundMonitoring,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> stopBackgroundMonitoring() => (super.noSuchMethod(
        Invocation.method(
          #stopBackgroundMonitoring,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<_i2.BaselineInitResult> initializeHealthBaseline() =>
      (super.noSuchMethod(
        Invocation.method(
          #initializeHealthBaseline,
          [],
        ),
        returnValue:
            _i4.Future<_i2.BaselineInitResult>.value(_FakeBaselineInitResult_2(
          this,
          Invocation.method(
            #initializeHealthBaseline,
            [],
          ),
        )),
      ) as _i4.Future<_i2.BaselineInitResult>);

  @override
  _i4.Future<_i2.HealthSyncResult> syncHealthDataWithBaseline({
    Map<String, String>? permissions,
    _i2.HealthData? preloadedHealthData,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #syncHealthDataWithBaseline,
          [],
          {
            #permissions: permissions,
            #preloadedHealthData: preloadedHealthData,
          },
        ),
        returnValue:
            _i4.Future<_i2.HealthSyncResult>.value(_FakeHealthSyncResult_1(
          this,
          Invocation.method(
            #syncHealthDataWithBaseline,
            [],
            {#permissions: permissions},
          ),
        )),
      ) as _i4.Future<_i2.HealthSyncResult>);

  @override
  _i4.Future<Map<String, dynamic>> syncHealthDataWithSession(
          {bool? skipPermissionCheck = false}) =>
      (super.noSuchMethod(
        Invocation.method(
          #syncHealthDataWithSession,
          [],
          {#skipPermissionCheck: skipPermissionCheck},
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<String> getDeviceId() => (super.noSuchMethod(
        Invocation.method(
          #getDeviceId,
          [],
        ),
        returnValue: _i4.Future<String>.value(_i5.dummyValue<String>(
          this,
          Invocation.method(
            #getDeviceId,
            [],
          ),
        )),
      ) as _i4.Future<String>);

  @override
  _i4.Future<Map<String, dynamic>> getHealthStatus() => (super.noSuchMethod(
        Invocation.method(
          #getHealthStatus,
          [],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<_i2.BaselineStatusResult> checkBaselineStatus() =>
      (super.noSuchMethod(
        Invocation.method(
          #checkBaselineStatus,
          [],
        ),
        returnValue: _i4.Future<_i2.BaselineStatusResult>.value(
            _FakeBaselineStatusResult_3(
          this,
          Invocation.method(
            #checkBaselineStatus,
            [],
          ),
        )),
      ) as _i4.Future<_i2.BaselineStatusResult>);

  @override
  _i4.Future<_i2.BaselineInitResult> handleDeviceSwitch() =>
      (super.noSuchMethod(
        Invocation.method(
          #handleDeviceSwitch,
          [],
        ),
        returnValue:
            _i4.Future<_i2.BaselineInitResult>.value(_FakeBaselineInitResult_2(
          this,
          Invocation.method(
            #handleDeviceSwitch,
            [],
          ),
        )),
      ) as _i4.Future<_i2.BaselineInitResult>);

  @override
  _i4.Future<_i2.HealthData> getCurrentHealthData() => (super.noSuchMethod(
        Invocation.method(
          #getCurrentHealthData,
          [],
        ),
        returnValue: _i4.Future<_i2.HealthData>.value(_FakeHealthData_0(
          this,
          Invocation.method(
            #getCurrentHealthData,
            [],
          ),
        )),
      ) as _i4.Future<_i2.HealthData>);

  @override
  _i4.Future<Map<String, dynamic>?> fetchHealthDataFromBackend() =>
      (super.noSuchMethod(
        Invocation.method(
          #fetchHealthDataFromBackend,
          [],
        ),
        returnValue: _i4.Future<Map<String, dynamic>?>.value(),
      ) as _i4.Future<Map<String, dynamic>?>);

  @override
  _i4.Future<_i2.HealthSyncResult> syncHealthDataToBackend() =>
      (super.noSuchMethod(
        Invocation.method(
          #syncHealthDataToBackend,
          [],
        ),
        returnValue:
            _i4.Future<_i2.HealthSyncResult>.value(_FakeHealthSyncResult_1(
          this,
          Invocation.method(
            #syncHealthDataToBackend,
            [],
          ),
        )),
      ) as _i4.Future<_i2.HealthSyncResult>);

  @override
  _i4.Future<void> clearSimulatorPermissionState() => (super.noSuchMethod(
        Invocation.method(
          #clearSimulatorPermissionState,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> clearCachedData() => (super.noSuchMethod(
        Invocation.method(
          #clearCachedData,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<List<dynamic>> getSpecificHealthData(
    dynamic dataType,
    DateTime? startTime,
    DateTime? endTime,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSpecificHealthData,
          [
            dataType,
            startTime,
            endTime,
          ],
        ),
        returnValue: _i4.Future<List<dynamic>>.value(<dynamic>[]),
      ) as _i4.Future<List<dynamic>>);

  @override
  _i4.Future<bool> baselineReset({required _i2.HealthData? totals}) =>
      (super.noSuchMethod(
        Invocation.method(
          #baselineReset,
          [],
          {#totals: totals},
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);
}
