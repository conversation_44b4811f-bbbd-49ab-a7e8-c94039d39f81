import 'package:flutter_test/flutter_test.dart';
import 'package:sweatmint/core/controllers/phase_gate_controller.dart';

void main() {
  group('PhaseGateController 并发测试', () {
    late PhaseGateController controller;

    setUp(() {
      controller = PhaseGateController();
    });

    tearDown(() {
      controller.dispose();
    });

    group('Mutex保护测试', () {
      test('并发状态更新应该保持原子性', () async {
        // 准备测试阶段
        final phase = V141Phase.STEP1_AUTH_CHECK;
        
        // 启动阶段
        await controller.markPhaseInProgress(phase);
        
        // 并发执行多个状态更新操作
        final futures = <Future>[];
        
        // 模拟10个并发的完成操作
        for (int i = 0; i < 10; i++) {
          futures.add(
            controller.markPhaseCompleted(phase, result: {'attempt': i})
          );
        }
        
        // 等待所有操作完成
        await Future.wait(futures);
        
        // 验证最终状态一致性
        expect(controller.getPhaseStatus(phase), PhaseGateStatus.COMPLETED);
        
        // 验证结果只有一个（不会被重复设置）
        final result = controller.getPhaseResult(phase);
        expect(result, isNotNull);
        expect(result!.status, PhaseGateStatus.COMPLETED);
      });

      test('并发失败和完成操作应该保持状态一致性', () async {
        final phase = V141Phase.STEP1_AUTH_CHECK;

        // 启动阶段
        await controller.markPhaseInProgress(phase);

        // 并发执行完成和失败操作
        final futures = <Future>[];

        // 5个完成操作
        for (int i = 0; i < 5; i++) {
          futures.add(
            controller.markPhaseCompleted(phase, result: {'success': true})
          );
        }

        // 5个失败操作
        for (int i = 0; i < 5; i++) {
          futures.add(
            controller.markPhaseFailed(phase, 'Test error $i')
          );
        }

        // 等待所有操作完成
        await Future.wait(futures);

        // 验证最终状态是确定的（要么完成要么失败，不会是中间状态）
        final status = controller.getPhaseStatus(phase);
        expect(status, anyOf(PhaseGateStatus.COMPLETED, PhaseGateStatus.FAILED));

        // 验证状态更新确认机制工作正常
        final result = controller.getPhaseResult(phase);
        expect(result, isNotNull);
        expect(result!.status, status);
      });

      test('顺序阶段启动应该保持正确性', () async {
        final phases = [
          V141Phase.STEP1_AUTH_CHECK,
          V141Phase.STEP2_PERMISSION_CHECK,
          V141Phase.STEP3_CROSS_DAY_BASELINE,
        ];

        // 按顺序启动和完成阶段
        for (int i = 0; i < phases.length; i++) {
          await controller.markPhaseInProgress(phases[i]);
          await controller.markPhaseCompleted(phases[i], result: {'step': i + 1});
        }

        // 验证所有阶段都正确完成
        for (final phase in phases) {
          expect(controller.getPhaseStatus(phase), PhaseGateStatus.COMPLETED);
        }
      });
    });

    group('状态更新确认机制测试', () {
      test('状态更新确认应该检测不一致状态', () async {
        final phase = V141Phase.STEP1_AUTH_CHECK;

        // 启动阶段
        await controller.markPhaseInProgress(phase);

        // 完成阶段
        await controller.markPhaseCompleted(phase, result: {'test': true});

        // 验证状态确认机制工作
        expect(controller.getPhaseStatus(phase), PhaseGateStatus.COMPLETED);

        // 验证状态更新后的一致性
        final result = controller.getPhaseResult(phase);
        expect(result, isNotNull);
        expect(result!.status, PhaseGateStatus.COMPLETED);
      });

      test('强制阶段完成应该正确更新状态', () async {
        final phase = V141Phase.STEP4_HEALTH_DATA_SYNC;
        
        // 直接强制完成（不先启动）
        await controller.forcePhaseCompletion(phase);
        
        // 验证状态正确更新
        expect(controller.getPhaseStatus(phase), PhaseGateStatus.COMPLETED);
        
        // 验证结果包含强制完成标记
        final result = controller.getPhaseResult(phase);
        expect(result, isNotNull);
        expect(result!.result?['forced_completion'], true);
      });
    });

    group('竞态条件测试', () {
      test('快速连续的状态变更应该保持一致性', () async {
        final phase = V141Phase.STEP1_AUTH_CHECK;
        
        // 快速连续执行状态变更
        await controller.markPhaseInProgress(phase);
        await controller.markPhaseCompleted(phase, result: {'fast': true});
        
        // 立即检查状态
        expect(controller.getPhaseStatus(phase), PhaseGateStatus.COMPLETED);
        
        // 再次尝试完成（应该被忽略）
        await controller.markPhaseCompleted(phase, result: {'duplicate': true});
        
        // 验证状态仍然一致
        expect(controller.getPhaseStatus(phase), PhaseGateStatus.COMPLETED);
      });

      test('监听器通知应该在状态更新后触发', () async {
        final phase = V141Phase.STEP1_AUTH_CHECK;
        bool notified = false;

        // 添加监听器
        controller.addListener(() {
          notified = true;
        });

        // 执行状态更新
        await controller.markPhaseInProgress(phase);

        // 短暂延迟确保异步操作完成
        await Future.delayed(const Duration(milliseconds: 50));

        // 验证监听器被触发
        expect(notified, true);
      });
    });

    group('步骤1-4完成状态测试', () {
      test('isSteps1to4Completed应该正确反映所有步骤状态', () async {
        final steps1to4 = [
          V141Phase.STEP1_AUTH_CHECK,
          V141Phase.STEP2_PERMISSION_CHECK,
          V141Phase.STEP3_CROSS_DAY_BASELINE,
          V141Phase.STEP4_HEALTH_DATA_SYNC,
        ];
        
        // 初始状态应该是未完成
        expect(controller.isSteps1to4Completed, false);
        
        // 逐步完成各个步骤
        for (int i = 0; i < steps1to4.length; i++) {
          await controller.markPhaseInProgress(steps1to4[i]);
          await controller.markPhaseCompleted(steps1to4[i], result: {'step': i + 1});
          
          // 只有所有步骤都完成时才返回true
          if (i == steps1to4.length - 1) {
            expect(controller.isSteps1to4Completed, true);
          } else {
            expect(controller.isSteps1to4Completed, false);
          }
        }
      });

      test('顺序完成步骤1-4应该保持状态一致性', () async {
        final steps1to4 = [
          V141Phase.STEP1_AUTH_CHECK,
          V141Phase.STEP2_PERMISSION_CHECK,
          V141Phase.STEP3_CROSS_DAY_BASELINE,
          V141Phase.STEP4_HEALTH_DATA_SYNC,
        ];

        // 按顺序启动和完成所有步骤
        for (int i = 0; i < steps1to4.length; i++) {
          await controller.markPhaseInProgress(steps1to4[i]);
          await controller.markPhaseCompleted(steps1to4[i], result: {'sequential': true, 'step': i + 1});
        }

        // 验证最终状态
        expect(controller.isSteps1to4Completed, true);

        // 验证每个步骤都正确完成
        for (final phase in steps1to4) {
          expect(controller.getPhaseStatus(phase), PhaseGateStatus.COMPLETED);
        }
      });
    });

    group('性能测试', () {
      test('大量顺序操作应该在合理时间内完成', () async {
        final stopwatch = Stopwatch()..start();

        // 创建100个顺序操作（重复执行步骤1）
        for (int i = 0; i < 100; i++) {
          final phase = V141Phase.STEP1_AUTH_CHECK;

          // 重置阶段状态以允许重复执行
          await controller.forcePhaseCompletion(phase);

          // 执行启动和完成操作
          await controller.markPhaseInProgress(phase);
          await controller.markPhaseCompleted(phase, result: {'batch': i});
        }

        stopwatch.stop();

        // 验证性能（应该在5秒内完成，考虑Mutex保护的开销）
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));

        // 验证最终状态一致性
        expect(controller.getPhaseStatus(V141Phase.STEP1_AUTH_CHECK), PhaseGateStatus.COMPLETED);
      });
    });
  });
}
