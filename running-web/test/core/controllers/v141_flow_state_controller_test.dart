import 'package:flutter_test/flutter_test.dart';
import 'package:sweatmint/core/controllers/v141_flow_state_controller.dart';

void main() {
  group('V141FlowStateController Tests', () {
    late V141FlowStateController controller;

    setUp(() {
      // 重置单例实例以确保测试隔离
      V141FlowStateController.resetInstance();
      controller = V141FlowStateController.instance;
    });

    tearDown(() {
      // 重置状态
      controller.resetFlowState(reason: 'test cleanup');
    });

    group('单例模式测试', () {
      test('应该返回相同的实例', () {
        final instance1 = V141FlowStateController.instance;
        final instance2 = V141FlowStateController.instance;
        
        expect(instance1, same(instance2));
      });
    });

    group('初始状态测试', () {
      test('初始状态应该正确', () {
        expect(controller.isSteps1to4Completed, false);
        expect(controller.isStep5Completed, false);
        expect(controller.isFlowCompleted, false);
        expect(controller.currentScenario, null);
        expect(controller.lastExecutionTime, null);
      });

      test('初始状态查询方法应该正确', () {
        expect(controller.canExecuteSteps1to4(), true);
        expect(controller.canExecuteStep5(), false);
        expect(controller.canExecuteFullFlow(), true);
      });
    });

    group('状态更新测试', () {
      test('标记步骤1-4完成应该更新状态', () {
        final result = {'success': true, 'data': 'test'};
        
        controller.markSteps1to4Completed(
          result: result,
          scenario: 'test_scenario',
        );

        expect(controller.isSteps1to4Completed, true);
        expect(controller.steps1to4Result, result);
        expect(controller.currentScenario, 'test_scenario');
        expect(controller.lastExecutionTime, isNotNull);
        expect(controller.canExecuteSteps1to4(), false);
        expect(controller.canExecuteStep5(), true);
      });

      test('标记步骤5完成应该更新状态', () {
        // 先完成步骤1-4
        controller.markSteps1to4Completed(
          result: {'success': true},
          scenario: 'test',
        );

        final step5Result = {'success': true, 'ui_loaded': true};
        
        controller.markStep5Completed(result: step5Result);

        expect(controller.isStep5Completed, true);
        expect(controller.isFlowCompleted, true);
        expect(controller.step5Result, step5Result);
        expect(controller.canExecuteStep5(), false);
      });

      test('标记完整流程完成应该更新状态', () {
        controller.markFlowCompleted(scenario: 'complete_test');

        expect(controller.isFlowCompleted, true);
        expect(controller.currentScenario, 'complete_test');
        expect(controller.canExecuteFullFlow(), false);
      });
    });

    group('状态重置测试', () {
      test('重置流程状态应该清除所有状态', () {
        // 设置一些状态
        controller.markSteps1to4Completed(
          result: {'success': true},
          scenario: 'test',
        );
        controller.markStep5Completed(result: {'success': true});

        // 重置状态
        controller.resetFlowState(reason: 'test reset');

        // 验证状态已重置
        expect(controller.isSteps1to4Completed, false);
        expect(controller.isStep5Completed, false);
        expect(controller.isFlowCompleted, false);
        expect(controller.currentScenario, null);
        expect(controller.lastExecutionTime, null);
        expect(controller.steps1to4Result, null);
        expect(controller.step5Result, null);
      });
    });

    group('强制状态设置测试', () {
      test('强制设置步骤1-4状态应该生效', () {
        controller.forceSetSteps1to4State(true, reason: 'test force');

        expect(controller.isSteps1to4Completed, true);
        expect(controller.lastExecutionTime, isNotNull);
      });

      test('强制设置步骤5状态应该生效', () {
        controller.forceSetStep5State(true, reason: 'test force');

        expect(controller.isStep5Completed, true);
        expect(controller.lastExecutionTime, isNotNull);
      });
    });

    group('状态摘要测试', () {
      test('获取流程状态摘要应该包含所有信息', () {
        controller.markSteps1to4Completed(
          result: {'success': true},
          scenario: 'test_scenario',
        );

        final summary = controller.getFlowStateSummary();

        expect(summary['steps_1to4_completed'], true);
        expect(summary['step_5_completed'], false);
        expect(summary['flow_completed'], false);
        expect(summary['current_scenario'], 'test_scenario');
        expect(summary['last_execution_time'], isNotNull);
        expect(summary['can_execute_steps_1to4'], false);
        expect(summary['can_execute_step_5'], true);
        expect(summary['can_execute_full_flow'], true);
      });
    });

    group('状态变更通知测试', () {
      test('状态更新应该触发通知', () {
        bool notified = false;
        controller.addListener(() {
          notified = true;
        });

        controller.markSteps1to4Completed(
          result: {'success': true},
          scenario: 'test',
        );

        expect(notified, true);
      });
    });
  });
}
