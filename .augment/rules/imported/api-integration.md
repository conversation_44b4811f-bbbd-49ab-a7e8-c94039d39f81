---
type: "agent_requested"
---

# SweatMint API集成规则

## API设计原则
- **RESTful风格**：资源名使用复数形式，标准HTTP方法
- **统一响应**：所有API使用统一的响应格式
- **版本控制**：支持API版本控制和向前兼容
- **文档完整**：每个API必须有完整文档和示例

## 统一响应格式

### 成功响应
```python
{
    "code": 200,              # 业务状态码
    "message": "操作成功",     # 友好提示信息
    "data": {                 # 业务数据
        "字段1": "值1",
        "字段2": "值2"
    },
    "timestamp": 1646041438   # 时间戳
}
```

### 失败响应
```python
{
    "code": 400,              # 业务状态码
    "message": "参数错误",     # 用户友好错误信息
    "details": "缺少必填参数x", # 详细错误信息
    "timestamp": 1646041438   # 时间戳
}
```

## 后端API实现

### API目录结构
- **用户API**：[running/users/](mdc:running/users) - 用户注册、登录、资料
- **任务API**：[running/tasks/](mdc:running/tasks) - 任务管理和完成记录
- **奖励API**：[running/rewards/](mdc:running/rewards) - 奖励查询和兑换
- **VIP API**：[running/vip/](mdc:running/vip) - VIP服务和购买
- **钱包API**：[running/wallet/](mdc:running/wallet) - 余额查询和交易记录

### 错误处理
- 输入验证和参数检查
- 业务逻辑错误处理
- 数据库操作异常处理
- 统一错误码和消息格式

## 前端API集成

### 网络层架构
- **ApiClient**：[lib/core/network/api_client.dart](mdc:running-web/lib/core/network/api_client.dart) - 统一网络请求
- **DataSource**：[lib/data/datasources/](mdc:running-web/lib/data/datasources) - API数据源
- **Repository**：[lib/data/repositories/](mdc:running-web/lib/data/repositories) - 数据仓库实现
- **Service**：[lib/domain/services/](mdc:running-web/lib/domain/services) - 业务服务层

### 错误处理流程
1. **ApiClient层**：捕获网络异常，转换为ApiException
2. **Repository层**：处理数据转换和缓存
3. **Service层**：处理业务逻辑异常
4. **ViewModel层**：使用ViewModelMixin统一处理UI状态

### 数据模型
- **DTO**：[lib/data/dto/](mdc:running-web/lib/data/dto) - 原始API数据结构
- **Model**：[lib/data/models/](mdc:running-web/lib/data/models) - 业务数据模型
- **Entity**：[lib/domain/entities/](mdc:running-web/lib/domain/entities) - 领域实体

## 认证和安全
- **Token管理**：JWT token自动添加到请求头
- **安全存储**：使用flutter_secure_storage存储敏感信息
- **网络检查**：请求前检查网络连接状态
- **重试机制**：网络失败时的自动重试

## 状态管理集成
- **ViewModelMixin**：统一管理API调用状态
- **executeAsyncAction**：包装异步API调用
- **错误状态**：自动设置和清除错误消息
- **加载状态**：自动管理加载指示器

## 测试策略
- **单元测试**：测试API调用逻辑
- **Mock测试**：模拟API响应
- **集成测试**：端到端API测试
- **错误场景测试**：网络异常和业务错误测试

## 监控和调试
- **请求日志**：记录API请求和响应
- **性能监控**：追踪API响应时间
- **错误追踪**：记录API错误和异常
- **网络状态**：监控网络连接状态
