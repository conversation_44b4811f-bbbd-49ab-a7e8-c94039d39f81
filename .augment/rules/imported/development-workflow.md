---
type: "agent_requested"
---

# SweatMint 开发工作流程

## 开发原则
- **严谨开发**：必须阅读相关代码和文档，禁止基于猜测开发
- **事实为准**：以现有代码和文档为基础，不能出现模棱两可的解决方案
- **渐进迭代**：按小功能点开发，避免大规模重构
- **向后兼容**：新功能不能破坏现有功能

## 环境配置
- **后端虚拟环境**：`/Users/<USER>/Documents/worker/sweatmint/running/.venv/bin/activate`
- **工作目录**：[running/](mdc:running) (后端) 和 [running-web/](mdc:running-web) (前端)
- **管理后台**：SimpleUI模板和组件

## 代码开发流程

### 1. 需求分析
- 阅读相关文档和现有代码
- 分析功能需求和技术约束
- 确定实现方案和影响范围

### 2. 代码实现
- **后端开发**：在 [running/](mdc:running) 目录下进行
- **前端开发**：在 [running-web/](mdc:running-web) 目录下进行
- 遵循对应的架构模式和代码规范
- 添加必要的中文注释和文档

### 3. 测试验证
- **单元测试**：[running/tests/](mdc:running/tests) 和 [running-web/test/](mdc:running-web/test)
- **集成测试**：验证模块间交互
- **功能测试**：确保业务逻辑正确

### 4. 代码审查
- 检查代码质量和规范
- 验证安全性和性能
- 确保向后兼容性

## 质量控制

### 后端质量标准
- 使用Django ORM进行数据库操作
- 实现完整的错误处理和日志记录
- API遵循RESTful规范
- 安全验证和输入验证

### 前端质量标准
- 遵循Flutter分层架构
- 使用Provider进行状态管理
- 基于Figma设计实现UI
- 响应式布局和性能优化

## 文件分类和组织
- **配置文件**：集中在config目录
- **业务逻辑**：按模块分离（users、tasks、rewards等）
- **工具函数**：放置在utils或core目录
- **测试文件**：与源码对应的test目录

## 版本控制
- 提交信息清晰描述变更内容
- 每次提交专注于单一功能
- 大功能分多次小提交
- 提交前完成必要测试

## 部署和监控
- 使用虚拟环境隔离依赖
- 配置适当的日志级别
- 实现健康检查接口
- 监控关键业务指标
