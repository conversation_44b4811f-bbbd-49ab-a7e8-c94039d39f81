开始运行测试...
正在激活虚拟环境: /Users/<USER>/Documents/worker/sweatmint/running/.venv/bin/activate
运行所有测试...
Requirement already satisfied: coverage in ./.venv/lib/python3.12/site-packages (7.8.0)
Requirement already satisfied: pytest in ./.venv/lib/python3.12/site-packages (8.3.4)
Requirement already satisfied: pytest-django in ./.venv/lib/python3.12/site-packages (4.10.0)
Requirement already satisfied: pytest-html in ./.venv/lib/python3.12/site-packages (4.1.1)
Requirement already satisfied: iniconfig in ./.venv/lib/python3.12/site-packages (from pytest) (2.0.0)
Requirement already satisfied: packaging in ./.venv/lib/python3.12/site-packages (from pytest) (24.2)
Requirement already satisfied: pluggy<2,>=1.5 in ./.venv/lib/python3.12/site-packages (from pytest) (1.5.0)
Requirement already satisfied: jinja2>=3.0.0 in ./.venv/lib/python3.12/site-packages (from pytest-html) (3.1.6)
Requirement already satisfied: pytest-metadata>=2.0.0 in ./.venv/lib/python3.12/site-packages (from pytest-html) (3.1.1)
Requirement already satisfied: MarkupSafe>=2.0 in ./.venv/lib/python3.12/site-packages (from jinja2>=3.0.0->pytest-html) (3.0.2)
在 /Users/<USER>/Documents/worker/sweatmint/running 中执行测试: tests
============================= test session starts ==============================
platform darwin -- Python 3.12.9, pytest-8.3.4, pluggy-1.5.0 -- /Users/<USER>/Documents/worker/sweatmint/running/.venv/bin/python
cachedir: .pytest_cache
metadata: {'Python': '3.12.9', 'Platform': 'macOS-15.3.2-arm64-arm-64bit', 'Packages': {'pytest': '8.3.4', 'pluggy': '1.5.0'}, 'Plugins': {'html': '4.1.1', 'metadata': '3.1.1', 'django': '4.10.0'}}
django: version: 4.2.11, settings: core.settings (from ini)
rootdir: /Users/<USER>/Documents/worker/sweatmint/running
configfile: pytest.ini
plugins: html-4.1.1, metadata-3.1.1, django-4.10.0
collecting ... collected 11 items / 3 errors

==================================== ERRORS ====================================
__________________ ERROR collecting tests/test_performance.py __________________
ImportError while importing test module '/Users/<USER>/Documents/worker/sweatmint/running/tests/test_performance.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
.venv/lib/python3.12/site-packages/_pytest/python.py:493: in importtestmodule
    mod = import_path(
.venv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path
    importlib.import_module(module_name)
/opt/homebrew/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1387: in _gcd_import
    ???
<frozen importlib._bootstrap>:1360: in _find_and_load
    ???
<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:935: in _load_unlocked
    ???
.venv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:184: in exec_module
    exec(co, module.__dict__)
tests/test_performance.py:7: in <module>
    from tasks.models import Task, UserTask, MemberLevel
E   ImportError: cannot import name 'MemberLevel' from 'tasks.models' (/Users/<USER>/Documents/worker/sweatmint/running/tasks/models.py)
___________________ ERROR collecting tests/test_stability.py ___________________
ImportError while importing test module '/Users/<USER>/Documents/worker/sweatmint/running/tests/test_stability.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
.venv/lib/python3.12/site-packages/_pytest/python.py:493: in importtestmodule
    mod = import_path(
.venv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path
    importlib.import_module(module_name)
/opt/homebrew/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1387: in _gcd_import
    ???
<frozen importlib._bootstrap>:1360: in _find_and_load
    ???
<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:935: in _load_unlocked
    ???
.venv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:184: in exec_module
    exec(co, module.__dict__)
tests/test_stability.py:9: in <module>
    from tasks.models import Task, UserTask, MemberLevel, VIPLevel
E   ImportError: cannot import name 'MemberLevel' from 'tasks.models' (/Users/<USER>/Documents/worker/sweatmint/running/tasks/models.py)
_________________ ERROR collecting tests/test_tasks_app_api.py _________________
ImportError while importing test module '/Users/<USER>/Documents/worker/sweatmint/running/tests/test_tasks_app_api.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
.venv/lib/python3.12/site-packages/_pytest/python.py:493: in importtestmodule
    mod = import_path(
.venv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path
    importlib.import_module(module_name)
/opt/homebrew/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1387: in _gcd_import
    ???
<frozen importlib._bootstrap>:1360: in _find_and_load
    ???
<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:935: in _load_unlocked
    ???
.venv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:184: in exec_module
    exec(co, module.__dict__)
tests/test_tasks_app_api.py:13: in <module>
    from tasks.models import Task, UserTask, MemberLevel, VIPLevel
E   ImportError: cannot import name 'MemberLevel' from 'tasks.models' (/Users/<USER>/Documents/worker/sweatmint/running/tasks/models.py)
=============================== warnings summary ===============================
.venv/lib/python3.12/site-packages/django/conf/__init__.py:267
  /Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/django/conf/__init__.py:267: RemovedInDjango50Warning: The USE_L10N setting is deprecated. Starting with Django 5.0, localized formatting of data will always be enabled. For example Django will display numbers and dates using the format of the current locale.
    warnings.warn(USE_L10N_DEPRECATED_MSG, RemovedInDjango50Warning)

.venv/lib/python3.12/site-packages/drf_yasg/__init__.py:2
  /Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/drf_yasg/__init__.py:2: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
    from pkg_resources import DistributionNotFound, get_distribution

.venv/lib/python3.12/site-packages/django_cryptography/core/signing.py:20
  /Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/django_cryptography/core/signing.py:20: RemovedInDjango50Warning: The django.utils.baseconv module is deprecated.
    from django.utils import baseconv

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
- Generated html report: file:///Users/<USER>/Documents/%E5%B7%A5%E4%BD%9C/sweatmint/running/tests/reports/full_test_report_20250417_005529.html -
=========================== short test summary info ============================
ERROR tests/test_performance.py
ERROR tests/test_stability.py
ERROR tests/test_tasks_app_api.py
!!!!!!!!!!!!!!!!!!! Interrupted: 3 errors during collection !!!!!!!!!!!!!!!!!!!!
======================== 3 warnings, 3 errors in 0.19s =========================
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/manage.py", line 27, in <module>
    main()
  File "/Users/<USER>/Documents/worker/sweatmint/running/manage.py", line 23, in main
    execute_from_command_line(sys.argv)
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/django/core/management/__init__.py", line 442, in execute_from_command_line
    utility.execute()
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/django/core/management/__init__.py", line 436, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/django/core/management/commands/test.py", line 24, in run_from_argv
    super().run_from_argv(argv)
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/django/core/management/base.py", line 404, in run_from_argv
    parser = self.create_parser(argv[0], argv[1])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/django/core/management/base.py", line 367, in create_parser
    self.add_arguments(parser)
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/django/core/management/commands/test.py", line 54, in add_arguments
    test_runner_class = get_runner(settings, self.test_runner)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/django/test/utils.py", line 388, in get_runner
    test_runner_class = test_runner_class or settings.TEST_RUNNER
                                             ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/django/conf/__init__.py", line 102, in __getattr__
    self._setup(name)
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/django/conf/__init__.py", line 89, in _setup
    self._wrapped = Settings(settings_module)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/django/conf/__init__.py", line 217, in __init__
    mod = importlib.import_module(self.SETTINGS_MODULE)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1310, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1310, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'sweatmint'
Wrote HTML report to /Users/<USER>/Documents/worker/sweatmint/running/tests/reports/full_test_report_20250417_005529_coverage/index.html
测试报告已生成：/Users/<USER>/Documents/worker/sweatmint/running/tests/reports/full_test_report_20250417_005529.html
覆盖率报告：/Users/<USER>/Documents/worker/sweatmint/running/tests/reports/full_test_report_20250417_005529_coverage
退出虚拟环境.
测试执行完成，退出码: 2
测试结果: 失败 ❌

测试执行完成，开始分析测试结果...
分析报告已保存至: /Users/<USER>/Documents/worker/sweatmint/running/tests/reports/analysis_report_report.txt
分析测试报告: /Users/<USER>/Documents/worker/sweatmint/running/tests/reports/full_test_report_20250417_005529.html

============= 测试结果统计 =============
通过测试: 0
失败测试: 0
错误测试: 0
跳过测试: 0

分析覆盖率报告: /Users/<USER>/Documents/worker/sweatmint/running/tests/reports/full_test_report_20250417_005529_coverage.txt
总体覆盖率: 1%

覆盖率过低的模块:
  - agents/__init__.py: 0%
  - agents/admin.py: 0%
  - agents/app_serializers.py: 0%
  - agents/app_views.py: 0%
  - agents/apps.py: 0%
  - agents/management/commands/fix_agent.py: 0%
  - agents/migrations/0001_initial.py: 0%
  - agents/migrations/0002_initial.py: 0%
  - agents/migrations/0003_agentrelationship_settlement_restored_at_and_more.py: 0%
  - agents/migrations/0004_settlementwarning.py: 0%
  - agents/migrations/0005_create_settlement_warning.py: 0%
  - agents/migrations/0006_add_processed_at_field.py: 0%
  - agents/models.py: 0%
  - agents/serializers.py: 0%
  - agents/services/__init__.py: 0%
  - agents/services/agent_service.py: 0%
  - agents/services/commission_service.py: 0%
  - agents/services/notification_service.py: 0%
  - agents/services/warning_service.py: 0%
  - agents/tasks.py: 0%
  - agents/urls.py: 0%
  - agents/urls_app.py: 0%
  - agents/urls_warning_redirect.py: 0%
  - agents/utils.py: 0%
  - agents/views.py: 0%
  - authentication/admin.py: 0%
  - authentication/app_views.py: 0%
  - authentication/apps.py: 0%
  - authentication/backends.py: 0%
  - authentication/middleware.py: 0%
  - authentication/migrations/0001_initial.py: 0%
  - authentication/migrations/0002_historicaltokenblacklist_device_info_and_more.py: 0%
  - authentication/migrations/0003_historicalusertoken_token_type_usertoken_token_type_and_more.py: 0%
  - authentication/migrations/0004_alter_historicaltokenblacklist_token_and_more.py: 0%
  - authentication/models.py: 0%
  - authentication/ratelimit.py: 0%
  - authentication/tasks.py: 0%
  - authentication/urls.py: 0%
  - authentication/urls_app.py: 0%
  - authentication/utils.py: 0%
  - authentication/views.py: 0%
  - authentication/views_app.py: 0%
  - check_user_tasks.py: 0%
  - clean_task_records.py: 0%
  - config/admin.py: 0%
  - config/apps.py: 0%
  - config/migrations/0001_initial.py: 0%
  - config/migrations/0002_initial.py: 0%
  - config/migrations/0003_historicalnetwork_network_type_network_network_type_and_more.py: 0%
  - config/migrations/0004_alter_historicalnetwork_gas_fee_and_more.py: 0%
  - config/migrations/0005_alter_historicalnetwork_network_type_and_more.py: 0%
  - config/migrations/0006_historicalsystemconfig_google_fit_api_key_and_more.py: 0%
  - config/migrations/0007_alter_historicalsystemconfig_google_fit_api_key_and_more.py: 0%
  - config/migrations/0008_alter_systemconfig_options.py: 0%
  - config/migrations/0009_historicalsystemconfig_email_from_address_and_more.py: 0%
  - config/migrations/0010_remove_historicalsystemconfig_email_use_ssl_and_more.py: 0%
  - config/models.py: 0%
  - config/serializers.py: 0%
  - config/signals.py: 0%
  - config/urls.py: 0%
  - config/urls_api.py: 0%
  - config/urls_app.py: 0%
  - config/views.py: 0%
  - core/__init__.py: 0%
  - core/admin.py: 0%
  - core/asgi.py: 0%
  - core/cache.py: 0%
  - core/celery.py: 0%
  - core/middleware.py: 0%
  - core/models.py: 0%
  - core/monitoring.py: 0%
  - core/settings.py: 0%
  - core/urls.py: 0%
  - core/utils/api_utils.py: 0%
  - core/utils/cache_utils.py: 0%
  - core/utils/ip_utils.py: 0%
  - core/utils/messages.py: 0%
  - core/wsgi.py: 0%
  - django_api_test.py: 0%
  - geo/admin.py: 0%
  - geo/apps.py: 0%
  - geo/management/commands/import_geo_data.py: 0%
  - geo/migrations/0001_initial.py: 0%
  - geo/models.py: 0%
  - geo/serializers.py: 0%
  - geo/urls.py: 0%
  - geo/views.py: 0%
  - message_center/admin.py: 0%
  - message_center/apps.py: 0%
  - message_center/migrations/0001_initial.py: 0%
  - message_center/migrations/0002_alter_historicalmessagetemplate_trigger_and_more.py: 0%
  - message_center/migrations/0003_alter_historicalmessagetemplate_options_and_more.py: 0%
  - message_center/migrations/0004_alter_bulkmessage_options_and_more.py: 0%
  - message_center/migrations/0005_alter_message_options.py: 0%
  - message_center/migrations/0006_alter_historicalmessagetemplate_content_and_more.py: 0%
  - message_center/models.py: 0%
  - message_center/serializers.py: 0%
  - message_center/services.py: 0%
  - message_center/urls.py: 0%
  - message_center/urls_app.py: 0%
  - message_center/urls_email_app.py: 0%
  - message_center/views.py: 0%
  - message_center/views_email_app.py: 0%
  - rewards/admin.py: 0%
  - rewards/apps.py: 0%
  - rewards/cache.py: 0%
  - rewards/management/commands/create_missing_transaction_records.py: 0%
  - rewards/management/commands/create_test_data.py: 0%
  - rewards/management/commands/fix_transaction_ids.py: 0%
  - rewards/migrations/0001_initial.py: 0%
  - rewards/migrations/0002_initial.py: 0%
  - rewards/migrations/0003_alter_reward_description.py: 0%
  - rewards/migrations/0004_exchangerecord_order_number.py: 0%
  - rewards/migrations/0005_exchangerecord_address_city_and_more.py: 0%
  - rewards/migrations/0006_auto_20250303_1533.py: 0%
  - rewards/migrations/0007_exchangerecord_user_address_and_more.py: 0%
  - rewards/migrations/0008_alter_exchangerecord_swmt_fee_and_more.py: 0%
  - rewards/migrations/0009_exchangerecord_address_postal_code.py: 0%
  - rewards/migrations/0010_exchangerecord_transaction_id_and_more.py: 0%
  - rewards/migrations/0011_exchangerecord_shipping_remark.py: 0%
  - rewards/migrations/0012_alter_exchangerecord_shipping_remark.py: 0%
  - rewards/migrations/0013_exchangerecord_received_remark.py: 0%
  - rewards/migrations/0014_alter_exchangerecord_status.py: 0%
  - rewards/models.py: 0%
  - rewards/security.py: 0%
  - rewards/serializers.py: 0%
  - rewards/signals.py: 0%
  - rewards/urls.py: 0%
  - rewards/urls_app.py: 0%
  - rewards/views.py: 0%
  - script_api_test.py: 0%
  - stats/admin.py: 0%
  - stats/apps.py: 0%
  - stats/migrations/0001_initial.py: 0%
  - stats/models.py: 0%
  - stats/tasks.py: 0%
  - stats/urls.py: 0%
  - stats/views.py: 0%
  - tasks/admin.py: 0%
  - tasks/app_views.py: 0%
  - tasks/apps.py: 0%
  - tasks/cache.py: 0%
  - tasks/data_monitoring_view.py: 0%
  - tasks/exceptions.py: 0%
  - tasks/forms.py: 0%
  - tasks/management/commands/check_tasks.py: 0%
  - tasks/management/commands/create_task_completions.py: 0%
  - tasks/management/commands/create_test_data.py: 0%
  - tasks/management/commands/generate_task_ids.py: 0%
  - tasks/management/commands/migrate_task_data.py: 0%
  - tasks/management/commands/remove_duplicate_logs.py: 0%
  - tasks/management/commands/reset_user_tasks.py: 0%
  - tasks/management/commands/task_summary.py: 0%
  - tasks/metrics.py: 0%
  - tasks/migrations/0001_initial.py: 0%
  - tasks/migrations/0002_alter_task_description.py: 0%
  - tasks/migrations/0003_alter_task_description.py: 0%
  - tasks/migrations/0004_alter_task_description.py: 0%
  - tasks/migrations/0005_alter_task_description_alter_task_member_levels.py: 0%
  - tasks/migrations/0006_task_created_by_task_updated_by.py: 0%
  - tasks/migrations/0007_task_priority_usertask_assignment_date_and_more.py: 0%
  - tasks/migrations/0008_alter_task_options_alter_task_priority.py: 0%
  - tasks/migrations/0009_task_daily_limit_alter_task_weight.py: 0%
  - tasks/migrations/0010_alter_task_exp_multiplier_alter_task_swmt_multiplier.py: 0%
  - tasks/migrations/0011_alter_task_required_distance.py: 0%
  - tasks/migrations/0012_alter_usertask_additional_bonus_swmt_and_more.py: 0%
  - tasks/migrations/0013_add_indexes_and_constraints.py: 0%
  - tasks/migrations/0014_suspicioushealthdata.py: 0%
  - tasks/migrations/0015_healthdataconfig_healthdata.py: 0%
  - tasks/migrations/0016_alter_task_max_exp_reward_alter_task_max_swmt_reward_and_more.py: 0%
  - tasks/migrations/0017_viplevel_taskresetfailure.py: 0%
  - tasks/migrations/0018_delete_viplevel.py: 0%
  - tasks/migrations/0019_alter_taskcompletionlog_options_and_more.py: 0%
  - tasks/migrations/0020_taskcompletionlog_task_id.py: 0%
  - tasks/migrations/0021_healthdataverificationlog_and_more.py: 0%
  - tasks/migrations/0022_taskcompletionlog_meta_data.py: 0%
  - tasks/migrations/0023_rewardsettlement.py: 0%
  - tasks/migrations/0024_alter_healthdata_options_alter_task_options_and_more.py: 0%
  - tasks/migrations/0025_rename_user_task_taskcompletionlog_user_tasks.py: 0%
  - tasks/migrations/0026_usertask_failure_reason_and_more.py: 0%
  - tasks/migrations/0027_usertask_error_category_and_more.py: 0%
  - tasks/migrations/0028_alter_task_task_type.py: 0%
  - tasks/migrations/0029_usertask_assignment_date_alter_task_task_type.py: 0%
  - tasks/migrations/0030_task_daily_limit_task_priority.py: 0%
  - tasks/migrations/0031_usertask_is_settled_usertask_settlement_date.py: 0%
  - tasks/models.py: 0%
  - tasks/resources.py: 0%
  - tasks/serializers.py: 0%
  - tasks/services/__init__.py: 0%
  - tasks/services/alert.py: 0%
  - tasks/services/anti_fraud.py: 0%
  - tasks/services/compatibility.py: 0%
  - tasks/services/completion_data_service.py: 0%
  - tasks/services/health_data_normalizer.py: 0%
  - tasks/services/health_data_providers.py: 0%
  - tasks/services/health_data_sync.py: 0%
  - tasks/services/health_data_verification.py: 0%
  - tasks/services/health_data_verification_enhanced.py: 0%
  - tasks/services/mock_health_providers.py: 0%
  - tasks/services/reward_calculation.py: 0%
  - tasks/services/reward_calculation_fix.py: 0%
  - tasks/services/reward_settlement.py: 0%
  - tasks/services/task_assignment.py: 0%
  - tasks/services/task_cache.py: 0%
  - tasks/services/task_completion.py: 0%
  - tasks/services/task_monitoring.py: 0%
  - tasks/services/task_reset.py: 0%
  - tasks/services/task_validation.py: 0%
  - tasks/signals.py: 0%
  - tasks/tasks.py: 0%
  - tasks/tasks/__init__.py: 0%
  - tasks/tasks/cache_monitoring.py: 0%
  - tasks/tasks/daily_reset.py: 0%
  - tasks/tasks/member_status.py: 0%
  - tasks/tests.py: 0%
  - tasks/urls.py: 0%
  - tasks/urls_app.py: 0%
  - tasks/utils.py: 0%
  - tasks/views.py: 0%
  - users/admin.py: 0%
  - users/apps.py: 0%
  - users/forms.py: 0%
  - users/migrations/0001_initial.py: 0%
  - users/migrations/0002_memberlevel_remove_user_usdt_address_user_agent_id_and_more.py: 0%
  - users/migrations/0003_generate_user_ids.py: 0%
  - users/migrations/0004_alter_user_user_id.py: 0%
  - users/migrations/0005_user_last_active_time_user_referral_code_and_more.py: 0%
  - users/migrations/0006_auto_20250213_1137.py: 0%
  - users/migrations/0007_alter_useraddress_city_alter_useraddress_country_and_more.py: 0%
  - users/migrations/0008_add_recipient_info_to_address.py: 0%
  - users/migrations/0009_make_city_optional.py: 0%
  - users/migrations/0010_user_addresses.py: 0%
  - users/migrations/0011_alter_useraddress_options_remove_user_addresses_and_more.py: 0%
  - users/migrations/0012_alter_useraddress_options_and_more.py: 0%
  - users/migrations/0013_alter_withdrawaladdress_unique_together.py: 0%
  - users/migrations/0014_alter_withdrawaladdress_unique_together.py: 0%
  - users/migrations/0015_user_referrer.py: 0%
  - users/migrations/0016_alter_user_agent_id.py: 0%
  - users/migrations/0017_useraddress_postal_code.py: 0%
  - users/migrations/0018_alter_useraddress_postal_code.py: 0%
  - users/models.py: 0%
  - users/serializers.py: 0%
  - users/serializers_app.py: 0%
  - users/signals.py: 0%
  - users/urls.py: 0%
  - users/urls_app.py: 0%
  - users/urls_profile.py: 0%
  - users/utils.py: 0%
  - users/views.py: 0%
  - users/views_app.py: 0%
  - vip/__init__.py: 0%
  - vip/admin.py: 0%
  - vip/app_serializers.py: 0%
  - vip/app_views.py: 0%
  - vip/apps.py: 0%
  - vip/migrations/0001_initial.py: 0%
  - vip/migrations/0002_uservip_is_refund_active_uservip_refund_progress_and_more.py: 0%
  - vip/migrations/0003_vipoperationlog.py: 0%
  - vip/models.py: 0%
  - vip/serializers.py: 0%
  - vip/services.py: 0%
  - vip/tasks.py: 0%
  - vip/urls.py: 0%
  - vip/urls_app.py: 0%
  - vip/utils.py: 0%
  - vip/views.py: 0%
  - wallet/admin.py: 0%
  - wallet/app_views.py: 0%
  - wallet/apps.py: 0%
  - wallet/management/commands/archive_transactions.py: 0%
  - wallet/management/commands/generate_transaction_ids.py: 0%
  - wallet/management/commands/update_transaction_balance.py: 0%
  - wallet/migrations/0001_initial.py: 0%
  - wallet/migrations/0002_remove_collectionbatch_end_time_and_more.py: 0%
  - wallet/migrations/0003_transactionrecord.py: 0%
  - wallet/migrations/0004_depositrecord_transaction_id_and_more.py: 0%
  - wallet/migrations/0005_transactionrecord_balance_after_and_more.py: 0%
  - wallet/migrations/0006_archivedtransactionrecord_and_more.py: 0%
  - wallet/models.py: 0%
  - wallet/serializers.py: 0%
  - wallet/signals.py: 0%
  - wallet/urls.py: 0%
  - wallet/urls_app.py: 0%
  - wallet/utils.py: 0%
  - wallet/views.py: 0%

============= 问题分析汇总 =============

未发现系统代码问题

未发现测试代码问题

未发现性能问题

未发现稳定性问题

覆盖率问题:
1. 总体覆盖率过低: 1%
   建议: 建议增加更多测试用例，提高覆盖率

2. 模块 agents/__init__.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

3. 模块 agents/admin.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

4. 模块 agents/app_serializers.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

5. 模块 agents/app_views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

6. 模块 agents/apps.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

7. 模块 agents/management/commands/fix_agent.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

8. 模块 agents/migrations/0001_initial.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

9. 模块 agents/migrations/0002_initial.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

10. 模块 agents/migrations/0003_agentrelationship_settlement_restored_at_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

11. 模块 agents/migrations/0004_settlementwarning.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

12. 模块 agents/migrations/0005_create_settlement_warning.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

13. 模块 agents/migrations/0006_add_processed_at_field.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

14. 模块 agents/models.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

15. 模块 agents/serializers.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

16. 模块 agents/services/__init__.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

17. 模块 agents/services/agent_service.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

18. 模块 agents/services/commission_service.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

19. 模块 agents/services/notification_service.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

20. 模块 agents/services/warning_service.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

21. 模块 agents/tasks.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

22. 模块 agents/urls.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

23. 模块 agents/urls_app.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

24. 模块 agents/urls_warning_redirect.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

25. 模块 agents/utils.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

26. 模块 agents/views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

27. 模块 authentication/admin.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

28. 模块 authentication/app_views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

29. 模块 authentication/apps.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

30. 模块 authentication/backends.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

31. 模块 authentication/middleware.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

32. 模块 authentication/migrations/0001_initial.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

33. 模块 authentication/migrations/0002_historicaltokenblacklist_device_info_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

34. 模块 authentication/migrations/0003_historicalusertoken_token_type_usertoken_token_type_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

35. 模块 authentication/migrations/0004_alter_historicaltokenblacklist_token_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

36. 模块 authentication/models.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

37. 模块 authentication/ratelimit.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

38. 模块 authentication/tasks.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

39. 模块 authentication/urls.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

40. 模块 authentication/urls_app.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

41. 模块 authentication/utils.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

42. 模块 authentication/views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

43. 模块 authentication/views_app.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

44. 模块 check_user_tasks.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

45. 模块 clean_task_records.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

46. 模块 config/admin.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

47. 模块 config/apps.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

48. 模块 config/migrations/0001_initial.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

49. 模块 config/migrations/0002_initial.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

50. 模块 config/migrations/0003_historicalnetwork_network_type_network_network_type_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

51. 模块 config/migrations/0004_alter_historicalnetwork_gas_fee_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

52. 模块 config/migrations/0005_alter_historicalnetwork_network_type_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

53. 模块 config/migrations/0006_historicalsystemconfig_google_fit_api_key_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

54. 模块 config/migrations/0007_alter_historicalsystemconfig_google_fit_api_key_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

55. 模块 config/migrations/0008_alter_systemconfig_options.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

56. 模块 config/migrations/0009_historicalsystemconfig_email_from_address_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

57. 模块 config/migrations/0010_remove_historicalsystemconfig_email_use_ssl_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

58. 模块 config/models.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

59. 模块 config/serializers.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

60. 模块 config/signals.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

61. 模块 config/urls.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

62. 模块 config/urls_api.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

63. 模块 config/urls_app.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

64. 模块 config/views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

65. 模块 core/__init__.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

66. 模块 core/admin.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

67. 模块 core/asgi.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

68. 模块 core/cache.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

69. 模块 core/celery.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

70. 模块 core/middleware.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

71. 模块 core/models.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

72. 模块 core/monitoring.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

73. 模块 core/settings.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

74. 模块 core/urls.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

75. 模块 core/utils/api_utils.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

76. 模块 core/utils/cache_utils.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

77. 模块 core/utils/ip_utils.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

78. 模块 core/utils/messages.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

79. 模块 core/wsgi.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

80. 模块 django_api_test.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

81. 模块 geo/admin.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

82. 模块 geo/apps.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

83. 模块 geo/management/commands/import_geo_data.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

84. 模块 geo/migrations/0001_initial.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

85. 模块 geo/models.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

86. 模块 geo/serializers.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

87. 模块 geo/urls.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

88. 模块 geo/views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

89. 模块 message_center/admin.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

90. 模块 message_center/apps.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

91. 模块 message_center/migrations/0001_initial.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

92. 模块 message_center/migrations/0002_alter_historicalmessagetemplate_trigger_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

93. 模块 message_center/migrations/0003_alter_historicalmessagetemplate_options_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

94. 模块 message_center/migrations/0004_alter_bulkmessage_options_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

95. 模块 message_center/migrations/0005_alter_message_options.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

96. 模块 message_center/migrations/0006_alter_historicalmessagetemplate_content_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

97. 模块 message_center/models.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

98. 模块 message_center/serializers.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

99. 模块 message_center/services.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

100. 模块 message_center/urls.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

101. 模块 message_center/urls_app.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

102. 模块 message_center/urls_email_app.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

103. 模块 message_center/views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

104. 模块 message_center/views_email_app.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

105. 模块 rewards/admin.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

106. 模块 rewards/apps.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

107. 模块 rewards/cache.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

108. 模块 rewards/management/commands/create_missing_transaction_records.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

109. 模块 rewards/management/commands/create_test_data.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

110. 模块 rewards/management/commands/fix_transaction_ids.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

111. 模块 rewards/migrations/0001_initial.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

112. 模块 rewards/migrations/0002_initial.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

113. 模块 rewards/migrations/0003_alter_reward_description.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

114. 模块 rewards/migrations/0004_exchangerecord_order_number.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

115. 模块 rewards/migrations/0005_exchangerecord_address_city_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

116. 模块 rewards/migrations/0006_auto_20250303_1533.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

117. 模块 rewards/migrations/0007_exchangerecord_user_address_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

118. 模块 rewards/migrations/0008_alter_exchangerecord_swmt_fee_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

119. 模块 rewards/migrations/0009_exchangerecord_address_postal_code.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

120. 模块 rewards/migrations/0010_exchangerecord_transaction_id_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

121. 模块 rewards/migrations/0011_exchangerecord_shipping_remark.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

122. 模块 rewards/migrations/0012_alter_exchangerecord_shipping_remark.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

123. 模块 rewards/migrations/0013_exchangerecord_received_remark.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

124. 模块 rewards/migrations/0014_alter_exchangerecord_status.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

125. 模块 rewards/models.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

126. 模块 rewards/security.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

127. 模块 rewards/serializers.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

128. 模块 rewards/signals.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

129. 模块 rewards/urls.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

130. 模块 rewards/urls_app.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

131. 模块 rewards/views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

132. 模块 script_api_test.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

133. 模块 stats/admin.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

134. 模块 stats/apps.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

135. 模块 stats/migrations/0001_initial.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

136. 模块 stats/models.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

137. 模块 stats/tasks.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

138. 模块 stats/urls.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

139. 模块 stats/views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

140. 模块 tasks/admin.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

141. 模块 tasks/app_views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

142. 模块 tasks/apps.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

143. 模块 tasks/cache.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

144. 模块 tasks/data_monitoring_view.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

145. 模块 tasks/exceptions.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

146. 模块 tasks/forms.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

147. 模块 tasks/management/commands/check_tasks.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

148. 模块 tasks/management/commands/create_task_completions.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

149. 模块 tasks/management/commands/create_test_data.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

150. 模块 tasks/management/commands/generate_task_ids.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

151. 模块 tasks/management/commands/migrate_task_data.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

152. 模块 tasks/management/commands/remove_duplicate_logs.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

153. 模块 tasks/management/commands/reset_user_tasks.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

154. 模块 tasks/management/commands/task_summary.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

155. 模块 tasks/metrics.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

156. 模块 tasks/migrations/0001_initial.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

157. 模块 tasks/migrations/0002_alter_task_description.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

158. 模块 tasks/migrations/0003_alter_task_description.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

159. 模块 tasks/migrations/0004_alter_task_description.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

160. 模块 tasks/migrations/0005_alter_task_description_alter_task_member_levels.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

161. 模块 tasks/migrations/0006_task_created_by_task_updated_by.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

162. 模块 tasks/migrations/0007_task_priority_usertask_assignment_date_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

163. 模块 tasks/migrations/0008_alter_task_options_alter_task_priority.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

164. 模块 tasks/migrations/0009_task_daily_limit_alter_task_weight.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

165. 模块 tasks/migrations/0010_alter_task_exp_multiplier_alter_task_swmt_multiplier.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

166. 模块 tasks/migrations/0011_alter_task_required_distance.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

167. 模块 tasks/migrations/0012_alter_usertask_additional_bonus_swmt_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

168. 模块 tasks/migrations/0013_add_indexes_and_constraints.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

169. 模块 tasks/migrations/0014_suspicioushealthdata.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

170. 模块 tasks/migrations/0015_healthdataconfig_healthdata.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

171. 模块 tasks/migrations/0016_alter_task_max_exp_reward_alter_task_max_swmt_reward_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

172. 模块 tasks/migrations/0017_viplevel_taskresetfailure.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

173. 模块 tasks/migrations/0018_delete_viplevel.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

174. 模块 tasks/migrations/0019_alter_taskcompletionlog_options_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

175. 模块 tasks/migrations/0020_taskcompletionlog_task_id.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

176. 模块 tasks/migrations/0021_healthdataverificationlog_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

177. 模块 tasks/migrations/0022_taskcompletionlog_meta_data.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

178. 模块 tasks/migrations/0023_rewardsettlement.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

179. 模块 tasks/migrations/0024_alter_healthdata_options_alter_task_options_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

180. 模块 tasks/migrations/0025_rename_user_task_taskcompletionlog_user_tasks.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

181. 模块 tasks/migrations/0026_usertask_failure_reason_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

182. 模块 tasks/migrations/0027_usertask_error_category_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

183. 模块 tasks/migrations/0028_alter_task_task_type.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

184. 模块 tasks/migrations/0029_usertask_assignment_date_alter_task_task_type.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

185. 模块 tasks/migrations/0030_task_daily_limit_task_priority.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

186. 模块 tasks/migrations/0031_usertask_is_settled_usertask_settlement_date.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

187. 模块 tasks/models.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

188. 模块 tasks/resources.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

189. 模块 tasks/serializers.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

190. 模块 tasks/services/__init__.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

191. 模块 tasks/services/alert.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

192. 模块 tasks/services/anti_fraud.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

193. 模块 tasks/services/compatibility.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

194. 模块 tasks/services/completion_data_service.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

195. 模块 tasks/services/health_data_normalizer.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

196. 模块 tasks/services/health_data_providers.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

197. 模块 tasks/services/health_data_sync.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

198. 模块 tasks/services/health_data_verification.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

199. 模块 tasks/services/health_data_verification_enhanced.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

200. 模块 tasks/services/mock_health_providers.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

201. 模块 tasks/services/reward_calculation.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

202. 模块 tasks/services/reward_calculation_fix.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

203. 模块 tasks/services/reward_settlement.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

204. 模块 tasks/services/task_assignment.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

205. 模块 tasks/services/task_cache.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

206. 模块 tasks/services/task_completion.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

207. 模块 tasks/services/task_monitoring.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

208. 模块 tasks/services/task_reset.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

209. 模块 tasks/services/task_validation.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

210. 模块 tasks/signals.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

211. 模块 tasks/tasks.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

212. 模块 tasks/tasks/__init__.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

213. 模块 tasks/tasks/cache_monitoring.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

214. 模块 tasks/tasks/daily_reset.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

215. 模块 tasks/tasks/member_status.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

216. 模块 tasks/tests.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

217. 模块 tasks/urls.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

218. 模块 tasks/urls_app.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

219. 模块 tasks/utils.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

220. 模块 tasks/views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

221. 模块 users/admin.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

222. 模块 users/apps.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

223. 模块 users/forms.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

224. 模块 users/migrations/0001_initial.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

225. 模块 users/migrations/0002_memberlevel_remove_user_usdt_address_user_agent_id_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

226. 模块 users/migrations/0003_generate_user_ids.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

227. 模块 users/migrations/0004_alter_user_user_id.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

228. 模块 users/migrations/0005_user_last_active_time_user_referral_code_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

229. 模块 users/migrations/0006_auto_20250213_1137.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

230. 模块 users/migrations/0007_alter_useraddress_city_alter_useraddress_country_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

231. 模块 users/migrations/0008_add_recipient_info_to_address.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

232. 模块 users/migrations/0009_make_city_optional.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

233. 模块 users/migrations/0010_user_addresses.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

234. 模块 users/migrations/0011_alter_useraddress_options_remove_user_addresses_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

235. 模块 users/migrations/0012_alter_useraddress_options_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

236. 模块 users/migrations/0013_alter_withdrawaladdress_unique_together.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

237. 模块 users/migrations/0014_alter_withdrawaladdress_unique_together.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

238. 模块 users/migrations/0015_user_referrer.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

239. 模块 users/migrations/0016_alter_user_agent_id.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

240. 模块 users/migrations/0017_useraddress_postal_code.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

241. 模块 users/migrations/0018_alter_useraddress_postal_code.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

242. 模块 users/models.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

243. 模块 users/serializers.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

244. 模块 users/serializers_app.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

245. 模块 users/signals.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

246. 模块 users/urls.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

247. 模块 users/urls_app.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

248. 模块 users/urls_profile.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

249. 模块 users/utils.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

250. 模块 users/views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

251. 模块 users/views_app.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

252. 模块 vip/__init__.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

253. 模块 vip/admin.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

254. 模块 vip/app_serializers.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

255. 模块 vip/app_views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

256. 模块 vip/apps.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

257. 模块 vip/migrations/0001_initial.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

258. 模块 vip/migrations/0002_uservip_is_refund_active_uservip_refund_progress_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

259. 模块 vip/migrations/0003_vipoperationlog.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

260. 模块 vip/models.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

261. 模块 vip/serializers.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

262. 模块 vip/services.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

263. 模块 vip/tasks.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

264. 模块 vip/urls.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

265. 模块 vip/urls_app.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

266. 模块 vip/utils.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

267. 模块 vip/views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

268. 模块 wallet/admin.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

269. 模块 wallet/app_views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

270. 模块 wallet/apps.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

271. 模块 wallet/management/commands/archive_transactions.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

272. 模块 wallet/management/commands/generate_transaction_ids.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

273. 模块 wallet/management/commands/update_transaction_balance.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

274. 模块 wallet/migrations/0001_initial.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

275. 模块 wallet/migrations/0002_remove_collectionbatch_end_time_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

276. 模块 wallet/migrations/0003_transactionrecord.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

277. 模块 wallet/migrations/0004_depositrecord_transaction_id_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

278. 模块 wallet/migrations/0005_transactionrecord_balance_after_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

279. 模块 wallet/migrations/0006_archivedtransactionrecord_and_more.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

280. 模块 wallet/models.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

281. 模块 wallet/serializers.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

282. 模块 wallet/signals.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

283. 模块 wallet/urls.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

284. 模块 wallet/urls_app.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

285. 模块 wallet/utils.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例

286. 模块 wallet/views.py 覆盖率过低: 0%
   建议: 建议为该模块添加更多测试用例


============= 改进建议 =============
5. 覆盖率问题 (286 个): 测试覆盖率不足，需要增加测试用例


测试结果: 失败 ❌
请查看测试报告分析结果，修复相关问题。

测试完整日志文件: /Users/<USER>/Documents/worker/sweatmint/running/tests/reports/test_run_20250417_005529.log
