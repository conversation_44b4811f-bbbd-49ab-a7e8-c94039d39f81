INFO 2025-07-23 12:29:46,535 [authentication.backends:128] JWT访问记录 - 用户ID: 473, 用户名: test_user3, IP: ***************, 路径: /api/app/v1/dashboard/, Token: eyJhbGciOiJIUzI1..., 时间: 2025-07-23T04:29:46.535925+00:00
INFO 2025-07-23 12:29:46,545 [authentication.backends:128] JWT访问记录 - 用户ID: 473, 用户名: test_user3, IP: ***************, 路径: /api/app/v1/dashboard/, Token: eyJhbGciOiJIUzI1..., 时间: 2025-07-23T04:29:46.545564+00:00
INFO 2025-07-23 12:29:46,583 [authentication.middleware:420] API请求处理完成: type=app, user_id=473, method=GET, path=/api/app/v1/dashboard/, ip=***************, status=200
INFO 2025-07-23 12:29:48,043 [authentication.backends:128] JWT访问记录 - 用户ID: 473, 用户名: test_user3, IP: ***************, 路径: /api/app/v1/health/session/init/, Token: eyJhbGciOiJIUzI1..., 时间: 2025-07-23T04:29:48.042998+00:00
INFO 2025-07-23 12:29:48,052 [authentication.middleware:420] API请求处理完成: type=app, user_id=473, method=POST, path=/api/app/v1/health/session/init/, ip=***************, status=200
INFO 2025-07-23 12:29:48,183 [authentication.backends:128] JWT访问记录 - 用户ID: 473, 用户名: test_user3, IP: ***************, 路径: /api/app/v1/health/sync/, Token: eyJhbGciOiJIUzI1..., 时间: 2025-07-23T04:29:48.183453+00:00
INFO 2025-07-23 12:29:48,203 [authentication.middleware:420] API请求处理完成: type=app, user_id=473, method=POST, path=/api/app/v1/health/sync/, ip=***************, status=200
INFO 2025-07-23 12:29:49,094 [authentication.middleware:420] API请求处理完成: type=app, user_id=anonymous, method=GET, path=/media/avatars/avatar_3.png, ip=***************, status=200
INFO 2025-07-23 12:30:25,162 [authentication.backends:128] JWT访问记录 - 用户ID: 473, 用户名: test_user3, IP: ***************, 路径: /api/app/v1/health/session/init/, Token: eyJhbGciOiJIUzI1..., 时间: 2025-07-23T04:30:25.162815+00:00
INFO 2025-07-23 12:30:25,170 [authentication.middleware:420] API请求处理完成: type=app, user_id=473, method=POST, path=/api/app/v1/health/session/init/, ip=***************, status=200
INFO 2025-07-23 12:30:25,353 [authentication.backends:128] JWT访问记录 - 用户ID: 473, 用户名: test_user3, IP: ***************, 路径: /api/app/v1/health/sync/, Token: eyJhbGciOiJIUzI1..., 时间: 2025-07-23T04:30:25.353419+00:00
INFO 2025-07-23 12:30:25,372 [authentication.middleware:420] API请求处理完成: type=app, user_id=473, method=POST, path=/api/app/v1/health/sync/, ip=***************, status=200
INFO 2025-07-23 12:30:26,225 [authentication.middleware:420] API请求处理完成: type=app, user_id=anonymous, method=GET, path=/media/avatars/avatar_3.png, ip=***************, status=200
INFO 2025-07-23 12:32:23,357 [authentication.views_app:1112] Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3
INFO 2025-07-23 12:32:23,357 [authentication.middleware:420] API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200
INFO 2025-07-23 12:40:23,302 [authentication.views_app:1112] Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3
INFO 2025-07-23 12:40:23,304 [authentication.middleware:420] API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200
INFO 2025-07-23 12:48:23,434 [authentication.views_app:1112] Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3
INFO 2025-07-23 12:48:23,436 [authentication.middleware:420] API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200
INFO 2025-07-23 12:56:23,253 [authentication.views_app:1112] Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3
INFO 2025-07-23 12:56:23,253 [authentication.middleware:420] API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200
INFO 2025-07-23 13:04:23,313 [authentication.views_app:1112] Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3
INFO 2025-07-23 13:04:23,315 [authentication.middleware:420] API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200
INFO 2025-07-23 13:12:23,267 [authentication.views_app:1112] Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3
INFO 2025-07-23 13:12:23,268 [authentication.middleware:420] API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200
INFO 2025-07-23 13:20:23,260 [authentication.views_app:1112] Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3
INFO 2025-07-23 13:20:23,260 [authentication.middleware:420] API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200
INFO 2025-07-23 13:28:23,230 [authentication.views_app:1112] Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3
INFO 2025-07-23 13:28:23,230 [authentication.middleware:420] API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200
INFO 2025-07-23 13:36:23,236 [authentication.views_app:1112] Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3
INFO 2025-07-23 13:36:23,237 [authentication.middleware:420] API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200
