INFO 2025-07-23 12:29:46,498 views 50317 8544591616 Tasks API module initialized at 2025-07-23T12:29:46.498926
INFO 2025-07-23 12:29:46,537 app_views 50317 ********** 开始为用户 <EMAIL> (ID: 473) 检查每日任务分配
INFO 2025-07-23 12:29:46,556 task_assignment 50317 ********** 开始为用户 <EMAIL> (原始: <EMAIL>) 分配每日任务
INFO 2025-07-23 12:29:46,556 app_views 50317 ********** 开始为用户 <EMAIL> (ID: 473) 检查附加任务分配
INFO 2025-07-23 12:29:46,557 app_views 50317 ********** 为用户 473 生成新的today_summary缓存，日期：2025-07-23
INFO 2025-07-23 12:29:46,557 app_views 50317 ********** 计算用户 473 今日收益，日期: 2025-07-23
INFO 2025-07-23 12:29:46,560 app_views 50317 ********** 用户 <EMAIL> 当天已有附加任务: True
INFO 2025-07-23 12:29:46,560 app_views 50317 ********** 用户 <EMAIL> 已有附加任务，无需重新分配
INFO 2025-07-23 12:29:46,561 app_views 50317 ********** 用户 473 今日没有已完成的 UserTask 记录
INFO 2025-07-23 12:29:46,561 app_views 50317 ********** 用户 473 今日收益计算完成: SWMT=0.00, XP=0
INFO 2025-07-23 12:29:46,563 task_assignment 50317 ********** 用户 <EMAIL> 今天已经分配了 4 个每日任务
INFO 2025-07-23 12:29:46,565 app_views 50317 ********** 已为用户 <EMAIL> (ID: 473) 分配新的每日任务。
INFO 2025-07-23 12:29:46,566 app_views 50317 ********** 为用户 <EMAIL> 查询并缓存已排序附加任务列表 (缓存key: addon_tasks_sorted_473_2025-07-23)
INFO 2025-07-23 12:29:46,567 app_views 50317 ********** 设置用户 473 的today_summary缓存，过期时间：180秒
WARNING 2025-07-23 12:29:48,187 health_data_verification 50317 ********** 未找到 HealthDataConfig 配置，将使用默认值。
INFO 2025-07-23 12:29:48,187 health_data_verification 50317 ********** 🔥 开始验证健康数据 - 用户: <EMAIL>
INFO 2025-07-23 12:29:48,187 health_data_verification 50317 ********** 开始执行多层验证 - 用户: <EMAIL>
INFO 2025-07-23 12:29:48,187 health_data_verification 50317 ********** 步数验证 - 用户: <EMAIL>, steps值: 0, 类型: <class 'int'>
INFO 2025-07-23 12:29:48,189 health_data_verification 50317 ********** 开始数据范围验证 - 用户: <EMAIL>
INFO 2025-07-23 12:29:48,189 health_data_verification 50317 ********** 用户 <EMAIL> 数据范围验证通过
INFO 2025-07-23 12:29:48,190 health_data_verification 50317 ********** 🔥 用户 <EMAIL> 健康数据验证通过 - 验证级别: comprehensive
INFO 2025-07-23 12:29:48,194 health_data_verification 50317 ********** 开始验证用户 <EMAIL> 的健康数据增量: {'steps': 0, 'distance': None, 'calories': None}
WARNING 2025-07-23 12:29:48,194 health_data_verification 50317 ********** 未找到 HealthDataConfig 配置，使用默认阈值进行验证
INFO 2025-07-23 12:29:48,194 health_data_verification 50317 ********** 用户 <EMAIL> 健康数据增量验证通过
WARNING 2025-07-23 12:30:25,357 health_data_verification 50317 ********** 未找到 HealthDataConfig 配置，将使用默认值。
INFO 2025-07-23 12:30:25,357 health_data_verification 50317 ********** 🔥 开始验证健康数据 - 用户: <EMAIL>
INFO 2025-07-23 12:30:25,357 health_data_verification 50317 ********** 开始执行多层验证 - 用户: <EMAIL>
INFO 2025-07-23 12:30:25,357 health_data_verification 50317 ********** 步数验证 - 用户: <EMAIL>, steps值: 0, 类型: <class 'int'>
INFO 2025-07-23 12:30:25,359 health_data_verification 50317 ********** 开始数据范围验证 - 用户: <EMAIL>
INFO 2025-07-23 12:30:25,359 health_data_verification 50317 ********** 用户 <EMAIL> 数据范围验证通过
INFO 2025-07-23 12:30:25,360 health_data_verification 50317 ********** 🔥 用户 <EMAIL> 健康数据验证通过 - 验证级别: comprehensive
INFO 2025-07-23 12:30:25,363 health_data_verification 50317 ********** 开始验证用户 <EMAIL> 的健康数据增量: {'steps': 0, 'distance': None, 'calories': None}
WARNING 2025-07-23 12:30:25,364 health_data_verification 50317 ********** 未找到 HealthDataConfig 配置，使用默认阈值进行验证
INFO 2025-07-23 12:30:25,364 health_data_verification 50317 ********** 用户 <EMAIL> 健康数据增量验证通过
