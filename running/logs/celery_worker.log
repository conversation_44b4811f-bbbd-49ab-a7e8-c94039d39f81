ERROR 2025-07-02 11:40:00,028 consumer 12331 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '30c68f8e-52f2-4568-bc08-8c962ee6ab6c', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '30c68f8e-52f2-4568-bc08-8c962ee6ab6c', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 11:42:00,003 consumer 12331 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '3932e103-5075-487a-a06f-cb1b3236823e', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '3932e103-5075-487a-a06f-cb1b3236823e', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 11:44:00,007 consumer 12331 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '1d899c12-637c-40b7-ad4c-fe56e407ab6c', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '1d899c12-637c-40b7-ad4c-fe56e407ab6c', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 11:46:00,008 consumer 12331 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'be66f313-c86f-4de0-8cfd-62aa4edbbf6c', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'be66f313-c86f-4de0-8cfd-62aa4edbbf6c', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 11:48:00,010 consumer 12331 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'f03a52f1-2fa1-4cfa-9e87-10bdd5da2588', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'f03a52f1-2fa1-4cfa-9e87-10bdd5da2588', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 11:50:00,003 consumer 12331 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '886de79d-d39d-4d93-a93d-5e6ac5fb6075', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '886de79d-d39d-4d93-a93d-5e6ac5fb6075', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 11:52:00,014 consumer 12331 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'f7c2aaa4-096b-4646-a5d7-b9546a2d1967', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'f7c2aaa4-096b-4646-a5d7-b9546a2d1967', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 11:54:00,008 consumer 12331 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '6796f7c6-3d65-4337-b56d-182ff7f6fb87', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '6796f7c6-3d65-4337-b56d-182ff7f6fb87', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 11:56:00,003 consumer 12331 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'e65fb75e-799b-4130-94ed-45a82a8a4039', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'e65fb75e-799b-4130-94ed-45a82a8a4039', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 11:58:00,006 consumer 12331 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'f80a6a84-ca85-4348-a519-2baf5524fccd', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'f80a6a84-ca85-4348-a519-2baf5524fccd', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:00:00,776 consumer 12331 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'a6265689-e750-4261-ac8d-d65687379ed3', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'a6265689-e750-4261-ac8d-d65687379ed3', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:02:00,018 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'ee70c1d5-461c-4397-958f-8b3bac91cabe', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'ee70c1d5-461c-4397-958f-8b3bac91cabe', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:04:00,008 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '1d9b61db-7571-455f-b14c-d7945d4838ee', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '1d9b61db-7571-455f-b14c-d7945d4838ee', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:06:00,008 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'fc15ccee-6d37-4e99-8a25-0c9a1c397e30', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'fc15ccee-6d37-4e99-8a25-0c9a1c397e30', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:08:00,001 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '111a2415-6b5c-42d1-8fb6-fbbc9fa44b96', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '111a2415-6b5c-42d1-8fb6-fbbc9fa44b96', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:10:00,007 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '4a579f34-d3d2-42b4-be7e-5d5a1a21b6d7', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '4a579f34-d3d2-42b4-be7e-5d5a1a21b6d7', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:12:00,010 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '3bfed2ac-ff36-4c7c-ab3c-a466d9bc46b0', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '3bfed2ac-ff36-4c7c-ab3c-a466d9bc46b0', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:14:00,013 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '50b3489b-cbec-4d9a-9933-54a5499392eb', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '50b3489b-cbec-4d9a-9933-54a5499392eb', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:16:00,009 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'c29a0f2a-5d75-4421-83f8-dbb5239fdc34', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'c29a0f2a-5d75-4421-83f8-dbb5239fdc34', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:18:00,011 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'd2fc5f16-af99-4071-add1-ad87c00c661b', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'd2fc5f16-af99-4071-add1-ad87c00c661b', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:20:00,004 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'e82ba3fd-f81b-4405-b621-c6f8e7159c71', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'e82ba3fd-f81b-4405-b621-c6f8e7159c71', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:22:00,006 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'f65111a9-57d0-405a-a08d-5cd57064f4e6', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'f65111a9-57d0-405a-a08d-5cd57064f4e6', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:24:00,003 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'f297069e-8fc6-4efe-9513-e7b9e4c4733e', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'f297069e-8fc6-4efe-9513-e7b9e4c4733e', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:26:00,007 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '86ccc404-73cc-45b8-9fde-90c20f8042d0', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '86ccc404-73cc-45b8-9fde-90c20f8042d0', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:28:00,009 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'bdb4d4d4-7554-4ee9-9b58-21d1add05375', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'bdb4d4d4-7554-4ee9-9b58-21d1add05375', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:30:00,719 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '2cb4fbe7-1ba2-4691-a34d-05a2e11b3a04', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '2cb4fbe7-1ba2-4691-a34d-05a2e11b3a04', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:32:00,008 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '05f89806-429e-493b-b014-fb90002cbcfe', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '05f89806-429e-493b-b014-fb90002cbcfe', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:34:00,014 consumer 13253 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '1559bba7-399d-45f3-a4a0-d6cff907c7c3', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '1559bba7-399d-45f3-a4a0-d6cff907c7c3', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:36:00,017 consumer 15048 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '1d2eab85-fa20-40a9-b21f-5493b59e1286', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '1d2eab85-fa20-40a9-b21f-5493b59e1286', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:38:00,011 consumer 15048 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'c56f85af-bcbd-4b88-921a-8131faf417f4', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'c56f85af-bcbd-4b88-921a-8131faf417f4', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:40:00,006 consumer 15048 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '3d04f77b-3ba2-4dab-856d-1c526d9f54f7', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '3d04f77b-3ba2-4dab-856d-1c526d9f54f7', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:42:00,016 consumer 15647 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': 'f8d0a1f7-d09a-4dd6-b208-be3f52fa4cc7', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'f8d0a1f7-d09a-4dd6-b208-be3f52fa4cc7', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:56:52,992 consumer 16704 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '620ecab8-2a3b-494b-b90c-82475c418357', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '620ecab8-2a3b-494b-b90c-82475c418357', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 12:58:00,010 consumer 16704 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '2da5c6e1-e9c7-4c2f-8152-29c78737a32f', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '2da5c6e1-e9c7-4c2f-8152-29c78737a32f', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 13:00:00,046 consumer 17056 ********** Received unregistered task of type 'offline_health_requests_replay'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'offline_health_requests_replay', 'id': '0d49aa62-eac4-42f3-a6f7-6f652f3f8eb7', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '0d49aa62-eac4-42f3-a6f7-6f652f3f8eb7', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'offline_health_requests_replay'
ERROR 2025-07-02 22:39:23,508 consumer 33534 ********** Received unregistered task of type 'core.celery_debug_task'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'core.celery_debug_task', 'id': '890f58a5-2368-41ab-a5f2-747349589ce7', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '890f58a5-2368-41ab-a5f2-747349589ce7', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'core.celery_debug_task'
ERROR 2025-07-02 23:35:49,372 consumer 36233 ********** Received unregistered task of type 'agents.tasks.check_pending_settlements [rate_limit=100/m]'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'agents.tasks.check_pending_settlements [rate_limit=100/m]', 'id': 'aa8637e1-811a-4a31-b5e3-737437ba1ff0', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'aa8637e1-811a-4a31-b5e3-737437ba1ff0', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'agents.tasks.check_pending_settlements [rate_limit=100/m]'
ERROR 2025-07-02 23:42:02,491 consumer 36515 ********** Received unregistered task of type 'nonexistent.task.test'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'nonexistent.task.test', 'id': 'f8fd4ab8-52ab-48ff-a357-87db8efa95b6', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': 'f8fd4ab8-52ab-48ff-a357-87db8efa95b6', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'nonexistent.task.test'
[2025-07-03 00:48:40,044: ERROR/MainProcess] Process 'ForkPoolWorker-1' pid:49668 exited with 'signal 9 (SIGKILL)'
ERROR 2025-07-03 00:48:40,083 request 49655 ********** Task handler raised error: WorkerLostError('Worker exited prematurely: signal 9 (SIGKILL) Job: 11.')
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/billiard/pool.py", line 1265, in mark_as_worker_lost
    raise WorkerLostError(
billiard.einfo.ExceptionWithTraceback: 
"""
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/billiard/pool.py", line 1265, in mark_as_worker_lost
    raise WorkerLostError(
billiard.exceptions.WorkerLostError: Worker exited prematurely: signal 9 (SIGKILL) Job: 11.
"""
[2025-07-03 01:17:41,309: ERROR/MainProcess] Process 'ForkPoolWorker-3' pid:60017 exited with 'signal 9 (SIGKILL)'
ERROR 2025-07-03 02:00:00,029 consumer 77976 ********** Received unregistered task of type 'users.check_and_fix_level_consistency'.
The message has been ignored and discarded.

Did you remember to import the module containing this task?
Or maybe you're using relative imports?

Please see
https://docs.celeryq.dev/en/latest/internals/protocol.html
for more information.

The full contents of the message body was:
b'[[], {}, {"callbacks": null, "errbacks": null, "chain": null, "chord": null}]' (77b)

The full contents of the message headers:
{'lang': 'py', 'task': 'users.check_and_fix_level_consistency', 'id': '********-ac3c-4a8e-8323-00ee7f9cb4ff', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [None, None], 'root_id': '********-ac3c-4a8e-8323-00ee7f9cb4ff', 'parent_id': None, 'argsrepr': '()', 'kwargsrepr': '{}', 'origin': '<EMAIL>', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'compression': 'application/x-gzip'}

The delivery info for this task is:
{'exchange': '', 'routing_key': 'default'}
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 658, in on_task_received
    strategy = strategies[type_]
               ~~~~~~~~~~^^^^^^^
KeyError: 'users.check_and_fix_level_consistency'
WARNING 2025-07-12 14:29:30,835 consumer 1187 ********** consumer: Connection to broker lost. Trying to re-establish the connection...
Traceback (most recent call last):
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 340, in start
    blueprint.start(self)
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/bootsteps.py", line 116, in start
    step.start(parent)
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 742, in start
    c.loop(*c.loop_args())
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/worker/loops.py", line 97, in asynloop
    next(loop)
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/kombu/asynchronous/hub.py", line 373, in create_loop
    cb(*cbargs)
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/kombu/transport/redis.py", line 1352, in on_readable
    self.cycle.on_readable(fileno)
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/kombu/transport/redis.py", line 569, in on_readable
    chan.handlers[type]()
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/kombu/transport/redis.py", line 967, in _brpop_read
    dest__item = self.client.parse_response(self.client.connection,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/redis/client.py", line 553, in parse_response
    response = connection.read_response()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/redis/connection.py", line 500, in read_response
    response = self._parser.read_response(disable_decoding=disable_decoding)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/redis/_parsers/resp2.py", line 15, in read_response
    result = self._read_response(disable_decoding=disable_decoding)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/redis/_parsers/resp2.py", line 25, in _read_response
    raw = self._buffer.readline()
          ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/redis/_parsers/socket.py", line 115, in readline
    self._read_from_socket()
  File "/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/redis/_parsers/socket.py", line 68, in _read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
ERROR 2025-07-12 14:29:31,167 consumer 1187 ********** consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 2.00 seconds... (1/100)

ERROR 2025-07-12 14:29:33,176 consumer 1187 ********** consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 4.00 seconds... (2/100)

ERROR 2025-07-12 14:29:37,207 consumer 1187 ********** consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 6.00 seconds... (3/100)

ERROR 2025-07-12 14:29:43,227 consumer 1187 ********** consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 8.00 seconds... (4/100)

