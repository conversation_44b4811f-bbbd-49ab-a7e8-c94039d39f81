{"time": "2025-07-23T12:29:46+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=473, method=GET, path=/api/app/v1/dashboard/, ip=***************, status=200"}
{"time": "2025-07-23T12:29:48+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=473, method=POST, path=/api/app/v1/health/session/init/, ip=***************, status=200"}
{"time": "2025-07-23T12:29:48+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=473, method=POST, path=/api/app/v1/health/sync/, ip=***************, status=200"}
{"time": "2025-07-23T12:29:49+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=anonymous, method=GET, path=/media/avatars/avatar_3.png, ip=***************, status=200"}
{"time": "2025-07-23T12:30:25+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=473, method=POST, path=/api/app/v1/health/session/init/, ip=***************, status=200"}
{"time": "2025-07-23T12:30:25+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=473, method=POST, path=/api/app/v1/health/sync/, ip=***************, status=200"}
{"time": "2025-07-23T12:30:26+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=anonymous, method=GET, path=/media/avatars/avatar_3.png, ip=***************, status=200"}
{"time": "2025-07-23T12:32:23+0800", "level": "INFO", "name": "authentication.views_app", "message": "Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3"}
{"time": "2025-07-23T12:32:23+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200"}
{"time": "2025-07-23T12:40:23+0800", "level": "INFO", "name": "authentication.views_app", "message": "Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3"}
{"time": "2025-07-23T12:40:23+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200"}
{"time": "2025-07-23T12:48:23+0800", "level": "INFO", "name": "authentication.views_app", "message": "Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3"}
{"time": "2025-07-23T12:48:23+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200"}
{"time": "2025-07-23T12:56:23+0800", "level": "INFO", "name": "authentication.views_app", "message": "Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3"}
{"time": "2025-07-23T12:56:23+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200"}
{"time": "2025-07-23T13:04:23+0800", "level": "INFO", "name": "authentication.views_app", "message": "Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3"}
{"time": "2025-07-23T13:04:23+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200"}
{"time": "2025-07-23T13:12:23+0800", "level": "INFO", "name": "authentication.views_app", "message": "Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3"}
{"time": "2025-07-23T13:12:23+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200"}
{"time": "2025-07-23T13:20:23+0800", "level": "INFO", "name": "authentication.views_app", "message": "Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3"}
{"time": "2025-07-23T13:20:23+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200"}
{"time": "2025-07-23T13:28:23+0800", "level": "INFO", "name": "authentication.views_app", "message": "Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3"}
{"time": "2025-07-23T13:28:23+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200"}
{"time": "2025-07-23T13:36:23+0800", "level": "INFO", "name": "authentication.views_app", "message": "Token refreshed: user_id=473, IP=***************, device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3"}
{"time": "2025-07-23T13:36:23+0800", "level": "INFO", "name": "authentication.middleware", "message": "API请求处理完成: type=app, user_id=anonymous, method=POST, path=/api/app/v1/authentication/token/refresh/, ip=***************, status=200"}
