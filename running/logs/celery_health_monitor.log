2025-07-02 15:50:51,339 [INFO] rewards.admin: rewards.admin日志器已初始化
INFO 2025-07-02 15:50:51,343 Dashboard app ready.
INFO 2025-07-02 15:50:51,343 Health API app ready.
INFO 2025-07-02 15:50:51,343 开始连续监控，检查间隔: 300秒
INFO 2025-07-02 15:50:51,343 开始执行健康检查...
INFO 2025-07-02 15:50:53,386 Worker <EMAIL>: 已处理 {} 个任务
INFO 2025-07-02 15:50:56,400 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 15:50:56,406 ✅ 系统状态: healthy
INFO 2025-07-02 15:50:56,406   ✅ redis: up
INFO 2025-07-02 15:50:56,406   ✅ database: up
INFO 2025-07-02 15:50:56,406   ✅ celery_worker: up
INFO 2025-07-02 15:50:56,406   ✅ celery_beat: up
INFO 2025-07-02 15:50:56,406   ✅ flower: up
INFO 2025-07-02 15:50:56,406 健康检查完成
INFO 2025-07-02 15:50:56,406 检查是否需要自动恢复...
INFO 2025-07-02 15:55:56,407 开始执行健康检查...
INFO 2025-07-02 15:55:58,419 Worker <EMAIL>: 已处理 {} 个任务
INFO 2025-07-02 15:56:01,430 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 15:56:01,433 ✅ 系统状态: healthy
INFO 2025-07-02 15:56:01,433   ✅ redis: up
INFO 2025-07-02 15:56:01,433   ✅ database: up
INFO 2025-07-02 15:56:01,433   ✅ celery_worker: up
INFO 2025-07-02 15:56:01,433   ✅ celery_beat: up
INFO 2025-07-02 15:56:01,433   ✅ flower: up
INFO 2025-07-02 15:56:01,433 健康检查完成
INFO 2025-07-02 15:56:01,433 检查是否需要自动恢复...
INFO 2025-07-02 16:01:01,434 开始执行健康检查...
INFO 2025-07-02 16:01:03,447 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 1, 'tasks.tasks.cache_monitoring_data': 1} 个任务
INFO 2025-07-02 16:01:06,458 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 16:01:06,461 ✅ 系统状态: healthy
INFO 2025-07-02 16:01:06,461   ✅ redis: up
INFO 2025-07-02 16:01:06,461   ✅ database: up
INFO 2025-07-02 16:01:06,461   ✅ celery_worker: up
INFO 2025-07-02 16:01:06,461   ✅ celery_beat: up
INFO 2025-07-02 16:01:06,461   ✅ flower: up
INFO 2025-07-02 16:01:06,461 健康检查完成
INFO 2025-07-02 16:01:06,461 检查是否需要自动恢复...
INFO 2025-07-02 16:06:06,465 开始执行健康检查...
INFO 2025-07-02 16:06:08,476 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 1, 'tasks.tasks.cache_monitoring_data': 1} 个任务
INFO 2025-07-02 16:06:11,486 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 16:06:11,489 ✅ 系统状态: healthy
INFO 2025-07-02 16:06:11,490   ✅ redis: up
INFO 2025-07-02 16:06:11,490   ✅ database: up
INFO 2025-07-02 16:06:11,490   ✅ celery_worker: up
INFO 2025-07-02 16:06:11,490   ✅ celery_beat: up
INFO 2025-07-02 16:06:11,490   ✅ flower: up
INFO 2025-07-02 16:06:11,490 健康检查完成
INFO 2025-07-02 16:06:11,490 检查是否需要自动恢复...
INFO 2025-07-02 16:11:11,500 开始执行健康检查...
INFO 2025-07-02 16:11:13,509 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 1, 'tasks.tasks.cache_monitoring_data': 1} 个任务
/Users/<USER>/Documents/worker/sweatmint/running/.venv/lib/python3.12/site-packages/celery/app/control.py:56: DuplicateNodenameWarning: Received multiple replies from node name: <EMAIL>.
Please make sure you give each node a unique nodename using
the celery worker `-n` option.
  warnings.warn(DuplicateNodenameWarning(
INFO 2025-07-02 16:11:16,523 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 16:11:16,526 ✅ 系统状态: healthy
INFO 2025-07-02 16:11:16,526   ✅ redis: up
INFO 2025-07-02 16:11:16,526   ✅ database: up
INFO 2025-07-02 16:11:16,526   ✅ celery_worker: up
INFO 2025-07-02 16:11:16,526   ✅ celery_beat: up
INFO 2025-07-02 16:11:16,526   ❌ flower: down
INFO 2025-07-02 16:11:16,526 健康检查完成
INFO 2025-07-02 16:11:16,526 检查是否需要自动恢复...
WARNING 2025-07-02 16:11:16,526 检测到问题: Flower监控未运行，尝试重启 flower
INFO 2025-07-02 16:11:23,812 服务 flower 重启成功
INFO 2025-07-02 16:11:23,813 自动恢复成功: flower
INFO 2025-07-02 16:16:23,819 开始执行健康检查...
INFO 2025-07-02 16:16:25,827 Worker <EMAIL>: 已处理 {} 个任务
INFO 2025-07-02 16:16:28,838 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 16:16:28,839 ✅ 系统状态: healthy
INFO 2025-07-02 16:16:28,839   ✅ redis: up
INFO 2025-07-02 16:16:28,839   ✅ database: up
INFO 2025-07-02 16:16:28,840   ✅ celery_worker: up
INFO 2025-07-02 16:16:28,840   ✅ celery_beat: up
INFO 2025-07-02 16:16:28,840   ✅ flower: up
INFO 2025-07-02 16:16:28,840 健康检查完成
INFO 2025-07-02 16:16:28,840 检查是否需要自动恢复...
INFO 2025-07-02 16:21:28,845 开始执行健康检查...
INFO 2025-07-02 16:21:30,874 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 1, 'tasks.tasks.cache_monitoring_data': 1} 个任务
INFO 2025-07-02 16:21:33,889 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 16:21:33,896 ✅ 系统状态: healthy
INFO 2025-07-02 16:21:33,896   ✅ redis: up
INFO 2025-07-02 16:21:33,896   ✅ database: up
INFO 2025-07-02 16:21:33,896   ✅ celery_worker: up
INFO 2025-07-02 16:21:33,896   ✅ celery_beat: up
INFO 2025-07-02 16:21:33,896   ❌ flower: down
INFO 2025-07-02 16:21:33,896 健康检查完成
INFO 2025-07-02 16:21:33,896 检查是否需要自动恢复...
WARNING 2025-07-02 16:21:33,896 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 16:21:33,897 启动脚本不存在
ERROR 2025-07-02 16:21:33,897 自动恢复失败: flower
INFO 2025-07-02 16:26:33,903 开始执行健康检查...
INFO 2025-07-02 16:26:35,918 Worker <EMAIL>: 已处理 {} 个任务
INFO 2025-07-02 16:26:38,933 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 16:26:38,939 ✅ 系统状态: healthy
INFO 2025-07-02 16:26:38,939   ✅ redis: up
INFO 2025-07-02 16:26:38,939   ✅ database: up
INFO 2025-07-02 16:26:38,939   ✅ celery_worker: up
INFO 2025-07-02 16:26:38,939   ✅ celery_beat: up
INFO 2025-07-02 16:26:38,940   ❌ flower: down
INFO 2025-07-02 16:26:38,940 健康检查完成
INFO 2025-07-02 16:26:38,940 检查是否需要自动恢复...
WARNING 2025-07-02 16:26:38,940 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 16:26:38,940 启动脚本不存在
ERROR 2025-07-02 16:26:38,940 自动恢复失败: flower
INFO 2025-07-02 16:31:38,926 开始执行健康检查...
INFO 2025-07-02 16:31:40,939 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 1, 'tasks.tasks.cache_monitoring_data': 2, 'users.check_new_user_levels': 1} 个任务
INFO 2025-07-02 16:31:43,965 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 16:31:43,969 ✅ 系统状态: healthy
INFO 2025-07-02 16:31:43,969   ✅ redis: up
INFO 2025-07-02 16:31:43,969   ✅ database: up
INFO 2025-07-02 16:31:43,969   ✅ celery_worker: up
INFO 2025-07-02 16:31:43,969   ✅ celery_beat: up
INFO 2025-07-02 16:31:43,969   ❌ flower: down
INFO 2025-07-02 16:31:43,970 健康检查完成
INFO 2025-07-02 16:31:43,970 检查是否需要自动恢复...
WARNING 2025-07-02 16:31:43,970 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 16:31:43,970 启动脚本不存在
ERROR 2025-07-02 16:31:43,970 自动恢复失败: flower
INFO 2025-07-02 16:36:43,973 开始执行健康检查...
INFO 2025-07-02 16:36:45,998 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 1, 'tasks.tasks.cache_monitoring_data': 1} 个任务
INFO 2025-07-02 16:36:49,027 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 16:36:49,034 ✅ 系统状态: healthy
INFO 2025-07-02 16:36:49,034   ✅ redis: up
INFO 2025-07-02 16:36:49,034   ✅ database: up
INFO 2025-07-02 16:36:49,034   ✅ celery_worker: up
INFO 2025-07-02 16:36:49,034   ✅ celery_beat: up
INFO 2025-07-02 16:36:49,034   ❌ flower: down
INFO 2025-07-02 16:36:49,034 健康检查完成
INFO 2025-07-02 16:36:49,034 检查是否需要自动恢复...
WARNING 2025-07-02 16:36:49,034 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 16:36:49,035 启动脚本不存在
ERROR 2025-07-02 16:36:49,035 自动恢复失败: flower
INFO 2025-07-02 16:41:49,038 开始执行健康检查...
INFO 2025-07-02 16:41:51,057 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 1, 'tasks.tasks.cache_monitoring_data': 2, 'users.check_new_user_levels': 1} 个任务
INFO 2025-07-02 16:41:54,084 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 16:41:54,091 ✅ 系统状态: healthy
INFO 2025-07-02 16:41:54,091   ✅ redis: up
INFO 2025-07-02 16:41:54,091   ✅ database: up
INFO 2025-07-02 16:41:54,092   ✅ celery_worker: up
INFO 2025-07-02 16:41:54,092   ✅ celery_beat: up
INFO 2025-07-02 16:41:54,092   ❌ flower: down
INFO 2025-07-02 16:41:54,092 健康检查完成
INFO 2025-07-02 16:41:54,092 检查是否需要自动恢复...
WARNING 2025-07-02 16:41:54,092 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 16:41:54,092 启动脚本不存在
ERROR 2025-07-02 16:41:54,092 自动恢复失败: flower
INFO 2025-07-02 16:46:54,094 开始执行健康检查...
INFO 2025-07-02 16:46:56,111 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 1, 'tasks.tasks.cache_monitoring_data': 2, 'users.check_new_user_levels': 1} 个任务
INFO 2025-07-02 16:46:59,130 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 16:46:59,134 ✅ 系统状态: healthy
INFO 2025-07-02 16:46:59,134   ✅ redis: up
INFO 2025-07-02 16:46:59,134   ✅ database: up
INFO 2025-07-02 16:46:59,134   ✅ celery_worker: up
INFO 2025-07-02 16:46:59,134   ✅ celery_beat: up
INFO 2025-07-02 16:46:59,134   ❌ flower: down
INFO 2025-07-02 16:46:59,135 健康检查完成
INFO 2025-07-02 16:46:59,135 检查是否需要自动恢复...
WARNING 2025-07-02 16:46:59,135 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 16:46:59,135 启动脚本不存在
ERROR 2025-07-02 16:46:59,135 自动恢复失败: flower
INFO 2025-07-02 16:51:59,144 开始执行健康检查...
INFO 2025-07-02 16:52:01,161 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 1, 'tasks.tasks.cache_monitoring_data': 2, 'users.check_new_user_levels': 1} 个任务
INFO 2025-07-02 16:52:04,178 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 16:52:04,180 ✅ 系统状态: healthy
INFO 2025-07-02 16:52:04,180   ✅ redis: up
INFO 2025-07-02 16:52:04,180   ✅ database: up
INFO 2025-07-02 16:52:04,180   ✅ celery_worker: up
INFO 2025-07-02 16:52:04,180   ✅ celery_beat: up
INFO 2025-07-02 16:52:04,180   ❌ flower: down
INFO 2025-07-02 16:52:04,180 健康检查完成
INFO 2025-07-02 16:52:04,180 检查是否需要自动恢复...
WARNING 2025-07-02 16:52:04,180 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 16:52:04,180 启动脚本不存在
ERROR 2025-07-02 16:52:04,180 自动恢复失败: flower
INFO 2025-07-02 16:57:04,190 开始执行健康检查...
INFO 2025-07-02 16:57:06,205 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 1, 'tasks.tasks.cache_monitoring_data': 2, 'users.check_new_user_levels': 1} 个任务
INFO 2025-07-02 16:57:09,223 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 16:57:09,224 ✅ 系统状态: healthy
INFO 2025-07-02 16:57:09,224   ✅ redis: up
INFO 2025-07-02 16:57:09,224   ✅ database: up
INFO 2025-07-02 16:57:09,224   ✅ celery_worker: up
INFO 2025-07-02 16:57:09,224   ✅ celery_beat: up
INFO 2025-07-02 16:57:09,224   ❌ flower: down
INFO 2025-07-02 16:57:09,224 健康检查完成
INFO 2025-07-02 16:57:09,224 检查是否需要自动恢复...
WARNING 2025-07-02 16:57:09,224 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 16:57:09,225 启动脚本不存在
ERROR 2025-07-02 16:57:09,225 自动恢复失败: flower
INFO 2025-07-02 17:02:09,235 开始执行健康检查...
INFO 2025-07-02 17:02:11,250 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 1, 'tasks.tasks.cache_monitoring_data': 2, 'authentication.tasks.cleanup_expired_tokens': 1} 个任务
INFO 2025-07-02 17:02:14,269 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 17:02:14,274 ✅ 系统状态: healthy
INFO 2025-07-02 17:02:14,275   ✅ redis: up
INFO 2025-07-02 17:02:14,275   ✅ database: up
INFO 2025-07-02 17:02:14,275   ✅ celery_worker: up
INFO 2025-07-02 17:02:14,275   ✅ celery_beat: up
INFO 2025-07-02 17:02:14,275   ❌ flower: down
INFO 2025-07-02 17:02:14,275 健康检查完成
INFO 2025-07-02 17:02:14,275 检查是否需要自动恢复...
WARNING 2025-07-02 17:02:14,275 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 17:02:14,275 启动脚本不存在
ERROR 2025-07-02 17:02:14,275 自动恢复失败: flower
INFO 2025-07-02 17:07:14,284 开始执行健康检查...
INFO 2025-07-02 17:07:16,304 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 2, 'tasks.tasks.cache_monitoring_data': 3, 'users.check_new_user_levels': 1} 个任务
INFO 2025-07-02 17:07:19,320 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 17:07:19,327 ✅ 系统状态: healthy
INFO 2025-07-02 17:07:19,327   ✅ redis: up
INFO 2025-07-02 17:07:19,327   ✅ database: up
INFO 2025-07-02 17:07:19,327   ✅ celery_worker: up
INFO 2025-07-02 17:07:19,327   ✅ celery_beat: up
INFO 2025-07-02 17:07:19,327   ❌ flower: down
INFO 2025-07-02 17:07:19,327 健康检查完成
INFO 2025-07-02 17:07:19,327 检查是否需要自动恢复...
WARNING 2025-07-02 17:07:19,327 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 17:07:19,327 启动脚本不存在
ERROR 2025-07-02 17:07:19,327 自动恢复失败: flower
INFO 2025-07-02 17:12:19,328 开始执行健康检查...
INFO 2025-07-02 17:12:21,346 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 1, 'tasks.tasks.cache_monitoring_data': 2, 'authentication.tasks.cleanup_expired_tokens': 1} 个任务
INFO 2025-07-02 17:12:24,374 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 17:12:24,379 ✅ 系统状态: healthy
INFO 2025-07-02 17:12:24,379   ✅ redis: up
INFO 2025-07-02 17:12:24,379   ✅ database: up
INFO 2025-07-02 17:12:24,379   ✅ celery_worker: up
INFO 2025-07-02 17:12:24,379   ✅ celery_beat: up
INFO 2025-07-02 17:12:24,379   ❌ flower: down
INFO 2025-07-02 17:12:24,379 健康检查完成
INFO 2025-07-02 17:12:24,379 检查是否需要自动恢复...
WARNING 2025-07-02 17:12:24,379 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 17:12:24,380 启动脚本不存在
ERROR 2025-07-02 17:12:24,380 自动恢复失败: flower
INFO 2025-07-02 17:17:24,380 开始执行健康检查...
INFO 2025-07-02 17:17:26,400 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 2, 'tasks.tasks.cache_monitoring_data': 3, 'users.check_new_user_levels': 1} 个任务
INFO 2025-07-02 17:17:29,421 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 17:17:29,426 ✅ 系统状态: healthy
INFO 2025-07-02 17:17:29,426   ✅ redis: up
INFO 2025-07-02 17:17:29,426   ✅ database: up
INFO 2025-07-02 17:17:29,426   ✅ celery_worker: up
INFO 2025-07-02 17:17:29,427   ✅ celery_beat: up
INFO 2025-07-02 17:17:29,427   ❌ flower: down
INFO 2025-07-02 17:17:29,427 健康检查完成
INFO 2025-07-02 17:17:29,427 检查是否需要自动恢复...
WARNING 2025-07-02 17:17:29,427 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 17:17:29,427 启动脚本不存在
ERROR 2025-07-02 17:17:29,427 自动恢复失败: flower
INFO 2025-07-02 17:22:29,470 开始执行健康检查...
INFO 2025-07-02 17:22:31,501 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 1, 'tasks.tasks.cache_monitoring_data': 2, 'authentication.tasks.cleanup_expired_tokens': 1} 个任务
INFO 2025-07-02 17:22:34,524 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 17:22:34,532 ✅ 系统状态: healthy
INFO 2025-07-02 17:22:34,532   ✅ redis: up
INFO 2025-07-02 17:22:34,532   ✅ database: up
INFO 2025-07-02 17:22:34,532   ✅ celery_worker: up
INFO 2025-07-02 17:22:34,533   ✅ celery_beat: up
INFO 2025-07-02 17:22:34,533   ❌ flower: down
INFO 2025-07-02 17:22:34,533 健康检查完成
INFO 2025-07-02 17:22:34,533 检查是否需要自动恢复...
WARNING 2025-07-02 17:22:34,533 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 17:22:34,533 启动脚本不存在
ERROR 2025-07-02 17:22:34,533 自动恢复失败: flower
INFO 2025-07-02 17:27:34,544 开始执行健康检查...
INFO 2025-07-02 17:27:36,571 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 2, 'tasks.tasks.cache_monitoring_data': 3, 'users.check_new_user_levels': 1} 个任务
INFO 2025-07-02 17:27:39,595 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 17:27:39,601 ✅ 系统状态: healthy
INFO 2025-07-02 17:27:39,601   ✅ redis: up
INFO 2025-07-02 17:27:39,601   ✅ database: up
INFO 2025-07-02 17:27:39,601   ✅ celery_worker: up
INFO 2025-07-02 17:27:39,601   ✅ celery_beat: up
INFO 2025-07-02 17:27:39,601   ❌ flower: down
INFO 2025-07-02 17:27:39,601 健康检查完成
INFO 2025-07-02 17:27:39,602 检查是否需要自动恢复...
WARNING 2025-07-02 17:27:39,602 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 17:27:39,602 启动脚本不存在
ERROR 2025-07-02 17:27:39,602 自动恢复失败: flower
INFO 2025-07-02 17:32:39,611 开始执行健康检查...
INFO 2025-07-02 17:32:41,633 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 2, 'tasks.tasks.cache_monitoring_data': 3, 'authentication.tasks.cleanup_expired_tokens': 1} 个任务
INFO 2025-07-02 17:32:44,657 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 17:32:44,664 ✅ 系统状态: healthy
INFO 2025-07-02 17:32:44,664   ✅ redis: up
INFO 2025-07-02 17:32:44,664   ✅ database: up
INFO 2025-07-02 17:32:44,664   ✅ celery_worker: up
INFO 2025-07-02 17:32:44,665   ✅ celery_beat: up
INFO 2025-07-02 17:32:44,665   ❌ flower: down
INFO 2025-07-02 17:32:44,665 健康检查完成
INFO 2025-07-02 17:32:44,665 检查是否需要自动恢复...
WARNING 2025-07-02 17:32:44,665 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 17:32:44,665 启动脚本不存在
ERROR 2025-07-02 17:32:44,665 自动恢复失败: flower
INFO 2025-07-02 17:37:44,676 开始执行健康检查...
INFO 2025-07-02 17:37:46,697 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 2, 'tasks.tasks.cache_monitoring_data': 3, 'authentication.tasks.cleanup_expired_tokens': 1} 个任务
INFO 2025-07-02 17:37:49,720 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 17:37:49,728 ✅ 系统状态: healthy
INFO 2025-07-02 17:37:49,728   ✅ redis: up
INFO 2025-07-02 17:37:49,728   ✅ database: up
INFO 2025-07-02 17:37:49,728   ✅ celery_worker: up
INFO 2025-07-02 17:37:49,729   ✅ celery_beat: up
INFO 2025-07-02 17:37:49,729   ❌ flower: down
INFO 2025-07-02 17:37:49,729 健康检查完成
INFO 2025-07-02 17:37:49,729 检查是否需要自动恢复...
WARNING 2025-07-02 17:37:49,729 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 17:37:49,729 启动脚本不存在
ERROR 2025-07-02 17:37:49,729 自动恢复失败: flower
INFO 2025-07-02 17:42:49,720 开始执行健康检查...
INFO 2025-07-02 17:42:51,742 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 2, 'tasks.tasks.cache_monitoring_data': 3, 'authentication.tasks.cleanup_expired_tokens': 1} 个任务
INFO 2025-07-02 17:42:54,767 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 17:42:54,774 ✅ 系统状态: healthy
INFO 2025-07-02 17:42:54,774   ✅ redis: up
INFO 2025-07-02 17:42:54,774   ✅ database: up
INFO 2025-07-02 17:42:54,774   ✅ celery_worker: up
INFO 2025-07-02 17:42:54,774   ✅ celery_beat: up
INFO 2025-07-02 17:42:54,775   ❌ flower: down
INFO 2025-07-02 17:42:54,775 健康检查完成
INFO 2025-07-02 17:42:54,775 检查是否需要自动恢复...
WARNING 2025-07-02 17:42:54,775 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 17:42:54,775 启动脚本不存在
ERROR 2025-07-02 17:42:54,775 自动恢复失败: flower
INFO 2025-07-02 17:47:54,777 开始执行健康检查...
INFO 2025-07-02 17:47:56,791 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 2, 'tasks.tasks.cache_monitoring_data': 4, 'users.check_new_user_levels': 2} 个任务
INFO 2025-07-02 17:47:59,815 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 17:47:59,819 ✅ 系统状态: healthy
INFO 2025-07-02 17:47:59,819   ✅ redis: up
INFO 2025-07-02 17:47:59,819   ✅ database: up
INFO 2025-07-02 17:47:59,819   ✅ celery_worker: up
INFO 2025-07-02 17:47:59,819   ✅ celery_beat: up
INFO 2025-07-02 17:47:59,819   ❌ flower: down
INFO 2025-07-02 17:47:59,820 健康检查完成
INFO 2025-07-02 17:47:59,820 检查是否需要自动恢复...
WARNING 2025-07-02 17:47:59,820 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 17:47:59,820 启动脚本不存在
ERROR 2025-07-02 17:47:59,820 自动恢复失败: flower
INFO 2025-07-02 17:52:59,830 开始执行健康检查...
INFO 2025-07-02 17:53:01,854 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 2, 'tasks.tasks.cache_monitoring_data': 4, 'users.check_new_user_levels': 2} 个任务
INFO 2025-07-02 17:53:04,881 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 17:53:04,888 ✅ 系统状态: healthy
INFO 2025-07-02 17:53:04,888   ✅ redis: up
INFO 2025-07-02 17:53:04,888   ✅ database: up
INFO 2025-07-02 17:53:04,888   ✅ celery_worker: up
INFO 2025-07-02 17:53:04,888   ✅ celery_beat: up
INFO 2025-07-02 17:53:04,888   ❌ flower: down
INFO 2025-07-02 17:53:04,888 健康检查完成
INFO 2025-07-02 17:53:04,889 检查是否需要自动恢复...
WARNING 2025-07-02 17:53:04,889 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 17:53:04,889 启动脚本不存在
ERROR 2025-07-02 17:53:04,889 自动恢复失败: flower
INFO 2025-07-02 17:58:04,891 开始执行健康检查...
INFO 2025-07-02 17:58:06,913 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 2, 'tasks.tasks.cache_monitoring_data': 3, 'authentication.tasks.cleanup_expired_tokens': 1} 个任务
INFO 2025-07-02 17:58:09,937 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 17:58:09,943 ✅ 系统状态: healthy
INFO 2025-07-02 17:58:09,944   ✅ redis: up
INFO 2025-07-02 17:58:09,944   ✅ database: up
INFO 2025-07-02 17:58:09,944   ✅ celery_worker: up
INFO 2025-07-02 17:58:09,944   ✅ celery_beat: up
INFO 2025-07-02 17:58:09,944   ❌ flower: down
INFO 2025-07-02 17:58:09,944 健康检查完成
INFO 2025-07-02 17:58:09,944 检查是否需要自动恢复...
WARNING 2025-07-02 17:58:09,944 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 17:58:09,945 启动脚本不存在
ERROR 2025-07-02 17:58:09,945 自动恢复失败: flower
INFO 2025-07-02 18:03:09,952 开始执行健康检查...
INFO 2025-07-02 18:03:11,979 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 2, 'tasks.tasks.cache_monitoring_data': 4, 'authentication.tasks.cleanup_expired_tokens': 2} 个任务
INFO 2025-07-02 18:03:15,003 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 18:03:15,006 ✅ 系统状态: healthy
INFO 2025-07-02 18:03:15,006   ✅ redis: up
INFO 2025-07-02 18:03:15,006   ✅ database: up
INFO 2025-07-02 18:03:15,006   ✅ celery_worker: up
INFO 2025-07-02 18:03:15,006   ✅ celery_beat: up
INFO 2025-07-02 18:03:15,006   ❌ flower: down
INFO 2025-07-02 18:03:15,006 健康检查完成
INFO 2025-07-02 18:03:15,007 检查是否需要自动恢复...
WARNING 2025-07-02 18:03:15,007 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 18:03:15,007 启动脚本不存在
ERROR 2025-07-02 18:03:15,007 自动恢复失败: flower
INFO 2025-07-02 18:08:15,013 开始执行健康检查...
INFO 2025-07-02 18:08:17,035 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 2, 'tasks.tasks.cache_monitoring_data': 4, 'authentication.tasks.cleanup_expired_tokens': 2} 个任务
INFO 2025-07-02 18:08:20,059 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 18:08:20,065 ✅ 系统状态: healthy
INFO 2025-07-02 18:08:20,065   ✅ redis: up
INFO 2025-07-02 18:08:20,065   ✅ database: up
INFO 2025-07-02 18:08:20,065   ✅ celery_worker: up
INFO 2025-07-02 18:08:20,066   ✅ celery_beat: up
INFO 2025-07-02 18:08:20,066   ❌ flower: down
INFO 2025-07-02 18:08:20,066 健康检查完成
INFO 2025-07-02 18:08:20,066 检查是否需要自动恢复...
WARNING 2025-07-02 18:08:20,066 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 18:08:20,066 启动脚本不存在
ERROR 2025-07-02 18:08:20,066 自动恢复失败: flower
INFO 2025-07-02 18:13:20,076 开始执行健康检查...
INFO 2025-07-02 18:13:22,099 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 2, 'tasks.tasks.cache_monitoring_data': 4, 'authentication.tasks.cleanup_expired_tokens': 2} 个任务
INFO 2025-07-02 18:13:25,126 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 18:13:25,130 ✅ 系统状态: healthy
INFO 2025-07-02 18:13:25,130   ✅ redis: up
INFO 2025-07-02 18:13:25,130   ✅ database: up
INFO 2025-07-02 18:13:25,130   ✅ celery_worker: up
INFO 2025-07-02 18:13:25,130   ✅ celery_beat: up
INFO 2025-07-02 18:13:25,130   ❌ flower: down
INFO 2025-07-02 18:13:25,131 健康检查完成
INFO 2025-07-02 18:13:25,131 检查是否需要自动恢复...
WARNING 2025-07-02 18:13:25,131 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 18:13:25,131 启动脚本不存在
ERROR 2025-07-02 18:13:25,131 自动恢复失败: flower
INFO 2025-07-02 18:18:25,135 开始执行健康检查...
INFO 2025-07-02 18:18:27,155 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 3, 'tasks.tasks.cache_monitoring_data': 5, 'users.check_new_user_levels': 2} 个任务
INFO 2025-07-02 18:18:30,178 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 18:18:30,181 ✅ 系统状态: healthy
INFO 2025-07-02 18:18:30,181   ✅ redis: up
INFO 2025-07-02 18:18:30,181   ✅ database: up
INFO 2025-07-02 18:18:30,181   ✅ celery_worker: up
INFO 2025-07-02 18:18:30,181   ✅ celery_beat: up
INFO 2025-07-02 18:18:30,181   ❌ flower: down
INFO 2025-07-02 18:18:30,181 健康检查完成
INFO 2025-07-02 18:18:30,181 检查是否需要自动恢复...
WARNING 2025-07-02 18:18:30,181 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 18:18:30,182 启动脚本不存在
ERROR 2025-07-02 18:18:30,182 自动恢复失败: flower
INFO 2025-07-02 18:23:30,184 开始执行健康检查...
INFO 2025-07-02 18:23:32,209 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 2, 'tasks.tasks.cache_monitoring_data': 4, 'authentication.tasks.cleanup_expired_tokens': 2} 个任务
INFO 2025-07-02 18:23:35,234 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 18:23:35,240 ✅ 系统状态: healthy
INFO 2025-07-02 18:23:35,240   ✅ redis: up
INFO 2025-07-02 18:23:35,240   ✅ database: up
INFO 2025-07-02 18:23:35,241   ✅ celery_worker: up
INFO 2025-07-02 18:23:35,241   ✅ celery_beat: up
INFO 2025-07-02 18:23:35,241   ❌ flower: down
INFO 2025-07-02 18:23:35,241 健康检查完成
INFO 2025-07-02 18:23:35,241 检查是否需要自动恢复...
WARNING 2025-07-02 18:23:35,241 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 18:23:35,241 启动脚本不存在
ERROR 2025-07-02 18:23:35,242 自动恢复失败: flower
INFO 2025-07-02 18:28:35,253 开始执行健康检查...
INFO 2025-07-02 18:28:37,280 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 3, 'tasks.tasks.cache_monitoring_data': 5, 'users.check_new_user_levels': 2} 个任务
INFO 2025-07-02 18:28:40,305 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 18:28:40,313 ✅ 系统状态: healthy
INFO 2025-07-02 18:28:40,313   ✅ redis: up
INFO 2025-07-02 18:28:40,313   ✅ database: up
INFO 2025-07-02 18:28:40,313   ✅ celery_worker: up
INFO 2025-07-02 18:28:40,313   ✅ celery_beat: up
INFO 2025-07-02 18:28:40,313   ❌ flower: down
INFO 2025-07-02 18:28:40,314 健康检查完成
INFO 2025-07-02 18:28:40,314 检查是否需要自动恢复...
WARNING 2025-07-02 18:28:40,314 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 18:28:40,314 启动脚本不存在
ERROR 2025-07-02 18:28:40,314 自动恢复失败: flower
INFO 2025-07-02 18:33:40,326 开始执行健康检查...
INFO 2025-07-02 18:33:42,349 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 3, 'tasks.tasks.cache_monitoring_data': 6, 'users.check_new_user_levels': 3} 个任务
INFO 2025-07-02 18:33:45,377 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 18:33:45,383 ✅ 系统状态: healthy
INFO 2025-07-02 18:33:45,383   ✅ redis: up
INFO 2025-07-02 18:33:45,383   ✅ database: up
INFO 2025-07-02 18:33:45,383   ✅ celery_worker: up
INFO 2025-07-02 18:33:45,384   ✅ celery_beat: up
INFO 2025-07-02 18:33:45,384   ❌ flower: down
INFO 2025-07-02 18:33:45,384 健康检查完成
INFO 2025-07-02 18:33:45,384 检查是否需要自动恢复...
WARNING 2025-07-02 18:33:45,384 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 18:33:45,384 启动脚本不存在
ERROR 2025-07-02 18:33:45,384 自动恢复失败: flower
INFO 2025-07-02 18:38:45,392 开始执行健康检查...
INFO 2025-07-02 18:38:47,412 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 3, 'tasks.tasks.cache_monitoring_data': 5, 'authentication.tasks.cleanup_expired_tokens': 2} 个任务
INFO 2025-07-02 18:38:50,440 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 18:38:50,444 ✅ 系统状态: healthy
INFO 2025-07-02 18:38:50,445   ✅ redis: up
INFO 2025-07-02 18:38:50,445   ✅ database: up
INFO 2025-07-02 18:38:50,445   ✅ celery_worker: up
INFO 2025-07-02 18:38:50,445   ✅ celery_beat: up
INFO 2025-07-02 18:38:50,445   ❌ flower: down
INFO 2025-07-02 18:38:50,445 健康检查完成
INFO 2025-07-02 18:38:50,445 检查是否需要自动恢复...
WARNING 2025-07-02 18:38:50,445 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 18:38:50,445 启动脚本不存在
ERROR 2025-07-02 18:38:50,445 自动恢复失败: flower
INFO 2025-07-02 18:43:50,452 开始执行健康检查...
INFO 2025-07-02 18:43:52,482 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 3, 'tasks.tasks.cache_monitoring_data': 6, 'users.check_new_user_levels': 3} 个任务
INFO 2025-07-02 18:43:55,499 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 18:43:55,506 ✅ 系统状态: healthy
INFO 2025-07-02 18:43:55,506   ✅ redis: up
INFO 2025-07-02 18:43:55,506   ✅ database: up
INFO 2025-07-02 18:43:55,507   ✅ celery_worker: up
INFO 2025-07-02 18:43:55,507   ✅ celery_beat: up
INFO 2025-07-02 18:43:55,507   ❌ flower: down
INFO 2025-07-02 18:43:55,507 健康检查完成
INFO 2025-07-02 18:43:55,507 检查是否需要自动恢复...
WARNING 2025-07-02 18:43:55,507 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 18:43:55,507 启动脚本不存在
ERROR 2025-07-02 18:43:55,507 自动恢复失败: flower
INFO 2025-07-02 18:48:55,515 开始执行健康检查...
INFO 2025-07-02 18:48:57,536 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 3, 'tasks.tasks.cache_monitoring_data': 5, 'authentication.tasks.cleanup_expired_tokens': 2} 个任务
INFO 2025-07-02 18:49:00,559 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 18:49:00,565 ✅ 系统状态: healthy
INFO 2025-07-02 18:49:00,565   ✅ redis: up
INFO 2025-07-02 18:49:00,566   ✅ database: up
INFO 2025-07-02 18:49:00,566   ✅ celery_worker: up
INFO 2025-07-02 18:49:00,566   ✅ celery_beat: up
INFO 2025-07-02 18:49:00,566   ❌ flower: down
INFO 2025-07-02 18:49:00,566 健康检查完成
INFO 2025-07-02 18:49:00,566 检查是否需要自动恢复...
WARNING 2025-07-02 18:49:00,566 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 18:49:00,567 启动脚本不存在
ERROR 2025-07-02 18:49:00,567 自动恢复失败: flower
INFO 2025-07-02 18:54:00,574 开始执行健康检查...
INFO 2025-07-02 18:54:02,593 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 3, 'tasks.tasks.cache_monitoring_data': 6, 'users.check_new_user_levels': 3} 个任务
INFO 2025-07-02 18:54:05,618 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 18:54:05,622 ✅ 系统状态: healthy
INFO 2025-07-02 18:54:05,623   ✅ redis: up
INFO 2025-07-02 18:54:05,623   ✅ database: up
INFO 2025-07-02 18:54:05,623   ✅ celery_worker: up
INFO 2025-07-02 18:54:05,623   ✅ celery_beat: up
INFO 2025-07-02 18:54:05,623   ❌ flower: down
INFO 2025-07-02 18:54:05,623 健康检查完成
INFO 2025-07-02 18:54:05,623 检查是否需要自动恢复...
WARNING 2025-07-02 18:54:05,623 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 18:54:05,623 启动脚本不存在
ERROR 2025-07-02 18:54:05,623 自动恢复失败: flower
INFO 2025-07-02 18:59:05,628 开始执行健康检查...
INFO 2025-07-02 18:59:07,649 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 3, 'tasks.tasks.cache_monitoring_data': 5, 'authentication.tasks.cleanup_expired_tokens': 2} 个任务
INFO 2025-07-02 18:59:10,672 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 18:59:10,676 ✅ 系统状态: healthy
INFO 2025-07-02 18:59:10,677   ✅ redis: up
INFO 2025-07-02 18:59:10,677   ✅ database: up
INFO 2025-07-02 18:59:10,677   ✅ celery_worker: up
INFO 2025-07-02 18:59:10,677   ✅ celery_beat: up
INFO 2025-07-02 18:59:10,677   ❌ flower: down
INFO 2025-07-02 18:59:10,677 健康检查完成
INFO 2025-07-02 18:59:10,677 检查是否需要自动恢复...
WARNING 2025-07-02 18:59:10,677 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 18:59:10,677 启动脚本不存在
ERROR 2025-07-02 18:59:10,677 自动恢复失败: flower
INFO 2025-07-02 19:04:10,687 开始执行健康检查...
INFO 2025-07-02 19:04:12,709 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 3, 'tasks.tasks.cache_monitoring_data': 6, 'authentication.tasks.cleanup_expired_tokens': 3} 个任务
INFO 2025-07-02 19:04:15,728 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 19:04:15,735 ✅ 系统状态: healthy
INFO 2025-07-02 19:04:15,735   ✅ redis: up
INFO 2025-07-02 19:04:15,735   ✅ database: up
INFO 2025-07-02 19:04:15,736   ✅ celery_worker: up
INFO 2025-07-02 19:04:15,736   ✅ celery_beat: up
INFO 2025-07-02 19:04:15,736   ❌ flower: down
INFO 2025-07-02 19:04:15,736 健康检查完成
INFO 2025-07-02 19:04:15,736 检查是否需要自动恢复...
WARNING 2025-07-02 19:04:15,736 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 19:04:15,736 启动脚本不存在
ERROR 2025-07-02 19:04:15,737 自动恢复失败: flower
INFO 2025-07-02 19:09:15,743 开始执行健康检查...
INFO 2025-07-02 19:09:17,767 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 4, 'tasks.tasks.cache_monitoring_data': 7, 'users.check_new_user_levels': 3} 个任务
INFO 2025-07-02 19:09:20,788 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 19:09:20,793 ✅ 系统状态: healthy
INFO 2025-07-02 19:09:20,794   ✅ redis: up
INFO 2025-07-02 19:09:20,794   ✅ database: up
INFO 2025-07-02 19:09:20,794   ✅ celery_worker: up
INFO 2025-07-02 19:09:20,794   ✅ celery_beat: up
INFO 2025-07-02 19:09:20,794   ❌ flower: down
INFO 2025-07-02 19:09:20,794 健康检查完成
INFO 2025-07-02 19:09:20,794 检查是否需要自动恢复...
WARNING 2025-07-02 19:09:20,794 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 19:09:20,794 启动脚本不存在
ERROR 2025-07-02 19:09:20,795 自动恢复失败: flower
INFO 2025-07-02 19:14:20,804 开始执行健康检查...
INFO 2025-07-02 19:14:22,824 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 4, 'tasks.tasks.cache_monitoring_data': 7, 'users.check_new_user_levels': 3} 个任务
INFO 2025-07-02 19:14:25,849 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 19:14:25,855 ✅ 系统状态: healthy
INFO 2025-07-02 19:14:25,855   ✅ redis: up
INFO 2025-07-02 19:14:25,855   ✅ database: up
INFO 2025-07-02 19:14:25,855   ✅ celery_worker: up
INFO 2025-07-02 19:14:25,855   ✅ celery_beat: up
INFO 2025-07-02 19:14:25,855   ❌ flower: down
INFO 2025-07-02 19:14:25,856 健康检查完成
INFO 2025-07-02 19:14:25,856 检查是否需要自动恢复...
WARNING 2025-07-02 19:14:25,856 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 19:14:25,856 启动脚本不存在
ERROR 2025-07-02 19:14:25,856 自动恢复失败: flower
INFO 2025-07-02 19:19:25,866 开始执行健康检查...
INFO 2025-07-02 19:19:27,890 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 3, 'tasks.tasks.cache_monitoring_data': 6, 'authentication.tasks.cleanup_expired_tokens': 3} 个任务
INFO 2025-07-02 19:19:30,914 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 19:19:30,921 ✅ 系统状态: healthy
INFO 2025-07-02 19:19:30,922   ✅ redis: up
INFO 2025-07-02 19:19:30,922   ✅ database: up
INFO 2025-07-02 19:19:30,922   ✅ celery_worker: up
INFO 2025-07-02 19:19:30,922   ✅ celery_beat: up
INFO 2025-07-02 19:19:30,922   ❌ flower: down
INFO 2025-07-02 19:19:30,922 健康检查完成
INFO 2025-07-02 19:19:30,923 检查是否需要自动恢复...
WARNING 2025-07-02 19:19:30,923 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 19:19:30,923 启动脚本不存在
ERROR 2025-07-02 19:19:30,923 自动恢复失败: flower
INFO 2025-07-02 19:24:30,934 开始执行健康检查...
INFO 2025-07-02 19:24:32,957 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 4, 'tasks.tasks.cache_monitoring_data': 7, 'users.check_new_user_levels': 3} 个任务
INFO 2025-07-02 19:24:35,984 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 19:24:35,988 ✅ 系统状态: healthy
INFO 2025-07-02 19:24:35,988   ✅ redis: up
INFO 2025-07-02 19:24:35,988   ✅ database: up
INFO 2025-07-02 19:24:35,988   ✅ celery_worker: up
INFO 2025-07-02 19:24:35,988   ✅ celery_beat: up
INFO 2025-07-02 19:24:35,988   ❌ flower: down
INFO 2025-07-02 19:24:35,988 健康检查完成
INFO 2025-07-02 19:24:35,988 检查是否需要自动恢复...
WARNING 2025-07-02 19:24:35,989 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 19:24:35,989 启动脚本不存在
ERROR 2025-07-02 19:24:35,989 自动恢复失败: flower
INFO 2025-07-02 19:29:35,991 开始执行健康检查...
INFO 2025-07-02 19:29:38,012 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 3, 'tasks.tasks.cache_monitoring_data': 6, 'authentication.tasks.cleanup_expired_tokens': 3} 个任务
INFO 2025-07-02 19:29:41,034 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 19:29:41,042 ✅ 系统状态: healthy
INFO 2025-07-02 19:29:41,042   ✅ redis: up
INFO 2025-07-02 19:29:41,043   ✅ database: up
INFO 2025-07-02 19:29:41,043   ✅ celery_worker: up
INFO 2025-07-02 19:29:41,043   ✅ celery_beat: up
INFO 2025-07-02 19:29:41,043   ❌ flower: down
INFO 2025-07-02 19:29:41,043 健康检查完成
INFO 2025-07-02 19:29:41,043 检查是否需要自动恢复...
WARNING 2025-07-02 19:29:41,043 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 19:29:41,044 启动脚本不存在
ERROR 2025-07-02 19:29:41,044 自动恢复失败: flower
INFO 2025-07-02 19:34:41,051 开始执行健康检查...
INFO 2025-07-02 19:34:43,078 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 4, 'tasks.tasks.cache_monitoring_data': 8, 'users.check_new_user_levels': 4} 个任务
INFO 2025-07-02 19:34:46,107 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 19:34:46,114 ✅ 系统状态: healthy
INFO 2025-07-02 19:34:46,114   ✅ redis: up
INFO 2025-07-02 19:34:46,114   ✅ database: up
INFO 2025-07-02 19:34:46,114   ✅ celery_worker: up
INFO 2025-07-02 19:34:46,114   ✅ celery_beat: up
INFO 2025-07-02 19:34:46,114   ❌ flower: down
INFO 2025-07-02 19:34:46,114 健康检查完成
INFO 2025-07-02 19:34:46,114 检查是否需要自动恢复...
WARNING 2025-07-02 19:34:46,114 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 19:34:46,114 启动脚本不存在
ERROR 2025-07-02 19:34:46,115 自动恢复失败: flower
INFO 2025-07-02 19:39:46,121 开始执行健康检查...
INFO 2025-07-02 19:39:48,140 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 4, 'tasks.tasks.cache_monitoring_data': 7, 'authentication.tasks.cleanup_expired_tokens': 3} 个任务
INFO 2025-07-02 19:39:51,163 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 19:39:51,171 ✅ 系统状态: healthy
INFO 2025-07-02 19:39:51,171   ✅ redis: up
INFO 2025-07-02 19:39:51,171   ✅ database: up
INFO 2025-07-02 19:39:51,171   ✅ celery_worker: up
INFO 2025-07-02 19:39:51,171   ✅ celery_beat: up
INFO 2025-07-02 19:39:51,171   ❌ flower: down
INFO 2025-07-02 19:39:51,171 健康检查完成
INFO 2025-07-02 19:39:51,172 检查是否需要自动恢复...
WARNING 2025-07-02 19:39:51,172 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 19:39:51,172 启动脚本不存在
ERROR 2025-07-02 19:39:51,172 自动恢复失败: flower
INFO 2025-07-02 19:44:51,176 开始执行健康检查...
INFO 2025-07-02 19:44:53,201 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 4, 'tasks.tasks.cache_monitoring_data': 8, 'users.check_new_user_levels': 4} 个任务
INFO 2025-07-02 19:44:56,229 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 19:44:56,237 ✅ 系统状态: healthy
INFO 2025-07-02 19:44:56,237   ✅ redis: up
INFO 2025-07-02 19:44:56,238   ✅ database: up
INFO 2025-07-02 19:44:56,238   ✅ celery_worker: up
INFO 2025-07-02 19:44:56,238   ✅ celery_beat: up
INFO 2025-07-02 19:44:56,238   ❌ flower: down
INFO 2025-07-02 19:44:56,238 健康检查完成
INFO 2025-07-02 19:44:56,238 检查是否需要自动恢复...
WARNING 2025-07-02 19:44:56,238 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 19:44:56,238 启动脚本不存在
ERROR 2025-07-02 19:44:56,239 自动恢复失败: flower
INFO 2025-07-02 19:49:56,247 开始执行健康检查...
INFO 2025-07-02 19:49:58,275 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 4, 'tasks.tasks.cache_monitoring_data': 8, 'users.check_new_user_levels': 4} 个任务
INFO 2025-07-02 19:50:01,304 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 19:50:01,313 ✅ 系统状态: healthy
INFO 2025-07-02 19:50:01,314   ✅ redis: up
INFO 2025-07-02 19:50:01,314   ✅ database: up
INFO 2025-07-02 19:50:01,314   ✅ celery_worker: up
INFO 2025-07-02 19:50:01,314   ✅ celery_beat: up
INFO 2025-07-02 19:50:01,314   ❌ flower: down
INFO 2025-07-02 19:50:01,314 健康检查完成
INFO 2025-07-02 19:50:01,315 检查是否需要自动恢复...
WARNING 2025-07-02 19:50:01,315 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 19:50:01,315 启动脚本不存在
ERROR 2025-07-02 19:50:01,315 自动恢复失败: flower
INFO 2025-07-02 19:55:01,324 开始执行健康检查...
INFO 2025-07-02 19:55:03,347 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 4, 'tasks.tasks.cache_monitoring_data': 7, 'authentication.tasks.cleanup_expired_tokens': 3} 个任务
INFO 2025-07-02 19:55:06,376 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 19:55:06,380 ✅ 系统状态: healthy
INFO 2025-07-02 19:55:06,380   ✅ redis: up
INFO 2025-07-02 19:55:06,380   ✅ database: up
INFO 2025-07-02 19:55:06,380   ✅ celery_worker: up
INFO 2025-07-02 19:55:06,381   ✅ celery_beat: up
INFO 2025-07-02 19:55:06,381   ❌ flower: down
INFO 2025-07-02 19:55:06,381 健康检查完成
INFO 2025-07-02 19:55:06,381 检查是否需要自动恢复...
WARNING 2025-07-02 19:55:06,381 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 19:55:06,381 启动脚本不存在
ERROR 2025-07-02 19:55:06,381 自动恢复失败: flower
INFO 2025-07-02 20:00:06,392 开始执行健康检查...
INFO 2025-07-02 20:00:08,420 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 4, 'tasks.tasks.cache_monitoring_data': 8, 'authentication.tasks.cleanup_expired_tokens': 4} 个任务
INFO 2025-07-02 20:00:11,445 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 20:00:11,451 ✅ 系统状态: healthy
INFO 2025-07-02 20:00:11,451   ✅ redis: up
INFO 2025-07-02 20:00:11,451   ✅ database: up
INFO 2025-07-02 20:00:11,452   ✅ celery_worker: up
INFO 2025-07-02 20:00:11,452   ✅ celery_beat: up
INFO 2025-07-02 20:00:11,452   ❌ flower: down
INFO 2025-07-02 20:00:11,452 健康检查完成
INFO 2025-07-02 20:00:11,452 检查是否需要自动恢复...
WARNING 2025-07-02 20:00:11,452 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 20:00:11,452 启动脚本不存在
ERROR 2025-07-02 20:00:11,452 自动恢复失败: flower
INFO 2025-07-02 20:05:11,477 开始执行健康检查...
INFO 2025-07-02 20:05:13,504 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 4, 'tasks.tasks.cache_monitoring_data': 8, 'authentication.tasks.cleanup_expired_tokens': 4} 个任务
INFO 2025-07-02 20:05:16,531 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 20:05:16,539 ✅ 系统状态: healthy
INFO 2025-07-02 20:05:16,539   ✅ redis: up
INFO 2025-07-02 20:05:16,539   ✅ database: up
INFO 2025-07-02 20:05:16,539   ✅ celery_worker: up
INFO 2025-07-02 20:05:16,539   ✅ celery_beat: up
INFO 2025-07-02 20:05:16,539   ❌ flower: down
INFO 2025-07-02 20:05:16,539 健康检查完成
INFO 2025-07-02 20:05:16,540 检查是否需要自动恢复...
WARNING 2025-07-02 20:05:16,540 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 20:05:16,540 启动脚本不存在
ERROR 2025-07-02 20:05:16,540 自动恢复失败: flower
INFO 2025-07-02 20:10:16,544 开始执行健康检查...
INFO 2025-07-02 20:10:18,568 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 4, 'tasks.tasks.cache_monitoring_data': 8, 'authentication.tasks.cleanup_expired_tokens': 4} 个任务
INFO 2025-07-02 20:10:21,590 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 20:10:21,594 ✅ 系统状态: healthy
INFO 2025-07-02 20:10:21,594   ✅ redis: up
INFO 2025-07-02 20:10:21,594   ✅ database: up
INFO 2025-07-02 20:10:21,594   ✅ celery_worker: up
INFO 2025-07-02 20:10:21,594   ✅ celery_beat: up
INFO 2025-07-02 20:10:21,594   ❌ flower: down
INFO 2025-07-02 20:10:21,594 健康检查完成
INFO 2025-07-02 20:10:21,594 检查是否需要自动恢复...
WARNING 2025-07-02 20:10:21,594 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 20:10:21,595 启动脚本不存在
ERROR 2025-07-02 20:10:21,595 自动恢复失败: flower
INFO 2025-07-02 20:15:21,604 开始执行健康检查...
INFO 2025-07-02 20:15:23,625 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 5, 'tasks.tasks.cache_monitoring_data': 9, 'users.check_new_user_levels': 4} 个任务
INFO 2025-07-02 20:15:26,652 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 20:15:26,660 ✅ 系统状态: healthy
INFO 2025-07-02 20:15:26,660   ✅ redis: up
INFO 2025-07-02 20:15:26,660   ✅ database: up
INFO 2025-07-02 20:15:26,660   ✅ celery_worker: up
INFO 2025-07-02 20:15:26,660   ✅ celery_beat: up
INFO 2025-07-02 20:15:26,660   ❌ flower: down
INFO 2025-07-02 20:15:26,660 健康检查完成
INFO 2025-07-02 20:15:26,661 检查是否需要自动恢复...
WARNING 2025-07-02 20:15:26,661 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 20:15:26,661 启动脚本不存在
ERROR 2025-07-02 20:15:26,661 自动恢复失败: flower
INFO 2025-07-02 20:20:26,665 开始执行健康检查...
INFO 2025-07-02 20:20:28,692 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 4, 'tasks.tasks.cache_monitoring_data': 8, 'authentication.tasks.cleanup_expired_tokens': 4} 个任务
INFO 2025-07-02 20:20:31,710 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 20:20:31,714 ✅ 系统状态: healthy
INFO 2025-07-02 20:20:31,714   ✅ redis: up
INFO 2025-07-02 20:20:31,715   ✅ database: up
INFO 2025-07-02 20:20:31,715   ✅ celery_worker: up
INFO 2025-07-02 20:20:31,715   ✅ celery_beat: up
INFO 2025-07-02 20:20:31,715   ❌ flower: down
INFO 2025-07-02 20:20:31,715 健康检查完成
INFO 2025-07-02 20:20:31,715 检查是否需要自动恢复...
WARNING 2025-07-02 20:20:31,715 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 20:20:31,715 启动脚本不存在
ERROR 2025-07-02 20:20:31,715 自动恢复失败: flower
INFO 2025-07-02 20:25:31,723 开始执行健康检查...
INFO 2025-07-02 20:25:33,747 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 4, 'tasks.tasks.cache_monitoring_data': 8, 'authentication.tasks.cleanup_expired_tokens': 4} 个任务
INFO 2025-07-02 20:25:36,769 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 20:25:36,771 ✅ 系统状态: healthy
INFO 2025-07-02 20:25:36,771   ✅ redis: up
INFO 2025-07-02 20:25:36,771   ✅ database: up
INFO 2025-07-02 20:25:36,771   ✅ celery_worker: up
INFO 2025-07-02 20:25:36,771   ✅ celery_beat: up
INFO 2025-07-02 20:25:36,771   ❌ flower: down
INFO 2025-07-02 20:25:36,771 健康检查完成
INFO 2025-07-02 20:25:36,771 检查是否需要自动恢复...
WARNING 2025-07-02 20:25:36,771 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 20:25:36,771 启动脚本不存在
ERROR 2025-07-02 20:25:36,771 自动恢复失败: flower
INFO 2025-07-02 20:30:36,775 开始执行健康检查...
INFO 2025-07-02 20:30:38,787 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 5, 'tasks.tasks.cache_monitoring_data': 9, 'authentication.tasks.cleanup_expired_tokens': 4} 个任务
INFO 2025-07-02 20:30:41,813 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 20:30:41,821 ✅ 系统状态: healthy
INFO 2025-07-02 20:30:41,821   ✅ redis: up
INFO 2025-07-02 20:30:41,821   ✅ database: up
INFO 2025-07-02 20:30:41,821   ✅ celery_worker: up
INFO 2025-07-02 20:30:41,821   ✅ celery_beat: up
INFO 2025-07-02 20:30:41,821   ❌ flower: down
INFO 2025-07-02 20:30:41,821 健康检查完成
INFO 2025-07-02 20:30:41,821 检查是否需要自动恢复...
WARNING 2025-07-02 20:30:41,822 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 20:30:41,822 启动脚本不存在
ERROR 2025-07-02 20:30:41,822 自动恢复失败: flower
INFO 2025-07-02 20:35:41,842 开始执行健康检查...
INFO 2025-07-02 20:35:43,861 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 5, 'tasks.tasks.cache_monitoring_data': 9, 'authentication.tasks.cleanup_expired_tokens': 4} 个任务
INFO 2025-07-02 20:35:46,881 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 20:35:46,890 ✅ 系统状态: healthy
INFO 2025-07-02 20:35:46,890   ✅ redis: up
INFO 2025-07-02 20:35:46,891   ✅ database: up
INFO 2025-07-02 20:35:46,891   ✅ celery_worker: up
INFO 2025-07-02 20:35:46,891   ✅ celery_beat: up
INFO 2025-07-02 20:35:46,891   ❌ flower: down
INFO 2025-07-02 20:35:46,891 健康检查完成
INFO 2025-07-02 20:35:46,891 检查是否需要自动恢复...
WARNING 2025-07-02 20:35:46,891 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 20:35:46,892 启动脚本不存在
ERROR 2025-07-02 20:35:46,892 自动恢复失败: flower
INFO 2025-07-02 20:40:46,903 开始执行健康检查...
INFO 2025-07-02 20:40:48,926 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 5, 'tasks.tasks.cache_monitoring_data': 10, 'users.check_new_user_levels': 5} 个任务
INFO 2025-07-02 20:40:51,947 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 20:40:51,954 ✅ 系统状态: healthy
INFO 2025-07-02 20:40:51,954   ✅ redis: up
INFO 2025-07-02 20:40:51,954   ✅ database: up
INFO 2025-07-02 20:40:51,954   ✅ celery_worker: up
INFO 2025-07-02 20:40:51,954   ✅ celery_beat: up
INFO 2025-07-02 20:40:51,954   ❌ flower: down
INFO 2025-07-02 20:40:51,954 健康检查完成
INFO 2025-07-02 20:40:51,955 检查是否需要自动恢复...
WARNING 2025-07-02 20:40:51,955 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 20:40:51,955 启动脚本不存在
ERROR 2025-07-02 20:40:51,955 自动恢复失败: flower
INFO 2025-07-02 20:45:51,967 开始执行健康检查...
INFO 2025-07-02 20:45:53,988 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 5, 'tasks.tasks.cache_monitoring_data': 9, 'authentication.tasks.cleanup_expired_tokens': 4} 个任务
INFO 2025-07-02 20:45:57,009 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 20:45:57,013 ✅ 系统状态: healthy
INFO 2025-07-02 20:45:57,013   ✅ redis: up
INFO 2025-07-02 20:45:57,013   ✅ database: up
INFO 2025-07-02 20:45:57,013   ✅ celery_worker: up
INFO 2025-07-02 20:45:57,013   ✅ celery_beat: up
INFO 2025-07-02 20:45:57,013   ❌ flower: down
INFO 2025-07-02 20:45:57,013 健康检查完成
INFO 2025-07-02 20:45:57,013 检查是否需要自动恢复...
WARNING 2025-07-02 20:45:57,013 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 20:45:57,014 启动脚本不存在
ERROR 2025-07-02 20:45:57,014 自动恢复失败: flower
INFO 2025-07-02 20:50:57,026 开始执行健康检查...
INFO 2025-07-02 20:50:59,048 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 5, 'tasks.tasks.cache_monitoring_data': 10, 'users.check_new_user_levels': 5} 个任务
INFO 2025-07-02 20:51:02,070 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 20:51:02,076 ✅ 系统状态: healthy
INFO 2025-07-02 20:51:02,077   ✅ redis: up
INFO 2025-07-02 20:51:02,077   ✅ database: up
INFO 2025-07-02 20:51:02,077   ✅ celery_worker: up
INFO 2025-07-02 20:51:02,077   ✅ celery_beat: up
INFO 2025-07-02 20:51:02,077   ❌ flower: down
INFO 2025-07-02 20:51:02,077 健康检查完成
INFO 2025-07-02 20:51:02,077 检查是否需要自动恢复...
WARNING 2025-07-02 20:51:02,078 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 20:51:02,078 启动脚本不存在
ERROR 2025-07-02 20:51:02,078 自动恢复失败: flower
INFO 2025-07-02 20:56:02,090 开始执行健康检查...
INFO 2025-07-02 20:56:04,120 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 5, 'tasks.tasks.cache_monitoring_data': 10, 'users.check_new_user_levels': 5} 个任务
INFO 2025-07-02 20:56:07,154 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 20:56:07,158 ✅ 系统状态: healthy
INFO 2025-07-02 20:56:07,158   ✅ redis: up
INFO 2025-07-02 20:56:07,158   ✅ database: up
INFO 2025-07-02 20:56:07,158   ✅ celery_worker: up
INFO 2025-07-02 20:56:07,158   ✅ celery_beat: up
INFO 2025-07-02 20:56:07,158   ❌ flower: down
INFO 2025-07-02 20:56:07,158 健康检查完成
INFO 2025-07-02 20:56:07,158 检查是否需要自动恢复...
WARNING 2025-07-02 20:56:07,158 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 20:56:07,158 启动脚本不存在
ERROR 2025-07-02 20:56:07,159 自动恢复失败: flower
INFO 2025-07-02 21:01:07,171 开始执行健康检查...
INFO 2025-07-02 21:01:09,199 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 5, 'tasks.tasks.cache_monitoring_data': 10, 'authentication.tasks.cleanup_expired_tokens': 5} 个任务
INFO 2025-07-02 21:01:12,229 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 21:01:12,239 ✅ 系统状态: healthy
INFO 2025-07-02 21:01:12,239   ✅ redis: up
INFO 2025-07-02 21:01:12,239   ✅ database: up
INFO 2025-07-02 21:01:12,240   ✅ celery_worker: up
INFO 2025-07-02 21:01:12,240   ✅ celery_beat: up
INFO 2025-07-02 21:01:12,240   ❌ flower: down
INFO 2025-07-02 21:01:12,240 健康检查完成
INFO 2025-07-02 21:01:12,240 检查是否需要自动恢复...
WARNING 2025-07-02 21:01:12,240 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 21:01:12,241 启动脚本不存在
ERROR 2025-07-02 21:01:12,241 自动恢复失败: flower
INFO 2025-07-02 21:06:12,251 开始执行健康检查...
INFO 2025-07-02 21:06:14,273 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 6, 'tasks.tasks.cache_monitoring_data': 11, 'users.check_new_user_levels': 5} 个任务
INFO 2025-07-02 21:06:17,305 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 21:06:17,312 ✅ 系统状态: healthy
INFO 2025-07-02 21:06:17,312   ✅ redis: up
INFO 2025-07-02 21:06:17,312   ✅ database: up
INFO 2025-07-02 21:06:17,312   ✅ celery_worker: up
INFO 2025-07-02 21:06:17,313   ✅ celery_beat: up
INFO 2025-07-02 21:06:17,313   ❌ flower: down
INFO 2025-07-02 21:06:17,313 健康检查完成
INFO 2025-07-02 21:06:17,313 检查是否需要自动恢复...
WARNING 2025-07-02 21:06:17,313 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 21:06:17,313 启动脚本不存在
ERROR 2025-07-02 21:06:17,313 自动恢复失败: flower
INFO 2025-07-02 21:11:17,323 开始执行健康检查...
INFO 2025-07-02 21:11:19,341 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 5, 'tasks.tasks.cache_monitoring_data': 10, 'authentication.tasks.cleanup_expired_tokens': 5} 个任务
INFO 2025-07-02 21:11:22,359 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 21:11:22,362 ✅ 系统状态: healthy
INFO 2025-07-02 21:11:22,362   ✅ redis: up
INFO 2025-07-02 21:11:22,362   ✅ database: up
INFO 2025-07-02 21:11:22,362   ✅ celery_worker: up
INFO 2025-07-02 21:11:22,362   ✅ celery_beat: up
INFO 2025-07-02 21:11:22,362   ❌ flower: down
INFO 2025-07-02 21:11:22,362 健康检查完成
INFO 2025-07-02 21:11:22,363 检查是否需要自动恢复...
WARNING 2025-07-02 21:11:22,363 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 21:11:22,363 启动脚本不存在
ERROR 2025-07-02 21:11:22,363 自动恢复失败: flower
INFO 2025-07-02 21:16:22,368 开始执行健康检查...
INFO 2025-07-02 21:16:24,393 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 5, 'tasks.tasks.cache_monitoring_data': 10, 'authentication.tasks.cleanup_expired_tokens': 5} 个任务
INFO 2025-07-02 21:16:27,417 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 21:16:27,424 ✅ 系统状态: healthy
INFO 2025-07-02 21:16:27,424   ✅ redis: up
INFO 2025-07-02 21:16:27,424   ✅ database: up
INFO 2025-07-02 21:16:27,424   ✅ celery_worker: up
INFO 2025-07-02 21:16:27,424   ✅ celery_beat: up
INFO 2025-07-02 21:16:27,424   ❌ flower: down
INFO 2025-07-02 21:16:27,424 健康检查完成
INFO 2025-07-02 21:16:27,425 检查是否需要自动恢复...
WARNING 2025-07-02 21:16:27,425 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 21:16:27,425 启动脚本不存在
ERROR 2025-07-02 21:16:27,425 自动恢复失败: flower
INFO 2025-07-02 21:21:27,436 开始执行健康检查...
INFO 2025-07-02 21:21:29,464 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 5, 'tasks.tasks.cache_monitoring_data': 10, 'authentication.tasks.cleanup_expired_tokens': 5} 个任务
INFO 2025-07-02 21:21:32,492 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 21:21:32,498 ✅ 系统状态: healthy
INFO 2025-07-02 21:21:32,499   ✅ redis: up
INFO 2025-07-02 21:21:32,499   ✅ database: up
INFO 2025-07-02 21:21:32,499   ✅ celery_worker: up
INFO 2025-07-02 21:21:32,499   ✅ celery_beat: up
INFO 2025-07-02 21:21:32,499   ❌ flower: down
INFO 2025-07-02 21:21:32,499 健康检查完成
INFO 2025-07-02 21:21:32,499 检查是否需要自动恢复...
WARNING 2025-07-02 21:21:32,499 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 21:21:32,500 启动脚本不存在
ERROR 2025-07-02 21:21:32,500 自动恢复失败: flower
INFO 2025-07-02 21:26:32,511 开始执行健康检查...
INFO 2025-07-02 21:26:34,537 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 6, 'tasks.tasks.cache_monitoring_data': 11, 'users.check_new_user_levels': 5} 个任务
INFO 2025-07-02 21:26:37,567 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 21:26:37,574 ✅ 系统状态: healthy
INFO 2025-07-02 21:26:37,574   ✅ redis: up
INFO 2025-07-02 21:26:37,574   ✅ database: up
INFO 2025-07-02 21:26:37,575   ✅ celery_worker: up
INFO 2025-07-02 21:26:37,575   ✅ celery_beat: up
INFO 2025-07-02 21:26:37,575   ❌ flower: down
INFO 2025-07-02 21:26:37,575 健康检查完成
INFO 2025-07-02 21:26:37,575 检查是否需要自动恢复...
WARNING 2025-07-02 21:26:37,575 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 21:26:37,575 启动脚本不存在
ERROR 2025-07-02 21:26:37,575 自动恢复失败: flower
INFO 2025-07-02 21:31:37,552 开始执行健康检查...
INFO 2025-07-02 21:31:39,568 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 6, 'tasks.tasks.cache_monitoring_data': 11, 'authentication.tasks.cleanup_expired_tokens': 5} 个任务
INFO 2025-07-02 21:31:42,589 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 21:31:42,592 ✅ 系统状态: healthy
INFO 2025-07-02 21:31:42,592   ✅ redis: up
INFO 2025-07-02 21:31:42,592   ✅ database: up
INFO 2025-07-02 21:31:42,593   ✅ celery_worker: up
INFO 2025-07-02 21:31:42,593   ✅ celery_beat: up
INFO 2025-07-02 21:31:42,593   ❌ flower: down
INFO 2025-07-02 21:31:42,593 健康检查完成
INFO 2025-07-02 21:31:42,593 检查是否需要自动恢复...
WARNING 2025-07-02 21:31:42,593 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 21:31:42,593 启动脚本不存在
ERROR 2025-07-02 21:31:42,593 自动恢复失败: flower
INFO 2025-07-02 21:36:42,594 开始执行健康检查...
INFO 2025-07-02 21:36:44,618 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 6, 'tasks.tasks.cache_monitoring_data': 11, 'authentication.tasks.cleanup_expired_tokens': 5} 个任务
INFO 2025-07-02 21:36:47,640 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 21:36:47,647 ✅ 系统状态: healthy
INFO 2025-07-02 21:36:47,647   ✅ redis: up
INFO 2025-07-02 21:36:47,648   ✅ database: up
INFO 2025-07-02 21:36:47,648   ✅ celery_worker: up
INFO 2025-07-02 21:36:47,648   ✅ celery_beat: up
INFO 2025-07-02 21:36:47,648   ❌ flower: down
INFO 2025-07-02 21:36:47,648 健康检查完成
INFO 2025-07-02 21:36:47,648 检查是否需要自动恢复...
WARNING 2025-07-02 21:36:47,648 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 21:36:47,648 启动脚本不存在
ERROR 2025-07-02 21:36:47,649 自动恢复失败: flower
INFO 2025-07-02 21:41:47,650 开始执行健康检查...
INFO 2025-07-02 21:41:49,672 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 6, 'tasks.tasks.cache_monitoring_data': 11, 'authentication.tasks.cleanup_expired_tokens': 5} 个任务
INFO 2025-07-02 21:41:52,693 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 21:41:52,697 ✅ 系统状态: healthy
INFO 2025-07-02 21:41:52,697   ✅ redis: up
INFO 2025-07-02 21:41:52,697   ✅ database: up
INFO 2025-07-02 21:41:52,697   ✅ celery_worker: up
INFO 2025-07-02 21:41:52,697   ✅ celery_beat: up
INFO 2025-07-02 21:41:52,697   ❌ flower: down
INFO 2025-07-02 21:41:52,697 健康检查完成
INFO 2025-07-02 21:41:52,697 检查是否需要自动恢复...
WARNING 2025-07-02 21:41:52,697 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 21:41:52,698 启动脚本不存在
ERROR 2025-07-02 21:41:52,698 自动恢复失败: flower
INFO 2025-07-02 21:46:52,709 开始执行健康检查...
INFO 2025-07-02 21:46:54,736 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 6, 'tasks.tasks.cache_monitoring_data': 11, 'authentication.tasks.cleanup_expired_tokens': 5} 个任务
INFO 2025-07-02 21:46:57,760 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 21:46:57,768 ✅ 系统状态: healthy
INFO 2025-07-02 21:46:57,768   ✅ redis: up
INFO 2025-07-02 21:46:57,768   ✅ database: up
INFO 2025-07-02 21:46:57,768   ✅ celery_worker: up
INFO 2025-07-02 21:46:57,768   ✅ celery_beat: up
INFO 2025-07-02 21:46:57,769   ❌ flower: down
INFO 2025-07-02 21:46:57,769 健康检查完成
INFO 2025-07-02 21:46:57,769 检查是否需要自动恢复...
WARNING 2025-07-02 21:46:57,769 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 21:46:57,769 启动脚本不存在
ERROR 2025-07-02 21:46:57,769 自动恢复失败: flower
INFO 2025-07-02 21:51:57,776 开始执行健康检查...
INFO 2025-07-02 21:51:59,796 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 6, 'tasks.tasks.cache_monitoring_data': 12, 'users.check_new_user_levels': 6} 个任务
INFO 2025-07-02 21:52:02,817 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 21:52:02,822 ✅ 系统状态: healthy
INFO 2025-07-02 21:52:02,822   ✅ redis: up
INFO 2025-07-02 21:52:02,822   ✅ database: up
INFO 2025-07-02 21:52:02,822   ✅ celery_worker: up
INFO 2025-07-02 21:52:02,822   ✅ celery_beat: up
INFO 2025-07-02 21:52:02,822   ❌ flower: down
INFO 2025-07-02 21:52:02,822 健康检查完成
INFO 2025-07-02 21:52:02,822 检查是否需要自动恢复...
WARNING 2025-07-02 21:52:02,822 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 21:52:02,822 启动脚本不存在
ERROR 2025-07-02 21:52:02,822 自动恢复失败: flower
INFO 2025-07-02 21:57:02,825 开始执行健康检查...
INFO 2025-07-02 21:57:04,856 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 6, 'tasks.tasks.cache_monitoring_data': 11, 'authentication.tasks.cleanup_expired_tokens': 5} 个任务
INFO 2025-07-02 21:57:07,879 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 21:57:07,886 ✅ 系统状态: healthy
INFO 2025-07-02 21:57:07,887   ✅ redis: up
INFO 2025-07-02 21:57:07,887   ✅ database: up
INFO 2025-07-02 21:57:07,887   ✅ celery_worker: up
INFO 2025-07-02 21:57:07,887   ✅ celery_beat: up
INFO 2025-07-02 21:57:07,887   ❌ flower: down
INFO 2025-07-02 21:57:07,887 健康检查完成
INFO 2025-07-02 21:57:07,887 检查是否需要自动恢复...
WARNING 2025-07-02 21:57:07,888 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 21:57:07,888 启动脚本不存在
ERROR 2025-07-02 21:57:07,888 自动恢复失败: flower
INFO 2025-07-02 22:02:07,893 开始执行健康检查...
INFO 2025-07-02 22:02:09,913 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 6, 'tasks.tasks.cache_monitoring_data': 12, 'authentication.tasks.cleanup_expired_tokens': 5} 个任务
INFO 2025-07-02 22:02:12,934 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 22:02:12,939 ✅ 系统状态: healthy
INFO 2025-07-02 22:02:12,939   ✅ redis: up
INFO 2025-07-02 22:02:12,939   ✅ database: up
INFO 2025-07-02 22:02:12,939   ✅ celery_worker: up
INFO 2025-07-02 22:02:12,939   ✅ celery_beat: up
INFO 2025-07-02 22:02:12,939   ❌ flower: down
INFO 2025-07-02 22:02:12,939 健康检查完成
INFO 2025-07-02 22:02:12,939 检查是否需要自动恢复...
WARNING 2025-07-02 22:02:12,939 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 22:02:12,940 启动脚本不存在
ERROR 2025-07-02 22:02:12,940 自动恢复失败: flower
INFO 2025-07-02 22:07:12,945 开始执行健康检查...
INFO 2025-07-02 22:07:14,971 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 6, 'tasks.tasks.cache_monitoring_data': 12, 'authentication.tasks.cleanup_expired_tokens': 5} 个任务
INFO 2025-07-02 22:07:17,993 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 22:07:18,000 ✅ 系统状态: healthy
INFO 2025-07-02 22:07:18,000   ✅ redis: up
INFO 2025-07-02 22:07:18,000   ✅ database: up
INFO 2025-07-02 22:07:18,001   ✅ celery_worker: up
INFO 2025-07-02 22:07:18,001   ✅ celery_beat: up
INFO 2025-07-02 22:07:18,001   ❌ flower: down
INFO 2025-07-02 22:07:18,001 健康检查完成
INFO 2025-07-02 22:07:18,001 检查是否需要自动恢复...
WARNING 2025-07-02 22:07:18,001 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 22:07:18,001 启动脚本不存在
ERROR 2025-07-02 22:07:18,001 自动恢复失败: flower
INFO 2025-07-02 22:12:18,012 开始执行健康检查...
INFO 2025-07-02 22:12:20,038 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 8, 'tasks.tasks.cache_monitoring_data': 13, 'users.check_new_user_levels': 6} 个任务
INFO 2025-07-02 22:12:23,064 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 22:12:23,071 ✅ 系统状态: healthy
INFO 2025-07-02 22:12:23,071   ✅ redis: up
INFO 2025-07-02 22:12:23,071   ✅ database: up
INFO 2025-07-02 22:12:23,072   ✅ celery_worker: up
INFO 2025-07-02 22:12:23,072   ✅ celery_beat: up
INFO 2025-07-02 22:12:23,072   ❌ flower: down
INFO 2025-07-02 22:12:23,072 健康检查完成
INFO 2025-07-02 22:12:23,072 检查是否需要自动恢复...
WARNING 2025-07-02 22:12:23,072 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 22:12:23,072 启动脚本不存在
ERROR 2025-07-02 22:12:23,072 自动恢复失败: flower
INFO 2025-07-02 22:17:23,081 开始执行健康检查...
INFO 2025-07-02 22:17:25,107 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 8, 'tasks.tasks.cache_monitoring_data': 13, 'users.check_new_user_levels': 6} 个任务
INFO 2025-07-02 22:17:28,132 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 22:17:28,139 ✅ 系统状态: healthy
INFO 2025-07-02 22:17:28,140   ✅ redis: up
INFO 2025-07-02 22:17:28,140   ✅ database: up
INFO 2025-07-02 22:17:28,140   ✅ celery_worker: up
INFO 2025-07-02 22:17:28,140   ✅ celery_beat: up
INFO 2025-07-02 22:17:28,140   ❌ flower: down
INFO 2025-07-02 22:17:28,140 健康检查完成
INFO 2025-07-02 22:17:28,140 检查是否需要自动恢复...
WARNING 2025-07-02 22:17:28,140 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 22:17:28,141 启动脚本不存在
ERROR 2025-07-02 22:17:28,141 自动恢复失败: flower
INFO 2025-07-02 22:22:28,149 开始执行健康检查...
INFO 2025-07-02 22:22:30,169 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 8, 'tasks.tasks.cache_monitoring_data': 13, 'users.check_new_user_levels': 6} 个任务
INFO 2025-07-02 22:22:33,182 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 22:22:33,187 ✅ 系统状态: healthy
INFO 2025-07-02 22:22:33,187   ✅ redis: up
INFO 2025-07-02 22:22:33,187   ✅ database: up
INFO 2025-07-02 22:22:33,187   ✅ celery_worker: up
INFO 2025-07-02 22:22:33,187   ✅ celery_beat: up
INFO 2025-07-02 22:22:33,187   ❌ flower: down
INFO 2025-07-02 22:22:33,187 健康检查完成
INFO 2025-07-02 22:22:33,187 检查是否需要自动恢复...
WARNING 2025-07-02 22:22:33,187 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 22:22:33,187 启动脚本不存在
ERROR 2025-07-02 22:22:33,187 自动恢复失败: flower
INFO 2025-07-02 22:27:33,211 开始执行健康检查...
INFO 2025-07-02 22:27:35,230 Worker <EMAIL>: 已处理 {'users.check_new_user_levels': 6, 'tasks.tasks.cache_monitoring_data': 12, 'authentication.tasks.cleanup_expired_tokens': 5} 个任务
INFO 2025-07-02 22:27:38,249 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 22:27:38,254 ✅ 系统状态: healthy
INFO 2025-07-02 22:27:38,254   ✅ redis: up
INFO 2025-07-02 22:27:38,254   ✅ database: up
INFO 2025-07-02 22:27:38,254   ✅ celery_worker: up
INFO 2025-07-02 22:27:38,255   ✅ celery_beat: up
INFO 2025-07-02 22:27:38,255   ❌ flower: down
INFO 2025-07-02 22:27:38,255 健康检查完成
INFO 2025-07-02 22:27:38,255 检查是否需要自动恢复...
WARNING 2025-07-02 22:27:38,255 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 22:27:38,255 启动脚本不存在
ERROR 2025-07-02 22:27:38,255 自动恢复失败: flower
INFO 2025-07-02 22:32:38,262 开始执行健康检查...
INFO 2025-07-02 22:32:40,282 Worker <EMAIL>: 已处理 {'authentication.tasks.cleanup_expired_tokens': 8, 'tasks.tasks.cache_monitoring_data': 14, 'users.check_new_user_levels': 7} 个任务
INFO 2025-07-02 22:32:43,298 任务队列状态 - 活跃: 0, 预留: 0, 调度: 0
INFO 2025-07-02 22:32:43,303 ✅ 系统状态: healthy
INFO 2025-07-02 22:32:43,303   ✅ redis: up
INFO 2025-07-02 22:32:43,303   ✅ database: up
INFO 2025-07-02 22:32:43,303   ✅ celery_worker: up
INFO 2025-07-02 22:32:43,303   ✅ celery_beat: up
INFO 2025-07-02 22:32:43,303   ❌ flower: down
INFO 2025-07-02 22:32:43,303 健康检查完成
INFO 2025-07-02 22:32:43,304 检查是否需要自动恢复...
WARNING 2025-07-02 22:32:43,304 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 22:32:43,304 启动脚本不存在
ERROR 2025-07-02 22:32:43,304 自动恢复失败: flower
INFO 2025-07-02 22:37:43,306 开始执行健康检查...
INFO 2025-07-02 22:37:43,310 ❌ 系统状态: unhealthy
INFO 2025-07-02 22:37:43,310   ✅ redis: up
INFO 2025-07-02 22:37:43,310   ✅ database: up
INFO 2025-07-02 22:37:43,310   ❌ celery_worker: down
INFO 2025-07-02 22:37:43,310   ❌ celery_beat: down
INFO 2025-07-02 22:37:43,310   ❌ flower: down
INFO 2025-07-02 22:37:43,310 健康检查完成
INFO 2025-07-02 22:37:43,311 检查是否需要自动恢复...
WARNING 2025-07-02 22:37:43,311 检测到问题: Celery Worker未运行，尝试重启 worker
ERROR 2025-07-02 22:37:43,311 启动脚本不存在
ERROR 2025-07-02 22:37:43,311 自动恢复失败: worker
WARNING 2025-07-02 22:37:43,311 检测到问题: Celery Beat未运行，尝试重启 beat
ERROR 2025-07-02 22:37:43,311 启动脚本不存在
ERROR 2025-07-02 22:37:43,311 自动恢复失败: beat
WARNING 2025-07-02 22:37:43,311 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 22:37:43,311 启动脚本不存在
ERROR 2025-07-02 22:37:43,311 自动恢复失败: flower
INFO 2025-07-02 22:42:43,251 开始执行健康检查...
INFO 2025-07-02 22:42:43,259 ❌ 系统状态: unhealthy
INFO 2025-07-02 22:42:43,259   ✅ redis: up
INFO 2025-07-02 22:42:43,259   ✅ database: up
INFO 2025-07-02 22:42:43,259   ❌ celery_worker: down
INFO 2025-07-02 22:42:43,259   ❌ celery_beat: down
INFO 2025-07-02 22:42:43,259   ❌ flower: down
INFO 2025-07-02 22:42:43,259 健康检查完成
INFO 2025-07-02 22:42:43,259 检查是否需要自动恢复...
WARNING 2025-07-02 22:42:43,259 检测到问题: Celery Worker未运行，尝试重启 worker
ERROR 2025-07-02 22:42:43,259 启动脚本不存在
ERROR 2025-07-02 22:42:43,259 自动恢复失败: worker
WARNING 2025-07-02 22:42:43,260 检测到问题: Celery Beat未运行，尝试重启 beat
ERROR 2025-07-02 22:42:43,260 启动脚本不存在
ERROR 2025-07-02 22:42:43,260 自动恢复失败: beat
WARNING 2025-07-02 22:42:43,260 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 22:42:43,260 启动脚本不存在
ERROR 2025-07-02 22:42:43,260 自动恢复失败: flower
INFO 2025-07-02 22:47:43,263 开始执行健康检查...
INFO 2025-07-02 22:47:43,268 ❌ 系统状态: unhealthy
INFO 2025-07-02 22:47:43,268   ✅ redis: up
INFO 2025-07-02 22:47:43,268   ✅ database: up
INFO 2025-07-02 22:47:43,268   ❌ celery_worker: down
INFO 2025-07-02 22:47:43,268   ❌ celery_beat: down
INFO 2025-07-02 22:47:43,269   ❌ flower: down
INFO 2025-07-02 22:47:43,269 健康检查完成
INFO 2025-07-02 22:47:43,269 检查是否需要自动恢复...
WARNING 2025-07-02 22:47:43,269 检测到问题: Celery Worker未运行，尝试重启 worker
ERROR 2025-07-02 22:47:43,269 启动脚本不存在
ERROR 2025-07-02 22:47:43,269 自动恢复失败: worker
WARNING 2025-07-02 22:47:43,269 检测到问题: Celery Beat未运行，尝试重启 beat
ERROR 2025-07-02 22:47:43,269 启动脚本不存在
ERROR 2025-07-02 22:47:43,269 自动恢复失败: beat
WARNING 2025-07-02 22:47:43,269 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 22:47:43,270 启动脚本不存在
ERROR 2025-07-02 22:47:43,270 自动恢复失败: flower
INFO 2025-07-02 22:52:43,269 开始执行健康检查...
INFO 2025-07-02 22:52:43,274 ❌ 系统状态: unhealthy
INFO 2025-07-02 22:52:43,275   ✅ redis: up
INFO 2025-07-02 22:52:43,275   ✅ database: up
INFO 2025-07-02 22:52:43,275   ❌ celery_worker: down
INFO 2025-07-02 22:52:43,275   ❌ celery_beat: down
INFO 2025-07-02 22:52:43,275   ❌ flower: down
INFO 2025-07-02 22:52:43,275 健康检查完成
INFO 2025-07-02 22:52:43,275 检查是否需要自动恢复...
WARNING 2025-07-02 22:52:43,275 检测到问题: Celery Worker未运行，尝试重启 worker
ERROR 2025-07-02 22:52:43,275 启动脚本不存在
ERROR 2025-07-02 22:52:43,275 自动恢复失败: worker
WARNING 2025-07-02 22:52:43,275 检测到问题: Celery Beat未运行，尝试重启 beat
ERROR 2025-07-02 22:52:43,275 启动脚本不存在
ERROR 2025-07-02 22:52:43,275 自动恢复失败: beat
WARNING 2025-07-02 22:52:43,275 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 22:52:43,276 启动脚本不存在
ERROR 2025-07-02 22:52:43,276 自动恢复失败: flower
INFO 2025-07-02 22:57:43,279 开始执行健康检查...
INFO 2025-07-02 22:57:43,289 ❌ 系统状态: unhealthy
INFO 2025-07-02 22:57:43,289   ✅ redis: up
INFO 2025-07-02 22:57:43,289   ✅ database: up
INFO 2025-07-02 22:57:43,289   ❌ celery_worker: down
INFO 2025-07-02 22:57:43,289   ❌ celery_beat: down
INFO 2025-07-02 22:57:43,289   ❌ flower: down
INFO 2025-07-02 22:57:43,289 健康检查完成
INFO 2025-07-02 22:57:43,289 检查是否需要自动恢复...
WARNING 2025-07-02 22:57:43,289 检测到问题: Celery Worker未运行，尝试重启 worker
ERROR 2025-07-02 22:57:43,289 启动脚本不存在
ERROR 2025-07-02 22:57:43,290 自动恢复失败: worker
WARNING 2025-07-02 22:57:43,290 检测到问题: Celery Beat未运行，尝试重启 beat
ERROR 2025-07-02 22:57:43,290 启动脚本不存在
ERROR 2025-07-02 22:57:43,290 自动恢复失败: beat
WARNING 2025-07-02 22:57:43,290 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 22:57:43,290 启动脚本不存在
ERROR 2025-07-02 22:57:43,290 自动恢复失败: flower
INFO 2025-07-02 23:02:43,289 开始执行健康检查...
INFO 2025-07-02 23:02:43,295 ❌ 系统状态: unhealthy
INFO 2025-07-02 23:02:43,295   ✅ redis: up
INFO 2025-07-02 23:02:43,295   ✅ database: up
INFO 2025-07-02 23:02:43,296   ❌ celery_worker: down
INFO 2025-07-02 23:02:43,296   ❌ celery_beat: down
INFO 2025-07-02 23:02:43,296   ❌ flower: down
INFO 2025-07-02 23:02:43,296 健康检查完成
INFO 2025-07-02 23:02:43,296 检查是否需要自动恢复...
WARNING 2025-07-02 23:02:43,296 检测到问题: Celery Worker未运行，尝试重启 worker
ERROR 2025-07-02 23:02:43,296 启动脚本不存在
ERROR 2025-07-02 23:02:43,297 自动恢复失败: worker
WARNING 2025-07-02 23:02:43,297 检测到问题: Celery Beat未运行，尝试重启 beat
ERROR 2025-07-02 23:02:43,297 启动脚本不存在
ERROR 2025-07-02 23:02:43,297 自动恢复失败: beat
WARNING 2025-07-02 23:02:43,297 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 23:02:43,297 启动脚本不存在
ERROR 2025-07-02 23:02:43,298 自动恢复失败: flower
INFO 2025-07-02 23:07:43,300 开始执行健康检查...
INFO 2025-07-02 23:07:43,304 ❌ 系统状态: unhealthy
INFO 2025-07-02 23:07:43,304   ✅ redis: up
INFO 2025-07-02 23:07:43,304   ✅ database: up
INFO 2025-07-02 23:07:43,304   ❌ celery_worker: down
INFO 2025-07-02 23:07:43,304   ❌ celery_beat: down
INFO 2025-07-02 23:07:43,304   ❌ flower: down
INFO 2025-07-02 23:07:43,304 健康检查完成
INFO 2025-07-02 23:07:43,304 检查是否需要自动恢复...
WARNING 2025-07-02 23:07:43,304 检测到问题: Celery Worker未运行，尝试重启 worker
ERROR 2025-07-02 23:07:43,305 启动脚本不存在
ERROR 2025-07-02 23:07:43,305 自动恢复失败: worker
WARNING 2025-07-02 23:07:43,305 检测到问题: Celery Beat未运行，尝试重启 beat
ERROR 2025-07-02 23:07:43,305 启动脚本不存在
ERROR 2025-07-02 23:07:43,305 自动恢复失败: beat
WARNING 2025-07-02 23:07:43,305 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 23:07:43,305 启动脚本不存在
ERROR 2025-07-02 23:07:43,305 自动恢复失败: flower
INFO 2025-07-02 23:12:43,313 开始执行健康检查...
INFO 2025-07-02 23:12:43,318 ❌ 系统状态: unhealthy
INFO 2025-07-02 23:12:43,318   ✅ redis: up
INFO 2025-07-02 23:12:43,318   ✅ database: up
INFO 2025-07-02 23:12:43,319   ❌ celery_worker: down
INFO 2025-07-02 23:12:43,319   ❌ celery_beat: down
INFO 2025-07-02 23:12:43,319   ❌ flower: down
INFO 2025-07-02 23:12:43,319 健康检查完成
INFO 2025-07-02 23:12:43,319 检查是否需要自动恢复...
WARNING 2025-07-02 23:12:43,319 检测到问题: Celery Worker未运行，尝试重启 worker
ERROR 2025-07-02 23:12:43,319 启动脚本不存在
ERROR 2025-07-02 23:12:43,319 自动恢复失败: worker
WARNING 2025-07-02 23:12:43,319 检测到问题: Celery Beat未运行，尝试重启 beat
ERROR 2025-07-02 23:12:43,319 启动脚本不存在
ERROR 2025-07-02 23:12:43,319 自动恢复失败: beat
WARNING 2025-07-02 23:12:43,319 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 23:12:43,319 启动脚本不存在
ERROR 2025-07-02 23:12:43,319 自动恢复失败: flower
INFO 2025-07-02 23:17:43,369 开始执行健康检查...
INFO 2025-07-02 23:17:43,374 ❌ 系统状态: unhealthy
INFO 2025-07-02 23:17:43,374   ✅ redis: up
INFO 2025-07-02 23:17:43,374   ✅ database: up
INFO 2025-07-02 23:17:43,374   ❌ celery_worker: down
INFO 2025-07-02 23:17:43,374   ❌ celery_beat: down
INFO 2025-07-02 23:17:43,374   ❌ flower: down
INFO 2025-07-02 23:17:43,374 健康检查完成
INFO 2025-07-02 23:17:43,374 检查是否需要自动恢复...
WARNING 2025-07-02 23:17:43,374 检测到问题: Celery Worker未运行，尝试重启 worker
ERROR 2025-07-02 23:17:43,374 启动脚本不存在
ERROR 2025-07-02 23:17:43,374 自动恢复失败: worker
WARNING 2025-07-02 23:17:43,374 检测到问题: Celery Beat未运行，尝试重启 beat
ERROR 2025-07-02 23:17:43,374 启动脚本不存在
ERROR 2025-07-02 23:17:43,375 自动恢复失败: beat
WARNING 2025-07-02 23:17:43,375 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 23:17:43,375 启动脚本不存在
ERROR 2025-07-02 23:17:43,375 自动恢复失败: flower
INFO 2025-07-02 23:22:43,383 开始执行健康检查...
INFO 2025-07-02 23:22:43,387 ❌ 系统状态: unhealthy
INFO 2025-07-02 23:22:43,388   ✅ redis: up
INFO 2025-07-02 23:22:43,388   ✅ database: up
INFO 2025-07-02 23:22:43,388   ❌ celery_worker: down
INFO 2025-07-02 23:22:43,388   ❌ celery_beat: down
INFO 2025-07-02 23:22:43,388   ❌ flower: down
INFO 2025-07-02 23:22:43,388 健康检查完成
INFO 2025-07-02 23:22:43,388 检查是否需要自动恢复...
WARNING 2025-07-02 23:22:43,388 检测到问题: Celery Worker未运行，尝试重启 worker
ERROR 2025-07-02 23:22:43,388 启动脚本不存在
ERROR 2025-07-02 23:22:43,388 自动恢复失败: worker
WARNING 2025-07-02 23:22:43,388 检测到问题: Celery Beat未运行，尝试重启 beat
ERROR 2025-07-02 23:22:43,388 启动脚本不存在
ERROR 2025-07-02 23:22:43,389 自动恢复失败: beat
WARNING 2025-07-02 23:22:43,389 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 23:22:43,389 启动脚本不存在
ERROR 2025-07-02 23:22:43,389 自动恢复失败: flower
INFO 2025-07-02 23:27:43,398 开始执行健康检查...
INFO 2025-07-02 23:27:43,402 ❌ 系统状态: unhealthy
INFO 2025-07-02 23:27:43,403   ✅ redis: up
INFO 2025-07-02 23:27:43,403   ✅ database: up
INFO 2025-07-02 23:27:43,403   ❌ celery_worker: down
INFO 2025-07-02 23:27:43,403   ❌ celery_beat: down
INFO 2025-07-02 23:27:43,403   ❌ flower: down
INFO 2025-07-02 23:27:43,403 健康检查完成
INFO 2025-07-02 23:27:43,403 检查是否需要自动恢复...
WARNING 2025-07-02 23:27:43,403 检测到问题: Celery Worker未运行，尝试重启 worker
ERROR 2025-07-02 23:27:43,403 启动脚本不存在
ERROR 2025-07-02 23:27:43,403 自动恢复失败: worker
WARNING 2025-07-02 23:27:43,403 检测到问题: Celery Beat未运行，尝试重启 beat
ERROR 2025-07-02 23:27:43,403 启动脚本不存在
ERROR 2025-07-02 23:27:43,403 自动恢复失败: beat
WARNING 2025-07-02 23:27:43,403 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 23:27:43,403 启动脚本不存在
ERROR 2025-07-02 23:27:43,403 自动恢复失败: flower
INFO 2025-07-02 23:32:43,406 开始执行健康检查...
INFO 2025-07-02 23:32:43,409 ❌ 系统状态: unhealthy
INFO 2025-07-02 23:32:43,410   ✅ redis: up
INFO 2025-07-02 23:32:43,410   ✅ database: up
INFO 2025-07-02 23:32:43,410   ❌ celery_worker: down
INFO 2025-07-02 23:32:43,410   ❌ celery_beat: down
INFO 2025-07-02 23:32:43,410   ❌ flower: down
INFO 2025-07-02 23:32:43,410 健康检查完成
INFO 2025-07-02 23:32:43,410 检查是否需要自动恢复...
WARNING 2025-07-02 23:32:43,410 检测到问题: Celery Worker未运行，尝试重启 worker
ERROR 2025-07-02 23:32:43,410 启动脚本不存在
ERROR 2025-07-02 23:32:43,410 自动恢复失败: worker
WARNING 2025-07-02 23:32:43,410 检测到问题: Celery Beat未运行，尝试重启 beat
ERROR 2025-07-02 23:32:43,410 启动脚本不存在
ERROR 2025-07-02 23:32:43,410 自动恢复失败: beat
WARNING 2025-07-02 23:32:43,410 检测到问题: Flower监控未运行，尝试重启 flower
ERROR 2025-07-02 23:32:43,410 启动脚本不存在
ERROR 2025-07-02 23:32:43,410 自动恢复失败: flower
