INFO 2025-07-23 12:28:47,610 apps 50317 ********** Dashboard app ready.
INFO 2025-07-23 12:28:47,610 apps 50317 ********** Health API app ready.
INFO 2025-07-23 12:28:47,633 cli 50317 ********** Starting server at tcp:port=8000:interface=0.0.0.0
INFO 2025-07-23 12:28:47,633 server 50317 ********** HTTP/2 support not enabled (install the http2 and tls Twisted extras)
INFO 2025-07-23 12:28:47,633 server 50317 ********** Configuring endpoint tcp:port=8000:interface=0.0.0.0
INFO 2025-07-23 12:28:47,633 server 50317 ********** Listening on TCP address 0.0.0.0:8000
INFO 2025-07-23 12:29:46,461 views 50317 ********** Dashboard API module initialized at 2025-07-23T12:29:46.461400
INFO 2025-07-23 12:29:46,462 views 50317 ********** VIP Purchase API module initialized at 2025-07-23T12:29:46.462143
INFO 2025-07-23 12:29:46,536 views 50317 ********** [-] DashboardViewSet: Home dashboard data requested by user 473 (<EMAIL>). IP: ***************
INFO 2025-07-23 12:29:46,536 services 50317 ********** [-] DashboardService: Starting data aggregation for user 473 (test_user3)
INFO 2025-07-23 12:29:46,537 services 50317 ********** 正在获取用户 <EMAIL> 的今日摘要数据
INFO 2025-07-23 12:29:46,543 services 50317 ********** 🔄 用户对象已刷新以获取最新数据: user=<EMAIL>, exp=25519, swmt_balance=6627.00000000
INFO 2025-07-23 12:29:46,568 services 50317 ********** 用户 <EMAIL> 今日收益: SWMT=0.00, XP=0
INFO 2025-07-23 12:29:46,568 services 50317 ********** ✅ Dashboard API专注聚合任务数据，健康数据由独立 /health API 提供
INFO 2025-07-23 12:29:46,582 services 50317 ********** [-] Cached complete dashboard data for user 473 for 300s
INFO 2025-07-23 12:29:46,582 services 50317 ********** [-] DashboardService: Data aggregation for user 473 completed in 0.0464 seconds.
INFO 2025-07-23 12:29:46,582 views 50317 ********** [-] Data quality: 5/5 modules complete - {'user_profile_complete': True, 'vip_status_complete': True, 'today_summary_complete': True, 'daily_tasks_complete': True, 'addon_tasks_complete': True}
INFO 2025-07-23 12:29:46,582 views 50317 ********** [-] DashboardViewSet: User <EMAIL> earned_today data: SWMT=0.00, XP=0
INFO 2025-07-23 12:29:46,582 views 50317 ********** [-] Tasks summary: Daily=0/4, Addon=0/2
INFO 2025-07-23 12:29:46,583 views 50317 ********** [-] DashboardViewSet: Home dashboard data served to user 473 in 0.047s
INFO 2025-07-23 12:29:48,043 views 50317 ********** [-] 用户 <EMAIL> 请求初始化健康数据会话
INFO 2025-07-23 12:29:48,043 views 50317 ********** [-] 基线初始化参数: device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3, permissions={'steps': True, 'distance': False, 'calories': False}
INFO 2025-07-23 12:29:48,043 views 50317 ********** [-] ✅ steps数据验证通过: 基线=0.0, 总量=0, 增量=0.0
INFO 2025-07-23 12:29:48,043 views 50317 ********** [-] 🔥 BOSS修复：前端提供基线数据: {'steps': 0.0}
INFO 2025-07-23 12:29:48,043 baseline_manager 50317 ********** 🔥 v14.0修复：已设置前端基线数据到线程: {'steps': 0.0}
INFO 2025-07-23 12:29:48,043 views 50317 ********** [-] 🕐 前端传递的会话开始时间: 2025-07-23 12:29:47.907085+08:00
INFO 2025-07-23 12:29:48,044 baseline_manager 50317 ********** 🔥 [BaselineManager] 开始为用户 <EMAIL> 初始化基线 (原因: 会话初始化API调用)
INFO 2025-07-23 12:29:48,044 baseline_manager 50317 ********** 📱 权限状态: {'steps': True, 'distance': False, 'calories': False}
INFO 2025-07-23 12:29:48,047 baseline_manager 50317 ********** 🆕 检测到app重启（API调用），遵循v2.0概念强制创建新会话
INFO 2025-07-23 12:29:48,049 baseline_manager 50317 ********** 🆕 创建新的用户会话
INFO 2025-07-23 12:29:48,050 baseline_manager 50317 ********** 🕐 使用前端传递的会话开始时间(新加坡): 2025-07-23 12:29:47 +08
INFO 2025-07-23 12:29:48,050 baseline_manager 50317 ********** ✅ 步数权限已授权，使用前端实际基线: 0
INFO 2025-07-23 12:29:48,050 baseline_manager 50317 ********** ❌ 距离权限未授权，基线保持null
INFO 2025-07-23 12:29:48,050 baseline_manager 50317 ********** ❌ 卡路里权限未授权，基线保持null
INFO 2025-07-23 12:29:48,052 baseline_manager 50317 ********** ✅ 新会话创建成功 (ID: 187)
INFO 2025-07-23 12:29:48,052 baseline_manager 50317 ********** 📅 基线日期（startDate-当天00:00）: 2025-07-23 00:00:00+08:00
INFO 2025-07-23 12:29:48,052 baseline_manager 50317 ********** 🕐 会话开始时间（session_start_time）: 2025-07-23 04:29:47.907085+00:00
INFO 2025-07-23 12:29:48,052 baseline_manager 50317 ********** ⏰ 基线结束时间（endDate-用于基线计算）: 2025-07-23 04:29:47.907085+00:00
INFO 2025-07-23 12:29:48,052 baseline_manager 50317 ********** [审计] 初始化基线 成功 - 用户: <EMAIL>, 状态: {'session_id': 187, 'authorized_permissions': ['steps'], 'baseline_date': '2025-07-22T16:00:00+00:00', 'steps_baseline': 0, 'distance_baseline': None, 'calories_baseline': None}
INFO 2025-07-23 12:29:48,052 baseline_manager 50317 ********** ✅ [BaselineManager] 基线初始化完成: 基线初始化成功，已授权权限: steps
INFO 2025-07-23 12:29:48,052 views 50317 ********** [-] BaselineManager调用成功: {'session_id': 187, 'baseline_date': datetime.datetime(2025, 7, 22, 16, 0, tzinfo=datetime.timezone.utc), 'session_baseline_end_time': datetime.datetime(2025, 7, 23, 4, 29, 47, 907085, tzinfo=datetime.timezone.utc), 'authorized_permissions': ['steps'], 'baseline_values': {'steps': 0, 'distance': None, 'calories': None}, 'session_start_time': datetime.datetime(2025, 7, 23, 4, 29, 47, 907085, tzinfo=datetime.timezone.utc), 'last_sync_time': datetime.datetime(2025, 7, 23, 4, 29, 48, 50319, tzinfo=datetime.timezone.utc), 'message': '基线初始化成功，已授权权限: steps'}
INFO 2025-07-23 12:29:48,052 baseline_manager 50317 ********** 🔥 v14.0修复：已清除线程基线数据
INFO 2025-07-23 12:29:48,052 views 50317 ********** [-] 用户 <EMAIL> 健康数据会话初始化成功，耗时: 0.009s
INFO 2025-07-23 12:29:48,183 views 50317 ********** [-] 用户 <EMAIL> 请求同步健康数据
INFO 2025-07-23 12:29:48,184 services 50317 ********** 正在为用户 <EMAIL> 同步健康数据 (session_based=False, skip_permission_check=False)
INFO 2025-07-23 12:29:48,184 services 50317 ********** 🔍 v14.1预检：用户 <EMAIL> 开始权限双重验证
INFO 2025-07-23 12:29:48,184 services 50317 **********   前端提交的权限状态: {'steps': True, 'distance': False, 'calories': False}
INFO 2025-07-23 12:29:48,186 services 50317 ********** 🔍 v14.1严格双重验证 - 用户 <EMAIL>:
INFO 2025-07-23 12:29:48,186 services 50317 **********   前端HKStatisticsQuery权限: {'steps': True, 'distance': False, 'calories': False}
INFO 2025-07-23 12:29:48,186 services 50317 **********   后端基线字段存在状态: {'steps': True, 'distance': False, 'calories': False}
INFO 2025-07-23 12:29:48,186 services 50317 **********   最终权限结果: {'steps': True, 'distance': False, 'calories': False}
INFO 2025-07-23 12:29:48,186 services 50317 ********** ✅ v14.1预检：用户 <EMAIL> 权限双重验证通过: {'steps': True, 'distance': False, 'calories': False}
INFO 2025-07-23 12:29:48,186 services 50317 ********** 开始验证用户 <EMAIL> 的原始健康数据
INFO 2025-07-23 12:29:48,186 services 50317 ********** 用户 <EMAIL> 构建的验证数据: {'steps': 0, 'distance': 0.0, 'calories': 0, 'device_id': 'FCD1C9A5-1559-4C06-AC7A-372E32E45AB3', 'permissions': {'steps': True, 'distance': False, 'calories': False}, 'platform': 'app', 'date': '2025-07-23', 'source': 'app', 'device_info': {'device_model': 'FCD1C9A5-1559-4C06-AC7A-372E32E45AB3', 'platform': 'mobile_app', 'app_version': '1.0.0'}}
INFO 2025-07-23 12:29:48,192 services 50317 ********** 用户 <EMAIL> 健康数据验证通过，验证级别: comprehensive
INFO 2025-07-23 12:29:48,192 baseline_manager 50317 ********** 🔥 BOSS新增：检测用户 <EMAIL> 权限变化并补充基线（v13.0规范）
INFO 2025-07-23 12:29:48,192 services 50317 ********** 用户 <EMAIL> 权限变化检测: 权限变化检测完成，发现0项变化
INFO 2025-07-23 12:29:48,194 services 50317 ********** 步数权限已授权，增量: 0
INFO 2025-07-23 12:29:48,194 services 50317 ********** 距离权限未授权或基线缺失，增量跳过
INFO 2025-07-23 12:29:48,194 services 50317 ********** 卡路里权限未授权或基线缺失，增量跳过
INFO 2025-07-23 12:29:48,194 services 50317 ********** v13.0规范增量计算完成 - 步数: 0, 距离: None, 卡路里: None
INFO 2025-07-23 12:29:48,194 services 50317 ********** 用户 <EMAIL> 计算净增量完成: {'steps': 0, 'distance': None, 'calories': None}
INFO 2025-07-23 12:29:48,200 services 50317 ********** ✅ UserSessionLog记录已创建: ID=284, 会话=187, 步数=0
INFO 2025-07-23 12:29:48,202 services 50317 ********** 健康数据同步记录已保存，用户: <EMAIL>, 增量: {'steps': 0, 'distance': None, 'calories': None}
INFO 2025-07-23 12:29:48,202 views 50317 ********** [-] 用户 <EMAIL> 健康数据同步成功，增量: {'steps': 0, 'distance': None, 'calories': None}, 耗时: 0.019s
INFO 2025-07-23 12:30:25,163 views 50317 ********** [-] 用户 <EMAIL> 请求初始化健康数据会话
INFO 2025-07-23 12:30:25,163 views 50317 ********** [-] 基线初始化参数: device_id=FCD1C9A5-1559-4C06-AC7A-372E32E45AB3, permissions={'steps': True, 'distance': False, 'calories': False}
INFO 2025-07-23 12:30:25,163 views 50317 ********** [-] ✅ steps数据验证通过: 基线=0.0, 总量=0, 增量=0.0
INFO 2025-07-23 12:30:25,163 views 50317 ********** [-] 🔥 BOSS修复：前端提供基线数据: {'steps': 0.0}
INFO 2025-07-23 12:30:25,163 baseline_manager 50317 ********** 🔥 v14.0修复：已设置前端基线数据到线程: {'steps': 0.0}
INFO 2025-07-23 12:30:25,163 views 50317 ********** [-] 🕐 前端传递的会话开始时间: 2025-07-23 12:30:24.974271+08:00
INFO 2025-07-23 12:30:25,163 baseline_manager 50317 ********** 🔥 [BaselineManager] 开始为用户 <EMAIL> 初始化基线 (原因: 会话初始化API调用)
INFO 2025-07-23 12:30:25,163 baseline_manager 50317 ********** 📱 权限状态: {'steps': True, 'distance': False, 'calories': False}
INFO 2025-07-23 12:30:25,166 baseline_manager 50317 ********** 🆕 检测到app重启（API调用），遵循v2.0概念强制创建新会话
INFO 2025-07-23 12:30:25,168 baseline_manager 50317 ********** 🆕 创建新的用户会话
INFO 2025-07-23 12:30:25,168 baseline_manager 50317 ********** 🕐 使用前端传递的会话开始时间(新加坡): 2025-07-23 12:30:24 +08
INFO 2025-07-23 12:30:25,168 baseline_manager 50317 ********** ✅ 步数权限已授权，使用前端实际基线: 0
INFO 2025-07-23 12:30:25,168 baseline_manager 50317 ********** ❌ 距离权限未授权，基线保持null
INFO 2025-07-23 12:30:25,168 baseline_manager 50317 ********** ❌ 卡路里权限未授权，基线保持null
INFO 2025-07-23 12:30:25,169 baseline_manager 50317 ********** ✅ 新会话创建成功 (ID: 188)
INFO 2025-07-23 12:30:25,169 baseline_manager 50317 ********** 📅 基线日期（startDate-当天00:00）: 2025-07-23 00:00:00+08:00
INFO 2025-07-23 12:30:25,169 baseline_manager 50317 ********** 🕐 会话开始时间（session_start_time）: 2025-07-23 04:30:24.974271+00:00
INFO 2025-07-23 12:30:25,169 baseline_manager 50317 ********** ⏰ 基线结束时间（endDate-用于基线计算）: 2025-07-23 04:30:24.974271+00:00
INFO 2025-07-23 12:30:25,169 baseline_manager 50317 ********** [审计] 初始化基线 成功 - 用户: <EMAIL>, 状态: {'session_id': 188, 'authorized_permissions': ['steps'], 'baseline_date': '2025-07-22T16:00:00+00:00', 'steps_baseline': 0, 'distance_baseline': None, 'calories_baseline': None}
INFO 2025-07-23 12:30:25,169 baseline_manager 50317 ********** ✅ [BaselineManager] 基线初始化完成: 基线初始化成功，已授权权限: steps
INFO 2025-07-23 12:30:25,169 views 50317 ********** [-] BaselineManager调用成功: {'session_id': 188, 'baseline_date': datetime.datetime(2025, 7, 22, 16, 0, tzinfo=datetime.timezone.utc), 'session_baseline_end_time': datetime.datetime(2025, 7, 23, 4, 30, 24, 974271, tzinfo=datetime.timezone.utc), 'authorized_permissions': ['steps'], 'baseline_values': {'steps': 0, 'distance': None, 'calories': None}, 'session_start_time': datetime.datetime(2025, 7, 23, 4, 30, 24, 974271, tzinfo=datetime.timezone.utc), 'last_sync_time': datetime.datetime(2025, 7, 23, 4, 30, 25, 168614, tzinfo=datetime.timezone.utc), 'message': '基线初始化成功，已授权权限: steps'}
INFO 2025-07-23 12:30:25,169 baseline_manager 50317 ********** 🔥 v14.0修复：已清除线程基线数据
INFO 2025-07-23 12:30:25,169 views 50317 ********** [-] 用户 <EMAIL> 健康数据会话初始化成功，耗时: 0.007s
INFO 2025-07-23 12:30:25,353 views 50317 ********** [-] 用户 <EMAIL> 请求同步健康数据
INFO 2025-07-23 12:30:25,354 services 50317 ********** 正在为用户 <EMAIL> 同步健康数据 (session_based=False, skip_permission_check=False)
INFO 2025-07-23 12:30:25,354 services 50317 ********** 🔍 v14.1预检：用户 <EMAIL> 开始权限双重验证
INFO 2025-07-23 12:30:25,354 services 50317 **********   前端提交的权限状态: {'steps': True, 'distance': False, 'calories': False}
INFO 2025-07-23 12:30:25,356 services 50317 ********** 🔍 v14.1严格双重验证 - 用户 <EMAIL>:
INFO 2025-07-23 12:30:25,356 services 50317 **********   前端HKStatisticsQuery权限: {'steps': True, 'distance': False, 'calories': False}
INFO 2025-07-23 12:30:25,356 services 50317 **********   后端基线字段存在状态: {'steps': True, 'distance': False, 'calories': False}
INFO 2025-07-23 12:30:25,356 services 50317 **********   最终权限结果: {'steps': True, 'distance': False, 'calories': False}
INFO 2025-07-23 12:30:25,356 services 50317 ********** ✅ v14.1预检：用户 <EMAIL> 权限双重验证通过: {'steps': True, 'distance': False, 'calories': False}
INFO 2025-07-23 12:30:25,356 services 50317 ********** 开始验证用户 <EMAIL> 的原始健康数据
INFO 2025-07-23 12:30:25,356 services 50317 ********** 用户 <EMAIL> 构建的验证数据: {'steps': 0, 'distance': 0.0, 'calories': 0, 'device_id': 'FCD1C9A5-1559-4C06-AC7A-372E32E45AB3', 'permissions': {'steps': True, 'distance': False, 'calories': False}, 'platform': 'app', 'date': '2025-07-23', 'source': 'app', 'device_info': {'device_model': 'FCD1C9A5-1559-4C06-AC7A-372E32E45AB3', 'platform': 'mobile_app', 'app_version': '1.0.0'}}
INFO 2025-07-23 12:30:25,361 services 50317 ********** 用户 <EMAIL> 健康数据验证通过，验证级别: comprehensive
INFO 2025-07-23 12:30:25,361 baseline_manager 50317 ********** 🔥 BOSS新增：检测用户 <EMAIL> 权限变化并补充基线（v13.0规范）
INFO 2025-07-23 12:30:25,362 services 50317 ********** 用户 <EMAIL> 权限变化检测: 权限变化检测完成，发现0项变化
INFO 2025-07-23 12:30:25,363 services 50317 ********** 步数权限已授权，增量: 0
INFO 2025-07-23 12:30:25,363 services 50317 ********** 距离权限未授权或基线缺失，增量跳过
INFO 2025-07-23 12:30:25,363 services 50317 ********** 卡路里权限未授权或基线缺失，增量跳过
INFO 2025-07-23 12:30:25,363 services 50317 ********** v13.0规范增量计算完成 - 步数: 0, 距离: None, 卡路里: None
INFO 2025-07-23 12:30:25,363 services 50317 ********** 用户 <EMAIL> 计算净增量完成: {'steps': 0, 'distance': None, 'calories': None}
INFO 2025-07-23 12:30:25,369 services 50317 ********** ✅ UserSessionLog记录已创建: ID=285, 会话=188, 步数=0
INFO 2025-07-23 12:30:25,371 services 50317 ********** 健康数据同步记录已保存，用户: <EMAIL>, 增量: {'steps': 0, 'distance': None, 'calories': None}
INFO 2025-07-23 12:30:25,372 views 50317 ********** [-] 用户 <EMAIL> 健康数据同步成功，增量: {'steps': 0, 'distance': None, 'calories': None}, 耗时: 0.018s
