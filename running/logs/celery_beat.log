INFO 2025-06-30 15:13:57,793 beat 9412 ********** beat: Starting...
INFO 2025-06-30 15:30:00,006 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 15:30:00,042 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-06-30 16:00:00,005 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-06-30 16:00:00,037 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 16:30:00,008 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-06-30 16:30:00,029 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 17:00:00,000 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 17:00:00,025 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-06-30 17:30:00,001 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 17:30:00,038 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-06-30 18:00:00,001 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-06-30 18:00:00,029 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 18:30:00,008 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 18:30:00,036 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-06-30 19:00:00,002 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 19:00:00,029 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-06-30 19:30:00,001 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-06-30 19:30:00,035 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 20:00:00,009 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-06-30 20:00:00,034 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 20:30:00,008 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 20:30:00,037 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-06-30 21:00:00,008 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-06-30 21:00:00,045 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 21:30:00,006 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 21:30:00,024 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-06-30 22:00:00,005 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 22:00:00,025 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-06-30 22:30:00,004 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-06-30 22:30:00,037 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 23:00:00,010 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-06-30 23:00:00,047 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 23:30:00,009 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-06-30 23:30:00,040 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 00:00:00,016 beat 9412 ********** Scheduler: Sending due task update-active-member-status (tasks.tasks.update_active_member_status)
INFO 2025-07-01 00:00:00,057 beat 9412 ********** Scheduler: Sending due task reset-daily-tasks (tasks.reset_daily_tasks)
INFO 2025-07-01 00:00:00,073 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 00:00:00,087 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 00:01:00,006 beat 9412 ********** Scheduler: Sending due task settle-extra-task-bonuses (tasks.settle_extra_task_bonuses)
INFO 2025-07-01 00:02:00,003 beat 9412 ********** Scheduler: Sending due task update-vip-refund-progress (vip.tasks.update_vip_refund_progress)
INFO 2025-07-01 00:03:00,010 beat 9412 ********** Scheduler: Sending due task clear-agent-cache-daily (agents.tasks.clear_agent_cache_daily)
INFO 2025-07-01 00:04:00,006 beat 9412 ********** Scheduler: Sending due task health-data-daily-reset (users.health_data_daily_reset)
INFO 2025-07-01 00:05:00,005 beat 9412 ********** Scheduler: Sending due task process-vip-refunds (vip.tasks.process_vip_refunds)
INFO 2025-07-01 00:10:00,010 beat 9412 ********** Scheduler: Sending due task update-daily-stats (stats.tasks.update_daily_stats)
INFO 2025-07-01 00:30:00,006 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 00:30:00,033 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 01:00:00,007 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 01:00:00,031 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 01:30:00,011 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 01:30:00,042 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 02:00:00,010 beat 9412 ********** Scheduler: Sending due task check-level-consistency (users.check_level_consistency)
INFO 2025-07-01 02:00:00,038 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 02:00:00,053 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 02:30:00,007 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 02:30:00,031 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 03:00:00,006 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 03:00:00,033 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 03:30:00,008 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 03:30:00,038 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 04:00:00,005 beat 9412 ********** Scheduler: Sending due task celery.backend_cleanup (celery.backend_cleanup)
INFO 2025-07-01 04:00:00,035 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 04:00:00,057 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 04:30:00,001 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 04:30:00,023 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 05:00:00,002 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 05:00:00,021 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 05:30:00,007 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 05:30:00,025 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 06:00:00,009 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 06:00:00,040 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 06:30:00,006 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 06:30:00,030 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 07:00:00,007 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 07:00:00,025 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 07:30:00,008 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 07:30:00,038 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 08:00:00,009 beat 9412 ********** Scheduler: Sending due task monitor-level-upgrade-performance (users.monitor_level_upgrade_performance)
INFO 2025-07-01 08:00:00,044 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 08:00:00,067 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 08:30:00,007 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 08:30:00,041 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 09:00:00,007 beat 9412 ********** Scheduler: Sending due task check-stalled-refund-plans (vip.tasks.check_stalled_refund_plans)
INFO 2025-07-01 09:00:00,040 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 09:00:00,064 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 09:30:00,005 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 09:30:00,027 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 10:00:00,005 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 10:00:00,037 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 10:30:00,008 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 10:30:00,035 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 11:00:00,005 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 11:00:00,042 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 11:30:00,004 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 11:30:00,027 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 12:00:00,000 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 12:00:00,025 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 12:30:00,009 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 12:30:00,045 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 13:00:00,009 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 13:00:00,044 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 13:30:00,001 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 13:30:00,028 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 14:00:00,004 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 14:00:00,029 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 14:30:00,004 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 14:30:00,027 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 15:00:00,001 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 15:00:00,030 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 15:30:00,009 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 15:30:00,034 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 16:00:00,009 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 16:00:00,037 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 16:30:00,007 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 16:30:00,043 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 17:00:00,008 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 17:00:00,042 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 17:30:00,006 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 17:30:00,036 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 18:00:00,007 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 18:00:00,030 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 18:30:00,013 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 18:30:00,037 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 19:00:00,000 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 19:00:00,037 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 19:30:00,000 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 19:30:00,032 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 20:00:00,017 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 20:00:00,047 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 20:30:00,006 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 20:30:00,032 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 21:00:00,003 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 21:00:00,035 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 21:30:00,008 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 21:30:00,040 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 22:00:00,001 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 22:00:00,029 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 22:30:00,001 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 22:30:00,027 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 23:00:00,003 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-01 23:00:00,042 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-01 23:30:00,006 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-01 23:30:00,048 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 00:00:00,012 beat 9412 ********** Scheduler: Sending due task reset-daily-tasks (tasks.reset_daily_tasks)
INFO 2025-07-02 00:00:00,097 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 00:00:00,125 beat 9412 ********** Scheduler: Sending due task update-active-member-status (tasks.tasks.update_active_member_status)
INFO 2025-07-02 00:00:00,154 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 00:01:00,006 beat 9412 ********** Scheduler: Sending due task settle-extra-task-bonuses (tasks.settle_extra_task_bonuses)
INFO 2025-07-02 00:02:00,006 beat 9412 ********** Scheduler: Sending due task update-vip-refund-progress (vip.tasks.update_vip_refund_progress)
INFO 2025-07-02 00:03:00,010 beat 9412 ********** Scheduler: Sending due task clear-agent-cache-daily (agents.tasks.clear_agent_cache_daily)
INFO 2025-07-02 00:04:00,013 beat 9412 ********** Scheduler: Sending due task health-data-daily-reset (users.health_data_daily_reset)
INFO 2025-07-02 00:05:00,007 beat 9412 ********** Scheduler: Sending due task process-vip-refunds (vip.tasks.process_vip_refunds)
INFO 2025-07-02 00:10:00,007 beat 9412 ********** Scheduler: Sending due task update-daily-stats (stats.tasks.update_daily_stats)
INFO 2025-07-02 00:30:00,004 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 00:30:00,116 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 01:00:00,006 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 01:00:00,136 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 01:30:00,005 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 01:30:00,055 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 02:00:00,006 beat 9412 ********** Scheduler: Sending due task check-level-consistency (users.check_level_consistency)
INFO 2025-07-02 02:00:00,049 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 02:00:00,081 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 02:30:00,000 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 02:30:00,026 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 03:00:00,011 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 03:00:00,054 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 03:30:00,010 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 03:30:00,059 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 04:00:00,012 beat 9412 ********** Scheduler: Sending due task celery.backend_cleanup (celery.backend_cleanup)
INFO 2025-07-02 04:00:00,048 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 04:00:00,090 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 04:30:00,010 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 04:30:00,057 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 05:00:00,010 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 05:00:00,057 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 05:30:00,009 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 05:30:00,058 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 06:00:00,001 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 06:00:00,029 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 06:30:00,012 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 06:30:00,058 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 07:00:00,009 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 07:00:00,055 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 07:30:00,011 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 07:30:00,052 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 08:00:00,013 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 08:00:00,058 beat 9412 ********** Scheduler: Sending due task monitor-level-upgrade-performance (users.monitor_level_upgrade_performance)
INFO 2025-07-02 08:00:00,078 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 08:30:00,007 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 08:30:00,035 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 09:00:00,007 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 09:00:00,038 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 09:00:00,076 beat 9412 ********** Scheduler: Sending due task check-stalled-refund-plans (vip.tasks.check_stalled_refund_plans)
INFO 2025-07-02 09:30:00,009 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 09:30:00,035 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 10:00:00,001 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 10:00:00,027 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 10:30:00,008 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 10:30:00,045 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 11:00:00,005 beat 9412 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 11:00:00,056 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 11:30:00,009 beat 9412 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 11:30:00,038 beat 9412 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 11:35:24,889 beat 11932 ********** beat: Starting...
INFO 2025-07-02 11:35:25,009 beat 11932 ********** Scheduler: Sending due task health-data-daily-reset (users.health_data_daily_reset)
INFO 2025-07-02 11:35:25,029 beat 11932 ********** Scheduler: Sending due task celery.backend_cleanup (celery.backend_cleanup)
INFO 2025-07-02 11:35:25,043 beat 11932 ********** Scheduler: Sending due task check-level-consistency (users.check_level_consistency)
INFO 2025-07-02 11:35:25,057 beat 11932 ********** Scheduler: Sending due task reset-daily-tasks (tasks.reset_daily_tasks)
INFO 2025-07-02 11:35:25,069 beat 11932 ********** Scheduler: Sending due task settle-extra-task-bonuses (tasks.settle_extra_task_bonuses)
INFO 2025-07-02 11:35:25,077 beat 11932 ********** Scheduler: Sending due task update-vip-refund-progress (vip.tasks.update_vip_refund_progress)
INFO 2025-07-02 11:35:25,085 beat 11932 ********** Scheduler: Sending due task update-active-member-status (tasks.tasks.update_active_member_status)
INFO 2025-07-02 11:35:25,097 beat 11932 ********** Scheduler: Sending due task weekly-commission-settlement (agents.tasks.weekly_commission_settlement)
INFO 2025-07-02 11:35:25,105 beat 11932 ********** Scheduler: Sending due task clear-agent-cache-daily (agents.tasks.clear_agent_cache_daily)
INFO 2025-07-02 11:35:25,113 beat 11932 ********** Scheduler: Sending due task process-vip-refunds (vip.tasks.process_vip_refunds)
INFO 2025-07-02 11:35:25,121 beat 11932 ********** Scheduler: Sending due task update-daily-stats (stats.tasks.update_daily_stats)
INFO 2025-07-02 11:39:23,468 beat 12353 ********** beat: Starting...
INFO 2025-07-02 11:40:00,011 beat 12353 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 11:42:00,001 beat 12353 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 11:44:00,003 beat 12353 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 11:46:00,005 beat 12353 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 11:48:00,008 beat 12353 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 11:50:00,001 beat 12353 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 11:52:00,012 beat 12353 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 11:54:00,004 beat 12353 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 11:56:00,001 beat 12353 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 11:58:00,004 beat 12353 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:00:00,003 beat 12353 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 12:00:00,027 beat 12353 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 12:00:00,041 beat 12353 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:00:33,621 beat 13275 ********** beat: Starting...
INFO 2025-07-02 12:02:00,005 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:04:00,002 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:06:00,006 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:08:00,000 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:10:00,003 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:12:00,006 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:14:00,008 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:16:00,006 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:18:00,008 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:20:00,001 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:22:00,001 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:24:00,000 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:26:00,003 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:28:00,006 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:30:00,006 beat 13275 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 12:30:00,025 beat 13275 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 12:30:00,041 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:32:00,005 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:34:00,011 beat 13275 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:35:38,983 beat 15065 ********** beat: Starting...
INFO 2025-07-02 12:36:00,007 beat 15065 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:38:00,008 beat 15065 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:40:00,004 beat 15065 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:41:42,803 beat 15673 ********** beat: Starting...
INFO 2025-07-02 12:42:00,005 beat 15673 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:42:28,690 beat 15903 ********** beat: Starting...
INFO 2025-07-02 12:43:17,013 beat 16136 ********** beat: Starting...
INFO 2025-07-02 12:44:00,010 beat 16136 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:56:52,930 beat 16731 ********** beat: Starting...
INFO 2025-07-02 12:56:52,983 beat 16731 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:58:00,009 beat 16731 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 12:59:34,314 beat 17071 ********** beat: Starting...
INFO 2025-07-02 13:00:00,004 beat 17071 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 13:00:00,033 beat 17071 ********** Scheduler: Sending due task offline_health_requests_replay_every_2min (offline_health_requests_replay)
INFO 2025-07-02 13:00:00,049 beat 17071 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 13:05:33,788 beat 17703 ********** beat: Starting...
INFO 2025-07-02 13:10:24,168 beat 18197 ********** beat: Starting...
INFO 2025-07-02 13:10:47,993 beat 18318 ********** beat: Starting...
INFO 2025-07-02 13:14:14,575 beat 18835 ********** beat: Starting...
INFO 2025-07-02 13:19:12,050 beat 19355 ********** beat: Starting...
INFO 2025-07-02 13:29:23,462 beat 20093 ********** beat: Starting...
INFO 2025-07-02 13:30:00,003 beat 20093 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 13:30:00,025 beat 20093 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 13:43:22,089 beat 31436 ********** beat: Starting...
INFO 2025-07-02 13:47:30,762 beat 34593 ********** beat: Starting...
INFO 2025-07-02 14:00:00,006 beat 34593 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 14:00:00,030 beat 34593 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 14:26:41,189 beat 63932 ********** beat: Starting...
INFO 2025-07-02 14:30:00,003 beat 63932 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 14:30:00,037 beat 63932 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 15:00:00,009 beat 63932 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 15:00:00,033 beat 63932 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 15:30:00,006 beat 63932 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 15:30:00,028 beat 63932 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 15:50:41,777 beat 91592 ********** beat: Starting...
INFO 2025-07-02 16:00:00,006 beat 91592 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 16:00:00,036 beat 91592 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 16:11:14,792 beat 7249 ********** beat: Starting...
INFO 2025-07-02 16:30:00,001 beat 91592 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 16:30:00,004 beat 7249 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 16:30:00,028 beat 91592 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 16:30:00,035 beat 7249 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 17:00:00,005 beat 91592 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 17:00:00,006 beat 7249 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 17:00:00,042 beat 91592 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 17:00:00,042 beat 7249 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 17:30:00,000 beat 91592 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 17:30:00,000 beat 7249 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 17:30:00,031 beat 91592 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 17:30:00,032 beat 7249 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 18:00:00,012 beat 91592 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 18:00:00,012 beat 7249 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 18:00:00,049 beat 7249 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 18:00:00,049 beat 91592 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 18:30:00,002 beat 7249 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 18:30:00,003 beat 91592 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 18:30:00,031 beat 91592 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 18:30:00,033 beat 7249 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 19:00:00,008 beat 91592 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 19:00:00,009 beat 7249 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 19:00:00,038 beat 91592 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 19:00:00,039 beat 7249 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 19:30:00,012 beat 91592 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 19:30:00,012 beat 7249 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 19:30:00,040 beat 7249 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 19:30:00,041 beat 91592 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 20:00:00,007 beat 7249 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 20:00:00,007 beat 91592 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 20:00:00,037 beat 91592 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 20:00:00,039 beat 7249 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 20:30:00,010 beat 91592 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 20:30:00,012 beat 7249 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 20:30:00,044 beat 91592 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 20:30:00,044 beat 7249 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 21:00:00,009 beat 7249 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 21:00:00,009 beat 91592 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 21:00:00,034 beat 7249 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 21:00:00,035 beat 91592 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 21:30:00,002 beat 91592 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 21:30:00,002 beat 7249 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 21:30:00,024 beat 7249 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 21:30:00,024 beat 91592 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 22:00:00,002 beat 91592 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 22:00:00,001 beat 7249 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 22:00:00,023 beat 7249 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 22:00:00,023 beat 91592 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 22:30:00,013 beat 91592 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 22:30:00,013 beat 7249 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 22:30:00,037 beat 7249 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 22:30:00,037 beat 91592 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 22:34:52,424 beat 33557 ********** beat: Starting...
INFO 2025-07-02 23:00:00,008 beat 33557 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-02 23:00:00,043 beat 33557 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 23:30:00,004 beat 33557 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-02 23:30:00,040 beat 33557 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-02 23:34:08,774 beat 36244 ********** beat: Starting...
INFO 2025-07-02 23:38:11,152 beat 36233 ********** beat: Shutting down...
INFO 2025-07-02 23:38:24,188 beat 36529 ********** beat: Starting...
INFO 2025-07-03 00:34:31,310 beat 36515 ********** beat: Shutting down...
INFO 2025-07-03 00:36:22,747 beat 47681 ********** beat: Starting...
INFO 2025-07-03 00:36:22,803 beat 47681 ********** Scheduler: Sending due task reset-daily-tasks (tasks.reset_daily_tasks)
INFO 2025-07-03 00:36:22,823 beat 47681 ********** Scheduler: Sending due task settle-extra-task-bonuses (tasks.settle_extra_task_bonuses)
INFO 2025-07-03 00:36:22,842 beat 47681 ********** Scheduler: Sending due task update-vip-refund-progress (vip.tasks.update_vip_refund_progress)
INFO 2025-07-03 00:36:22,847 beat 47681 ********** Scheduler: Sending due task update-active-member-status (tasks.tasks.update_active_member_status)
INFO 2025-07-03 00:36:22,854 beat 47681 ********** Scheduler: Sending due task clear-agent-cache-daily (agents.tasks.clear_agent_cache_daily)
INFO 2025-07-03 00:36:22,859 beat 47681 ********** Scheduler: Sending due task process-vip-refunds (vip.tasks.process_vip_refunds)
INFO 2025-07-03 00:36:22,865 beat 47681 ********** Scheduler: Sending due task update-daily-stats (stats.tasks.update_daily_stats)
INFO 2025-07-03 00:36:22,871 beat 47681 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 00:36:22,876 beat 47681 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 00:36:22,881 beat 47681 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 00:36:22,886 beat 47681 ********** Scheduler: Sending due task health-data-daily-reset (users.health_data_daily_reset)
INFO 2025-07-03 00:40:40,698 beat 49689 ********** beat: Starting...
INFO 2025-07-03 00:49:22,048 beat 60034 ********** beat: Starting...
INFO 2025-07-03 01:00:00,004 beat 60034 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 01:00:00,021 beat 60034 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 01:19:38,144 beat 78013 ********** beat: Starting...
INFO 2025-07-03 01:30:00,014 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 01:30:00,038 beat 78013 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 02:00:00,011 beat 78013 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 02:00:00,028 beat 78013 ********** Scheduler: Sending due task check-level-consistency (users.check_and_fix_level_consistency)
INFO 2025-07-03 02:00:00,037 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 02:30:00,010 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 02:30:00,028 beat 78013 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 03:00:00,010 beat 78013 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 03:00:00,022 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 03:30:00,011 beat 78013 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 03:30:00,026 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 04:00:00,003 beat 78013 ********** Scheduler: Sending due task celery.backend_cleanup (celery.backend_cleanup)
INFO 2025-07-03 04:00:00,017 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 04:00:00,030 beat 78013 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 04:30:00,008 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 04:30:00,016 beat 78013 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 05:00:00,006 beat 78013 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 05:00:00,015 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 05:30:00,006 beat 78013 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 05:30:00,021 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 06:00:00,004 beat 78013 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 06:00:00,018 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 06:30:00,005 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 06:30:00,021 beat 78013 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 07:00:00,006 beat 78013 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 07:00:00,015 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 07:30:00,006 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 07:30:00,015 beat 78013 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 08:00:00,004 beat 78013 ********** Scheduler: Sending due task monitor-level-upgrade-performance (users.monitor_level_upgrade_performance)
INFO 2025-07-03 08:00:00,013 beat 78013 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 08:00:00,019 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 08:30:00,009 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 08:30:00,027 beat 78013 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 09:00:00,004 beat 78013 ********** Scheduler: Sending due task check-stalled-refund-plans (vip.tasks.check_stalled_refund_plans)
INFO 2025-07-03 09:00:00,014 beat 78013 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 09:00:00,022 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 09:30:00,001 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 09:30:00,019 beat 78013 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 10:00:00,002 beat 78013 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 10:00:00,017 beat 78013 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 10:07:50,969 beat 96885 ********** beat: Starting...
INFO 2025-07-03 10:08:59,361 beat 97823 ********** beat: Starting...
INFO 2025-07-03 10:13:29,657 beat 98561 ********** beat: Starting...
INFO 2025-07-03 10:14:50,772 beat 493 ********** beat: Starting...
INFO 2025-07-03 10:16:06,451 beat 1221 ********** beat: Starting...
INFO 2025-07-03 10:30:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 10:30:00,030 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 11:00:00,018 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 11:00:00,028 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 11:30:00,006 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 11:30:00,028 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 12:00:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 12:00:00,013 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 12:30:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 12:30:00,024 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 13:00:00,013 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 13:00:00,029 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 13:30:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 13:30:00,021 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 14:00:00,017 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 14:00:00,035 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 14:30:00,006 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 14:30:00,019 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 15:00:00,012 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 15:00:00,035 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 15:30:00,008 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 15:30:00,024 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 16:00:00,012 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 16:00:00,032 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 16:30:00,006 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 16:30:00,020 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 17:00:00,006 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 17:00:00,019 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 17:30:00,009 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 17:30:00,025 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 18:00:00,008 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 18:00:00,024 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 18:30:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 18:30:00,015 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 19:00:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 19:00:00,021 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 19:30:00,006 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 19:30:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 20:00:00,004 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 20:00:00,014 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 20:30:00,007 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 20:30:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 21:00:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 21:00:00,019 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 21:30:00,012 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 21:30:00,024 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 22:00:00,001 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 22:00:00,012 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 22:30:00,011 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 22:30:00,027 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 23:00:00,002 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-03 23:00:00,017 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-03 23:30:00,006 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-03 23:30:00,029 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 00:00:00,012 beat 1221 ********** Scheduler: Sending due task update-active-member-status (tasks.tasks.update_active_member_status)
INFO 2025-07-04 00:00:00,030 beat 1221 ********** Scheduler: Sending due task reset-daily-tasks (tasks.reset_daily_tasks)
INFO 2025-07-04 00:00:00,045 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 00:00:00,053 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 00:01:00,002 beat 1221 ********** Scheduler: Sending due task settle-extra-task-bonuses (tasks.settle_extra_task_bonuses)
INFO 2025-07-04 00:02:00,001 beat 1221 ********** Scheduler: Sending due task update-vip-refund-progress (vip.tasks.update_vip_refund_progress)
INFO 2025-07-04 00:03:00,009 beat 1221 ********** Scheduler: Sending due task clear-agent-cache-daily (agents.tasks.clear_agent_cache_daily)
INFO 2025-07-04 00:04:00,005 beat 1221 ********** Scheduler: Sending due task health-data-daily-reset (users.health_data_daily_reset)
INFO 2025-07-04 00:05:00,004 beat 1221 ********** Scheduler: Sending due task process-vip-refunds (vip.tasks.process_vip_refunds)
INFO 2025-07-04 00:10:00,001 beat 1221 ********** Scheduler: Sending due task update-daily-stats (stats.tasks.update_daily_stats)
INFO 2025-07-04 00:30:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 00:30:00,024 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 01:00:00,010 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 01:00:00,032 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 01:30:00,002 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 01:30:00,025 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 02:00:00,003 beat 1221 ********** Scheduler: Sending due task check-level-consistency (users.check_and_fix_level_consistency)
INFO 2025-07-04 02:00:00,018 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 02:00:00,034 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 02:30:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 02:30:00,026 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 03:00:00,011 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 03:00:00,036 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 03:30:00,002 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 03:30:00,020 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 04:00:00,009 beat 1221 ********** Scheduler: Sending due task celery.backend_cleanup (celery.backend_cleanup)
INFO 2025-07-04 04:00:00,035 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 04:00:00,057 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 04:30:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 04:30:00,024 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 05:00:00,004 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 05:00:00,028 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 05:30:00,001 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 05:30:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 06:00:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 06:00:00,018 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 06:30:00,011 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 06:30:00,035 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 07:00:00,000 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 07:00:00,018 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 07:30:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 07:30:00,033 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 08:00:00,001 beat 1221 ********** Scheduler: Sending due task monitor-level-upgrade-performance (users.monitor_level_upgrade_performance)
INFO 2025-07-04 08:00:00,020 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 08:00:00,039 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 08:30:00,009 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 08:30:00,026 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 09:00:00,007 beat 1221 ********** Scheduler: Sending due task check-stalled-refund-plans (vip.tasks.check_stalled_refund_plans)
INFO 2025-07-04 09:00:00,019 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 09:00:00,030 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 09:30:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 09:30:00,033 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 10:00:00,008 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 10:00:00,030 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 10:30:00,004 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 10:30:00,021 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 11:00:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 11:00:00,030 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 11:30:00,000 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 11:30:00,016 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 12:00:00,012 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 12:00:00,035 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 12:30:00,013 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 12:30:00,033 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 13:00:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 13:00:00,028 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 13:30:00,012 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 13:30:00,037 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 14:00:00,007 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 14:00:00,028 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 14:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 14:30:00,030 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 15:00:00,006 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 15:00:00,035 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 15:30:00,012 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 15:30:00,036 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 16:00:00,010 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 16:00:00,033 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 16:30:00,013 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 16:30:00,042 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 17:00:00,016 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 17:00:00,047 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 17:30:00,013 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 17:30:00,046 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 18:00:00,015 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 18:00:00,037 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 18:30:00,012 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 18:30:00,031 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 19:00:00,013 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 19:00:00,046 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 19:30:00,011 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 19:30:00,031 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 20:00:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 20:00:00,025 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 20:30:00,006 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 20:30:00,029 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 21:00:00,005 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 21:00:00,025 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 21:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 21:30:00,031 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 22:00:00,010 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 22:00:00,030 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 22:30:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 22:30:00,034 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 23:00:00,016 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-04 23:00:00,047 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-04 23:30:00,003 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-04 23:30:00,036 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 00:00:00,012 beat 1221 ********** Scheduler: Sending due task reset-daily-tasks (tasks.reset_daily_tasks)
INFO 2025-07-05 00:00:00,064 beat 1221 ********** Scheduler: Sending due task update-active-member-status (tasks.tasks.update_active_member_status)
INFO 2025-07-05 00:00:00,085 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 00:00:00,103 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 00:01:00,001 beat 1221 ********** Scheduler: Sending due task settle-extra-task-bonuses (tasks.settle_extra_task_bonuses)
INFO 2025-07-05 00:02:00,008 beat 1221 ********** Scheduler: Sending due task update-vip-refund-progress (vip.tasks.update_vip_refund_progress)
INFO 2025-07-05 00:03:00,005 beat 1221 ********** Scheduler: Sending due task clear-agent-cache-daily (agents.tasks.clear_agent_cache_daily)
INFO 2025-07-05 00:04:00,010 beat 1221 ********** Scheduler: Sending due task health-data-daily-reset (users.health_data_daily_reset)
INFO 2025-07-05 00:05:00,017 beat 1221 ********** Scheduler: Sending due task process-vip-refunds (vip.tasks.process_vip_refunds)
INFO 2025-07-05 00:10:00,011 beat 1221 ********** Scheduler: Sending due task update-daily-stats (stats.tasks.update_daily_stats)
INFO 2025-07-05 00:30:00,014 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 00:30:00,037 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 01:00:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 01:00:00,045 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 01:30:00,009 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 01:30:00,032 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 02:00:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 02:00:00,023 beat 1221 ********** Scheduler: Sending due task check-level-consistency (users.check_and_fix_level_consistency)
INFO 2025-07-05 02:00:00,044 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 02:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 02:30:00,018 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 03:00:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 03:00:00,037 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 03:30:00,000 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 03:30:00,023 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 04:00:00,000 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 04:00:00,011 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 04:00:00,022 beat 1221 ********** Scheduler: Sending due task celery.backend_cleanup (celery.backend_cleanup)
INFO 2025-07-05 04:30:00,009 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 04:30:00,027 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 05:00:00,006 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 05:00:00,028 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 05:30:00,007 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 05:30:00,018 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 06:00:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 06:00:00,027 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 06:30:00,004 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 06:30:00,016 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 07:00:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 07:00:00,017 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 07:30:00,008 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 07:30:00,023 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 08:00:00,002 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 08:00:00,021 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 08:00:00,040 beat 1221 ********** Scheduler: Sending due task monitor-level-upgrade-performance (users.monitor_level_upgrade_performance)
INFO 2025-07-05 08:30:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 08:30:00,018 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 09:00:00,004 beat 1221 ********** Scheduler: Sending due task check-stalled-refund-plans (vip.tasks.check_stalled_refund_plans)
INFO 2025-07-05 09:00:00,021 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 09:00:00,049 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 09:30:00,007 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 09:30:00,019 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 10:00:00,009 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 10:00:00,031 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 10:30:00,009 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 10:30:00,030 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 11:00:00,004 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 11:00:00,026 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 11:30:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 11:30:00,030 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 12:00:00,009 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 12:00:00,036 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 12:30:00,006 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 12:30:00,021 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 13:00:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 13:00:00,020 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 13:30:00,003 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 13:30:00,022 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 14:00:00,009 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 14:00:00,042 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 14:30:00,010 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 14:30:00,034 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 15:00:00,006 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 15:00:00,025 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 15:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 15:30:00,037 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 16:00:00,010 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 16:00:00,031 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 16:30:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 16:30:00,034 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 17:00:00,005 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 17:00:00,022 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 17:30:00,008 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 17:30:00,033 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 18:00:00,005 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 18:00:00,036 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 18:30:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 18:30:00,034 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 19:00:00,005 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 19:00:00,017 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 19:30:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 19:30:00,021 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 20:00:00,000 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 20:00:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 20:30:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 20:30:00,018 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 21:00:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 21:00:00,023 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 21:30:00,008 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 21:30:00,030 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 22:00:00,010 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 22:00:00,031 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 22:30:00,011 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 22:30:00,034 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-05 23:00:00,010 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-05 23:00:00,032 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 23:30:00,000 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-05 23:30:00,017 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 00:00:00,007 beat 1221 ********** Scheduler: Sending due task update-active-member-status (tasks.tasks.update_active_member_status)
INFO 2025-07-06 00:00:00,027 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 00:00:00,047 beat 1221 ********** Scheduler: Sending due task reset-daily-tasks (tasks.reset_daily_tasks)
INFO 2025-07-06 00:00:00,064 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 00:01:00,012 beat 1221 ********** Scheduler: Sending due task settle-extra-task-bonuses (tasks.settle_extra_task_bonuses)
INFO 2025-07-06 00:02:00,012 beat 1221 ********** Scheduler: Sending due task update-vip-refund-progress (vip.tasks.update_vip_refund_progress)
INFO 2025-07-06 00:03:00,008 beat 1221 ********** Scheduler: Sending due task clear-agent-cache-daily (agents.tasks.clear_agent_cache_daily)
INFO 2025-07-06 00:04:00,070 beat 1221 ********** Scheduler: Sending due task health-data-daily-reset (users.health_data_daily_reset)
INFO 2025-07-06 00:05:00,012 beat 1221 ********** Scheduler: Sending due task process-vip-refunds (vip.tasks.process_vip_refunds)
INFO 2025-07-06 00:10:00,011 beat 1221 ********** Scheduler: Sending due task update-daily-stats (stats.tasks.update_daily_stats)
INFO 2025-07-06 00:30:00,004 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 00:30:00,031 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 01:00:00,019 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 01:00:00,044 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 01:30:00,003 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 01:30:00,020 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 02:00:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 02:00:00,024 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 02:00:00,051 beat 1221 ********** Scheduler: Sending due task check-level-consistency (users.check_and_fix_level_consistency)
INFO 2025-07-06 02:30:00,010 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 02:30:00,030 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 03:00:00,009 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 03:00:00,028 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 03:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 03:30:00,028 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 04:00:00,002 beat 1221 ********** Scheduler: Sending due task celery.backend_cleanup (celery.backend_cleanup)
INFO 2025-07-06 04:00:00,026 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 04:00:00,044 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 04:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 04:30:00,019 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 05:00:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 05:00:00,026 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 05:30:00,012 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 05:30:00,030 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 06:00:00,004 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 06:00:00,014 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 06:30:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 06:30:00,022 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 07:00:00,005 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 07:00:00,024 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 07:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 07:30:00,027 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 08:00:00,008 beat 1221 ********** Scheduler: Sending due task monitor-level-upgrade-performance (users.monitor_level_upgrade_performance)
INFO 2025-07-06 08:00:00,026 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 08:00:00,045 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 08:30:00,010 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 08:30:00,031 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 09:00:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 09:00:00,028 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 09:00:00,049 beat 1221 ********** Scheduler: Sending due task check-stalled-refund-plans (vip.tasks.check_stalled_refund_plans)
INFO 2025-07-06 09:30:00,008 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 09:30:00,027 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 10:00:00,004 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 10:00:00,021 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 10:30:00,002 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 10:30:00,021 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 11:00:00,001 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 11:00:00,015 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 11:30:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 11:30:00,027 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 12:00:00,007 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 12:00:00,027 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 12:30:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 12:30:00,041 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 13:00:00,002 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 13:00:00,026 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 13:30:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 13:30:00,028 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 14:00:00,008 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 14:00:00,042 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 14:30:00,008 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 14:30:00,029 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 15:00:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 15:00:00,031 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 15:30:00,015 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 15:30:00,043 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 16:00:00,002 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 16:00:00,023 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 16:30:00,004 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 16:30:00,024 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 17:00:00,006 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 17:00:00,023 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 17:30:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 17:30:00,032 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 18:00:00,009 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 18:00:00,026 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 18:30:00,004 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 18:30:00,035 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 19:00:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 19:00:00,031 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 19:30:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 19:30:00,035 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 20:00:00,003 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 20:00:00,025 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 20:30:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 20:30:00,031 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 21:00:00,001 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 21:00:00,026 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 21:30:00,002 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 21:30:00,031 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 22:00:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 22:00:00,018 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 22:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 22:30:00,027 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 23:00:00,000 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-06 23:00:00,028 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-06 23:30:00,009 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-06 23:30:00,023 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 00:00:00,012 beat 1221 ********** Scheduler: Sending due task weekly-commission-settlement (agents.tasks.weekly_commission_settlement)
INFO 2025-07-07 00:00:00,042 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 00:00:00,060 beat 1221 ********** Scheduler: Sending due task reset-daily-tasks (tasks.reset_daily_tasks)
INFO 2025-07-07 00:00:00,074 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 00:00:00,083 beat 1221 ********** Scheduler: Sending due task update-active-member-status (tasks.tasks.update_active_member_status)
INFO 2025-07-07 00:01:00,006 beat 1221 ********** Scheduler: Sending due task settle-extra-task-bonuses (tasks.settle_extra_task_bonuses)
INFO 2025-07-07 00:02:00,012 beat 1221 ********** Scheduler: Sending due task update-vip-refund-progress (vip.tasks.update_vip_refund_progress)
INFO 2025-07-07 00:03:00,003 beat 1221 ********** Scheduler: Sending due task clear-agent-cache-daily (agents.tasks.clear_agent_cache_daily)
INFO 2025-07-07 00:04:00,001 beat 1221 ********** Scheduler: Sending due task health-data-daily-reset (users.health_data_daily_reset)
INFO 2025-07-07 00:05:00,011 beat 1221 ********** Scheduler: Sending due task process-vip-refunds (vip.tasks.process_vip_refunds)
INFO 2025-07-07 00:10:00,007 beat 1221 ********** Scheduler: Sending due task update-daily-stats (stats.tasks.update_daily_stats)
INFO 2025-07-07 00:30:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 00:30:00,026 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 01:00:00,004 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 01:00:00,018 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 01:30:00,009 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 01:30:00,030 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 02:00:00,010 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 02:00:00,035 beat 1221 ********** Scheduler: Sending due task check-level-consistency (users.check_and_fix_level_consistency)
INFO 2025-07-07 02:00:00,052 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 02:30:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 02:30:00,038 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 03:00:00,016 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 03:00:00,040 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 03:30:00,010 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 03:30:00,033 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 04:00:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 04:00:00,041 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 04:00:00,069 beat 1221 ********** Scheduler: Sending due task celery.backend_cleanup (celery.backend_cleanup)
INFO 2025-07-07 04:30:00,011 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 04:30:00,031 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 05:00:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 05:00:00,022 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 05:30:00,010 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 05:30:00,029 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 06:00:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 06:00:00,030 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 06:30:00,003 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 06:30:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 07:00:00,000 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 07:00:00,017 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 07:30:00,010 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 07:30:00,034 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 08:00:00,009 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 08:00:00,031 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 08:00:00,057 beat 1221 ********** Scheduler: Sending due task monitor-level-upgrade-performance (users.monitor_level_upgrade_performance)
INFO 2025-07-07 08:30:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 08:30:00,033 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 09:00:00,003 beat 1221 ********** Scheduler: Sending due task check-stalled-refund-plans (vip.tasks.check_stalled_refund_plans)
INFO 2025-07-07 09:00:00,016 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 09:00:00,027 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 09:30:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 09:30:00,029 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 10:00:00,015 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 10:00:00,045 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 10:30:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 10:30:00,028 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 11:00:00,013 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 11:00:00,039 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 11:30:00,014 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 11:30:00,030 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 12:00:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 12:00:00,036 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 12:30:00,000 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 12:30:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 13:00:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 13:00:00,038 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 13:30:00,008 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 13:30:00,032 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 14:00:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 14:00:00,017 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 14:30:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 14:30:00,028 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 15:00:00,012 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 15:00:00,033 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 15:30:00,004 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 15:30:00,028 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 16:00:00,002 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 16:00:00,020 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 16:30:00,000 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 16:30:00,021 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 17:00:00,012 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 17:00:00,030 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 17:30:00,009 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 17:30:00,036 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 18:00:00,010 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 18:00:00,039 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 18:30:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 18:30:00,028 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 19:00:00,009 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 19:00:00,032 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 19:30:00,009 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 19:30:00,033 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 20:00:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 20:00:00,024 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 20:30:00,004 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 20:30:00,030 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 21:00:00,007 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 21:00:00,034 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 21:30:00,006 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 21:30:00,031 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 22:00:00,009 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 22:00:00,033 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 22:30:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 22:30:00,020 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 23:00:00,010 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-07 23:00:00,032 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-07 23:30:00,002 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-07 23:30:00,019 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 00:00:00,001 beat 1221 ********** Scheduler: Sending due task update-active-member-status (tasks.tasks.update_active_member_status)
INFO 2025-07-08 00:00:00,024 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 00:00:00,055 beat 1221 ********** Scheduler: Sending due task reset-daily-tasks (tasks.reset_daily_tasks)
INFO 2025-07-08 00:00:00,068 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 00:01:00,010 beat 1221 ********** Scheduler: Sending due task settle-extra-task-bonuses (tasks.settle_extra_task_bonuses)
INFO 2025-07-08 00:02:00,002 beat 1221 ********** Scheduler: Sending due task update-vip-refund-progress (vip.tasks.update_vip_refund_progress)
INFO 2025-07-08 00:03:00,000 beat 1221 ********** Scheduler: Sending due task clear-agent-cache-daily (agents.tasks.clear_agent_cache_daily)
INFO 2025-07-08 00:04:00,008 beat 1221 ********** Scheduler: Sending due task health-data-daily-reset (users.health_data_daily_reset)
INFO 2025-07-08 00:05:00,012 beat 1221 ********** Scheduler: Sending due task process-vip-refunds (vip.tasks.process_vip_refunds)
INFO 2025-07-08 00:10:00,017 beat 1221 ********** Scheduler: Sending due task update-daily-stats (stats.tasks.update_daily_stats)
INFO 2025-07-08 00:30:00,004 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 00:30:00,016 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 01:00:00,001 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 01:00:00,023 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 01:30:00,009 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 01:30:00,036 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 02:00:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 02:00:00,025 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 02:00:00,050 beat 1221 ********** Scheduler: Sending due task check-level-consistency (users.check_and_fix_level_consistency)
INFO 2025-07-08 02:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 02:30:00,021 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 03:00:00,004 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 03:00:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 03:30:00,016 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 03:30:00,039 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 04:00:00,006 beat 1221 ********** Scheduler: Sending due task celery.backend_cleanup (celery.backend_cleanup)
INFO 2025-07-08 04:00:00,023 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 04:00:00,041 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 04:30:00,018 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 04:30:00,039 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 05:00:00,005 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 05:00:00,017 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 05:30:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 05:30:00,015 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 06:00:00,003 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 06:00:00,026 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 06:30:00,000 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 06:30:00,013 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 07:00:00,009 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 07:00:00,034 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 07:30:00,009 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 07:30:00,031 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 08:00:00,003 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 08:00:00,026 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 08:00:00,042 beat 1221 ********** Scheduler: Sending due task monitor-level-upgrade-performance (users.monitor_level_upgrade_performance)
INFO 2025-07-08 08:30:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 08:30:00,018 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 09:00:00,004 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 09:00:00,032 beat 1221 ********** Scheduler: Sending due task check-stalled-refund-plans (vip.tasks.check_stalled_refund_plans)
INFO 2025-07-08 09:00:00,056 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 09:30:00,012 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 09:30:00,043 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 10:00:00,005 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 10:00:00,027 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 10:30:00,000 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 10:30:00,017 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 11:00:00,015 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 11:00:00,036 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 11:30:00,006 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 11:30:00,029 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 12:00:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 12:00:00,020 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 12:30:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 12:30:00,021 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 13:00:00,004 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 13:00:00,020 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 13:30:00,007 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 13:30:00,026 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 14:00:00,010 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 14:00:00,037 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 14:30:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 14:30:00,022 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 15:00:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 15:00:00,020 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 15:30:00,001 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 15:30:00,019 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 16:00:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 16:00:00,030 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 16:30:00,010 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 16:30:00,035 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 17:00:00,003 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 17:00:00,019 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 17:30:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 17:30:00,028 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 18:00:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 18:00:00,035 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 18:30:00,008 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 18:30:00,035 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 19:00:00,006 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 19:00:00,027 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 19:30:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 19:30:00,019 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 20:00:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 20:00:00,018 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 20:30:00,003 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 20:30:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 21:00:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 21:00:00,023 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 21:30:00,007 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 21:30:00,035 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 22:00:00,008 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 22:00:00,032 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 22:30:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 22:30:00,015 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 23:00:00,013 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-08 23:00:00,029 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-08 23:30:00,012 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-08 23:30:00,030 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 00:00:00,011 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 00:00:00,026 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 00:00:00,039 beat 1221 ********** Scheduler: Sending due task reset-daily-tasks (tasks.reset_daily_tasks)
INFO 2025-07-09 00:00:00,049 beat 1221 ********** Scheduler: Sending due task update-active-member-status (tasks.tasks.update_active_member_status)
INFO 2025-07-09 00:01:00,004 beat 1221 ********** Scheduler: Sending due task settle-extra-task-bonuses (tasks.settle_extra_task_bonuses)
INFO 2025-07-09 00:02:00,012 beat 1221 ********** Scheduler: Sending due task update-vip-refund-progress (vip.tasks.update_vip_refund_progress)
INFO 2025-07-09 00:03:00,006 beat 1221 ********** Scheduler: Sending due task clear-agent-cache-daily (agents.tasks.clear_agent_cache_daily)
INFO 2025-07-09 00:04:00,007 beat 1221 ********** Scheduler: Sending due task health-data-daily-reset (users.health_data_daily_reset)
INFO 2025-07-09 00:05:00,011 beat 1221 ********** Scheduler: Sending due task process-vip-refunds (vip.tasks.process_vip_refunds)
INFO 2025-07-09 00:10:00,011 beat 1221 ********** Scheduler: Sending due task update-daily-stats (stats.tasks.update_daily_stats)
INFO 2025-07-09 00:30:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 00:30:00,023 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 01:00:00,001 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 01:00:00,015 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 01:30:00,003 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 01:30:00,017 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 02:00:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 02:00:00,015 beat 1221 ********** Scheduler: Sending due task check-level-consistency (users.check_and_fix_level_consistency)
INFO 2025-07-09 02:00:00,032 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 02:30:00,005 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 02:30:00,018 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 03:00:00,005 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 03:00:00,018 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 03:30:00,009 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 03:30:00,023 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 04:00:00,011 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 04:00:00,035 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 04:00:00,052 beat 1221 ********** Scheduler: Sending due task celery.backend_cleanup (celery.backend_cleanup)
INFO 2025-07-09 04:30:00,012 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 04:30:00,043 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 05:00:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 05:00:00,035 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 05:30:00,013 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 05:30:00,039 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 06:00:00,004 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 06:00:00,027 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 06:30:00,002 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 06:30:00,019 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 07:00:00,006 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 07:00:00,023 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 07:30:00,011 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 07:30:00,037 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 08:00:00,009 beat 1221 ********** Scheduler: Sending due task monitor-level-upgrade-performance (users.monitor_level_upgrade_performance)
INFO 2025-07-09 08:00:00,030 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 08:00:00,048 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 08:30:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 08:30:00,035 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 09:00:00,009 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 09:00:00,034 beat 1221 ********** Scheduler: Sending due task check-stalled-refund-plans (vip.tasks.check_stalled_refund_plans)
INFO 2025-07-09 09:00:00,055 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 09:30:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 09:30:00,024 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 10:00:00,008 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 10:00:00,032 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 10:30:00,013 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 10:30:00,030 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 11:00:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 11:00:00,028 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 11:30:00,004 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 11:30:00,032 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 12:00:00,013 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 12:00:00,030 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 12:30:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 12:30:00,027 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 13:00:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 13:00:00,034 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 13:30:00,013 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 13:30:00,034 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 14:00:00,006 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 14:00:00,024 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 14:30:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 14:30:00,027 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 15:00:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 15:00:00,031 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 15:30:00,002 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 15:30:00,022 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 16:00:00,004 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 16:00:00,027 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 16:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 16:30:00,026 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 17:00:00,008 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 17:00:00,033 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 17:30:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 17:30:00,032 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 18:00:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 18:00:00,036 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 18:30:00,006 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 18:30:00,025 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 19:00:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 19:00:00,029 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 19:30:00,006 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 19:30:00,028 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 20:00:00,011 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 20:00:00,033 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 20:30:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 20:30:00,038 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 21:00:00,012 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 21:00:00,034 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 21:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 21:30:00,031 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 22:00:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 22:00:00,029 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 22:30:00,004 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-09 22:30:00,020 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 23:00:00,003 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-09 23:00:00,017 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 23:30:00,000 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-09 23:30:00,016 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 00:00:00,001 beat 1221 ********** Scheduler: Sending due task update-active-member-status (tasks.tasks.update_active_member_status)
INFO 2025-07-10 00:00:00,023 beat 1221 ********** Scheduler: Sending due task reset-daily-tasks (tasks.reset_daily_tasks)
INFO 2025-07-10 00:00:00,035 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 00:00:00,043 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 00:01:00,001 beat 1221 ********** Scheduler: Sending due task settle-extra-task-bonuses (tasks.settle_extra_task_bonuses)
INFO 2025-07-10 00:02:00,008 beat 1221 ********** Scheduler: Sending due task update-vip-refund-progress (vip.tasks.update_vip_refund_progress)
INFO 2025-07-10 00:03:00,012 beat 1221 ********** Scheduler: Sending due task clear-agent-cache-daily (agents.tasks.clear_agent_cache_daily)
INFO 2025-07-10 00:04:00,007 beat 1221 ********** Scheduler: Sending due task health-data-daily-reset (users.health_data_daily_reset)
INFO 2025-07-10 00:05:00,008 beat 1221 ********** Scheduler: Sending due task process-vip-refunds (vip.tasks.process_vip_refunds)
INFO 2025-07-10 00:10:00,009 beat 1221 ********** Scheduler: Sending due task update-daily-stats (stats.tasks.update_daily_stats)
INFO 2025-07-10 00:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 00:30:00,032 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 01:00:00,002 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 01:00:00,019 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 01:30:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 01:30:00,026 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 02:00:00,012 beat 1221 ********** Scheduler: Sending due task check-level-consistency (users.check_and_fix_level_consistency)
INFO 2025-07-10 02:00:00,040 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 02:00:00,054 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 02:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 02:30:00,035 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 03:00:00,001 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 03:00:00,024 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 03:30:00,004 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 03:30:00,029 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 04:00:00,000 beat 1221 ********** Scheduler: Sending due task celery.backend_cleanup (celery.backend_cleanup)
INFO 2025-07-10 04:00:00,024 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 04:00:00,045 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 04:30:00,009 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 04:30:00,020 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 05:00:00,000 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 05:00:00,012 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 05:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 05:30:00,033 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 06:00:00,007 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 06:00:00,030 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 06:30:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 06:30:00,022 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 07:00:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 07:00:00,033 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 07:30:00,007 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 07:30:00,021 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 08:00:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 08:00:00,018 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 08:00:00,029 beat 1221 ********** Scheduler: Sending due task monitor-level-upgrade-performance (users.monitor_level_upgrade_performance)
INFO 2025-07-10 08:30:00,001 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 08:30:00,021 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 09:00:00,009 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 09:00:00,037 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 09:00:00,050 beat 1221 ********** Scheduler: Sending due task check-stalled-refund-plans (vip.tasks.check_stalled_refund_plans)
INFO 2025-07-10 09:30:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 09:30:00,021 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 10:00:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 10:00:00,028 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 10:30:00,014 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 10:30:00,033 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 11:00:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 11:00:00,027 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 11:30:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 11:30:00,026 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 12:00:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 12:00:00,016 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 12:30:00,002 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 12:30:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 13:00:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 13:00:00,032 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 13:30:00,002 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 13:30:00,021 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 14:00:00,002 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 14:00:00,023 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 14:30:00,002 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 14:30:00,021 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 15:00:00,007 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 15:00:00,025 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 15:30:00,012 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 15:30:00,024 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 16:00:00,013 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 16:00:00,034 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 16:30:00,003 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 16:30:00,023 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 17:00:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 17:00:00,029 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 17:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 17:30:00,030 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 18:00:00,000 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 18:00:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 18:30:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 18:30:00,021 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 19:00:00,013 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 19:00:00,036 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 19:30:00,013 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 19:30:00,034 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 20:00:00,006 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 20:00:00,032 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 20:30:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 20:30:00,029 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 21:00:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 21:00:00,038 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 21:30:00,011 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 21:30:00,034 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 22:00:00,011 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 22:00:00,029 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 22:30:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 22:30:00,040 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-10 23:00:00,018 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-10 23:00:00,038 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 23:30:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-10 23:30:00,012 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 00:00:00,011 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 00:00:00,051 beat 1221 ********** Scheduler: Sending due task reset-daily-tasks (tasks.reset_daily_tasks)
INFO 2025-07-11 00:00:00,076 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 00:00:00,090 beat 1221 ********** Scheduler: Sending due task update-active-member-status (tasks.tasks.update_active_member_status)
INFO 2025-07-11 00:01:00,008 beat 1221 ********** Scheduler: Sending due task settle-extra-task-bonuses (tasks.settle_extra_task_bonuses)
INFO 2025-07-11 00:02:00,005 beat 1221 ********** Scheduler: Sending due task update-vip-refund-progress (vip.tasks.update_vip_refund_progress)
INFO 2025-07-11 00:03:00,006 beat 1221 ********** Scheduler: Sending due task clear-agent-cache-daily (agents.tasks.clear_agent_cache_daily)
INFO 2025-07-11 00:04:00,001 beat 1221 ********** Scheduler: Sending due task health-data-daily-reset (users.health_data_daily_reset)
INFO 2025-07-11 00:05:00,012 beat 1221 ********** Scheduler: Sending due task process-vip-refunds (vip.tasks.process_vip_refunds)
INFO 2025-07-11 00:10:00,006 beat 1221 ********** Scheduler: Sending due task update-daily-stats (stats.tasks.update_daily_stats)
INFO 2025-07-11 00:30:00,003 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 00:30:00,026 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 01:00:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 01:00:00,026 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 01:30:00,011 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 01:30:00,030 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 02:00:00,007 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 02:00:00,027 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 02:00:00,059 beat 1221 ********** Scheduler: Sending due task check-level-consistency (users.check_and_fix_level_consistency)
INFO 2025-07-11 02:30:00,009 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 02:30:00,032 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 03:00:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 03:00:00,031 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 03:30:00,009 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 03:30:00,031 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 04:00:00,002 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 04:00:00,016 beat 1221 ********** Scheduler: Sending due task celery.backend_cleanup (celery.backend_cleanup)
INFO 2025-07-11 04:00:00,026 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 04:30:00,013 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 04:30:00,038 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 05:00:00,005 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 05:00:00,026 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 05:30:00,002 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 05:30:00,014 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 06:00:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 06:00:00,038 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 06:30:00,015 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 06:30:00,040 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 07:00:00,011 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 07:00:00,037 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 07:30:00,008 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 07:30:00,031 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 08:00:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 08:00:00,033 beat 1221 ********** Scheduler: Sending due task monitor-level-upgrade-performance (users.monitor_level_upgrade_performance)
INFO 2025-07-11 08:00:00,055 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 08:30:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 08:30:00,018 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 09:00:00,006 beat 1221 ********** Scheduler: Sending due task check-stalled-refund-plans (vip.tasks.check_stalled_refund_plans)
INFO 2025-07-11 09:00:00,037 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 09:00:00,058 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 09:30:00,000 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 09:30:00,013 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 10:00:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 10:00:00,036 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 10:30:00,006 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 10:30:00,023 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 11:00:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 11:00:00,033 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 11:30:00,008 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 11:30:00,030 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 12:00:00,010 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 12:00:00,031 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 12:30:00,008 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 12:30:00,029 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 13:00:00,013 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 13:00:00,032 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 13:30:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 13:30:00,029 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 14:00:00,004 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 14:00:00,020 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 14:30:00,007 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 14:30:00,021 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 15:00:00,009 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 15:00:00,040 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 15:30:00,000 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 15:30:00,014 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 16:00:00,007 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 16:00:00,033 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 16:30:00,014 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 16:30:00,042 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 17:00:00,008 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 17:00:00,032 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 17:30:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 17:30:00,018 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 18:00:00,006 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 18:00:00,028 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 18:30:00,023 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 18:30:00,051 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 19:00:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 19:00:00,014 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 19:30:00,009 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 19:30:00,026 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 20:00:00,006 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 20:00:00,023 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 20:30:00,005 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 20:30:00,015 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 21:00:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 21:00:00,020 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 21:30:00,001 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 21:30:00,017 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 22:00:00,004 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 22:00:00,017 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 22:30:00,002 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 22:30:00,013 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-11 23:00:00,000 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-11 23:00:00,017 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 23:30:00,002 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-11 23:30:00,022 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-12 00:00:00,000 beat 1221 ********** Scheduler: Sending due task update-active-member-status (tasks.tasks.update_active_member_status)
INFO 2025-07-12 00:00:00,017 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 00:00:00,035 beat 1221 ********** Scheduler: Sending due task reset-daily-tasks (tasks.reset_daily_tasks)
INFO 2025-07-12 00:00:00,046 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-12 00:01:00,017 beat 1221 ********** Scheduler: Sending due task settle-extra-task-bonuses (tasks.settle_extra_task_bonuses)
INFO 2025-07-12 00:02:00,005 beat 1221 ********** Scheduler: Sending due task update-vip-refund-progress (vip.tasks.update_vip_refund_progress)
INFO 2025-07-12 00:03:00,000 beat 1221 ********** Scheduler: Sending due task clear-agent-cache-daily (agents.tasks.clear_agent_cache_daily)
INFO 2025-07-12 00:04:00,001 beat 1221 ********** Scheduler: Sending due task health-data-daily-reset (users.health_data_daily_reset)
INFO 2025-07-12 00:05:00,010 beat 1221 ********** Scheduler: Sending due task process-vip-refunds (vip.tasks.process_vip_refunds)
INFO 2025-07-12 00:10:00,001 beat 1221 ********** Scheduler: Sending due task update-daily-stats (stats.tasks.update_daily_stats)
INFO 2025-07-12 00:30:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 00:30:00,024 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-12 01:00:00,005 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-12 01:00:00,024 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 01:30:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 01:30:00,017 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-12 02:00:00,002 beat 1221 ********** Scheduler: Sending due task check-level-consistency (users.check_and_fix_level_consistency)
INFO 2025-07-12 02:00:00,013 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 02:00:00,021 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-12 02:30:00,006 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 02:30:00,016 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-12 03:00:00,004 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-12 03:00:00,012 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 03:30:00,000 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 03:30:00,010 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-12 04:00:00,002 beat 1221 ********** Scheduler: Sending due task celery.backend_cleanup (celery.backend_cleanup)
INFO 2025-07-12 04:00:00,012 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-12 04:00:00,020 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 04:30:00,003 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-12 04:30:00,013 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 05:00:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 05:00:00,021 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-12 05:30:00,003 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 05:30:00,012 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-12 06:00:00,002 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-12 06:00:00,012 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 06:30:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 06:30:00,016 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-12 07:00:00,001 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-12 07:00:00,012 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 07:30:00,004 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-12 07:30:00,015 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 08:00:00,000 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 08:00:00,010 beat 1221 ********** Scheduler: Sending due task monitor-level-upgrade-performance (users.monitor_level_upgrade_performance)
INFO 2025-07-12 08:00:00,019 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-12 08:30:00,002 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-12 08:30:00,012 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 09:00:00,000 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 09:00:00,010 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-12 09:00:00,018 beat 1221 ********** Scheduler: Sending due task check-stalled-refund-plans (vip.tasks.check_stalled_refund_plans)
INFO 2025-07-12 09:30:00,002 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-12 09:30:00,011 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 10:00:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 10:00:00,011 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-12 10:30:00,003 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-12 10:30:00,013 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 11:00:00,007 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-12 11:00:00,020 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 11:30:00,002 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 11:30:00,015 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-12 12:00:00,018 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-12 12:00:00,121 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 12:30:00,002 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-12 12:30:00,011 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 13:00:00,001 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 13:00:00,015 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
INFO 2025-07-12 13:30:00,003 beat 1221 ********** Scheduler: Sending due task check-new-user-levels (users.check_new_user_levels)
INFO 2025-07-12 13:30:00,013 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 14:00:00,005 beat 1221 ********** Scheduler: Sending due task cache-monitoring-data (tasks.tasks.cache_monitoring_data)
INFO 2025-07-12 14:00:00,017 beat 1221 ********** Scheduler: Sending due task cleanup-expired-tokens (authentication.tasks.cleanup_expired_tokens)
