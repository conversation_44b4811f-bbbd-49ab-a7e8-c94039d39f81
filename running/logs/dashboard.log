INFO 2025-07-23 12:29:46,461 dashboard.api 50317 8544591616 Dashboard API module initialized at 2025-07-23T12:29:46.461400
INFO 2025-07-23 12:29:46,536 dashboard.api 50317 ********** [-] DashboardViewSet: Home dashboard data requested by user 473 (<EMAIL>). IP: ***************
INFO 2025-07-23 12:29:46,536 dashboard.api 50317 ********** [-] DashboardService: Starting data aggregation for user 473 (test_user3)
INFO 2025-07-23 12:29:46,537 dashboard.api 50317 ********** 正在获取用户 <EMAIL> 的今日摘要数据
INFO 2025-07-23 12:29:46,543 dashboard.api 50317 ********** 🔄 用户对象已刷新以获取最新数据: user=<EMAIL>, exp=25519, swmt_balance=6627.00000000
DEBUG 2025-07-23 12:29:46,567 dashboard.api 50317 ********** today_summary 原始响应: {'code': 200, 'message': 'Success', 'data': {'date': '2025-07-23', 'total_tasks': 4, 'completed_tasks': 0, 'completion_percentage': 0, 'earned_today': {'swmt': '0.00', 'exp': 0}, 'current_streak': 0, 'health_data': {'steps': 0, 'distance': 0.0, 'calories': 0}, 'next_tasks': [{'id': '120', 'name': '广告03-L3-100权重-45秒', 'type': 'ad', 'icon': None, 'total_swmt': '702.40', 'total_exp': 936}, {'id': '118', 'name': '广告01-L3-100权重-30秒', 'type': 'ad', 'icon': None, 'total_swmt': '1235.20', 'total_exp': 753}, {'id': '113', 'name': '步数02-L3-5000步-权重2', 'type': 'steps', 'icon': None, 'total_swmt': '702.40', 'total_exp': 1540}], 'all_tasks_completed': False}}
INFO 2025-07-23 12:29:46,568 dashboard.api 50317 ********** 用户 <EMAIL> 今日收益: SWMT=0.00, XP=0
INFO 2025-07-23 12:29:46,568 dashboard.api 50317 ********** ✅ Dashboard API专注聚合任务数据，健康数据由独立 /health API 提供
INFO 2025-07-23 12:29:46,582 dashboard.api 50317 ********** [-] Cached complete dashboard data for user 473 for 300s
INFO 2025-07-23 12:29:46,582 dashboard.api 50317 ********** [-] DashboardService: Data aggregation for user 473 completed in 0.0464 seconds.
INFO 2025-07-23 12:29:46,582 dashboard.api 50317 ********** [-] Data quality: 5/5 modules complete - {'user_profile_complete': True, 'vip_status_complete': True, 'today_summary_complete': True, 'daily_tasks_complete': True, 'addon_tasks_complete': True}
INFO 2025-07-23 12:29:46,582 dashboard.api 50317 ********** [-] DashboardViewSet: User <EMAIL> earned_today data: SWMT=0.00, XP=0
INFO 2025-07-23 12:29:46,582 dashboard.api 50317 ********** [-] Tasks summary: Daily=0/4, Addon=0/2
INFO 2025-07-23 12:29:46,583 dashboard.api 50317 ********** [-] DashboardViewSet: Home dashboard data served to user 473 in 0.047s
DEBUG 2025-07-23 12:29:46,583 dashboard.api 50317 ********** [-] DashboardViewSet Response earned_today: {"swmt": "0.00", "exp": 0}
DEBUG 2025-07-23 12:29:46,583 dashboard.api 50317 ********** [-] DashboardViewSet Response key data: {"user_profile": {"avatar": null}, "vip_status": {"has_vip": true}, "today_summary": {"earned_today": {"swmt": "0.00", "exp": 0}, "completion_percentage": 0}, "data_quality": {"user_profile_complete": true, "vip_status_complete": true, "today_summary_complete": true, "daily_tasks_complete": true, "addon_tasks_complete": true}}
