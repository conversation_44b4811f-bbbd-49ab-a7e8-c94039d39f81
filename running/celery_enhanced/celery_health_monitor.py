#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SweatMint Celery 健康监控脚本
基于最新Celery最佳实践，提供自动监控和修复功能
"""

import os
import sys
import time
import json
import logging
import subprocess
from datetime import datetime, timedelta
from pathlib import Path

# 添加Django项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
os.chdir(project_root)  # 切换到项目根目录
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')

import django
django.setup()

from celery import Celery
from django.core.cache import cache
from django.db import connection
from django_celery_beat.models import PeriodicTask, PeriodicTasks

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/celery_health_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CeleryHealthMonitor:
    """Celery健康监控器"""
    
    def __init__(self):
        self.app = Celery('core')
        self.app.config_from_object('django.conf:settings', namespace='CELERY')
        # 尝试加载优化配置，如果失败则使用默认配置
        try:
            import importlib.util
            config_path = os.path.join(os.path.dirname(__file__), 'celery_config.py')
            if os.path.exists(config_path):
                spec = importlib.util.spec_from_file_location("celery_config", config_path)
                celery_config = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(celery_config)
                
                # 应用配置
                for attr_name in dir(celery_config):
                    if not attr_name.startswith('_'):
                        attr_value = getattr(celery_config, attr_name)
                        if not callable(attr_value) and not isinstance(attr_value, type):
                            setattr(self.app.conf, attr_name, attr_value)
        except Exception:
            # 如果无法导入优化配置，使用默认配置
            pass
        
        self.pid_dir = Path('/tmp/celery_pids')
        self.log_dir = Path('logs')
        
        # 健康检查阈值
        self.max_log_size = 100 * 1024 * 1024  # 100MB
        self.max_memory_usage = 800 * 1024 * 1024  # 800MB (提高阈值，考虑到Django+Celery的正常内存需求)
        self.max_task_runtime = 900  # 15分钟 (增加任务超时时间)
        
        # 监控指标
        self.metrics = {
            'worker_status': False,
            'beat_status': False,
    
            'redis_status': False,
            'db_status': False,
            'task_queue_length': 0,
            'failed_tasks_count': 0,
            'active_tasks_count': 0,
            'last_check_time': None
        }
    
    def check_process_running(self, pid_file):
        """检查进程是否运行"""
        try:
            if not pid_file.exists():
                return False
            
            with open(pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # 检查进程是否存在
            try:
                os.kill(pid, 0)
                return True
            except OSError:
                # 进程不存在，删除PID文件
                pid_file.unlink(missing_ok=True)
                return False
        except Exception as e:
            logger.error(f"检查进程状态失败: {e}")
            return False
    
    def check_redis_connection(self):
        """检查Redis连接"""
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.ping()
            return True
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            return False
    
    def check_database_connection(self):
        """检查数据库连接"""
        try:
            # 确保数据库连接是活跃的
            from django.db import connections
            from django.core.management import execute_from_command_line
            
            # 使用Django的数据库检查
            db_conn = connections['default']
            db_conn.ensure_connection()
            
            with db_conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if result and result[0] == 1:
                    return True
                else:
                    logger.error("数据库查询返回异常结果")
                    return False
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def check_celery_workers(self):
        """检查Celery Worker状态"""
        try:
            inspect = self.app.control.inspect()
            
            # 检查活跃的workers
            active_workers = inspect.active()
            if not active_workers:
                logger.warning("没有活跃的Celery Workers")
                return False
            
            # 检查worker统计信息
            stats = inspect.stats()
            if not stats:
                logger.warning("无法获取Worker统计信息")
                return False
            
            # 检查每个worker的健康状态
            for worker_name, worker_stats in stats.items():
                # 检查内存使用
                if 'rusage' in worker_stats:
                    memory_usage = worker_stats['rusage'].get('maxrss', 0)  # 已经是字节
                    if memory_usage > self.max_memory_usage:
                        logger.warning(f"Worker {worker_name} 内存使用过高: {memory_usage / 1024 / 1024:.1f}MB")
                
                # 检查任务处理情况
                total_tasks = worker_stats.get('total', {})
                logger.info(f"Worker {worker_name}: 已处理 {total_tasks} 个任务")
            
            return True
        except Exception as e:
            logger.error(f"检查Celery Workers失败: {e}")
            return False
    
    def check_task_queues(self):
        """检查任务队列状态"""
        try:
            inspect = self.app.control.inspect()
            
            # 检查活跃任务
            active_tasks = inspect.active()
            active_count = sum(len(tasks) for tasks in active_tasks.values()) if active_tasks else 0
            
            # 检查预留任务
            reserved_tasks = inspect.reserved()
            reserved_count = sum(len(tasks) for tasks in reserved_tasks.values()) if reserved_tasks else 0
            
            # 检查调度任务
            scheduled_tasks = inspect.scheduled()
            scheduled_count = sum(len(tasks) for tasks in scheduled_tasks.values()) if scheduled_tasks else 0
            
            self.metrics['active_tasks_count'] = active_count
            
            logger.info(f"任务队列状态 - 活跃: {active_count}, 预留: {reserved_count}, 调度: {scheduled_count}")
            
            # 检查是否有长时间运行的任务
            if active_tasks:
                for worker_name, tasks in active_tasks.items():
                    for task in tasks:
                        if 'time_start' in task:
                            start_time = task['time_start']
                            runtime = time.time() - start_time
                            if runtime > self.max_task_runtime:
                                logger.warning(f"任务 {task['id']} 运行时间过长: {runtime:.1f}秒")
            
            return True
        except Exception as e:
            logger.error(f"检查任务队列失败: {e}")
            return False
    
    def check_periodic_tasks(self):
        """检查定时任务状态"""
        try:
            # 检查关键定时任务的最后执行时间
            critical_tasks = [
                'reset-daily-tasks',
                'settle-extra-task-bonuses',
                'update-active-member-status'
            ]
            
            for task_name in critical_tasks:
                try:
                    task = PeriodicTask.objects.get(name=task_name)
                    if task.last_run_at:
                        time_since_last_run = datetime.now(task.last_run_at.tzinfo) - task.last_run_at
                        if time_since_last_run > timedelta(days=2):
                            logger.warning(f"定时任务 {task_name} 超过2天未执行")
                    else:
                        logger.warning(f"定时任务 {task_name} 从未执行过")
                except PeriodicTask.DoesNotExist:
                    logger.error(f"定时任务 {task_name} 不存在")
            
            return True
        except Exception as e:
            logger.error(f"检查定时任务失败: {e}")
            return False
    
    def check_log_files(self):
        """检查日志文件大小"""
        try:
            log_files = [
                self.log_dir / 'celery_worker.log',
                self.log_dir / 'celery_beat.log',
    
            ]
            
            for log_file in log_files:
                if log_file.exists():
                    size = log_file.stat().st_size
                    if size > self.max_log_size:
                        logger.warning(f"日志文件过大: {log_file} ({size / 1024 / 1024:.1f}MB)")
                        # 自动轮转日志
                        self.rotate_log_file(log_file)
            
            return True
        except Exception as e:
            logger.error(f"检查日志文件失败: {e}")
            return False
    
    def rotate_log_file(self, log_file):
        """轮转日志文件"""
        try:
            backup_file = log_file.with_suffix(f'.log.{datetime.now().strftime("%Y%m%d_%H%M%S")}')
            log_file.rename(backup_file)
            log_file.touch()
            logger.info(f"日志文件已轮转: {backup_file}")
            
            # 压缩旧日志
            subprocess.run(['gzip', str(backup_file)], check=False)
        except Exception as e:
            logger.error(f"轮转日志文件失败: {e}")
    
    def restart_service(self, service_name):
        """重启服务"""
        try:
            script_path = Path(__file__).parent / 'start_celery_enhanced.sh'
            if script_path.exists():
                result = subprocess.run([str(script_path), service_name], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    logger.info(f"服务 {service_name} 重启成功")
                    return True
                else:
                    logger.error(f"服务 {service_name} 重启失败: {result.stderr}")
                    return False
            else:
                logger.error("启动脚本不存在")
                return False
        except Exception as e:
            logger.error(f"重启服务失败: {e}")
            return False
    
    def perform_health_check(self):
        """执行完整的健康检查"""
        logger.info("开始执行健康检查...")
        
        # 更新检查时间
        self.metrics['last_check_time'] = datetime.now()
        
        # 检查基础服务
        self.metrics['redis_status'] = self.check_redis_connection()
        self.metrics['db_status'] = self.check_database_connection()
        
        # 检查Celery进程
        self.metrics['worker_status'] = self.check_process_running(self.pid_dir / 'celery_worker.pid')
        self.metrics['beat_status'] = self.check_process_running(self.pid_dir / 'celery_beat.pid')

        
        # 检查Celery功能
        if self.metrics['worker_status']:
            self.check_celery_workers()
            self.check_task_queues()
        
        if self.metrics['beat_status']:
            self.check_periodic_tasks()
        
        # 检查日志文件
        self.check_log_files()
        
        # 生成健康报告
        self.generate_health_report()
        
        logger.info("健康检查完成")
    
    def generate_health_report(self):
        """生成健康报告"""
        report = {
            'timestamp': self.metrics['last_check_time'].isoformat(),
            'overall_status': 'healthy' if all([
                self.metrics['redis_status'],
                self.metrics['db_status'],
                self.metrics['worker_status'],
                self.metrics['beat_status']
            ]) else 'unhealthy',
            'services': {
                'redis': 'up' if self.metrics['redis_status'] else 'down',
                'database': 'up' if self.metrics['db_status'] else 'down',
                'celery_worker': 'up' if self.metrics['worker_status'] else 'down',
                'celery_beat': 'up' if self.metrics['beat_status'] else 'down',
    
            },
            'metrics': {
                'active_tasks': self.metrics['active_tasks_count'],
                'failed_tasks': self.metrics['failed_tasks_count']
            }
        }
        
        # 保存报告到文件
        report_file = self.log_dir / 'health_report.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        # 输出简要状态
        status_emoji = "✅" if report['overall_status'] == 'healthy' else "❌"
        logger.info(f"{status_emoji} 系统状态: {report['overall_status']}")
        
        for service, status in report['services'].items():
            emoji = "✅" if status == 'up' else "❌"
            logger.info(f"  {emoji} {service}: {status}")
    
    def auto_recovery(self):
        """自动恢复功能"""
        logger.info("检查是否需要自动恢复...")
        
        recovery_actions = []
        
        # 检查Worker状态
        if not self.metrics['worker_status']:
            recovery_actions.append(('worker', 'Celery Worker未运行'))
        
        # 检查Beat状态
        if not self.metrics['beat_status']:
            recovery_actions.append(('beat', 'Celery Beat未运行'))
        

        
        # 执行恢复操作
        for service, reason in recovery_actions:
            logger.warning(f"检测到问题: {reason}，尝试重启 {service}")
            if self.restart_service(service):
                logger.info(f"自动恢复成功: {service}")
            else:
                logger.error(f"自动恢复失败: {service}")
    
    def run_continuous_monitoring(self, interval=300):
        """运行连续监控"""
        logger.info(f"开始连续监控，检查间隔: {interval}秒")
        
        while True:
            try:
                self.perform_health_check()
                self.auto_recovery()
                time.sleep(interval)
            except KeyboardInterrupt:
                logger.info("监控已停止")
                break
            except Exception as e:
                logger.error(f"监控过程中发生错误: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SweatMint Celery 健康监控')
    parser.add_argument('--interval', type=int, default=300, 
                       help='监控间隔（秒），默认300秒')
    parser.add_argument('--once', action='store_true', 
                       help='只执行一次检查，不进行连续监控')
    parser.add_argument('--auto-recovery', action='store_true', 
                       help='启用自动恢复功能')
    
    args = parser.parse_args()
    
    monitor = CeleryHealthMonitor()
    
    if args.once:
        monitor.perform_health_check()
        if args.auto_recovery:
            monitor.auto_recovery()
    else:
        monitor.run_continuous_monitoring(args.interval)

if __name__ == '__main__':
    main() 