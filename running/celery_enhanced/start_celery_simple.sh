#!/bin/bash

# SweatMint Celery 简化启动脚本 (无Flower)
# 现在使用Django原生监控界面：后台 → 数据中心 → 🌸 任务监控

set -e

# 项目配置
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CELERY_APP="core"
LOG_DIR="$PROJECT_ROOT/logs"
PID_DIR="$PROJECT_ROOT/celery_enhanced/pids"

# 虚拟环境配置
VENV_PATH="$PROJECT_ROOT/.venv"
PYTHON_CMD="$VENV_PATH/bin/python"
CELERY_CMD="$VENV_PATH/bin/celery"

# 日志和PID文件
WORKER_LOG="$LOG_DIR/celery_worker.log"
BEAT_LOG="$LOG_DIR/celery_beat.log"
WORKER_PID="$PID_DIR/worker.pid"
BEAT_PID="$PID_DIR/beat.pid"

# 创建必要目录
mkdir -p "$LOG_DIR" "$PID_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查进程是否运行
is_running() {
    local pid_file="$1"
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# 停止进程
stop_process() {
    local pid_file="$1"
    local name="$2"
    
    if is_running "$pid_file"; then
        local pid=$(cat "$pid_file")
        log_info "停止 $name (PID: $pid)..."
        kill "$pid" 2>/dev/null || true
        
        # 等待进程停止
        local count=0
        while ps -p "$pid" > /dev/null 2>&1 && [ $count -lt 10 ]; do
            sleep 1
            ((count++))
        done
        
        # 如果还没停止，强制杀死
        if ps -p "$pid" > /dev/null 2>&1; then
            log_warn "强制停止 $name..."
            kill -9 "$pid" 2>/dev/null || true
        fi
        
        rm -f "$pid_file"
        log_success "$name 已停止"
    else
        log_warn "$name 未运行"
    fi
}

# 启动Worker
start_worker() {
    if is_running "$WORKER_PID"; then
        log_warn "Celery Worker 已在运行"
        return 0
    fi

    log_info "启动 Celery Worker..."
    
    # 检查虚拟环境
    if [[ ! -f "$CELERY_CMD" ]]; then
        log_error "未找到Celery命令: $CELERY_CMD"
        log_error "请确保虚拟环境已正确安装: $VENV_PATH"
        return 1
    fi
    
    # 启动Worker，启用事件发送以支持监控
    "$CELERY_CMD" -A "$CELERY_APP" worker --detach -E --pidfile="$WORKER_PID" --logfile="$WORKER_LOG"
    
    # 等待一下确保启动
    sleep 2
    
    if is_running "$WORKER_PID"; then
        local pid=$(cat "$WORKER_PID")
        log_success "Celery Worker 启动成功 (PID: $pid)"
        return 0
    else
        log_error "Celery Worker 启动失败"
        return 1
    fi
}

# 启动Beat
start_beat() {
    if is_running "$BEAT_PID"; then
        log_warn "Celery Beat 已在运行"
        return 0
    fi

    log_info "启动 Celery Beat..."
    
    # 检查虚拟环境
    if [[ ! -f "$CELERY_CMD" ]]; then
        log_error "未找到Celery命令: $CELERY_CMD"
        log_error "请确保虚拟环境已正确安装: $VENV_PATH"
        return 1
    fi
    
    # 启动Beat调度器
    "$CELERY_CMD" -A "$CELERY_APP" beat --detach --pidfile="$BEAT_PID" --logfile="$BEAT_LOG"
    
    # 等待一下确保启动
    sleep 2
    
    if is_running "$BEAT_PID"; then
        local pid=$(cat "$BEAT_PID")
        log_success "Celery Beat 启动成功 (PID: $pid)"
        return 0
    else
        log_error "Celery Beat 启动失败"
        return 1
    fi
}

# 检查状态
check_status() {
    echo ""
    log_info "=== Celery 服务状态 ==="
    
    # Worker状态
    if is_running "$WORKER_PID"; then
        local pid=$(cat "$WORKER_PID")
        echo -e "${GREEN}✓${NC} Worker: 运行中 (PID: $pid)"
    else
        echo -e "${RED}✗${NC} Worker: 未运行"
    fi
    
    # Beat状态
    if is_running "$BEAT_PID"; then
        local pid=$(cat "$BEAT_PID")
        echo -e "${GREEN}✓${NC} Beat: 运行中 (PID: $pid)"
    else
        echo -e "${RED}✗${NC} Beat: 未运行"
    fi
    
    echo ""
    log_info "📊 任务监控: Django后台 → 数据中心 → 🌸 任务监控"
    log_info "🔗 监控地址: http://127.0.0.1:8000/admin/monitoring/celery/"
    echo ""
    
    # 显示系统统计
    if [[ -f "$CELERY_CMD" ]]; then
        "$CELERY_CMD" -A "$CELERY_APP" status 2>/dev/null || echo "无法获取Celery状态信息"
    else
        echo "无法获取Celery状态信息（命令不存在）"
    fi
}

# 显示日志
show_logs() {
    echo ""
    log_info "=== 最近日志 ==="
    
    for log_file in "$WORKER_LOG" "$BEAT_LOG"; do
        if [[ -f "$log_file" ]]; then
            echo ""
            echo "=== $(basename "$log_file") ==="
            tail -n 10 "$log_file"
        fi
    done
}

# 启动所有服务
start_all() {
    log_info "启动 SweatMint Celery 系统..."
    
    start_worker
    start_beat
    
    echo ""
    log_success "🎉 SweatMint Celery 系统启动完成！"
    check_status
}

# 停止所有服务
stop_all() {
    log_info "停止 SweatMint Celery 系统..."
    
    # 停止进程
    stop_process "$WORKER_PID" "Celery Worker"
    stop_process "$BEAT_PID" "Celery Beat"
    
    # 清理残留进程
    pkill -f "celery.*$CELERY_APP" || true
    
    log_success "🛑 SweatMint Celery 系统已停止"
}

# 重启所有服务
restart_all() {
    log_info "重启 SweatMint Celery 系统..."
    stop_all
    sleep 2
    start_all
}

# 主要函数
main() {
    case "${1:-}" in
        start)
            start_all
            ;;
        stop)
            stop_all
            ;;
        restart)
            restart_all
            ;;
        status)
            check_status
            ;;
        logs)
            show_logs
            ;;
        worker)
            start_worker
            ;;
        beat)
            start_beat
            ;;
        *)
            echo "Usage: $0 {start|stop|restart|status|logs|worker|beat}"
            echo ""
            echo "Commands:"
            echo "  start         启动所有Celery服务"
            echo "  stop          停止所有Celery服务"
            echo "  restart       重启所有Celery服务"
            echo "  status        查看服务状态"
            echo "  logs          查看最近日志"
            echo "  worker        仅启动worker"
            echo "  beat          仅启动beat"
            echo ""
            echo "监控界面: Django后台 → 数据中心 → 🌸 任务监控"
            echo "监控地址: http://127.0.0.1:8000/admin/monitoring/celery/"
            exit 1
            ;;
    esac
}

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 执行主函数
main "$@" 