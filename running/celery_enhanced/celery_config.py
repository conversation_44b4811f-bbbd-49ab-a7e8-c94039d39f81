# -*- coding: utf-8 -*-
"""
SweatMint Celery 优化配置
基于最新Celery 5.3+ 最佳实践
"""

import os
from kombu import Queue, Exchange
from celery.schedules import crontab

# ========================= 基础配置 =========================

# 时区配置
timezone = 'Asia/Singapore'
enable_utc = True

# 任务序列化
task_serializer = 'json'
result_serializer = 'json'
accept_content = ['json']

# 结果后端配置
result_backend = 'redis://localhost:6379/1'
result_expires = 3600  # 1小时后过期

# ========================= Broker配置 =========================

# Redis配置 - 修复连接问题
broker_url = 'redis://localhost:6379/0'
broker_connection_retry_on_startup = True
broker_connection_retry = True
broker_connection_max_retries = 10

# 连接池配置 - 简化配置以避免socket选项问题
broker_pool_limit = 10
broker_heartbeat = 120
broker_transport_options = {
    'visibility_timeout': 3600,
    'fanout_prefix': True,
    'fanout_patterns': True,
    # 移除可能导致socket配置错误的选项
    # 'socket_keepalive': True,
    'health_check_interval': 30,
}

# ========================= 任务执行配置 =========================

# 任务确认配置 - 最新最佳实践
task_acks_late = True  # 任务完成后才确认
worker_prefetch_multiplier = 1  # 每个worker只预取1个任务

# 任务时间限制 - 大幅增加超时时间以确保大批量任务能够完成
task_soft_time_limit = 900  # 15分钟软限制 (从600秒增加到900秒)
task_time_limit = 1200  # 20分钟硬限制 (从720秒增加到1200秒)

# 任务重试配置
task_reject_on_worker_lost = True
task_ignore_result = False

# ========================= Worker配置 =========================

# Worker优化配置
worker_max_tasks_per_child = 500  # 降低到500个任务后重启，避免内存泄漏
worker_disable_rate_limits = False
worker_enable_remote_control = True

# 并发配置 - 针对重型任务优化
worker_concurrency = 1  # 降低到1个并发进程，避免资源竞争
worker_pool = 'prefork'  # 使用prefork池

# 内存管理 - 增加内存限制以支持大任务
worker_max_memory_per_child = 1000000  # 1GB内存限制 (从500MB增加到1GB)

# ========================= 队列配置 =========================

# 默认队列配置
task_default_queue = 'default'
task_default_exchange = 'default'
task_default_exchange_type = 'direct'
task_default_routing_key = 'default'

# 队列定义
task_queues = (
    Queue('default', Exchange('default'), routing_key='default'),
    Queue('high_priority', Exchange('high_priority'), routing_key='high_priority'),
    Queue('low_priority', Exchange('low_priority'), routing_key='low_priority'),
    # 瞬态队列用于不重要的任务
    Queue('transient', Exchange('transient', delivery_mode=1), 
          routing_key='transient', durable=False),
)

# 任务路由配置
task_routes = {
    # 高优先级任务
    'tasks.tasks.reset_daily_tasks': {'queue': 'high_priority'},
    'tasks.tasks.settle_extra_task_bonuses': {'queue': 'high_priority'},
    
    # 普通任务
    'tasks.tasks.update_active_member_status': {'queue': 'default'},
    
    # 低优先级任务
    'tasks.tasks.cache_monitoring_data': {'queue': 'low_priority'},
    
    # 瞬态任务（不重要，可以丢失）
    'core.celery.debug_task': {'queue': 'transient', 'delivery_mode': 'transient'},
}

# ========================= 监控配置 =========================

# 事件配置
worker_send_task_events = True
task_send_sent_event = True

# 监控配置
worker_enable_remote_control = True
worker_log_color = False  # 生产环境关闭颜色

# ========================= 安全配置 =========================

# 安全配置
worker_hijack_root_logger = False
worker_log_format = '[%(asctime)s: %(levelname)s/%(processName)s] %(message)s'
worker_task_log_format = '[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s'

# ========================= Beat配置 =========================

# Beat调度器配置
beat_scheduler = 'django_celery_beat.schedulers:DatabaseScheduler'
beat_schedule_filename = '/tmp/celerybeat-schedule'
beat_sync_every = 1  # 每秒同步一次数据库

# ========================= 自定义定时任务 =========================

# 注释掉beat_schedule，使用DatabaseScheduler管理所有定时任务
# 离线健康请求重放 - 每2分钟执行一次
# beat_schedule = {
#     "offline_health_requests_replay_every_2min": {
#         "task": "offline_health_requests_replay",
#         "schedule": crontab(minute="*/2"),
#         "options": {"queue": "default"}
#     },
# }

# ========================= 结果后端优化 =========================

# Redis结果后端优化 - 移除socket_keepalive以避免socket配置错误
result_backend_transport_options = {
    # 'socket_keepalive': True,  # 注释掉这行以避免socket配置错误
    'health_check_interval': 30,
}

# 结果缓存配置
result_cache_max = 10000  # 最多缓存10000个结果
result_persistent = True

# ========================= 错误处理配置 =========================

# 任务失败处理
task_annotations = {
    '*': {
        'rate_limit': '100/m',  # 全局限流：每分钟100个任务
        'time_limit': 600,      # 10分钟超时 (从360秒增加到600秒)
        'soft_time_limit': 480, # 8分钟软超时 (从300秒增加到480秒)
    },
    'tasks.tasks.reset_daily_tasks': {
        'rate_limit': '1/m',    # 每分钟最多1次
        'max_retries': 2,       # 减少重试次数为2次，避免重复超时
        'default_retry_delay': 300,  # 重试间隔5分钟 (增加到300秒)
        'time_limit': 1200,     # 20分钟硬超时
        'soft_time_limit': 900, # 15分钟软超时
        'bind': True,           # 绑定任务实例
    },
    'tasks.tasks.settle_extra_task_bonuses': {
        'rate_limit': '10/m',   # 每分钟最多10次
        'max_retries': 2,
        'default_retry_delay': 30,
        'time_limit': 300,      # 5分钟超时
        'soft_time_limit': 240, # 4分钟软超时
    },
}

# ========================= 日志配置 =========================

# 日志级别
worker_log_level = 'INFO'
beat_log_level = 'INFO'

# 日志格式
worker_log_format = '[%(asctime)s: %(levelname)s/%(processName)s] %(message)s'
worker_task_log_format = '[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s'

# ========================= 性能优化 =========================

# 性能优化配置
task_compression = 'gzip'  # 启用任务压缩
result_compression = 'gzip'  # 启用结果压缩

# 连接池优化
broker_pool_limit = 10
broker_connection_retry = True
broker_connection_max_retries = 10

# 预取优化
worker_prefetch_multiplier = 1  # 禁用预取以确保公平分发

# ========================= 开发/调试配置 =========================

# 开发环境配置（生产环境应设为False）
task_always_eager = False  # 不要在生产环境设为True
task_eager_propagates = False

# 调试配置
worker_redirect_stdouts = True
worker_redirect_stdouts_level = 'INFO'

# ========================= 生产环境特殊配置 =========================

if os.environ.get('DJANGO_SETTINGS_MODULE') == 'core.settings.production':
    # 生产环境配置
    worker_concurrency = 8
    worker_prefetch_multiplier = 2
    broker_pool_limit = 20
    
    # 更严格的超时
    task_soft_time_limit = 180
    task_time_limit = 240
    
    # 更频繁的健康检查
    broker_transport_options['health_check_interval'] = 15 