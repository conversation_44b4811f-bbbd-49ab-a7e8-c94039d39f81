import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.contrib.auth import get_user_model
from decimal import Decimal
from users.models import UnifiedUserSession, DailyHealthSnapshot
from tasks.models import UserTask, HealthDataVerificationLog
from tasks.services.health_data_verification import HealthDataVerificationService
from tasks.services.reward_calculation import RewardCalculationService
from core.utils.lock_utils import user_lock
from core.utils.offline_cache import enqueue_offline_request
from .baseline_manager import BaselineManager
import time
import pytz

logger = logging.getLogger(__name__)
User = get_user_model()


# 🔥 BOSS统一管理：HealthSessionService已删除，统一使用BaselineManager


class HealthSyncService:
    """健康数据同步服务"""
    
    @staticmethod
    def sync_health_data(user, new_totals, device_id, permissions=None, session_based=False, skip_permission_check=False):
        """
        同步健康数据增量
        
        Args:
            user: 用户对象
            new_totals: 最新健康数据总量
            device_id: 设备唯一标识
            permissions: 权限状态 {"steps": True, "distance": False, "calories": True}
            session_based: v14.1新增 - 基于会话的同步模式，用于2分钟定时同步优化
            skip_permission_check: v14.1新增 - 跳过前端权限检查，由API内部处理权限逻辑
            
        Returns:
            dict: 同步结果
        """
        logger.info(f"正在为用户 {user.email} 同步健康数据 (session_based={session_based}, skip_permission_check={skip_permission_check})")
        
        # 🔥 v14.1轻量化处理：基于会话的同步模式
        if session_based and skip_permission_check:
            logger.info(f"⚡ v14.1轻量化：用户 {user.email} 使用基于会话的同步模式，跳过权限双重验证")
            
            # 基于会话存在性获取权限状态
            try:
                session = UnifiedUserSession.objects.get(user=user, device_id=device_id, is_active=True)
                # 从现有会话中获取权限状态
                session_permissions = {
                    'steps': session.steps_baseline is not None,
                    'distance': session.distance_baseline is not None,
                    'calories': session.calories_baseline is not None,
                }
                permissions = session_permissions
                logger.info(f"✅ v14.1轻量化：从会话获取权限状态: {permissions}")
            except UnifiedUserSession.DoesNotExist:
                logger.warning(f"⚠️ v14.1轻量化：用户 {user.email} 没有活跃会话，使用降级处理")
                # 降级处理：如果没有会话，返回错误，让前端使用完整流程
                return {
                    'success': False,
                    'message': '会话不存在，请使用完整流程重新初始化',
                    'error_code': 'no_active_session',
                    'requires_full_flow': True,
                    'net_increment': {},
                    'updated_tasks': []
                }
        else:
            # 传统完整权限检查流程
            # 🔥 BOSS修复：处理权限独立性
            if permissions is None:
                permissions = {'steps': True, 'distance': True, 'calories': True}
            
            # 🔥 v14.1核心修复：增强权限预检机制 - 传递前端实际权限进行双重验证
            logger.info(f"🔍 v14.1预检：用户 {user.email} 开始权限双重验证")
            logger.info(f"  前端提交的权限状态: {permissions}")
            
            # 🔥 关键修复：将前端权限状态传递给HealthPermissionService进行双重验证
            backend_verified_permissions = HealthPermissionService.get_user_permissions(user, actual_permissions=permissions)
            
            # 🔥 验证前端提交权限与后端双重验证结果的一致性
            permission_mismatch = []
            for perm_type in ['steps', 'distance', 'calories']:
                frontend_perm = permissions.get(perm_type, False)
                backend_perm = backend_verified_permissions.get(perm_type, False)
                if frontend_perm != backend_perm:
                    permission_mismatch.append(f"{perm_type}: 前端{frontend_perm} vs 后端验证{backend_perm}")
            
            if permission_mismatch:
                logger.warning(f"🚫 用户 {user.email} 权限双重验证失败: {permission_mismatch}")
                logger.warning(f"  前端提交权限: {permissions}")
                logger.warning(f"  后端验证结果: {backend_verified_permissions}")
                return {
                    'success': False,
                    'message': f'权限双重验证失败，前后端权限状态不一致: {permission_mismatch}',
                    'error_code': 'permission_verification_failed',
                    'frontend_permissions': permissions,
                    'backend_verified_permissions': backend_verified_permissions,
                    'mismatches': permission_mismatch,
                    'net_increment': {},
                    'updated_tasks': []
                }
            
            logger.info(f"✅ v14.1预检：用户 {user.email} 权限双重验证通过: {permissions}")
        
        try:
            # 🔥 异常数据处理第1步：在增量计算前先验证原始数据完整性
            logger.info(f"开始验证用户 {user.email} 的原始健康数据")
            
            # 🔥 BOSS修复：构建正确的验证数据格式，将new_totals字段直接展开
            verification_data = {
                # 🔥 关键修复：直接展开new_totals字段，并确保正确的类型转换
                'steps': int(new_totals.get('steps', 0)),  # 确保是int类型
                'distance': float(new_totals.get('distance', 0.0)),  # 确保是float类型
                'calories': int(new_totals.get('calories', 0)),  # 确保是int类型
                'device_id': device_id,
                'permissions': permissions,
                'platform': 'app',  # 🔥 修复：使用支持的平台类型
                'date': timezone.now().strftime('%Y-%m-%d'),
                'source': 'app',    # 🔥 修复：使用支持的数据来源
                'device_info': {    # 🔥 修复：添加必要的设备信息
                    'device_model': device_id,
                    'platform': 'mobile_app',
                    'app_version': '1.0.0'
                }
            }
            
            # 🔥 BOSS调试：显示构建的验证数据
            logger.info(f"用户 {user.email} 构建的验证数据: {verification_data}")
            
            # 使用完整的健康数据验证服务
            verification_service = HealthDataVerificationService(user)
            verification_result = verification_service.verify_health_data(verification_data)
            
            # 🔥 关键：只有验证通过的数据才能进行增量计算
            if not verification_result.get('is_valid', False):
                logger.warning(f"用户 {user.email} 的健康数据验证失败: {verification_result.get('error_message', '未知错误')}")
                
                # 异常数据不参与计算，但加入离线队列用于监控
                enqueue_offline_request({
                    'type': 'sync_failed_validation',
                    'user_id': user.id,
                    'device_id': device_id,
                    'new_totals': new_totals,
                    'permissions': permissions,
                    'validation_error': verification_result,
                    'timestamp': int(time.time())
                })
                
                return {
                    'success': False,
                    'message': f'健康数据验证失败: {verification_result.get("error_message", "数据异常")}',
                    'error_code': verification_result.get('error_code', 'validation_failed'),
                    'verification_level': verification_result.get('verification_level', 'unknown'),
                    'net_increment': {},
                    'updated_tasks': []
                }
            
            logger.info(f"用户 {user.email} 健康数据验证通过，验证级别: {verification_result.get('verification_level', 'unknown')}")
            
            # 🔥 BOSS新增：权限变化检测
            from .baseline_manager import BaselineManager
            permission_check_result = BaselineManager.check_permission_changes_and_update_baseline(
                user, device_id, permissions, new_totals
            )
            logger.info(f"用户 {user.email} 权限变化检测: {permission_check_result.get('message', 'Unknown')}")
            
            # 并发锁，防止同步与重置并发执行
            with user_lock(user.id):
                # 获取用户会话
                session = UnifiedUserSession.objects.filter(
                    user=user,
                    device_id=device_id,
                    is_active=True
                ).first()
            
            if not session:
                logger.warning(f"用户 {user.email} 的健康数据会话不存在或已失效")
                enqueue_offline_request({
                    'type': 'sync',
                    'user_id': user.id,
                    'device_id': device_id,
                    'new_totals': new_totals,
                    'permissions': permissions,
                    'timestamp': int(time.time())
                })
                return {
                    'success': False,
                    'message': '健康数据会话不存在，请重新初始化 (已加入离线队列)',
                    'net_increment': {},
                    'updated_tasks': [],
                    'permission_changes': permission_check_result
                }
            
            # 🔥 异常数据处理第2步：验证通过后才计算净增量
            net_increment = HealthSyncService._calculate_net_increment(session, new_totals, permissions)
            logger.info(f"用户 {user.email} 计算净增量完成: {net_increment}")
            
            # 🔥 异常数据处理第3步：对计算出的增量进行二次验证
            if not HealthDataVerificationService.verify_increment_data(user, net_increment):
                logger.warning(f"用户 {user.email} 的健康数据增量验证失败")
                
                # 增量异常也记录但不参与任务更新
                enqueue_offline_request({
                    'type': 'sync_increment_invalid',
                    'user_id': user.id,
                    'device_id': device_id,
                    'new_totals': new_totals,
                    'net_increment': net_increment,
                    'permissions': permissions,
                    'timestamp': int(time.time())
                })
                
                return {
                    'success': False,
                    'message': '健康数据增量异常，请稍后重试 (已记录异常)',
                    'error_code': 'increment_validation_failed',
                    'net_increment': net_increment,
                    'updated_tasks': []
                }
            
            # 🔥 异常数据处理第4步：只有通过所有验证的数据才更新任务进度
            updated_tasks = HealthSyncService._update_task_progress(user, net_increment)
            
            # 记录数据同步日志
            HealthSyncService._log_sync_activity(user, net_increment, updated_tasks)
            
            return {
                'success': True,
                'message': '健康数据同步成功（含权限变化检测）',
                'verification_level': verification_result.get('verification_level', 'comprehensive'),
                'net_increment': net_increment,
                'updated_tasks': updated_tasks,
                'permission_changes': permission_check_result
            }
            
        except Exception as e:
            logger.error(f"健康数据同步失败，用户: {user.email}, 错误: {str(e)}", exc_info=True)
            enqueue_offline_request({
                'type': 'sync_error',
                'user_id': user.id,
                'device_id': device_id,
                'new_totals': new_totals,
                'permissions': permissions,
                'error': str(e),
                'timestamp': int(time.time())
            })
            return {
                'success': False,
                'message': '健康数据同步失败，请稍后重试 (已记录错误)',
                'error_code': 'sync_exception',
                'net_increment': {},
                'updated_tasks': []
            }
    
    @staticmethod
    def _calculate_net_increment(session, new_totals, permissions):
        """
        🔥 BOSS修复：计算净增量，严格遵循v13.0规范的权限独立性
        只对已授权权限计算增量，未授权权限增量为None（不参与计算）
        """
        increment_values = {}
        
        # 🔥 BOSS修复：步数增量计算 - 只有权限授权且基线存在时才计算
        if permissions.get('steps', False) and session.session_baseline_steps is not None:
            increment_steps = max(0, int(new_totals.get('steps', 0)) - session.session_baseline_steps)
            increment_values['steps'] = increment_steps
            logger.info(f"步数权限已授权，增量: {increment_steps}")
        else:
            increment_values['steps'] = None  # 🔥 未授权权限增量为None，不是0
            logger.info("步数权限未授权或基线缺失，增量跳过")
        
        # 🔥 BOSS修复：距离增量计算 - 只有权限授权且基线存在时才计算
        if permissions.get('distance', False) and session.session_baseline_distance is not None:
            increment_distance = max(0, float(new_totals.get('distance', 0)) - float(session.session_baseline_distance))
            increment_values['distance'] = round(increment_distance, 2)
            logger.info(f"距离权限已授权，增量: {increment_distance}")
        else:
            increment_values['distance'] = None  # 🔥 未授权权限增量为None，不是0
            logger.info("距离权限未授权或基线缺失，增量跳过")
        
        # 🔥 BOSS修复：卡路里增量计算 - 只有权限授权且基线存在时才计算
        if permissions.get('calories', False) and session.session_baseline_calories is not None:
            increment_calories = max(0, int(new_totals.get('calories', 0)) - session.session_baseline_calories)
            increment_values['calories'] = increment_calories
            logger.info(f"卡路里权限已授权，增量: {increment_calories}")
        else:
            increment_values['calories'] = None  # 🔥 未授权权限增量为None，不是0
            logger.info("卡路里权限未授权或基线缺失，增量跳过")
        
        logger.info(f"v13.0规范增量计算完成 - 步数: {increment_values['steps']}, 距离: {increment_values['distance']}, 卡路里: {increment_values['calories']}")
        
        return increment_values
    
    @staticmethod
    def _update_task_progress(user, net_increment):
        """更新任务进度"""
        updated_tasks = []
        
        try:
            # 获取用户今日活跃的健康相关任务
            today = timezone.now().date()
            health_tasks = UserTask.objects.filter(
                user=user,
                assignment_date=today,
                task__task_type__in=['daily', 'addon'],
                status='pending'
            ).select_related('task')
            
            for user_task in health_tasks:
                task = user_task.task
                old_progress = user_task.current_progress
                
                # 🔥 BOSS修复：根据任务类型更新进度，只有已授权权限才参与计算
                if 'step' in task.target_metric.lower():
                    if net_increment.get('steps') is not None:
                        user_task.current_progress += net_increment.get('steps', 0)
                elif 'distance' in task.target_metric.lower():
                    if net_increment.get('distance') is not None:
                        user_task.current_progress += net_increment.get('distance', 0)
                elif 'calorie' in task.target_metric.lower():
                    if net_increment.get('calories') is not None:
                        user_task.current_progress += net_increment.get('calories', 0)
                
                # 检查任务是否完成
                if user_task.current_progress >= task.target_value and user_task.status == 'pending':
                    user_task.status = 'completed'
                    user_task.completed_at = timezone.now()
                    
                    # 发放奖励
                    RewardCalculationService.award_task_completion(user, user_task)
                    
                    logger.info(f"任务完成: {task.name}, 用户: {user.email}")
                
                user_task.save()
                
                updated_tasks.append({
                    'task_id': task.id,
                    'task_name': task.name,
                    'old_progress': old_progress,
                    'new_progress': user_task.current_progress,
                    'is_completed': user_task.status == 'completed'
                })
        
        except Exception as e:
            logger.error(f"更新任务进度失败，用户: {user.email}, 错误: {str(e)}", exc_info=True)
        
        return updated_tasks
    
    @staticmethod
    def _log_sync_activity(user, net_increment, updated_tasks):
        """记录同步活动日志"""
        try:
            # 🔥 BOSS关键修复：创建UserSessionLog记录（这是遗漏的核心功能）
            # 获取当前活跃会话
            active_session = user.sessions.filter(is_active=True).first()
            if active_session:
                from tasks.models import UserSessionLog
                
                # 创建会话同步记录 - 这是显示健康数据的关键
                session_log = UserSessionLog.objects.create(
                    user=user,
                    session_id=active_session.id,
                    steps_increment=net_increment.get('steps') if net_increment.get('steps') is not None else 0,
                    distance_increment=net_increment.get('distance') if net_increment.get('distance') is not None else 0.0,
                    calories_increment=net_increment.get('calories') if net_increment.get('calories') is not None else 0,
                    sync_time=timezone.now()
                )
                logger.info(f"✅ UserSessionLog记录已创建: ID={session_log.id}, 会话={active_session.id}, 步数={session_log.steps_increment}")
            else:
                logger.warning(f"⚠️ 用户 {user.email} 没有活跃会话，跳过UserSessionLog创建")
            
            # 创建每日健康快照
            today = timezone.now().date()
            # 🔥 BOSS修复：只记录已授权权限的增量数据
            defaults = {
                'device_id': active_session.device_id if active_session else 'unknown',
                'snapshot_type': 'daily_reset'
            }
            
            # 只设置已授权权限的默认值
            if net_increment.get('steps') is not None:
                defaults['snapshot_steps'] = net_increment.get('steps', 0)
            if net_increment.get('distance') is not None:
                defaults['snapshot_distance'] = net_increment.get('distance', 0)
            if net_increment.get('calories') is not None:
                defaults['snapshot_calories'] = net_increment.get('calories', 0)
            
            snapshot, created = DailyHealthSnapshot.objects.get_or_create(
                user=user,
                snapshot_date=today,
                defaults=defaults
            )
            
            if not created:
                # 🔥 BOSS修复：累计今日数据，只累计已授权权限
                if net_increment.get('steps') is not None:
                    snapshot.snapshot_steps += net_increment.get('steps', 0)
                if net_increment.get('distance') is not None:
                    snapshot.snapshot_distance += net_increment.get('distance', 0)
                if net_increment.get('calories') is not None:
                    snapshot.snapshot_calories += net_increment.get('calories', 0)
                snapshot.save()
            
            logger.info(f"健康数据同步记录已保存，用户: {user.email}, 增量: {net_increment}")
            
        except Exception as e:
            logger.error(f"记录同步活动失败，用户: {user.email}, 错误: {str(e)}", exc_info=True)


# 🔥 BOSS统一管理：HealthBaselineService已删除，统一使用BaselineManager 


class HealthPermissionService:
    """健康数据权限服务"""
    
    @staticmethod
    def get_user_permissions(user, actual_permissions=None):
        """
        🔥 v14.2修复：获取用户当前的健康数据权限状态（符合v14.1真实权限检查）
        
        v14.1文档要求：
        - 使用HKStatisticsQuery独立检查三种权限
        - 实时检查权限状态，不使用缓存
        - error为nil && result有效 = 权限已授权
        
        双重验证逻辑：
        1. 实际权限状态（从健康平台获取，如果提供）
        2. 基线字段存在状态（从会话数据库获取）
        3. 只有两者都为True，权限才为True
        
        Args:
            user: 用户对象
            actual_permissions: 可选，实际的健康平台权限状态
            
        Returns:
            dict: 权限状态字典 {'steps': bool, 'distance': bool, 'calories': bool}
        """
        try:
            # 获取用户的活跃会话
            active_session = UnifiedUserSession.objects.filter(
                user=user,
                is_active=True
            ).first()
            
            if not active_session:
                # 如果没有活跃会话，返回所有权限为False
                logger.info(f"用户 {user.email} 没有活跃会话，返回所有权限为False")
                return {
                    'steps': False,
                    'distance': False,
                    'calories': False
                }
            
            # 🔥 v14.2核心修复：基线存在状态
            baseline_permissions = {
                'steps': active_session.session_baseline_steps is not None,
                'distance': active_session.session_baseline_distance is not None,
                'calories': active_session.session_baseline_calories is not None
            }
            
            # 🔥 v14.1严格双重验证：必须同时满足前端实际权限AND后端基线存在
            if actual_permissions is not None:
                # 双重验证：前端HKStatisticsQuery权限 AND 后端基线字段存在
                final_permissions = {
                    'steps': actual_permissions.get('steps', False) and baseline_permissions['steps'],
                    'distance': actual_permissions.get('distance', False) and baseline_permissions['distance'],
                    'calories': actual_permissions.get('calories', False) and baseline_permissions['calories']
                }
                
                logger.info(f"🔍 v14.1严格双重验证 - 用户 {user.email}:")
                logger.info(f"  前端HKStatisticsQuery权限: {actual_permissions}")
                logger.info(f"  后端基线字段存在状态: {baseline_permissions}")
                logger.info(f"  最终权限结果: {final_permissions}")
                return final_permissions
            else:
                # 🔥 CRITICAL FIX: 当前端未传递权限参数时，返回保守的FALSE状态
                # 基线存在≠权限已授权！基线可能来自默认值0或历史会话残留
                logger.error(f"❌ CRITICAL: 用户 {user.email} 未提供前端权限参数，无法准确判断权限状态")
                logger.error(f"  会话ID {active_session.id} 基线状态（不可信）:")
                logger.error(f"  步数基线: {active_session.session_baseline_steps} → 基线存在但不代表权限授权")
                logger.error(f"  距离基线: {active_session.session_baseline_distance} → 基线存在但不代表权限授权")
                logger.error(f"  卡路里基线: {active_session.session_baseline_calories} → 基线存在但不代表权限授权")
                
                # 🔥 CRITICAL FIX: 基线存在≠权限授权，返回保守FALSE避免显示错误数据
                conservative_permissions = {
                    'steps': False,
                    'distance': False,
                    'calories': False
                }
                logger.error(f"🔍 保守权限策略（基线存在≠权限授权）: {conservative_permissions}")
                logger.error(f"🚨 解决方案：前端必须通过checkRealPermissions()传递actual_permissions参数")
                return conservative_permissions
            
        except Exception as e:
            logger.error(f"获取用户权限状态失败，用户: {user.email}, 错误: {e}", exc_info=True)
            # 发生错误时返回所有权限为False
            return {
                'steps': False,
                'distance': False,
                'calories': False
            }


class HealthService:
    """健康数据服务"""
    
    @staticmethod
    def get_user_health_status(user, actual_permissions=None):
        """
        🔥 v14.2修复：获取用户当天运动增量（权限感知响应）
        
        v14.2规范核心原则：
        - 当天运动增量 = 会话1增量 + 会话2增量 + 会话3增量...
        - 前端Overview显示的应该是当天的总运动增量
        - 未授权权限返回None，让前端显示"--"
        - 已授权但无数据返回0
        
        Args:
            user: 用户对象
            actual_permissions: 前端实际权限状态（必须传递以避免错误判断）
            
        Returns:
            dict: 包含steps、distance、calories的当天运动增量（权限感知）
        """
        # 🔥 CRITICAL FIX：传递actual_permissions参数避免错误的fallback逻辑
        permissions = HealthPermissionService.get_user_permissions(user, actual_permissions=actual_permissions)
        logger.info(f"📊 用户 {user.email} 权限状态: {permissions}")
        
        # 使用新加坡时区获取今天的日期，确保时区一致性
        singapore_tz = pytz.timezone('Asia/Singapore')
        today = timezone.now().astimezone(singapore_tz).date()
        
        try:
            # 🔥 BOSS修复：获取用户当天的健康数据快照（包含当天总增量）
            snapshot = DailyHealthSnapshot.objects.filter(user=user, snapshot_date=today).first()
            
            sync_count = 0
            
            if snapshot:
                # 🔥 v14.2核心修复：基于权限状态返回数据（None=未授权，数值=已授权）
                result = {
                    "steps": (snapshot.snapshot_steps or 0) if permissions['steps'] else None,
                    "distance": float(snapshot.snapshot_distance or 0.0) if permissions['distance'] else None,
                    "calories": (snapshot.snapshot_calories or 0) if permissions['calories'] else None,
                    "sync_count": 1,
                }
                logger.info(f"📊 从快照获取权限感知的当天运动增量: 用户{user.email} - {result}")
                return result
            
            # 🔥 如果没有快照，尝试从当前会话和历史数据计算当天增量
            # 获取用户今天所有的会话增量总和
            today_sessions = UnifiedUserSession.objects.filter(
                user=user,
                session_start_time__date=today
            )
            
            total_increment = {'steps': 0, 'distance': 0.0, 'calories': 0}
            
            # 🔥 BOSS核心修复：获取当前设备健康数据总量
            latest_health_log = HealthDataVerificationLog.objects.filter(
                user=user,
                created_at__date=today
            ).order_by('-created_at').first()
            
            current_totals = {'steps': 0, 'distance': 0.0, 'calories': 0}
            
            if latest_health_log:
                verification_data = latest_health_log.verification_data
                
                # 🔥 BOSS关键修复：verification_data结构是{'submitted_data': health_data, 'result_details': {...}}
                # 需要从submitted_data中获取实际的健康数据
                submitted_data = verification_data.get('submitted_data', {})
                
                if submitted_data:
                    current_totals = {
                        'steps': submitted_data.get('steps', 0),
                        'distance': submitted_data.get('distance', 0.0),
                        'calories': submitted_data.get('calories', 0)
                    }
                    sync_count = 1
                    logger.info(f"📊 从验证日志的submitted_data获取当前总量: {current_totals}")
                    logger.info(f"🔍 原始verification_data结构: {verification_data}")
                else:
                    # 兼容旧格式，直接从verification_data获取
                    current_totals = {
                        'steps': verification_data.get('steps', 0),
                        'distance': verification_data.get('distance', 0.0),
                        'calories': verification_data.get('calories', 0)
                    }
                    sync_count = 1
                    logger.info(f"📊 兼容模式：从verification_data直接获取当前总量: {current_totals}")
            
            # 🔥 v14.2关键修复：基于权限状态返回当天运动增量（权限感知）
            if sync_count > 0:
                result = {
                    "steps": current_totals['steps'] if permissions['steps'] else None,
                    "distance": current_totals['distance'] if permissions['distance'] else None,
                    "calories": current_totals['calories'] if permissions['calories'] else None,
                    "sync_count": sync_count,
                }
                logger.info(f"🔥 v14.2规范响应（权限感知的当天运动增量）: 用户{user.email} - {result}")
                return result
            
            # 🔥 v14.2修复：没有数据时也要基于权限返回（None=未授权，0=已授权但无数据）
            result = {
                "steps": 0 if permissions['steps'] else None,
                "distance": 0.0 if permissions['distance'] else None,
                "calories": 0 if permissions['calories'] else None,
                "sync_count": 0,
            }
            
            logger.info(f"🔥 用户{user.email} 今天还没有健康数据，返回权限感知响应: {result}")
            return result
            
        except Exception as e:
            logger.error(f"获取用户健康数据状态失败，用户: {user.email}, 错误: {e}", exc_info=True)
            # 🔥 v14.2修复：异常时也要基于权限返回
            permissions = HealthPermissionService.get_user_permissions(user)
            return {
                "steps": 0 if permissions['steps'] else None,
                "distance": 0.0 if permissions['distance'] else None,
                "calories": 0 if permissions['calories'] else None,
                "sync_count": 0,
            } 