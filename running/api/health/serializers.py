from rest_framework import serializers
from drf_spectacular.utils import extend_schema_serializer, OpenApiExample


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            "会话初始化示例",
            description="用户登录时初始化健康数据会话",
            value={
                "totals": {
                    "steps": 5000,
                    "distance": 3.2,
                    "calories": 250
                },
                "device_id": "iOS_12345",
                "platform": "ios",
                "health_source": "apple_health",
                "permissions": {
                    "steps": True,
                    "distance": True,
                    "calories": True
                },
                "baseline_data": {
                    "steps": 1500,
                    "distance": 1.2,
                    "calories": 80
                }
            }
        ),
        OpenApiExample(
            "v14.0修复示例 - 无基线数据",
            description="当前端未提供基线数据时，后端将使用临时值0",
            value={
                "totals": {
                    "steps": 5000,
                    "distance": 3.2,
                    "calories": 250
                },
                "device_id": "iOS_12345",
                "platform": "ios",
                "health_source": "apple_health",
                "permissions": {
                    "steps": True,
                    "distance": True,
                    "calories": True
                }
            }
        )
    ]
)
class SessionInitSerializer(serializers.Serializer):
    """健康数据会话初始化序列化器"""
    totals = serializers.DictField(
        help_text="⚠️ 重要：设备上从当天0:00到会话开始时间的健康数据累计总量（这将作为会话基线）"
        # 🔥 BOSS修复：移除child=serializers.FloatField()限制，允许自定义验证逻辑
    )
    device_id = serializers.CharField(max_length=255, help_text="设备唯一标识")
    platform = serializers.ChoiceField(
        choices=[('ios', 'iOS'), ('android', 'Android')],
        help_text="设备平台"
    )
    health_source = serializers.CharField(
        max_length=50,
        default='unknown',
        help_text="健康数据来源"
    )
    permissions = serializers.DictField(
        required=False,
        help_text="健康数据权限状态 {'steps': True, 'distance': False, 'calories': True}",
        child=serializers.BooleanField()
    )
    baseline_data = serializers.DictField(
        required=False,
        help_text="🔥 v14.0新增：前端HealthKit查询的实际基线数据 {'steps': 1500, 'distance': 1.2, 'calories': 80}",
        child=serializers.FloatField(min_value=0)
    )
    session_start_time = serializers.DateTimeField(
        required=False,
        help_text="🔥 BOSS核心修复：会话开始时间（新加坡时间），用于确保前后端时间一致性"
    )

    def validate_totals(self, value):
        """验证健康数据总量格式"""
        # 🔥 BOSS修复：过滤掉date和source字段，只保留数字健康数据
        filtered_totals = {}
        
        # 只处理数字类型的健康数据字段
        valid_health_fields = ['steps', 'distance', 'calories', 'heart_rate', 'active_minutes']
        
        for field, val in value.items():
            if field in valid_health_fields and val is not None:
                if not isinstance(val, (int, float)) or val < 0:
                    raise serializers.ValidationError(f"{field} 必须是非负数字")
                filtered_totals[field] = val
        
        # 检查是否至少有一个有效的健康数据字段
        if not filtered_totals:
            raise serializers.ValidationError("至少需要提供一个有效的健康数据字段")
        
        return filtered_totals

    def validate_permissions(self, value):
        """验证权限状态格式"""
        if value is not None:
            expected_fields = ['steps', 'distance', 'calories']
            for field in expected_fields:
                if field in value and not isinstance(value[field], bool):
                    raise serializers.ValidationError(f"{field} 权限必须是布尔值")
        return value

    def validate_baseline_data(self, value):
        """🔥 v14.0新增：验证前端基线数据格式"""
        if value is not None:
            expected_fields = ['steps', 'distance', 'calories']
            for field, field_value in value.items():
                if field in expected_fields:
                    if not isinstance(field_value, (int, float)) or field_value < 0:
                        raise serializers.ValidationError(f"baseline_data.{field} 必须是非负数字")
                else:
                    raise serializers.ValidationError(f"baseline_data中不支持字段: {field}")
        return value


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            "数据同步示例",
            description="定期同步健康数据增量",
            value={
                "new_totals": {
                    "steps": 8000,
                    "distance": 5.8,
                    "calories": 400
                },
                "device_id": "iOS_12345"
            }
        )
    ]
)
class HealthSyncSerializer(serializers.Serializer):
    """健康数据同步序列化器"""
    new_totals = serializers.DictField(
        help_text="设备上最新的健康数据累计总量",
        child=serializers.FloatField()
    )
    device_id = serializers.CharField(max_length=255, help_text="设备唯一标识")
    permissions = serializers.DictField(
        required=False,
        help_text="健康数据权限状态 {'steps': True, 'distance': False, 'calories': True}",
        child=serializers.BooleanField()
    )
    
    # 🔥 v14.1新增：支持智能轻量化定时同步
    session_based = serializers.BooleanField(
        default=False,
        help_text="v14.1新增：基于会话的同步模式，用于2分钟定时同步优化"
    )
    skip_permission_check = serializers.BooleanField(
        default=False,
        help_text="v14.1新增：跳过前端权限检查，由API内部处理权限逻辑"
    )

    def validate_new_totals(self, value):
        """
        🔥 BOSS修复：验证新的健康数据总量格式
        根据v13.0规范，只验证已授权权限对应的字段
        """
        # 🔥 v13.0规范：未授权权限的字段可以缺失，不强制要求所有字段
        available_fields = ['steps', 'distance', 'calories']
        
        # 检查存在的字段格式是否正确
        for field, field_value in value.items():
            if field in available_fields:
                if not isinstance(field_value, (int, float)) or field_value < 0:
                    raise serializers.ValidationError(f"{field} 必须是非负数字")
        
        # 🔥 如果一个字段都没有，则报错
        if not any(field in value for field in available_fields):
            raise serializers.ValidationError("至少需要提供一个健康数据字段 (steps, distance, calories)")
            
        return value
    
    def validate_permissions(self, value):
        """验证权限状态格式"""
        if value is not None:
            expected_fields = ['steps', 'distance', 'calories']
            for field in expected_fields:
                if field in value and not isinstance(value[field], bool):
                    raise serializers.ValidationError(f"{field} 权限必须是布尔值")
        return value


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            "基线重置示例",
            description="跨天时重置健康数据基线",
            value={
                "new_totals": {
                    "steps": 1000,
                    "distance": 0.8,
                    "calories": 50
                },
                "device_id": "iOS_12345"
            }
        )
    ]
)
class BaselineResetSerializer(serializers.Serializer):
    """健康数据基线重置序列化器"""
    new_totals = serializers.DictField(
        help_text="跨天后设备上的新健康数据累计总量",
        child=serializers.FloatField()
    )
    device_id = serializers.CharField(max_length=255, help_text="设备唯一标识")
    permissions = serializers.DictField(
        required=False,
        help_text="健康数据权限状态 {'steps': True, 'distance': False, 'calories': True}",
        child=serializers.BooleanField()
    )

    def validate_new_totals(self, value):
        """
        🔥 BOSS修复：验证新的健康数据总量格式
        根据v13.0规范，只验证已授权权限对应的字段
        """
        # 🔥 v13.0规范：未授权权限的字段可以缺失，不强制要求所有字段
        available_fields = ['steps', 'distance', 'calories']
        
        # 检查存在的字段格式是否正确
        for field, field_value in value.items():
            if field in available_fields:
                if not isinstance(field_value, (int, float)) or field_value < 0:
                    raise serializers.ValidationError(f"{field} 必须是非负数字")
        
        # 🔥 如果一个字段都没有，则报错
        if not any(field in value for field in available_fields):
            raise serializers.ValidationError("至少需要提供一个健康数据字段 (steps, distance, calories)")
            
        return value
    
    def validate_permissions(self, value):
        """验证权限状态格式"""
        if value is not None:
            expected_fields = ['steps', 'distance', 'calories']
            for field in expected_fields:
                if field in value and not isinstance(value[field], bool):
                    raise serializers.ValidationError(f"{field} 权限必须是布尔值")
        return value


# 响应序列化器
class HealthDataResponseSerializer(serializers.Serializer):
    """健康数据响应序列化器"""
    net_increment = serializers.DictField(help_text="净增量数据")
    updated_tasks = serializers.ListField(help_text="更新的任务列表")
    message = serializers.CharField(help_text="响应消息")


class SessionInitResponseSerializer(serializers.Serializer):
    """会话初始化响应序列化器"""
    session_id = serializers.IntegerField(help_text="会话ID")
    baseline_date = serializers.DateTimeField(help_text="基线设置日期")
    message = serializers.CharField(help_text="响应消息")


class BaselineResetResponseSerializer(serializers.Serializer):
    """基线重置响应序列化器"""
    baseline_steps = serializers.IntegerField(help_text="新的基线步数")
    reset_date = serializers.DateTimeField(help_text="重置日期")
    message = serializers.CharField(help_text="响应消息")


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            "获取运动增量示例",
            description="获取用户当天运动增量",
            value={
                "current_totals": {
                    "steps": 8000,
                    "distance": 5.2,
                    "calories": 300
                },
                "device_id": "iOS_12345",
                "permissions": {
                    "steps": True,
                    "distance": False,
                    "calories": True
                }
            }
        )
    ]
)
class HealthStatusSerializer(serializers.Serializer):
    """健康数据状态查询序列化器"""
    current_totals = serializers.DictField(
        help_text="设备上当前的健康数据累计总量",
        child=serializers.FloatField()
    )
    device_id = serializers.CharField(max_length=255, help_text="设备唯一标识")
    permissions = serializers.DictField(
        required=False,
        help_text="健康数据权限状态 {'steps': True, 'distance': False, 'calories': True}",
        child=serializers.BooleanField()
    )

    def validate_current_totals(self, value):
        """验证当前健康数据总量格式"""
        required_fields = ['steps', 'distance', 'calories']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"缺少必需字段: {field}")
            if not isinstance(value[field], (int, float)) or value[field] < 0:
                raise serializers.ValidationError(f"{field} 必须是非负数字")
        return value

    def validate_permissions(self, value):
        """验证权限状态格式"""
        if value is not None:
            expected_fields = ['steps', 'distance', 'calories']
            for field in expected_fields:
                if field in value and not isinstance(value[field], bool):
                    raise serializers.ValidationError(f"{field} 权限必须是布尔值")
        return value


class HealthStatusResponseSerializer(serializers.Serializer):
    """健康数据状态响应序列化器"""
    daily_increment = serializers.DictField(help_text="当天运动增量")
    permissions = serializers.DictField(help_text="权限状态")
    has_session = serializers.BooleanField(help_text="是否有活跃会话")
    baseline_date = serializers.DateTimeField(required=False, help_text="基线设置日期")
    message = serializers.CharField(help_text="响应消息")


class DailyHealthStatusResponseSerializer(serializers.Serializer):
    """当天健康数据状态响应序列化器"""
    steps = serializers.IntegerField(help_text="今日累计步数增量")
    distance = serializers.FloatField(help_text="今日累计距离增量 (公里)")
    calories = serializers.IntegerField(help_text="今日累计卡路里增量")
    sync_count = serializers.IntegerField(help_text="今日同步次数")


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            "基线查询示例",
            description="查询指定时间范围的健康数据基线",
            value={
                "start_date": "2024-01-15T00:00:00+08:00",
                "end_date": "2024-01-15T08:30:00+08:00",
                "device_id": "iOS_12345",
                "permissions": {
                    "steps": True,
                    "distance": False,
                    "calories": True
                }
            }
        )
    ]
)
class BaselineQuerySerializer(serializers.Serializer):
    """基线查询序列化器 - v14.0规范"""
    start_date = serializers.DateTimeField(
        help_text="查询开始时间（新加坡时区当天0:00）"
    )
    end_date = serializers.DateTimeField(
        help_text="查询结束时间（会话开始时间或权限新增时间）"
    )
    device_id = serializers.CharField(
        max_length=255, 
        help_text="设备唯一标识"
    )
    permissions = serializers.DictField(
        help_text="健康数据权限状态 {'steps': True, 'distance': False, 'calories': True}",
        child=serializers.BooleanField()
    )
    
    def validate(self, data):
        """验证时间范围和权限状态"""
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        if start_date and end_date:
            if end_date <= start_date:
                raise serializers.ValidationError("结束时间必须晚于开始时间")
            
            # 检查时间范围是否合理（不超过24小时）
            time_diff = end_date - start_date
            if time_diff.total_seconds() > 24 * 3600:
                raise serializers.ValidationError("查询时间范围不能超过24小时")
        
        permissions = data.get('permissions', {})
        if not any(permissions.values()):
            raise serializers.ValidationError("至少需要一个授权的健康数据权限")
        
        return data


class BaselineQueryResponseSerializer(serializers.Serializer):
    """基线查询响应序列化器 - v14.0规范"""
    baseline_data = serializers.DictField(
        help_text="查询到的基线数据 {'steps': 1500, 'distance': null, 'calories': 120}"
    )
    query_time_range = serializers.DictField(
        help_text="实际查询的时间范围 {'start': '2024-01-15T00:00:00+08:00', 'end': '2024-01-15T08:30:00+08:00'}"
    )
    permissions_used = serializers.DictField(
        help_text="使用的权限状态 {'steps': True, 'distance': False, 'calories': True}"
    )
    message = serializers.CharField(help_text="查询结果说明")


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            "批量基线查询示例",
            description="为新增权限批量查询基线数据",
            value={
                "queries": [
                    {
                        "start_date": "2024-01-15T00:00:00+08:00",
                        "end_date": "2024-01-15T08:30:00+08:00",
                        "permission_type": "steps"
                    },
                    {
                        "start_date": "2024-01-15T00:00:00+08:00", 
                        "end_date": "2024-01-15T08:30:00+08:00",
                        "permission_type": "calories"
                    }
                ],
                "device_id": "iOS_12345"
            }
        )
    ]
)
class BatchBaselineQuerySerializer(serializers.Serializer):
    """批量基线查询序列化器 - v14.0规范"""
    queries = serializers.ListField(
        child=serializers.DictField(),
        help_text="批量查询请求列表"
    )
    device_id = serializers.CharField(
        max_length=255,
        help_text="设备唯一标识" 
    )
    
    def validate_queries(self, value):
        """验证批量查询请求格式"""
        if not value:
            raise serializers.ValidationError("查询列表不能为空")
        
        if len(value) > 10:
            raise serializers.ValidationError("单次批量查询不能超过10个请求")
        
        for i, query in enumerate(value):
            required_fields = ['start_date', 'end_date', 'permission_type']
            for field in required_fields:
                if field not in query:
                    raise serializers.ValidationError(f"查询 {i+1} 缺少必需字段: {field}")
            
            if query['permission_type'] not in ['steps', 'distance', 'calories']:
                raise serializers.ValidationError(f"查询 {i+1} 权限类型无效: {query['permission_type']}")
        
        return value


class BatchBaselineQueryResponseSerializer(serializers.Serializer):
    """批量基线查询响应序列化器 - v14.0规范"""
    results = serializers.ListField(
        child=serializers.DictField(),
        help_text="批量查询结果列表"
    )
    total_queries = serializers.IntegerField(help_text="总查询数")
    successful_queries = serializers.IntegerField(help_text="成功查询数")
    failed_queries = serializers.IntegerField(help_text="失败查询数")
    message = serializers.CharField(help_text="批量查询结果说明") 