"""
SweatMint 离线数据处理API接口
🔥 BOSS需求：提供离线数据恢复和完整性检查的API接口

接口列表：
1. POST /api/app/v1/health/offline/process-queue/ - 处理离线队列
2. GET /api/app/v1/health/offline/queue-status/ - 获取队列状态
3. POST /api/app/v1/health/offline/data-recovery/ - 数据恢复
4. POST /api/app/v1/health/offline/integrity-check/ - 完整性检查
5. POST /api/app/v1/health/session/force-new/ - 强制创建新会话
6. GET /api/app/v1/health/session/check-continuity/ - 检查会话连续性
"""
import logging
from datetime import datetime, timedelta
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils import timezone
from django.db import transaction
from django.contrib.auth import get_user_model
import pytz

from users.models import UnifiedUserSession, DailyHealthSnapshot
from api.health.offline_data_service import OfflineDataService, DataIntegrityChecker
from api.health.audit_logger import AuditLogger
from api.health.baseline_manager import BaselineManager

logger = logging.getLogger('sweatmint.api')
User = get_user_model()

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def process_offline_queue(request):
    """
    处理离线数据队列
    根据OFFLINE_PROCESSING_STANDARDS.md实现
    """
    try:
        user = request.user
        logger.info(f"🔄 处理离线队列: 用户 {user.email}")
        
        # 获取当前活跃会话
        current_session = UnifiedUserSession.objects.filter(
            user=user,
            is_active=True
        ).first()
        
        if not current_session:
            logger.warning(f"⚠️ 用户 {user.email} 无活跃会话，无法处理离线队列")
            return Response({
                'code': 400,
                'message': '无活跃会话',
                'data': None,
                'timestamp': int(timezone.now().timestamp())
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 模拟离线队列处理
        # TODO: 实现实际的离线数据处理逻辑
        processed_count = 0
        
        logger.info(f"✅ 用户 {user.email} 离线队列处理完成，处理了 {processed_count} 条记录")
        
        return Response({
            'code': 200,
            'message': '离线队列处理成功',
            'data': {
                'processed_count': processed_count,
                'session_id': current_session.id,
                'processing_time': timezone.now().isoformat()
            },
            'timestamp': int(timezone.now().timestamp())
        })
        
    except Exception as e:
        logger.error(f"❌ 处理离线队列失败: {str(e)}")
        return Response({
            'code': 500,
            'message': '离线队列处理失败',
            'details': str(e),
            'timestamp': int(timezone.now().timestamp())
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_offline_queue_status(request):
    """
    获取用户的离线队列状态
    """
    try:
        queue_status = OfflineDataService.get_offline_queue_status(request.user)
        
        return Response({
            'code': 200,
            'message': '队列状态获取成功',
            'data': queue_status,
            'timestamp': timezone.now().timestamp()
        })
        
    except Exception as e:
        logger.error(f"❌ 获取离线队列状态失败: {str(e)}", exc_info=e)
        
        return Response({
            'code': 500,
            'message': '获取队列状态失败',
            'details': str(e),
            'timestamp': timezone.now().timestamp()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def data_recovery(request):
    """
    数据恢复接口
    🔥 BOSS需求：处理跨天数据补齐和缺失数据恢复
    
    请求参数：
    - last_sync_time: 最后同步时间（ISO格式）
    - current_time: 当前时间（ISO格式，可选）
    - permissions: 健康权限状态（可选）
    """
    try:
        start_time = timezone.now()
        
        # 解析请求参数
        data = request.data
        last_sync_time_str = data.get('last_sync_time')
        current_time_str = data.get('current_time')
        permissions = data.get('permissions', {})
        
        if not last_sync_time_str:
            return Response({
                'code': 400,
                'message': '缺少必填参数：last_sync_time',
                'timestamp': timezone.now().timestamp()
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 解析时间
        try:
            last_sync_time = datetime.fromisoformat(last_sync_time_str.replace('Z', '+00:00'))
            current_time = timezone.now()
            if current_time_str:
                current_time = datetime.fromisoformat(current_time_str.replace('Z', '+00:00'))
        except ValueError as e:
            return Response({
                'code': 400,
                'message': f'时间格式错误: {str(e)}',
                'timestamp': timezone.now().timestamp()
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 执行数据恢复逻辑
        # 注意：这里需要调用Flutter端的HealthDataRecovery服务
        # 后端主要负责处理恢复后的数据同步和任务更新
        
        recovery_result = {
            'recovery_triggered': True,
            'last_sync_time': last_sync_time.isoformat(),
            'current_time': current_time.isoformat(),
            'time_gap_hours': (current_time - last_sync_time).total_seconds() / 3600,
            'permissions': permissions,
            'message': '数据恢复已触发，请在客户端执行具体恢复逻辑'
        }
        
        # 如果时间差超过24小时，可能需要跨天处理
        if (current_time - last_sync_time).total_seconds() > 86400:  # 24小时
            recovery_result['cross_day_needed'] = True
            recovery_result['cross_day_count'] = int((current_time - last_sync_time).days)
            
            # 添加到离线队列进行跨天处理
            OfflineDataService.add_offline_operation(
                user=request.user,
                operation_type='cross_day_processing',
                operation_data={
                    'cross_date': last_sync_time.date().isoformat(),
                    'health_data': data.get('health_data', {}),
                    'recovery_trigger': True
                },
                priority=10  # 高优先级
            )
        
        # 记录审计日志
        duration_ms = int((timezone.now() - start_time).total_seconds() * 1000)
        AuditLogger.log_data_recovery(
            user=request.user,
            operation_name='触发数据恢复',
            operation_success=True,
            recovery_data=recovery_result,
            gaps_found=[{
                'start': last_sync_time.isoformat(),
                'end': current_time.isoformat(),
                'duration_hours': recovery_result['time_gap_hours']
            }],
            duration_ms=duration_ms,
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT')
        )
        
        return Response({
            'code': 200,
            'message': '数据恢复处理完成',
            'data': recovery_result,
            'timestamp': timezone.now().timestamp()
        })
        
    except Exception as e:
        logger.error(f"❌ 数据恢复失败: {str(e)}", exc_info=e)
        
        # 记录失败的审计日志
        AuditLogger.log_data_recovery(
            user=request.user,
            operation_name='触发数据恢复',
            operation_success=False,
            error_message=str(e),
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT')
        )
        
        return Response({
            'code': 500,
            'message': '数据恢复失败',
            'details': str(e),
            'timestamp': timezone.now().timestamp()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def integrity_check(request):
    """
    数据完整性检查接口
    🔥 BOSS需求：确保数据一致性和完整性
    
    请求参数：
    - start_date: 检查开始日期（可选，默认7天前）
    - end_date: 检查结束日期（可选，默认今天）
    - auto_fix: 是否自动修复问题（可选，默认false）
    """
    try:
        start_time = timezone.now()
        
        # 解析请求参数
        data = request.data
        
        # 默认检查最近7天
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=7)
        
        if data.get('start_date'):
            try:
                start_date = datetime.fromisoformat(data['start_date']).date()
            except ValueError:
                return Response({
                    'code': 400,
                    'message': '开始日期格式错误',
                    'timestamp': timezone.now().timestamp()
                }, status=status.HTTP_400_BAD_REQUEST)
        
        if data.get('end_date'):
            try:
                end_date = datetime.fromisoformat(data['end_date']).date()
            except ValueError:
                return Response({
                    'code': 400,
                    'message': '结束日期格式错误',
                    'timestamp': timezone.now().timestamp()
                }, status=status.HTTP_400_BAD_REQUEST)
        
        auto_fix = data.get('auto_fix', False)
        
        # 执行完整性检查
        check_result = DataIntegrityChecker.check_session_integrity(
            user=request.user,
            start_date=start_date,
            end_date=end_date
        )
        
        # 如果需要自动修复且发现问题
        fix_result = None
        if auto_fix and not check_result['is_healthy']:
            try:
                fix_result = DataIntegrityChecker.fix_integrity_issues(
                    user=request.user,
                    issues=check_result['issues']
                )
                check_result['auto_fix_applied'] = True
                check_result['fix_result'] = fix_result
            except Exception as fix_error:
                logger.error(f"❌ 自动修复失败: {str(fix_error)}", exc_info=fix_error)
                check_result['auto_fix_failed'] = str(fix_error)
        
        # 记录审计日志
        duration_ms = int((timezone.now() - start_time).total_seconds() * 1000)
        AuditLogger.log_integrity_check(
            user=request.user,
            operation_name=f'数据完整性检查 ({start_date} 到 {end_date})',
            operation_success=True,
            check_result=check_result,
            issues_found=check_result['issues'],
            duration_ms=duration_ms,
            additional_data={
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'auto_fix': auto_fix,
                'fix_result': fix_result
            },
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT')
        )
        
        return Response({
            'code': 200,
            'message': '完整性检查完成',
            'data': {
                'check_period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'integrity_result': check_result
            },
            'timestamp': timezone.now().timestamp()
        })
        
    except Exception as e:
        logger.error(f"❌ 完整性检查失败: {str(e)}", exc_info=e)
        
        # 记录失败的审计日志
        AuditLogger.log_integrity_check(
            user=request.user,
            operation_name='数据完整性检查',
            operation_success=False,
            error_message=str(e),
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT')
        )
        
        return Response({
            'code': 500,
            'message': '完整性检查失败',
            'details': str(e),
            'timestamp': timezone.now().timestamp()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def force_create_new_session(request):
    """
    强制创建新会话
    🔥 BOSS需求：处理APP重启应该创建新会话的情况
    
    请求参数：
    - device_id: 设备ID
    - reason: 创建新会话的原因（可选）
    """
    try:
        start_time = timezone.now()
        
        data = request.data
        device_id = data.get('device_id')
        reason = data.get('reason', 'force_new_session')
        
        if not device_id:
            return Response({
                'code': 400,
                'message': '缺少必填参数：device_id',
                'timestamp': timezone.now().timestamp()
            }, status=status.HTTP_400_BAD_REQUEST)
        
        with transaction.atomic():
            # 1. 停用当前活跃会话
            active_sessions = UnifiedUserSession.objects.filter(
                user=request.user,
                is_active=True
            )
            
            session_before_state = {
                'active_sessions_count': active_sessions.count(),
                'active_session_ids': list(active_sessions.values_list('id', flat=True))
            }
            
            # 停用所有活跃会话
            active_sessions.update(
                is_active=False,
                logout_time=timezone.now(),
                logout_reason='force_new_session'
            )
            
            # 2. 创建新会话
            new_session = UnifiedUserSession.objects.create(
                user=request.user,
                device_id=device_id,
                session_date=timezone.now().date(),
                session_start_time=timezone.now(),
                is_active=True,
                # 基线将在第一次健康数据同步时设置
                session_baseline_steps=None,
                session_baseline_distance=None,
                session_baseline_calories=None
            )
            
            session_after_state = {
                'new_session_id': new_session.id,
                'new_session_date': new_session.session_date.isoformat(),
                'new_session_start_time': new_session.session_start_time.isoformat()
            }
        
        # 记录审计日志
        duration_ms = int((timezone.now() - start_time).total_seconds() * 1000)
        AuditLogger.log_session_operation(
            user=request.user,
            operation_name='强制创建新会话',
            operation_success=True,
            device_id=device_id,
            before_state=session_before_state,
            after_state=session_after_state,
            related_object=new_session,
            duration_ms=duration_ms,
            additional_data={'reason': reason},
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT')
        )
        
        logger.info(f"✅ 强制创建新会话成功: 用户 {request.user.id}, 会话 {new_session.id}")
        
        return Response({
            'code': 200,
            'message': '新会话创建成功',
            'data': {
                'session_id': new_session.id,
                'session_date': new_session.session_date.isoformat(),
                'session_start_time': new_session.session_start_time.isoformat(),
                'device_id': device_id,
                'previous_sessions_closed': session_before_state['active_sessions_count']
            },
            'timestamp': timezone.now().timestamp()
        })
        
    except Exception as e:
        logger.error(f"❌ 强制创建新会话失败: {str(e)}", exc_info=e)
        
        # 记录失败的审计日志
        AuditLogger.log_session_operation(
            user=request.user,
            operation_name='强制创建新会话',
            operation_success=False,
            device_id=data.get('device_id'),
            error_message=str(e),
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT')
        )
        
        return Response({
            'code': 500,
            'message': '创建新会话失败',
            'details': str(e),
            'timestamp': timezone.now().timestamp()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_session_continuity(request):
    """
    检查会话连续性
    🔥 BOSS修复：根据v13.2规范，增加应用重启检测
    根据OFFLINE_PROCESSING_STANDARDS.md第5.3节实现
    """
    try:
        user = request.user
        # 🔥 BOSS新增：获取应用重启参数
        is_app_restart = request.GET.get('is_app_restart', 'false').lower() == 'true'
        restart_reason = request.GET.get('restart_reason', '')
        timestamp = request.GET.get('timestamp', '')
        
        logger.info(f"🔍 检查会话连续性: 用户 {user.email}{'（应用重启）' if is_app_restart else ''}")
        
        # 🔥 BOSS核心修复：应用重启时直接创建新会话
        if is_app_restart:
            logger.info(f"🔄 检测到应用重启: {restart_reason}，强制创建新会话")
            
            # 结束当前活跃会话
            current_sessions = UnifiedUserSession.objects.filter(
                user=user,
                is_active=True
            )
            
            session_before_count = current_sessions.count()
            if session_before_count > 0:
                current_sessions.update(
                    is_active=False,
                    logout_time=timezone.now(),
                    logout_reason='app_restart'
                )
                logger.info(f"🔄 已结束 {session_before_count} 个活跃会话")
            
            return Response({
                'code': 200,
                'message': '应用重启检测：需要创建新会话',
                'data': {
                    'need_new_session': True,
                    'session_age_hours': 999.0,  # 表示应用重启
                    'last_activity_hours': 999.0,
                    'reason': f'应用重启检测: {restart_reason}',
                    'current_session_id': None,
                    'last_sync_time': None,
                    'is_app_restart': True,
                    'restart_reason': restart_reason,
                    'previous_sessions_closed': session_before_count,
                },
                'timestamp': timezone.now().timestamp()
            })
        
        # 🔥 BOSS修复：正常的会话连续性检查逻辑
        # 获取当前活跃会话
        current_session = UnifiedUserSession.objects.filter(
            user=user,
            is_active=True
        ).first()
        
        if not current_session:
            logger.info(f"✅ 用户 {user.email} 无活跃会话，需要创建新会话")
            return Response({
                'code': 200,
                'message': '无活跃会话',
                'data': {
                    'need_new_session': True,
                    'session_age_hours': 999.0,
                    'last_activity_hours': 999.0,
                    'reason': '用户无活跃会话，需要创建新会话',
                    'current_session_id': None,
                    'last_sync_time': None,
                    'is_app_restart': False,
                },
                'timestamp': timezone.now().timestamp()
            })
        
        # 计算会话年龄和最后活动时间
        now = timezone.now()
        session_age = now - current_session.login_time
        last_activity = now - (current_session.last_sync_time or current_session.login_time)
        
        session_age_hours = session_age.total_seconds() / 3600
        last_activity_hours = last_activity.total_seconds() / 3600
        
        # 🔥 BOSS修复：遵循v13.2规范的会话连续性判断
        # 1. 超过4小时无活动 → 创建新会话
        # 2. 会话超过24小时 → 强制创建新会话  
        # 3. 跨天检查 → 如果不是今天的基线，创建新会话
        
        need_new_session = False
        reason = ""
        
        # 检查1：4小时活动阈值
        if last_activity_hours >= 4.0:
            need_new_session = True
            reason = f'会话超时：超过4小时无活动 ({last_activity_hours:.1f}小时)'
        
        # 检查2：24小时会话年龄阈值
        elif session_age_hours >= 24.0:
            need_new_session = True
            reason = f'会话过期：会话已存在超过24小时 ({session_age_hours:.1f}小时)'
        
        # 检查3：跨天基线检查
        else:
            singapore_tz = pytz.timezone('Asia/Singapore')
            today_start = timezone.now().astimezone(singapore_tz).replace(hour=0, minute=0, second=0, microsecond=0)
            
            if current_session.baseline_date:
                baseline_date_sg = current_session.baseline_date.astimezone(singapore_tz).date()
                today_sg = today_start.date()
                
                if baseline_date_sg != today_sg:
                    need_new_session = True
                    reason = f'跨天检测：基线日期({baseline_date_sg}) != 今天({today_sg})'
            else:
                need_new_session = True
                reason = '基线日期为空，需要创建新会话'
        
        # 🔥 BOSS修复：如果需要新会话，结束当前会话
        if need_new_session:
            logger.warning(f"⚠️ 用户 {user.email} 会话需要重置: {reason}")
            # 标记旧会话为非活跃
            current_session.is_active = False
            current_session.logout_time = now
            current_session.logout_reason = 'session_continuity_check'
            current_session.save()
        else:
            logger.info(f"✅ 用户 {user.email} 会话正常，最后活动: {last_activity_hours:.1f}小时前")
            reason = f'会话活跃：{last_activity_hours:.1f}小时前有活动'
        
        return Response({
            'code': 200,
            'message': '会话连续性检查完成',
            'data': {
                'need_new_session': need_new_session,
                'session_age_hours': round(session_age_hours, 2),
                'last_activity_hours': round(last_activity_hours, 2),
                'reason': reason,
                'current_session_id': current_session.id if not need_new_session else None,
                'last_sync_time': current_session.last_sync_time.isoformat() if current_session.last_sync_time else None,
                'is_app_restart': False,
            },
            'timestamp': timezone.now().timestamp()
        })
        
    except Exception as e:
        logger.error(f"❌ 检查会话连续性失败: {str(e)}", exc_info=e)
        
        return Response({
            'code': 500,
            'message': '检查会话连续性失败',
            'data': {
                'need_new_session': True,  # 错误时安全策略：创建新会话
                'session_age_hours': 999.0,
                'last_activity_hours': 999.0,
                'reason': f'检查失败，创建新会话: {str(e)}',
                'current_session_id': None,
                'last_sync_time': None,
                'error': True,
                'is_app_restart': False,
            },
            'timestamp': timezone.now().timestamp()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def session_start(request):
    """
    🔥 BOSS修复：会话开始标记端点 - 符合v13.2规范
    
    v13.2规范要求：
    - 此API仅标记现有会话的开始时间
    - 不创建新会话！session/init才是唯一创建会话的地方
    - 会话生命周期：会话开始=用户重启app并登录状态，或用户重新登录
    """
    try:
        user = request.user
        data = request.data
        reason = data.get('reason', 'session_start')
        metadata = data.get('metadata', {})
        
        logger.info(f"🚀 会话开始标记: 用户 {user.email}, 原因: {reason}")
        
        # 🔥 BOSS核心修复：只查找现有活跃会话，不创建新会话
        singapore_tz = pytz.timezone('Asia/Singapore')
        now = timezone.now()
        now_singapore = now.astimezone(singapore_tz)
        baseline_date_singapore = now_singapore.replace(hour=0, minute=0, second=0, microsecond=0)
        
        # 查找现有活跃会话 - 应该由session/init创建
        existing_session = UnifiedUserSession.objects.filter(
            user=user,
            is_active=True
        ).first()
        
        if existing_session:
            # 🔥 BOSS修复：仅更新现有会话的开始时间，符合v13.2规范
            if existing_session.session_start_time is None:
                existing_session.session_start_time = now
                logger.info(f"✅ 更新现有会话的开始时间: {now}")
            
            if existing_session.baseline_date is None:
                existing_session.baseline_date = baseline_date_singapore.astimezone(timezone.utc)
                logger.info(f"✅ 更新现有会话的基线日期: {baseline_date_singapore}")
            
            existing_session.last_sync_time = now
            existing_session.save()
            
            session_id = existing_session.id
            logger.info(f"✅ 会话开始标记成功: 会话ID={session_id}")
            
        else:
            # 🔥 BOSS核心修复：如果没有活跃会话，应该先调用session/init创建会话
            logger.warning(f"⚠️ 用户 {user.email} 没有活跃会话，无法标记会话开始")
            logger.warning(f"📋 请先调用 session/init API 创建会话（v13.2规范）")
            
            return Response({
                'code': 400,
                'message': '没有活跃会话，请先调用 session/init 创建会话',
                'details': '根据v13.2规范，session/start 仅标记现有会话的开始时间',
                'timestamp': timezone.now().timestamp()
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 记录审计日志
        AuditLogger.log_session_operation(
            user=user,
            operation_name='会话开始标记',
            operation_success=True,
            additional_data={
                'session_id': session_id,
                'reason': reason,
                'metadata': metadata,
                'session_start_time': now.isoformat(),
                'baseline_date': baseline_date_singapore.isoformat(),
                'v13_2_compliant': True  # 标记符合v13.2规范
            },
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT')
        )
        
        return Response({
            'code': 200,
            'message': '会话开始标记成功',
            'data': {
                'session_id': session_id,
                'session_start_time': now.isoformat(),
                'baseline_date': baseline_date_singapore.isoformat(),
                'reason': reason,
                'v13_2_compliant': True
            },
            'timestamp': timezone.now().timestamp()
        })
        
    except Exception as e:
        logger.error(f"❌ 会话开始标记失败: {str(e)}", exc_info=e)
        
        # 记录失败的审计日志
        AuditLogger.log_session_operation(
            user=request.user,
            operation_name='会话开始标记',
            operation_success=False,
            error_message=str(e),
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT')
        )
        
        return Response({
            'code': 500,
            'message': '会话开始标记失败',
            'details': str(e),
            'timestamp': timezone.now().timestamp()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def session_end(request):
    """
    🔥 BOSS修复：会话结束标记端点
    记录会话结束时间和最终健康数据
    """
    try:
        user = request.user
        data = request.data
        reason = data.get('reason', 'session_end')
        health_data = data.get('health_data', {})
        metadata = data.get('metadata', {})
        
        logger.info(f"🔚 会话结束标记: 用户 {user.email}, 原因: {reason}")
        
        # 获取当前活跃会话
        active_session = UnifiedUserSession.objects.filter(
            user=user,
            is_active=True
        ).first()
        
        if not active_session:
            logger.warning(f"⚠️ 用户 {user.email} 没有活跃会话可结束")
            return Response({
                'code': 404,
                'message': '没有活跃会话可结束',
                'timestamp': timezone.now().timestamp()
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 标记会话结束
        now = timezone.now()
        active_session.is_active = False
        active_session.logout_time = now
        active_session.logout_reason = reason
        active_session.save()
        
        # 创建会话结束快照
        if health_data:
            try:
                snapshot = DailyHealthSnapshot.objects.create(
                    user=user,
                    device_id=active_session.device_id,
                    snapshot_date=now.date(),
                    snapshot_steps=health_data.get('steps'),
                    snapshot_distance=health_data.get('distance'),
                    snapshot_calories=health_data.get('calories'),
                    snapshot_type='logout',
                )
                logger.info(f"✅ 创建会话结束快照: ID={snapshot.id}")
            except Exception as e:
                logger.warning(f"⚠️ 创建会话结束快照失败: {str(e)}")
        
        # 记录审计日志
        AuditLogger.log_session_operation(
            user=user,
            operation_name='会话结束标记',
            operation_success=True,
            additional_data={
                'session_id': active_session.id,
                'reason': reason,
                'health_data': health_data,
                'metadata': metadata,
                'session_end_time': now.isoformat(),
                'session_duration_hours': (now - active_session.session_start_time).total_seconds() / 3600 if active_session.session_start_time else None
            },
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT')
        )
        
        return Response({
            'code': 200,
            'message': '会话结束标记成功',
            'data': {
                'session_id': active_session.id,
                'session_end_time': now.isoformat(),
                'reason': reason,
                'session_duration_hours': (now - active_session.session_start_time).total_seconds() / 3600 if active_session.session_start_time else None
            },
            'timestamp': timezone.now().timestamp()
        })
        
    except Exception as e:
        logger.error(f"❌ 会话结束标记失败: {str(e)}", exc_info=e)
        
        # 记录失败的审计日志
        AuditLogger.log_session_operation(
            user=request.user,
            operation_name='会话结束标记',
            operation_success=False,
            error_message=str(e),
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT')
        )
        
        return Response({
            'code': 500,
            'message': '会话结束标记失败',
            'details': str(e),
            'timestamp': timezone.now().timestamp()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 