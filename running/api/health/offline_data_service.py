"""
SweatMint 离线数据处理服务
🔥 BOSS需求：系统性处理离线状态和数据补齐机制

职责：
1. 离线数据队列管理
2. 数据完整性检查
3. 跨天数据处理
4. 数据恢复和同步
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from django.db import transaction
from django.utils import timezone
from django.contrib.auth.models import User
from users.models import UnifiedUserSession, DailyHealthSnapshot
from api.health.models import OfflineOperation
from api.health.services import HealthSyncService
from api.health.baseline_manager import BaselineManager

logger = logging.getLogger('sweatmint.offline')

class OfflineDataService:
    """离线数据处理服务"""
    
    @staticmethod
    def process_offline_queue(user_id: int) -> Dict:
        """
        处理用户的离线数据队列
        🔥 BOSS需求：处理APP恢复后的离线操作队列
        """
        logger.info(f"🔄 开始处理用户 {user_id} 的离线数据队列...")
        
        try:
            user = User.objects.get(id=user_id)
            
            # 获取待处理的离线操作
            pending_operations = OfflineOperation.objects.filter(
                user=user,
                status='pending',
                created_at__gte=timezone.now() - timedelta(days=7)  # 只处理7天内的操作
            ).order_by('created_at')
            
            results = {
                'total_operations': pending_operations.count(),
                'successful_operations': 0,
                'failed_operations': 0,
                'operations_details': []
            }
            
            logger.info(f"📊 发现 {results['total_operations']} 个待处理的离线操作")
            
            # 处理每个离线操作
            for operation in pending_operations:
                try:
                    with transaction.atomic():
                        result = OfflineDataService._process_single_operation(operation)
                        
                        operation.status = 'completed'
                        operation.result = result
                        operation.completed_at = timezone.now()
                        operation.save()
                        
                        results['successful_operations'] += 1
                        results['operations_details'].append({
                            'operation_id': operation.id,
                            'type': operation.operation_type,
                            'status': 'success',
                            'result': result
                        })
                        
                        logger.debug(f"✅ 离线操作处理成功: {operation.operation_type}")
                        
                except Exception as e:
                    operation.status = 'failed'
                    operation.error_message = str(e)
                    operation.retry_count += 1
                    operation.save()
                    
                    results['failed_operations'] += 1
                    results['operations_details'].append({
                        'operation_id': operation.id,
                        'type': operation.operation_type,
                        'status': 'failed',
                        'error': str(e)
                    })
                    
                    logger.error(f"❌ 离线操作处理失败: {operation.operation_type}", exc_info=e)
            
            logger.info(f"✅ 离线数据队列处理完成 - 成功: {results['successful_operations']}, 失败: {results['failed_operations']}")
            return results
            
        except Exception as e:
            logger.error(f"❌ 处理离线数据队列失败: {str(e)}", exc_info=e)
            raise
    
    @staticmethod
    def _process_single_operation(operation: OfflineOperation) -> Dict:
        """处理单个离线操作"""
        operation_data = operation.operation_data
        
        if operation.operation_type == 'health_data_sync':
            return OfflineDataService._process_health_data_sync(operation.user, operation_data)
        elif operation.operation_type == 'task_completion':
            return OfflineDataService._process_task_completion(operation.user, operation_data)
        elif operation.operation_type == 'baseline_reset':
            return OfflineDataService._process_baseline_reset(operation.user, operation_data)
        elif operation.operation_type == 'cross_day_processing':
            return OfflineDataService._process_cross_day_data(operation.user, operation_data)
        else:
            raise ValueError(f"未知的离线操作类型: {operation.operation_type}")
    
    @staticmethod
    def _process_health_data_sync(user: User, data: Dict) -> Dict:
        """处理健康数据同步操作"""
        logger.info(f"🔄 处理健康数据同步: 用户 {user.id}")
        
        health_data = data.get('health_data', {})
        sync_time = data.get('sync_time')
        device_id = data.get('device_id', 'offline_device')
        
        if sync_time:
            sync_time = datetime.fromisoformat(sync_time)
        else:
            sync_time = timezone.now()
        
        # 调用健康数据同步服务
        result = HealthSyncService.sync_health_data(
            user=user,
            new_totals=health_data,
            device_id=device_id
        )
        
        return {
            'type': 'health_data_sync',
            'sync_time': sync_time.isoformat(),
            'steps': health_data.get('steps', 0),
            'distance': health_data.get('distance', 0),
            'calories': health_data.get('calories', 0),
            'result': result
        }
    
    @staticmethod
    def _process_task_completion(user: User, data: Dict) -> Dict:
        """处理任务完成操作"""
        logger.info(f"🔄 处理任务完成: 用户 {user.id}")
        
        task_id = data.get('task_id')
        completion_data = data.get('completion_data', {})
        
        # TODO: 实现任务完成逻辑
        # task_service.complete_task(user, task_id, completion_data)
        
        return {
            'type': 'task_completion',
            'task_id': task_id,
            'completion_data': completion_data
        }
    
    @staticmethod
    def _process_baseline_reset(user: User, data: Dict) -> Dict:
        """处理基线重置操作"""
        logger.info(f"🔄 处理基线重置: 用户 {user.id}")
        
        reset_date = data.get('reset_date')
        if reset_date:
            reset_date = datetime.fromisoformat(reset_date).date()
        else:
            reset_date = timezone.now().date()
        
        baseline_manager = BaselineManager(user.id)
        result = baseline_manager.reset_daily_baseline(reset_date)
        
        return {
            'type': 'baseline_reset',
            'reset_date': reset_date.isoformat(),
            'result': result
        }
    
    @staticmethod
    def _process_cross_day_data(user: User, data: Dict) -> Dict:
        """
        处理跨天数据
        🔥 BOSS核心需求：23:00登录→次日1:00唤醒的数据补齐
        """
        logger.info(f"🌅 处理跨天数据: 用户 {user.id}")
        
        try:
            cross_date = data.get('cross_date')
            health_data = data.get('health_data', {})
            
            if cross_date:
                cross_date = datetime.fromisoformat(cross_date).date()
            else:
                cross_date = timezone.now().date() - timedelta(days=1)
            
            # 1. 归档前一天的数据
            previous_day_result = OfflineDataService._archive_previous_day_data(
                user, cross_date, health_data
            )
            
            # 2. 重置新一天的基线
            next_day = cross_date + timedelta(days=1)
            baseline_reset_result = OfflineDataService._reset_new_day_baseline(
                user, next_day
            )
            
            # 3. 更新任务状态
            task_update_result = OfflineDataService._update_cross_day_tasks(
                user, cross_date, previous_day_result.get('final_increment', {})
            )
            
            return {
                'type': 'cross_day_processing',
                'cross_date': cross_date.isoformat(),
                'previous_day_archive': previous_day_result,
                'baseline_reset': baseline_reset_result,
                'task_update': task_update_result
            }
            
        except Exception as e:
            logger.error(f"❌ 跨天数据处理失败: {str(e)}", exc_info=e)
            raise
    
    @staticmethod
    def _archive_previous_day_data(user: User, date, health_data: Dict) -> Dict:
        """归档前一天数据"""
        logger.info(f"📦 归档前一天数据: {date}")
        
        try:
            # 获取或创建前一天的健康快照
            snapshot, created = DailyHealthSnapshot.objects.get_or_create(
                user=user,
                date=date,
                defaults={
                    'steps': health_data.get('steps', 0),
                    'distance': health_data.get('distance', 0.0),
                    'calories': health_data.get('calories', 0),
                    'session_baseline_steps': 0,
                    'session_baseline_distance': 0.0,
                    'session_baseline_calories': 0,
                    'is_final': True,
                    'finalized_at': timezone.now()
                }
            )
            
            if not created and not snapshot.is_final:
                # 更新最终数据
                snapshot.steps = health_data.get('steps', snapshot.steps)
                snapshot.distance = health_data.get('distance', snapshot.distance)
                snapshot.calories = health_data.get('calories', snapshot.calories)
                snapshot.is_final = True
                snapshot.finalized_at = timezone.now()
                snapshot.save()
            
            # 计算最终增量
            final_increment = {
                'steps': max(0, snapshot.steps - snapshot.session_baseline_steps),
                'distance': max(0.0, snapshot.distance - snapshot.session_baseline_distance),
                'calories': max(0, snapshot.calories - snapshot.session_baseline_calories)
            }
            
            logger.info(f"✅ 前一天数据归档完成 - 最终增量: {final_increment}")
            
            return {
                'snapshot_id': snapshot.id,
                'final_data': {
                    'steps': snapshot.steps,
                    'distance': snapshot.distance,
                    'calories': snapshot.calories
                },
                'final_increment': final_increment,
                'is_newly_created': created
            }
            
        except Exception as e:
            logger.error(f"❌ 前一天数据归档失败: {str(e)}", exc_info=e)
            raise
    
    @staticmethod
    def _reset_new_day_baseline(user: User, date) -> Dict:
        """重置新一天基线"""
        logger.info(f"🌅 重置新一天基线: {date}")
        
        try:
            baseline_manager = BaselineManager(user.id)
            
            # 创建新会话
            session = UnifiedUserSession.objects.create(
                user=user,
                device_id=f"offline_recovery_{timezone.now().timestamp()}",
                session_date=date,
                session_start_time=timezone.now(),
                is_active=True,
                # 基线将在第一次数据同步时设置
                session_baseline_steps=None,
                session_baseline_distance=None,
                session_baseline_calories=None
            )
            
            logger.info(f"✅ 新一天基线重置完成 - 会话ID: {session.id}")
            
            return {
                'session_id': session.id,
                'session_date': date.isoformat(),
                'baseline_reset': True
            }
            
        except Exception as e:
            logger.error(f"❌ 新一天基线重置失败: {str(e)}", exc_info=e)
            raise
    
    @staticmethod
    def _update_cross_day_tasks(user: User, date, final_increment: Dict) -> Dict:
        """更新跨天任务状态"""
        logger.info(f"📋 更新跨天任务状态: {date}")
        
        try:
            # TODO: 实现任务状态更新逻辑
            # task_service.update_daily_task_progress(user, date, final_increment)
            
            return {
                'date': date.isoformat(),
                'updated_increment': final_increment,
                'task_updates': 'TODO: 实现任务更新逻辑'
            }
            
        except Exception as e:
            logger.error(f"❌ 跨天任务状态更新失败: {str(e)}", exc_info=e)
            raise
    
    @staticmethod
    def add_offline_operation(
        user: User, 
        operation_type: str, 
        operation_data: Dict,
        priority: int = 0
    ) -> OfflineOperation:
        """添加离线操作到队列"""
        logger.info(f"📝 添加离线操作: {operation_type} - 用户 {user.id}")
        
        try:
            operation = OfflineOperation.objects.create(
                user=user,
                operation_type=operation_type,
                operation_data=operation_data,
                priority=priority,
                status='pending',
                retry_count=0
            )
            
            logger.debug(f"✅ 离线操作已添加到队列: {operation.id}")
            return operation
            
        except Exception as e:
            logger.error(f"❌ 添加离线操作失败: {str(e)}", exc_info=e)
            raise
    
    @staticmethod
    def get_offline_queue_status(user: User) -> Dict:
        """获取离线队列状态"""
        try:
            pending_count = OfflineOperation.objects.filter(
                user=user,
                status='pending'
            ).count()
            
            failed_count = OfflineOperation.objects.filter(
                user=user,
                status='failed',
                retry_count__lt=3  # 最多重试3次
            ).count()
            
            recent_operations = OfflineOperation.objects.filter(
                user=user,
                created_at__gte=timezone.now() - timedelta(hours=24)
            ).order_by('-created_at')[:10]
            
            return {
                'pending_operations': pending_count,
                'failed_operations': failed_count,
                'recent_operations': [
                    {
                        'id': op.id,
                        'type': op.operation_type,
                        'status': op.status,
                        'created_at': op.created_at.isoformat(),
                        'retry_count': op.retry_count
                    }
                    for op in recent_operations
                ]
            }
            
        except Exception as e:
            logger.error(f"❌ 获取离线队列状态失败: {str(e)}", exc_info=e)
            raise


class DataIntegrityChecker:
    """数据完整性检查器"""
    
    @staticmethod
    def check_session_integrity(user: User, start_date, end_date) -> Dict:
        """
        检查会话数据完整性
        🔥 BOSS需求：确保数据一致性和完整性
        """
        logger.info(f"🔍 检查会话数据完整性: {user.id} - {start_date} 到 {end_date}")
        
        try:
            sessions = UnifiedUserSession.objects.filter(
                user=user,
                session_date__range=[start_date, end_date]
            ).order_by('session_date')
            
            integrity_issues = []
            
            for session in sessions:
                # 检查基线合理性
                if session.session_baseline_steps is not None and session.session_baseline_steps < 0:
                    integrity_issues.append({
                        'type': 'negative_baseline',
                        'session_id': session.id,
                        'field': 'steps',
                        'value': session.session_baseline_steps,
                        'severity': 'high'
                    })
                
                if session.session_baseline_distance is not None and session.session_baseline_distance < 0:
                    integrity_issues.append({
                        'type': 'negative_baseline',
                        'session_id': session.id,
                        'field': 'distance',
                        'value': session.session_baseline_distance,
                        'severity': 'high'
                    })
                
                if session.session_baseline_calories is not None and session.session_baseline_calories < 0:
                    integrity_issues.append({
                        'type': 'negative_baseline',
                        'session_id': session.id,
                        'field': 'calories',
                        'value': session.session_baseline_calories,
                        'severity': 'high'
                    })
                
                # 检查数据连续性
                if DataIntegrityChecker._has_data_gap(session):
                    integrity_issues.append({
                        'type': 'data_gap',
                        'session_id': session.id,
                        'session_date': session.session_date.isoformat(),
                        'severity': 'medium'
                    })
                
                # 检查跨天处理
                if DataIntegrityChecker._needs_cross_day_processing(session):
                    integrity_issues.append({
                        'type': 'missing_cross_day_processing',
                        'session_id': session.id,
                        'session_date': session.session_date.isoformat(),
                        'severity': 'high'
                    })
            
            # 检查重复会话
            duplicate_sessions = DataIntegrityChecker._find_duplicate_sessions(user, start_date, end_date)
            for dup in duplicate_sessions:
                integrity_issues.append({
                    'type': 'duplicate_session',
                    'session_date': dup['date'].isoformat(),
                    'session_ids': dup['session_ids'],
                    'severity': 'high'
                })
            
            result = {
                'total_sessions': sessions.count(),
                'total_issues': len(integrity_issues),
                'issues': integrity_issues,
                'is_healthy': len(integrity_issues) == 0
            }
            
            logger.info(f"✅ 数据完整性检查完成 - 发现 {result['total_issues']} 个问题")
            return result
            
        except Exception as e:
            logger.error(f"❌ 数据完整性检查失败: {str(e)}", exc_info=e)
            raise
    
    @staticmethod
    def _has_data_gap(session: UnifiedUserSession) -> bool:
        """检查是否有数据缺口"""
        # 简化实现：检查基线是否为None（表示可能有数据缺口）
        return (
            session.session_baseline_steps is None or
            session.session_baseline_distance is None or
            session.session_baseline_calories is None
        )
    
    @staticmethod
    def _needs_cross_day_processing(session: UnifiedUserSession) -> bool:
        """检查是否需要跨天处理"""
        # 检查会话日期与开始时间是否一致
        if session.session_start_time and session.session_date:
            session_day = session.session_start_time.date()
            recorded_day = session.session_date
            return session_day != recorded_day
        return False
    
    @staticmethod
    def _find_duplicate_sessions(user: User, start_date, end_date) -> List[Dict]:
        """查找重复会话"""
        from django.db.models import Count
        
        duplicates = UnifiedUserSession.objects.filter(
            user=user,
            session_date__range=[start_date, end_date]
        ).values('session_date').annotate(
            count=Count('id')
        ).filter(count__gt=1)
        
        duplicate_info = []
        for dup in duplicates:
            session_ids = list(
                UnifiedUserSession.objects.filter(
                    user=user,
                    session_date=dup['session_date']
                ).values_list('id', flat=True)
            )
            
            duplicate_info.append({
                'date': dup['session_date'],
                'count': dup['count'],
                'session_ids': session_ids
            })
        
        return duplicate_info
    
    @staticmethod
    def fix_integrity_issues(user: User, issues: List[Dict]) -> Dict:
        """修复数据完整性问题"""
        logger.info(f"🔧 开始修复数据完整性问题: {user.id} - {len(issues)} 个问题")
        
        fixed_count = 0
        failed_count = 0
        fix_results = []
        
        try:
            with transaction.atomic():
                for issue in issues:
                    try:
                        if issue['type'] == 'negative_baseline':
                            # 修复负基线
                            session = UnifiedUserSession.objects.get(id=issue['session_id'])
                            field = issue['field']
                            if field == 'steps':
                                session.session_baseline_steps = 0
                            elif field == 'distance':
                                session.session_baseline_distance = 0.0
                            elif field == 'calories':
                                session.session_baseline_calories = 0
                            session.save()
                            
                            fix_results.append({
                                'issue_type': issue['type'],
                                'session_id': issue['session_id'],
                                'status': 'fixed',
                                'action': f'重置{field}基线为0'
                            })
                            fixed_count += 1
                            
                        elif issue['type'] == 'duplicate_session':
                            # 修复重复会话 - 保留最新的，删除其他的
                            session_ids = issue['session_ids']
                            sessions = UnifiedUserSession.objects.filter(
                                id__in=session_ids
                            ).order_by('-session_start_time')
                            
                            # 保留第一个（最新的），删除其余的
                            keep_session = sessions.first()
                            delete_sessions = sessions[1:]
                            
                            deleted_ids = list(delete_sessions.values_list('id', flat=True))
                            delete_sessions.delete()
                            
                            fix_results.append({
                                'issue_type': issue['type'],
                                'session_date': issue['session_date'],
                                'status': 'fixed',
                                'action': f'保留会话{keep_session.id}，删除{deleted_ids}'
                            })
                            fixed_count += 1
                            
                        else:
                            # 其他问题类型暂时跳过
                            fix_results.append({
                                'issue_type': issue['type'],
                                'status': 'skipped',
                                'reason': '暂不支持自动修复此类问题'
                            })
                            
                    except Exception as e:
                        logger.error(f"❌ 修复问题失败: {issue}", exc_info=e)
                        fix_results.append({
                            'issue_type': issue.get('type', 'unknown'),
                            'status': 'failed',
                            'error': str(e)
                        })
                        failed_count += 1
            
            result = {
                'total_issues': len(issues),
                'fixed_count': fixed_count,
                'failed_count': failed_count,
                'fix_results': fix_results
            }
            
            logger.info(f"✅ 数据完整性问题修复完成 - 成功: {fixed_count}, 失败: {failed_count}")
            return result
            
        except Exception as e:
            logger.error(f"❌ 数据完整性问题修复失败: {str(e)}", exc_info=e)
            raise 