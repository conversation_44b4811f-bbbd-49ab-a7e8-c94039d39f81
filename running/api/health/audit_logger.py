"""
SweatMint 健康数据审计日志系统
🔥 BOSS需求：完整的审计日志记录和数据一致性保障

职责：
1. 记录所有健康数据操作
2. 提供操作溯源能力
3. 支持数据完整性验证
4. 异常行为检测和告警
"""
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from django.db import models, transaction
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey

# 导入模型
from .models import HealthDataAuditLog

logger = logging.getLogger('sweatmint.audit')

User = get_user_model()

class AuditLogger:
    """
    审计日志记录器
    🔥 BOSS需求：统一的审计日志记录接口
    """
    
    @staticmethod
    def log_session_operation(
        user: User,
        operation_name: str,
        operation_success: bool,
        device_id: str = None,
        before_state: Dict = None,
        after_state: Dict = None,
        health_data: Dict = None,
        error_message: str = None,
        session_id: str = None,
        request_id: str = None,
        ip_address: str = None,
        user_agent: str = None,
        related_object: Any = None,
        duration_ms: int = None,
        additional_data: Dict = None
    ) -> HealthDataAuditLog:
        """
        记录会话相关操作
        """
        return AuditLogger._create_audit_log(
            user=user,
            operation_type='session_init',
            operation_name=operation_name,
            operation_success=operation_success,
            device_id=device_id,
            before_state=before_state,
            after_state=after_state,
            health_data=health_data,
            error_message=error_message,
            session_id=session_id,
            request_id=request_id,
            ip_address=ip_address,
            user_agent=user_agent,
            related_object=related_object,
            duration_ms=duration_ms,
            additional_data=additional_data
        )
    
    @staticmethod
    def log_cross_day_operation(
        user: User,
        operation_name: str,
        operation_success: bool,
        cross_date: str,
        before_state: Dict = None,
        after_state: Dict = None,
        health_data: Dict = None,
        error_message: str = None,
        **kwargs
    ) -> HealthDataAuditLog:
        """
        记录跨天处理操作
        🔥 BOSS核心需求：23:00登录→次日1:00唤醒的跨天处理审计
        """
        additional_data = kwargs.get('additional_data', {})
        additional_data['cross_date'] = cross_date
        
        return AuditLogger._create_audit_log(
            user=user,
            operation_type='cross_day',
            operation_name=operation_name,
            operation_success=operation_success,
            before_state=before_state,
            after_state=after_state,
            health_data=health_data,
            error_message=error_message,
            additional_data=additional_data,
            **{k: v for k, v in kwargs.items() if k != 'additional_data'}
        )
    
    @staticmethod
    def log_sync_operation(
        user: User,
        operation_name: str,
        operation_success: bool,
        health_data: Dict = None,
        sync_result: Dict = None,
        **kwargs
    ) -> HealthDataAuditLog:
        """
        记录数据同步操作
        """
        return AuditLogger._create_audit_log(
            user=user,
            operation_type='sync',
            operation_name=operation_name,
            operation_success=operation_success,
            health_data=health_data,
            after_state=sync_result,
            **kwargs
        )
    
    @staticmethod
    def log_baseline_operation(
        user: User,
        operation_name: str,
        operation_success: bool,
        baseline_before: Dict = None,
        baseline_after: Dict = None,
        **kwargs
    ) -> HealthDataAuditLog:
        """
        记录基线操作
        """
        return AuditLogger._create_audit_log(
            user=user,
            operation_type='baseline_reset',
            operation_name=operation_name,
            operation_success=operation_success,
            before_state=baseline_before,
            after_state=baseline_after,
            **kwargs
        )
    
    @staticmethod
    def log_permission_change(
        user: User,
        operation_name: str,
        operation_success: bool,
        permissions_before: Dict = None,
        permissions_after: Dict = None,
        **kwargs
    ) -> HealthDataAuditLog:
        """
        记录权限变更操作
        """
        return AuditLogger._create_audit_log(
            user=user,
            operation_type='permission_change',
            operation_name=operation_name,
            operation_success=operation_success,
            before_state=permissions_before,
            after_state=permissions_after,
            **kwargs
        )
    
    @staticmethod
    def log_data_recovery(
        user: User,
        operation_name: str,
        operation_success: bool,
        recovery_data: Dict = None,
        gaps_found: List = None,
        **kwargs
    ) -> HealthDataAuditLog:
        """
        记录数据恢复操作
        """
        additional_data = kwargs.get('additional_data', {})
        additional_data.update({
            'recovery_data': recovery_data,
            'gaps_found': gaps_found
        })
        
        return AuditLogger._create_audit_log(
            user=user,
            operation_type='data_recovery',
            operation_name=operation_name,
            operation_success=operation_success,
            additional_data=additional_data,
            **{k: v for k, v in kwargs.items() if k != 'additional_data'}
        )
    
    @staticmethod
    def log_integrity_check(
        user: User,
        operation_name: str,
        operation_success: bool,
        check_result: Dict = None,
        issues_found: List = None,
        **kwargs
    ) -> HealthDataAuditLog:
        """
        记录完整性检查操作
        """
        additional_data = kwargs.get('additional_data', {})
        additional_data.update({
            'check_result': check_result,
            'issues_found': issues_found
        })
        
        return AuditLogger._create_audit_log(
            user=user,
            operation_type='integrity_check',
            operation_name=operation_name,
            operation_success=operation_success,
            additional_data=additional_data,
            **{k: v for k, v in kwargs.items() if k != 'additional_data'}
        )
    
    @staticmethod
    def log_offline_queue_operation(
        user: User,
        operation_name: str,
        operation_success: bool,
        queue_result: Dict = None,
        **kwargs
    ) -> HealthDataAuditLog:
        """
        记录离线队列处理操作
        """
        return AuditLogger._create_audit_log(
            user=user,
            operation_type='offline_queue',
            operation_name=operation_name,
            operation_success=operation_success,
            after_state=queue_result,
            **kwargs
        )
    
    @staticmethod
    def log_session_continuity_check(
        user: User,
        device_id: str,
        need_new_session: bool,
        session_data: Dict,
        reason: str = None,
        duration_ms: int = None,
        ip_address: str = None,
        user_agent: str = None
    ) -> HealthDataAuditLog:
        """
        记录会话连续性检查操作
        🔥 BOSS需求：审计会话连续性检查的完整过程
        """
        try:
            audit_data = {
                'device_id': device_id,
                'need_new_session': need_new_session,
                'reason': reason,
                'session_data': session_data,
                'duration_ms': duration_ms,
                'ip_address': ip_address,
                'user_agent': user_agent
            }
            
            return AuditLogger._create_audit_log(
                user=user,
                operation_type='session_continuity_check',
                operation_name='会话连续性检查',
                operation_success=True,
                device_id=device_id,
                after_state=audit_data,
                duration_ms=duration_ms,
                ip_address=ip_address,
                user_agent=user_agent,
                additional_data=audit_data
            )
            
        except Exception as e:
            logger.error(f"记录会话连续性检查审计日志失败: {str(e)}")
            return None
    
    @staticmethod
    def _create_audit_log(
        user: User,
        operation_type: str,
        operation_name: str,
        operation_success: bool,
        device_id: str = None,
        before_state: Dict = None,
        after_state: Dict = None,
        health_data: Dict = None,
        error_message: str = None,
        session_id: str = None,
        request_id: str = None,
        ip_address: str = None,
        user_agent: str = None,
        related_object: Any = None,
        duration_ms: int = None,
        additional_data: Dict = None,
        severity: str = None
    ) -> HealthDataAuditLog:
        """
        创建审计日志记录
        """
        try:
            # 自动确定严重程度
            if severity is None:
                if not operation_success:
                    severity = 'error'
                elif error_message:
                    severity = 'warning'
                else:
                    severity = 'info'
            
            # 处理关联对象
            content_type = None
            object_id = None
            if related_object:
                content_type = ContentType.objects.get_for_model(related_object)
                object_id = related_object.pk
            
            # 创建审计日志
            audit_log = HealthDataAuditLog.objects.create(
                user=user,
                device_id=device_id,
                operation_type=operation_type,
                operation_name=operation_name,
                before_state=before_state,
                after_state=after_state,
                health_data=health_data,
                operation_success=operation_success,
                error_message=error_message,
                severity=severity,
                ip_address=ip_address,
                user_agent=user_agent,
                request_id=request_id,
                session_id=session_id,
                content_type=content_type,
                object_id=object_id,
                duration_ms=duration_ms,
                additional_data=additional_data or {}
            )
            
            # 记录到应用日志
            log_level = logging.ERROR if not operation_success else logging.INFO
            logger.log(
                log_level,
                f"📋 审计日志: {user.username} - {operation_name} - "
                f"{'成功' if operation_success else '失败'}"
            )
            
            return audit_log
            
        except Exception as e:
            logger.error(f"❌ 创建审计日志失败: {str(e)}", exc_info=e)
            # 审计日志失败不应该影响主业务流程
            # 但需要记录到应用日志中
            logger.critical(
                f"🚨 审计日志记录失败 - 用户: {user.username}, "
                f"操作: {operation_name}, 错误: {str(e)}"
            )
            raise
    
    @staticmethod
    def query_user_operations(
        user: User,
        operation_type: str = None,
        start_date: datetime = None,
        end_date: datetime = None,
        limit: int = 100
    ) -> List[HealthDataAuditLog]:
        """
        查询用户操作历史
        """
        query = HealthDataAuditLog.objects.filter(user=user)
        
        if operation_type:
            query = query.filter(operation_type=operation_type)
        
        if start_date:
            query = query.filter(timestamp__gte=start_date)
        
        if end_date:
            query = query.filter(timestamp__lte=end_date)
        
        return query.order_by('-timestamp')[:limit]
    
    @staticmethod
    def detect_anomalies(
        user: User,
        hours: int = 24
    ) -> Dict:
        """
        检测异常操作模式
        """
        since = timezone.now() - timezone.timedelta(hours=hours)
        
        operations = HealthDataAuditLog.objects.filter(
            user=user,
            timestamp__gte=since
        )
        
        # 统计分析
        total_operations = operations.count()
        failed_operations = operations.filter(operation_success=False).count()
        error_rate = (failed_operations / total_operations * 100) if total_operations > 0 else 0
        
        # 设备分析
        device_counts = {}
        for op in operations:
            if op.device_id:
                device_counts[op.device_id] = device_counts.get(op.device_id, 0) + 1
        
        # 操作类型分析
        operation_type_counts = {}
        for op in operations:
            operation_type_counts[op.operation_type] = operation_type_counts.get(op.operation_type, 0) + 1
        
        # 异常检测
        anomalies = []
        
        # 高错误率
        if error_rate > 20:
            anomalies.append({
                'type': 'high_error_rate',
                'severity': 'high',
                'description': f'错误率过高: {error_rate:.1f}%',
                'value': error_rate
            })
        
        # 多设备同时操作
        if len(device_counts) > 2:
            anomalies.append({
                'type': 'multiple_devices',
                'severity': 'medium',
                'description': f'检测到{len(device_counts)}个不同设备',
                'value': list(device_counts.keys())
            })
        
        # 频繁操作
        if total_operations > 100:
            anomalies.append({
                'type': 'high_frequency',
                'severity': 'medium',
                'description': f'操作频率过高: {total_operations}次/{hours}小时',
                'value': total_operations
            })
        
        return {
            'period_hours': hours,
            'total_operations': total_operations,
            'failed_operations': failed_operations,
            'error_rate': error_rate,
            'device_counts': device_counts,
            'operation_type_counts': operation_type_counts,
            'anomalies': anomalies,
            'risk_level': 'high' if len([a for a in anomalies if a['severity'] == 'high']) > 0 else 
                        'medium' if len(anomalies) > 0 else 'low'
        } 