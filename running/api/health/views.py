import logging
import time
from rest_framework.viewsets import ViewSet
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.openapi import OpenApiTypes
import uuid
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from django.utils import timezone

from .serializers import (
    SessionInitSerializer, HealthSyncSerializer, BaselineResetSerializer,
    SessionInitResponseSerializer, HealthDataResponseSerializer, BaselineResetResponseSerializer,
    HealthStatusSerializer, HealthStatusResponseSerializer, DailyHealthStatusResponseSerializer,
    BaselineQuerySerializer, BaselineQueryResponseSerializer, 
    BatchBaselineQuerySerializer, BatchBaselineQueryResponseSerializer
)
from .services import HealthSyncService
from .baseline_manager import BaselineManager
from users.models import UnifiedUserSession

logger = logging.getLogger(__name__)


class HealthViewSet(ViewSet):
    """
    健康数据统一API视图集
    
    提供会话初始化、数据同步和基线重置功能
    遵循《健康数据对接最终方案.md》规范
    """
    permission_classes = [IsAuthenticated]

    @extend_schema(
        operation_id="health_session_init",
        summary="初始化健康数据会话",
        description="""
        用户登录或应用启动时调用，初始化健康数据会话基线。
        
        **核心原则：**
        - 会话内增量：基线设定后，所有奖励计算基于此会话的增量
        - 后端中心化：所有增量计算在后端完成
        - 状态驱动：基于权限状态执行操作
        """,
        request=SessionInitSerializer,
        responses={
            200: SessionInitResponseSerializer,
            400: "请求参数错误",
            401: "未授权访问",
            500: "服务器内部错误"
        },
        tags=["健康数据"]
    )
    @action(detail=False, methods=['post'], url_path='session/init')
    def session_init(self, request):
        """健康数据会话初始化"""
        start_time = time.time()
        user = request.user
        request_id = getattr(request, 'request_id', '-')
        
        logger.info(f"[{request_id}] 用户 {user.email} 请求初始化健康数据会话")
        
        try:
            # 验证请求数据
            serializer = SessionInitSerializer(data=request.data)
            if not serializer.is_valid():
                logger.warning(f"[{request_id}] 会话初始化参数验证失败: {serializer.errors}")
                return Response({
                    "code": 400,
                    "message": "请求参数错误",
                    "details": serializer.errors,
                    "timestamp": int(time.time())
                }, status=400)
            
            validated_data = serializer.validated_data
            
            # 🔥 BOSS核心修复：按照正确的方法签名调用BaselineManager
            # BaselineManager.initialize_user_baseline(user, device_id, permissions_status, force_create_new_session=False, reason="正常会话创建")
            
            # 🔥 修复1：从permissions字段构建permissions_status字典，提供默认值
            permissions_data = validated_data.get('permissions', {})
            permissions_status = {
                'steps': permissions_data.get('steps', False),
                'distance': permissions_data.get('distance', False), 
                'calories': permissions_data.get('calories', False)
            }
            
            logger.info(f"[{request_id}] 基线初始化参数: device_id={validated_data['device_id']}, permissions={permissions_status}")
            
            # 🔥 BOSS核心修复：验证基线数据和当前总量的一致性（v2.0规范）
            frontend_baseline_data = validated_data.get('baseline_data', {})
            totals = validated_data.get('totals', {})

            if frontend_baseline_data and totals:
                # 验证基线数据和当前总量的逻辑一致性
                for data_type in ['steps', 'distance', 'calories']:
                    if permissions_status.get(data_type, False):
                        baseline_value = frontend_baseline_data.get(data_type, 0)
                        total_value = totals.get(data_type, 0)
                        if baseline_value > total_value:
                            logger.warning(f"[{request_id}] ⚠️ {data_type}基线值({baseline_value})大于总量({total_value})，数据可能异常")
                        else:
                            logger.info(f"[{request_id}] ✅ {data_type}数据验证通过: 基线={baseline_value}, 总量={total_value}, 增量={total_value - baseline_value}")

                logger.info(f"[{request_id}] 🔥 BOSS修复：前端提供基线数据: {frontend_baseline_data}")
                # 设置前端基线数据到当前线程，供BaselineManager使用
                BaselineManager.set_frontend_baseline_data(frontend_baseline_data)
            else:
                logger.warning(f"[{request_id}] ⚠️ 前端未提供完整的基线数据或总量数据，将使用默认值0")
            
            # 🔥 BOSS核心修复：传递会话开始时间，确保前后端时间一致性
            session_start_time = validated_data.get('session_start_time')
            if session_start_time:
                logger.info(f"[{request_id}] 🕐 前端传递的会话开始时间: {session_start_time}")
            else:
                logger.info(f"[{request_id}] ⚠️ 前端未传递会话开始时间，将使用当前时间")

            # 🔥 修复2：使用正确的参数顺序和名称调用，增加错误处理
            try:
                result = BaselineManager.initialize_user_baseline(
                    user=user,
                    device_id=validated_data['device_id'],
                    permissions_status=permissions_status,
                    force_create_new_session=False,
                    reason="会话初始化API调用",
                    session_start_time=session_start_time,  # 🔥 传递会话开始时间
                    baseline_data=frontend_baseline_data  # 🔥 修复：传递baseline_data参数
                )
                logger.info(f"[{request_id}] BaselineManager调用成功: {result}")
                
            except Exception as baseline_error:
                logger.error(f"[{request_id}] BaselineManager调用失败: {str(baseline_error)}", exc_info=True)
                # 重新抛出异常，让外层异常处理器处理
                raise baseline_error
            finally:
                # 🔥 v14.0修复：无论成功失败都要清除线程基线数据
                BaselineManager.clear_frontend_baseline_data()
            
            # 🔥 修复3：兼容返回格式，之前期望result有'success'字段，但新方法直接返回数据或抛异常
            # 如果执行到这里说明成功，构建兼容的result格式
            if 'session_id' in result:
                session = {"id": result['session_id'], "baseline_date": result['baseline_date']}
                created = True
                success_result = result
            else:
                # 如果返回格式不匹配，包装为success格式
                success_result = {
                    'success': True,
                    'session_id': result.get('session_id'),
                    'baseline_date': result.get('baseline_date')
                }
                session = {"id": result['session_id'], "baseline_date": result['baseline_date']}
                created = True
            
            # 构建响应
            response_data = {
                "session_id": session["id"],
                "baseline_date": session["baseline_date"],
                "message": "健康数据会话初始化成功" if created else "健康数据会话已更新"
            }
            
            logger.info(f"[{request_id}] 用户 {user.email} 健康数据会话初始化成功，耗时: {time.time() - start_time:.3f}s")
            
            return Response({
                "code": 200,
                "message": "操作成功",
                "data": response_data,
                "timestamp": int(time.time())
            })
            
        except Exception as e:
            logger.error(f"[{request_id}] 健康数据会话初始化失败，用户: {user.email}, 错误: {str(e)}", exc_info=True)
            return Response({
                "code": 500,
                "message": "健康数据会话初始化失败，请稍后重试",
                "details": str(e),
                "timestamp": int(time.time())
            }, status=500)

    @extend_schema(
        operation_id="health_sync",
        summary="同步健康数据增量",
        description="""
        定期同步健康数据增量，更新任务进度并发放奖励。
        
        **核心逻辑：**
        - 客户端上传最新累计总量
        - 后端计算增量 = 新总量 - 基线
        - 验证增量合理性
        - 更新任务进度并发放奖励
        """,
        request=HealthSyncSerializer,
        responses={
            200: HealthDataResponseSerializer,
            400: "请求参数错误",
            401: "未授权访问",
            404: "会话不存在",
            500: "服务器内部错误"
        },
        tags=["健康数据"]
    )
    @action(detail=False, methods=['post'], url_path='sync')
    def sync(self, request):
        """同步健康数据增量"""
        start_time = time.time()
        user = request.user
        request_id = getattr(request, 'request_id', '-')
        
        logger.info(f"[{request_id}] 用户 {user.email} 请求同步健康数据")
        
        try:
            # 验证请求数据
            serializer = HealthSyncSerializer(data=request.data)
            if not serializer.is_valid():
                logger.warning(f"[{request_id}] 健康数据同步参数验证失败: {serializer.errors}")
                return Response({
                    "code": 400,
                    "message": "请求参数错误",
                    "details": serializer.errors,
                    "timestamp": int(time.time())
                }, status=400)
            
            validated_data = serializer.validated_data
            
            # 调用服务层同步数据
            sync_result = HealthSyncService.sync_health_data(
                user=user,
                new_totals=validated_data['new_totals'],
                device_id=validated_data['device_id'],
                permissions=validated_data.get('permissions'),
                session_based=validated_data.get('session_based', False),
                skip_permission_check=validated_data.get('skip_permission_check', False)
            )
            
            if not sync_result['success']:
                logger.warning(f"[{request_id}] 用户 {user.email} 健康数据同步失败: {sync_result['message']}")
                return Response({
                    "code": 404 if "会话不存在" in sync_result['message'] else 400,
                    "message": sync_result['message'],
                    "details": sync_result,
                    "timestamp": int(time.time())
                }, status=404 if "会话不存在" in sync_result['message'] else 400)
            
            # 构建响应数据
            response_data = {
                "net_increment": sync_result['net_increment'],
                "updated_tasks": sync_result['updated_tasks'],
                "message": sync_result['message']
            }
            
            logger.info(f"[{request_id}] 用户 {user.email} 健康数据同步成功，增量: {sync_result['net_increment']}, 耗时: {time.time() - start_time:.3f}s")
            
            return Response({
                "code": 200,
                "message": "操作成功",
                "data": response_data,
                "timestamp": int(time.time())
            })
            
        except Exception as e:
            logger.error(f"[{request_id}] 健康数据同步失败，用户: {user.email}, 错误: {str(e)}", exc_info=True)
            return Response({
                "code": 500,
                "message": "健康数据同步失败，请稍后重试",
                "details": str(e),
                "timestamp": int(time.time())
            }, status=500)

    @extend_schema(
        operation_id="health_baseline_reset",
        summary="重置健康数据基线",
        description="""
        跨天时重置健康数据基线，归档前一天数据。
        
        **使用场景：**
        - 应用检测到跨天
        - 后台任务执行跨天重置
        - 用户手动触发重置
        
        **处理逻辑：**
        - 归档前一天数据
        - 重置基线为新的累计总量
        - 为新的一天准备数据基础
        """,
        request=BaselineResetSerializer,
        responses={
            200: BaselineResetResponseSerializer,
            400: "请求参数错误",
            401: "未授权访问",
            404: "会话不存在",
            500: "服务器内部错误"
        },
        tags=["健康数据"]
    )
    @action(detail=False, methods=['post'], url_path='baseline-reset')
    def baseline_reset(self, request):
        """重置健康数据基线（跨天处理）"""
        start_time = time.time()
        user = request.user
        request_id = getattr(request, 'request_id', '-')
        
        logger.info(f"[{request_id}] 用户 {user.email} 请求重置健康数据基线")
        
        try:
            # 验证请求数据
            serializer = BaselineResetSerializer(data=request.data)
            if not serializer.is_valid():
                logger.warning(f"[{request_id}] 基线重置参数验证失败: {serializer.errors}")
                return Response({
                    "code": 400,
                    "message": "请求参数错误",
                    "details": serializer.errors,
                    "timestamp": int(time.time())
                }, status=400)
            
            validated_data = serializer.validated_data
            
            # 调用统一BaselineManager重置基线
            reset_result = BaselineManager.reset_baseline_for_cross_day(
                user=user,
                new_totals=validated_data['new_totals'],
                device_id=validated_data['device_id'],
                permissions=validated_data.get('permissions')
            )
            
            if not reset_result['success']:
                logger.warning(f"[{request_id}] 用户 {user.email} 基线重置失败: {reset_result['message']}")
                return Response({
                    "code": 404 if "会话不存在" in reset_result['message'] else 400,
                    "message": reset_result['message'],
                    "details": reset_result,
                    "timestamp": int(time.time())
                }, status=404 if "会话不存在" in reset_result['message'] else 400)
            
            # 构建响应数据
            response_data = {
                "baseline_steps": reset_result['baseline_steps'],
                "reset_date": reset_result['reset_date'],
                "message": reset_result['message']
            }
            
            logger.info(f"[{request_id}] 用户 {user.email} 健康数据基线重置成功，耗时: {time.time() - start_time:.3f}s")
            
            return Response({
                "code": 200,
                "message": "操作成功",
                "data": response_data,
                "timestamp": int(time.time())
            })
            
        except Exception as e:
            logger.error(f"[{request_id}] 健康数据基线重置失败，用户: {user.email}, 错误: {str(e)}", exc_info=True)
            return Response({
                "code": 500,
                "message": "健康数据基线重置失败，请稍后重试",
                "details": str(e),
                "timestamp": int(time.time())
            }, status=500)

    @extend_schema(
        operation_id="health_status_get",
        summary="获取当天累计运动增量",
        description="""
        获取用户当天累计的运动增量（基于后端已同步数据）。

        **核心逻辑：**
        - 直接从后端数据库读取今天（新加坡时间）已成功同步的所有会话增量的总和。
        - 此接口**不**接受任何来自客户端的健康数据，也**不受**当前设备权限状态的影响。
        - 专门为前端 `Overview` 卡片提供数据。

        **返回值：**
        - 返回步数、距离、卡路里的今日累计增量。
        - 如果今日没有任何同步记录，则各项返回0。
        """,
        request=None,
        responses={
            200: DailyHealthStatusResponseSerializer,
            401: "未授权访问",
            500: "服务器内部错误"
        },
        tags=["健康数据"]
    )
    @action(detail=False, methods=['get'], url_path='status')
    def get_status(self, request):
        """获取当天累计运动增量 - 专门为Overview模块提供数据"""
        user = request.user
        request_id = getattr(request, 'request_id', str(uuid.uuid4())[:8])
        
        logger.info(f"[{request_id}] 用户 {user.email} 请求获取当天健康数据状态")
        
        try:
            from .services import HealthService
            from .services import HealthPermissionService
            
            # 🔥 v14.1核心修复：获取前端提交的权限状态
            frontend_permissions = request.GET.get('permissions')
            if frontend_permissions:
                try:
                    import json
                    frontend_permissions = json.loads(frontend_permissions)
                    logger.info(f"[{request_id}] 用户 {user.email} 提交的前端权限状态: {frontend_permissions}")
                except (json.JSONDecodeError, ValueError) as e:
                    logger.warning(f"[{request_id}] 用户 {user.email} 权限状态解析失败: {e}")
                    frontend_permissions = None
            
            # 🔥 CRITICAL FIX：获取当天运动增量时传递actual_permissions避免错误判断
            status_data = HealthService.get_user_health_status(user=user, actual_permissions=frontend_permissions)
            
            # 🔥 v14.1核心修复：传递前端权限状态进行双重验证
            permissions_data = HealthPermissionService.get_user_permissions(user=user, actual_permissions=frontend_permissions)
            
            # 🔥 BOSS核心修复：获取当前活跃会话信息用于计算会话内增量
            current_session = UnifiedUserSession.objects.filter(
                user=user,
                is_active=True
            ).first()

            session_info = {}
            if current_session:
                session_info = {
                    "session_id": current_session.id,
                    "session_start_time": current_session.session_start_time.isoformat() if current_session.session_start_time else None,
                    "session_baseline_end_time": current_session.session_baseline_end_time.isoformat() if current_session.session_baseline_end_time else None,
                    "session_duration_hours": current_session.get_session_duration_hours(),
                    "session_baseline": {
                        "steps": current_session.session_baseline_steps,
                        "distance": float(current_session.session_baseline_distance) if current_session.session_baseline_distance else None,
                        "calories": current_session.session_baseline_calories,
                    }
                }

            # 🔥 v14.2修复：构建权限感知的前端数据结构（None表示未授权）
            response_data = {
                "daily_increment": {
                    "steps": status_data.get("steps"),      # None=未授权，数值=已授权
                    "distance": status_data.get("distance"),  # None=未授权，数值=已授权
                    "calories": status_data.get("calories")   # None=未授权，数值=已授权
                },
                "session_increment": {
                    "steps": status_data.get("steps"),      # 当前实现：会话内增量等于当天增量
                    "distance": status_data.get("distance"),
                    "calories": status_data.get("calories")
                },
                "session_info": session_info,
                "permissions": {
                    "steps": permissions_data.get("steps", False),
                    "distance": permissions_data.get("distance", False),
                    "calories": permissions_data.get("calories", False)
                },
                "permission_details": {
                    "steps_status": "authorized" if permissions_data.get("steps", False) else "not_authorized",
                    "distance_status": "authorized" if permissions_data.get("distance", False) else "not_authorized",
                    "calories_status": "authorized" if permissions_data.get("calories", False) else "not_authorized"
                },
                "sync_count": status_data.get("sync_count", 0),
                "has_session": True,
                "message": "当天运动增量获取成功（权限感知响应）"
            }
            
            logger.info(f"[{request_id}] 为用户 {user.email} 返回健康数据状态: {response_data}")
            
            return Response({
                "code": 200,
                "message": "操作成功",
                "data": response_data,
                "timestamp": int(time.time())
            })
            
        except Exception as e:
            logger.error(f"[{request_id}] 获取健康数据状态失败: {str(e)}", exc_info=True)
            return Response({
                "code": 500,
                "message": "获取健康数据状态失败，请稍后重试",
                "details": str(e),
                "timestamp": int(time.time())
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @extend_schema(
        operation_id="health_baseline_status",
        summary="检查健康数据基线状态",
        description="""
        检查用户当前设备的健康数据基线初始化状态。
        
        **核心逻辑：**
        - 检查当前设备是否已初始化基线
        - 返回基线设置时间和基本信息
        - 检测设备冲突情况
        
        **使用场景：**
        - 应用启动时检查基线状态
        - 决定是否需要初始化基线
        - 跨天重置前的状态检查
        """,
        request=None,
        responses={
            200: "基线状态信息",
            401: "未授权访问",
            500: "服务器内部错误"
        },
        tags=["健康数据"]
    )
    @action(detail=False, methods=['get'], url_path='baseline-status')
    def baseline_status(self, request):
        """检查健康数据基线状态"""
        user = request.user
        request_id = getattr(request, 'request_id', str(uuid.uuid4())[:8])
        
        logger.info(f"[{request_id}] 用户 {user.email} 请求检查健康数据基线状态")
        
        try:
            # 获取设备ID（从请求参数或Header中获取）
            device_id = request.GET.get('device_id') or request.META.get('HTTP_DEVICE_ID', 'unknown')
            
            # 调用BaselineManager检查基线状态
            status_result = BaselineManager.check_baseline_status(user=user, device_id=device_id)
            
            response_data = {
                "has_baseline": status_result['has_baseline'],
                "baseline_date": status_result.get('baseline_date'),
                "baseline_steps": status_result.get('baseline_steps'),
                "baseline_distance": status_result.get('baseline_distance'),
                "baseline_calories": status_result.get('baseline_calories'),
                "device_id": status_result.get('device_id'),
                "message": status_result.get('message', '基线状态检查完成')
            }
            
            logger.info(f"[{request_id}] 用户 {user.email} 基线状态检查完成: {status_result['has_baseline']}")
            
            return Response({
                "code": 200,
                "message": "操作成功",
                "data": response_data,
                "timestamp": int(time.time())
            })
            
        except Exception as e:
            logger.error(f"[{request_id}] 基线状态检查失败，用户: {user.email}, 错误: {str(e)}", exc_info=True)
            return Response({
                "code": 500,
                "message": "基线状态检查失败，请稍后重试",
                "details": str(e),
                "timestamp": int(time.time())
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @extend_schema(
        operation_id="health_baseline_query",
        summary="查询健康数据基线 - v14.0规范",
        description="""
        查询指定时间范围的健康数据基线，用于修复基线硬编码问题。
        
        **核心功能：**
        - 替代基线硬编码为0的问题
        - 支持权限新增时的基线确定
        - 使用HealthKit查询实际基线数据
        
        **查询逻辑：**
        - startDate: 新加坡时区当天0:00
        - endDate: 会话开始时间或权限新增时间
        - 只查询已授权权限的数据
        """,
        request=BaselineQuerySerializer,
        responses={
            200: BaselineQueryResponseSerializer,
            400: "请求参数错误",
            401: "未授权访问",
            500: "服务器内部错误"
        },
        tags=["健康数据"]
    )
    @action(detail=False, methods=['post'], url_path='baseline-query')
    def baseline_query(self, request):
        """查询健康数据基线 - v14.0规范"""
        start_time = time.time()
        user = request.user
        request_id = getattr(request, 'request_id', '-')
        
        logger.info(f"[{request_id}] 用户 {user.email} 请求查询健康数据基线")
        
        try:
            # 验证请求数据
            serializer = BaselineQuerySerializer(data=request.data)
            if not serializer.is_valid():
                logger.warning(f"[{request_id}] 基线查询参数验证失败: {serializer.errors}")
                return Response({
                    "code": 400,
                    "message": "请求参数错误",
                    "details": serializer.errors,
                    "timestamp": int(time.time())
                }, status=400)
            
            validated_data = serializer.validated_data
            
            # 🔥 v14.0核心修复：这里应该调用前端HealthKit查询基线数据
            # 当前返回模拟数据，实际应该调用iOS/Android健康数据查询
            # TODO: 实现真实的HealthKit/GoogleFit查询逻辑
            
            permissions = validated_data.get('permissions', {})
            baseline_data = {}
            
            # 根据权限状态决定查询哪些数据
            if permissions.get('steps', False):
                # TODO: 调用HealthKit查询步数基线
                # 当前返回0，应该替换为实际查询结果
                baseline_data['steps'] = 0
                logger.info(f"[{request_id}] 查询步数基线: 0 (需要HealthKit实现)")
            
            if permissions.get('distance', False):
                # TODO: 调用HealthKit查询距离基线
                baseline_data['distance'] = 0.0
                logger.info(f"[{request_id}] 查询距离基线: 0.0 (需要HealthKit实现)")
            
            if permissions.get('calories', False):
                # TODO: 调用HealthKit查询卡路里基线
                baseline_data['calories'] = 0
                logger.info(f"[{request_id}] 查询卡路里基线: 0 (需要HealthKit实现)")
            
            # 构建响应数据
            response_data = {
                "baseline_data": baseline_data,
                "query_time_range": {
                    "start": validated_data['start_date'].isoformat(),
                    "end": validated_data['end_date'].isoformat()
                },
                "permissions_used": permissions,
                "message": f"基线查询成功，查询到 {len(baseline_data)} 项数据"
            }
            
            logger.info(f"[{request_id}] 用户 {user.email} 基线查询成功，耗时: {time.time() - start_time:.3f}s")
            
            return Response({
                "code": 200,
                "message": "操作成功",
                "data": response_data,
                "timestamp": int(time.time())
            })
            
        except Exception as e:
            logger.error(f"[{request_id}] 基线查询失败，用户: {user.email}, 错误: {str(e)}", exc_info=True)
            return Response({
                "code": 500,
                "message": "基线查询失败，请稍后重试",
                "details": str(e),
                "timestamp": int(time.time())
            }, status=500)

    @extend_schema(
        operation_id="health_baseline_batch_query",
        summary="批量查询健康数据基线 - v14.0规范",
        description="""
        批量查询多个时间范围的健康数据基线，用于权限新增时的批量基线确定。
        
        **使用场景：**
        - 权限新增时批量确定基线
        - 多个会话的基线补偿查询
        - 历史数据基线修正
        
        **批量逻辑：**
        - 支持最多10个并发查询
        - 每个查询独立处理，部分失败不影响其他查询
        - 返回详细的成功/失败统计
        """,
        request=BatchBaselineQuerySerializer,
        responses={
            200: BatchBaselineQueryResponseSerializer,
            400: "请求参数错误",
            401: "未授权访问",
            500: "服务器内部错误"
        },
        tags=["健康数据"]
    )
    @action(detail=False, methods=['post'], url_path='baseline-batch-query')
    def baseline_batch_query(self, request):
        """批量查询健康数据基线 - v14.0规范"""
        start_time = time.time()
        user = request.user
        request_id = getattr(request, 'request_id', '-')
        
        logger.info(f"[{request_id}] 用户 {user.email} 请求批量查询健康数据基线")
        
        try:
            # 验证请求数据
            serializer = BatchBaselineQuerySerializer(data=request.data)
            if not serializer.is_valid():
                logger.warning(f"[{request_id}] 批量基线查询参数验证失败: {serializer.errors}")
                return Response({
                    "code": 400,
                    "message": "请求参数错误",
                    "details": serializer.errors,
                    "timestamp": int(time.time())
                }, status=400)
            
            validated_data = serializer.validated_data
            queries = validated_data.get('queries', [])
            device_id = validated_data.get('device_id')
            
            results = []
            successful_queries = 0
            failed_queries = 0
            
            # 批量处理查询请求
            for i, query in enumerate(queries):
                try:
                    # TODO: 实现真实的HealthKit/GoogleFit批量查询
                    # 当前返回模拟数据
                    permission_type = query.get('permission_type')
                    start_date = query.get('start_date')
                    end_date = query.get('end_date')
                    
                    # 模拟查询结果
                    baseline_value = 0
                    if permission_type == 'distance':
                        baseline_value = 0.0
                    
                    result = {
                        "query_index": i,
                        "permission_type": permission_type,
                        "baseline_value": baseline_value,
                        "time_range": {
                            "start": start_date,
                            "end": end_date
                        },
                        "success": True,
                        "message": f"{permission_type}基线查询成功"
                    }
                    
                    results.append(result)
                    successful_queries += 1
                    
                    logger.info(f"[{request_id}] 批量查询 {i+1}/{len(queries)} 成功: {permission_type}")
                    
                except Exception as query_error:
                    # 单个查询失败不影响其他查询
                    result = {
                        "query_index": i,
                        "permission_type": query.get('permission_type', 'unknown'),
                        "baseline_value": None,
                        "time_range": {
                            "start": query.get('start_date'),
                            "end": query.get('end_date')
                        },
                        "success": False,
                        "error": str(query_error),
                        "message": f"查询失败: {str(query_error)}"
                    }
                    
                    results.append(result)
                    failed_queries += 1
                    
                    logger.error(f"[{request_id}] 批量查询 {i+1}/{len(queries)} 失败: {str(query_error)}")
            
            # 构建响应数据
            response_data = {
                "results": results,
                "total_queries": len(queries),
                "successful_queries": successful_queries,
                "failed_queries": failed_queries,
                "message": f"批量查询完成，成功 {successful_queries}/{len(queries)} 个查询"
            }
            
            logger.info(f"[{request_id}] 用户 {user.email} 批量基线查询完成，耗时: {time.time() - start_time:.3f}s")
            
            return Response({
                "code": 200,
                "message": "操作成功",
                "data": response_data,
                "timestamp": int(time.time())
            })
            
        except Exception as e:
            logger.error(f"[{request_id}] 批量基线查询失败，用户: {user.email}, 错误: {str(e)}", exc_info=True)
            return Response({
                "code": 500,
                "message": "批量基线查询失败，请稍后重试",
                "details": str(e),
                "timestamp": int(time.time())
            }, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def force_new_session(request):
    """
    强制创建新会话 API
    用于前端检测到APP重启、重新登录等场景
    """
    try:
        user = request.user
        device_id = request.data.get('device_id')
        platform = request.data.get('platform', 'unknown')
        health_source = request.data.get('health_source', 'unknown')
        reason = request.data.get('reason', 'APP重启')
        
        if not device_id:
            return Response({
                'code': 400,
                'message': '缺少设备ID',
                'data': None
            }, status=400)
        
        logger.info(f"🔄 用户 {user.email} 请求强制创建新会话，原因: {reason}")
        
        # 使用BaselineManager强制创建新会话
        result = BaselineManager._force_create_new_session(user, device_id, reason)
        
        if result:
            # 获取当前会话状态
            session_status = BaselineManager.check_baseline_status(user, device_id)
            
            return Response({
                'code': 200,
                'message': '新会话创建成功',
                'data': {
                    'session_created': True,
                    'reason': reason,
                    'need_baseline_init': not session_status.get('has_baseline', False),
                    'session_status': session_status
                }
            })
        else:
            return Response({
                'code': 500,
                'message': '新会话创建失败',
                'data': None
            }, status=500)
            
    except Exception as e:
        logger.error(f"强制创建新会话API失败: {str(e)}", exc_info=True)
        return Response({
            'code': 500,
            'message': f'服务器内部错误: {str(e)}',
            'data': None
        }, status=500)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_session_continuity(request):
    """
    🔥 BOSS修复：检查会话连续性 - 增强版本，支持应用重启检测
    
    支持参数：
    - is_app_restart: 是否为应用重启（布尔值）
    - restart_reason: 重启原因（字符串）
    - device_id: 设备ID（字符串）
    
    返回：
    - should_continue_session: 是否应该继续会话
    - session_info: 会话信息
    - time_info: 时间信息（统一新加坡时区）
    """
    user = request.user
    request_id = getattr(request, 'request_id', str(uuid.uuid4())[:8])
    
    # 🔥 BOSS修复：统一时区处理 - 所有时间转换为新加坡时区
    singapore_tz = timezone.get_fixed_timezone(480)  # UTC+8 新加坡时区
    singapore_now = timezone.now().astimezone(singapore_tz)
    
    # 获取请求参数
    is_app_restart = request.GET.get('is_app_restart', 'false').lower() == 'true'
    restart_reason = request.GET.get('restart_reason', '')
    device_id = request.GET.get('device_id', '')
    
    logger.info(f"[{request_id}] 用户 {user.email} 检查会话连续性")
    logger.info(f"[{request_id}] 参数: is_app_restart={is_app_restart}, restart_reason={restart_reason}, device_id={device_id}")
    logger.info(f"[{request_id}] 新加坡时间: {singapore_now}")
    
    try:
        # 查找最新的活跃会话
        latest_session = UnifiedUserSession.objects.filter(
            user=user,
            is_active=True
        ).order_by('-login_time').first()
        
        should_continue = False
        session_info = {}
        
        if latest_session:
            # 🔥 BOSS修复：如果明确标识为应用重启，强制结束旧会话
            if is_app_restart:
                logger.info(f"[{request_id}] 检测到应用重启，强制结束旧会话 (ID: {latest_session.id})")
                latest_session.is_active = False
                latest_session.logout_time = singapore_now
                latest_session.logout_reason = 'app_restart'
                latest_session.save()
                
                should_continue = False
                session_info = {
                    'previous_session_id': latest_session.id,
                    'previous_session_ended': True,
                    'end_reason': 'app_restart'
                }
            else:
                # 检查会话是否在有效期内（4小时内活跃，24小时内有效）
                session_start_time = latest_session.session_start_time or latest_session.login_time
                last_sync_time = latest_session.last_sync_time or latest_session.login_time
                
                if session_start_time and last_sync_time:
                    # 转换为新加坡时区
                    session_start_sg = session_start_time.astimezone(singapore_tz)
                    last_sync_sg = last_sync_time.astimezone(singapore_tz)
                    
                    # 计算时间差
                    time_since_start = singapore_now - session_start_sg
                    time_since_sync = singapore_now - last_sync_sg
                    
                    # 检查会话有效性
                    if time_since_start.total_seconds() <= 24 * 3600:  # 24小时内
                        if time_since_sync.total_seconds() <= 4 * 3600:  # 4小时内活跃
                            should_continue = True
                            session_info = {
                                'session_id': latest_session.id,
                                'session_start_time': session_start_sg.isoformat(),
                                'last_sync_time': last_sync_sg.isoformat(),
                                'can_continue': True
                            }
                            logger.info(f"[{request_id}] 会话可继续 (ID: {latest_session.id})")
                        else:
                            # 超过4小时未同步，结束会话
                            latest_session.is_active = False
                            latest_session.logout_time = singapore_now
                            latest_session.logout_reason = 'session_timeout'
                            latest_session.save()
                            
                            session_info = {
                                'previous_session_id': latest_session.id,
                                'previous_session_ended': True,
                                'end_reason': 'session_timeout',
                                'inactive_hours': time_since_sync.total_seconds() / 3600
                            }
                            logger.info(f"[{request_id}] 会话因超时结束 (ID: {latest_session.id})")
                    else:
                        # 超过24小时，会话过期
                        latest_session.is_active = False
                        latest_session.logout_time = singapore_now
                        latest_session.logout_reason = 'session_expired'
                        latest_session.save()
                        
                        session_info = {
                            'previous_session_id': latest_session.id,
                            'previous_session_ended': True,
                            'end_reason': 'session_expired',
                            'session_age_hours': time_since_start.total_seconds() / 3600
                        }
                        logger.info(f"[{request_id}] 会话因过期结束 (ID: {latest_session.id})")
                else:
                    # 时间字段缺失，强制结束会话
                    latest_session.is_active = False
                    latest_session.logout_time = singapore_now
                    latest_session.logout_reason = 'invalid_session'
                    latest_session.save()
                    
                    session_info = {
                        'previous_session_id': latest_session.id,
                        'previous_session_ended': True,
                        'end_reason': 'invalid_session'
                    }
                    logger.warning(f"[{request_id}] 会话因时间字段缺失结束 (ID: {latest_session.id})")
        else:
            logger.info(f"[{request_id}] 用户无活跃会话")
        
        # 🔥 BOSS修复：返回统一时区的时间信息
        response_data = {
            'should_continue_session': should_continue,
            'session_info': session_info,
            'time_info': {
                'singapore_time': singapore_now.isoformat(),
                'timezone': 'Asia/Singapore',
                'utc_offset': '+08:00'
            },
            'app_restart_info': {
                'is_app_restart': is_app_restart,
                'restart_reason': restart_reason
            }
        }
        
        logger.info(f"[{request_id}] 会话连续性检查完成: should_continue={should_continue}")
        
        return Response({
            "code": 200,
            "message": "会话连续性检查完成",
            "data": response_data,
            "timestamp": int(singapore_now.timestamp())
        })
        
    except Exception as e:
        logger.error(f"[{request_id}] 会话连续性检查失败: {str(e)}", exc_info=True)
        return Response({
            "code": 500,
            "message": "会话连续性检查失败",
            "details": str(e),
            "timestamp": int(singapore_now.timestamp())
        }, status=500) 