from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views
from . import views_offline

router = DefaultRouter()
# 健康数据API端点 - 统一路径 /api/app/v1/health/
router.register(r'', views.HealthViewSet, basename='health')

app_name = 'api_health'  # 命名空间

urlpatterns = [
    path('', include(router.urls)),
    # 🔥 BOSS新增：会话管理API
    path('session/start/', views_offline.session_start, name='session_start'),
    path('session/end/', views_offline.session_end, name='session_end'),
    path('session/force-new/', views_offline.force_create_new_session, name='force_new_session'),
    path('session/check-continuity/', views_offline.check_session_continuity, name='check_session_continuity'),
    # 🔥 BOSS修复：添加缺失的离线处理队列端点
    path('offline/process-queue/', views_offline.process_offline_queue, name='process_offline_queue'),
]

# 最终的API端点路径：
# POST /api/app/v1/health/session/init/           - 健康数据会话初始化
# POST /api/app/v1/health/sync/                   - 健康数据同步
# GET  /api/app/v1/health/status/                 - 获取健康数据状态
# GET  /api/app/v1/health/baseline-status/        - 检查健康数据基线状态
# POST /api/app/v1/health/session/start/          - 🔥 会话开始标记
# POST /api/app/v1/health/session/end/            - 🔥 会话结束标记
# POST /api/app/v1/health/session/force-new/      - 🔥 强制创建新会话
# GET  /api/app/v1/health/session/check-continuity/ - 🔥 检查会话连续性
# POST /api/app/v1/health/offline/process-queue/  - 处理离线队列 