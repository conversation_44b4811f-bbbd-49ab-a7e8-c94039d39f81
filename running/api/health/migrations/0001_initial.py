# Generated by Django 4.2.11 on 2025-07-04 08:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='OfflineOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_type', models.CharField(choices=[('health_data_sync', '健康数据同步'), ('task_completion', '任务完成'), ('baseline_reset', '基线重置'), ('cross_day_processing', '跨天数据处理')], help_text='离线操作的类型', max_length=50, verbose_name='操作类型')),
                ('operation_data', models.JSONField(default=dict, help_text='操作相关的数据（JSON格式）', verbose_name='操作数据')),
                ('priority', models.IntegerField(default=0, help_text='操作优先级（数字越大优先级越高）', verbose_name='优先级')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('processing', '处理中'), ('completed', '已完成'), ('failed', '失败')], default='pending', help_text='操作当前状态', max_length=20, verbose_name='状态')),
                ('retry_count', models.IntegerField(default=0, help_text='操作重试次数', verbose_name='重试次数')),
                ('result', models.JSONField(blank=True, help_text='操作执行结果（JSON格式）', null=True, verbose_name='操作结果')),
                ('error_message', models.TextField(blank=True, help_text='操作失败时的错误信息', null=True, verbose_name='错误信息')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='操作创建时间', verbose_name='创建时间')),
                ('completed_at', models.DateTimeField(blank=True, help_text='操作完成时间', null=True, verbose_name='完成时间')),
                ('user', models.ForeignKey(help_text='执行操作的用户', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '离线操作',
                'verbose_name_plural': '离线操作',
                'db_table': 'health_offline_operation',
                'ordering': ['-priority', 'created_at'],
                'indexes': [models.Index(fields=['user', 'status'], name='health_offl_user_id_225136_idx'), models.Index(fields=['operation_type', 'status'], name='health_offl_operati_33e07f_idx'), models.Index(fields=['created_at'], name='health_offl_created_f2f0db_idx')],
            },
        ),
        migrations.CreateModel(
            name='HealthDataAuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device_id', models.CharField(blank=True, help_text='操作设备的唯一标识', max_length=100, null=True, verbose_name='设备ID')),
                ('operation_type', models.CharField(choices=[('session_init', '会话初始化'), ('session_continuity_check', '会话连续性检查'), ('cross_day', '跨天处理'), ('sync', '数据同步'), ('baseline_reset', '基线重置'), ('permission_change', '权限变更'), ('data_recovery', '数据恢复'), ('integrity_check', '完整性检查'), ('offline_queue', '离线队列处理')], help_text='操作的类型分类', max_length=50, verbose_name='操作类型')),
                ('operation_name', models.CharField(help_text='具体操作的描述性名称', max_length=200, verbose_name='操作名称')),
                ('before_state', models.JSONField(blank=True, help_text='操作执行前的数据状态', null=True, verbose_name='操作前状态')),
                ('after_state', models.JSONField(blank=True, help_text='操作执行后的数据状态', null=True, verbose_name='操作后状态')),
                ('health_data', models.JSONField(blank=True, help_text='操作涉及的健康数据', null=True, verbose_name='健康数据')),
                ('operation_success', models.BooleanField(help_text='操作是否成功执行', verbose_name='操作成功')),
                ('error_message', models.TextField(blank=True, help_text='操作失败时的错误详情', null=True, verbose_name='错误信息')),
                ('severity', models.CharField(choices=[('info', '信息'), ('warning', '警告'), ('error', '错误'), ('critical', '严重')], default='info', help_text='操作的严重程度级别', max_length=20, verbose_name='严重程度')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='操作发起的IP地址', null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, help_text='操作的用户代理信息', null=True, verbose_name='用户代理')),
                ('request_id', models.CharField(blank=True, help_text='关联的请求唯一标识', max_length=100, null=True, verbose_name='请求ID')),
                ('session_id', models.CharField(blank=True, help_text='关联的用户会话ID', max_length=100, null=True, verbose_name='会话ID')),
                ('object_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='关联对象ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True, help_text='操作发生的时间', verbose_name='时间戳')),
                ('duration_ms', models.PositiveIntegerField(blank=True, help_text='操作执行的时长', null=True, verbose_name='执行时长(毫秒)')),
                ('additional_data', models.JSONField(blank=True, help_text='其他相关的上下文数据', null=True, verbose_name='附加数据')),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype', verbose_name='关联模型类型')),
                ('user', models.ForeignKey(help_text='执行操作的用户', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '健康数据审计日志',
                'verbose_name_plural': '健康数据审计日志',
                'db_table': 'health_audit_log',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', 'timestamp'], name='health_audi_user_id_76447a_idx'), models.Index(fields=['operation_type', 'timestamp'], name='health_audi_operati_7194a6_idx'), models.Index(fields=['device_id', 'timestamp'], name='health_audi_device__06e01a_idx'), models.Index(fields=['operation_success', 'severity'], name='health_audi_operati_fe200a_idx'), models.Index(fields=['content_type', 'object_id'], name='health_audi_content_99ce3f_idx')],
            },
        ),
    ]
