"""
SweatMint 健康数据相关模型
🔥 BOSS需求：离线操作队列和审计日志模型
"""
from django.db import models
from django.conf import settings
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey

class OfflineOperation(models.Model):
    """
    离线操作模型
    🔥 BOSS需求：管理离线期间的操作队列
    """
    OPERATION_TYPES = [
        ('health_data_sync', '健康数据同步'),
        ('task_completion', '任务完成'),
        ('baseline_reset', '基线重置'),
        ('cross_day_processing', '跨天数据处理'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败'),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE,
        verbose_name='用户',
        help_text='执行操作的用户'
    )
    
    operation_type = models.CharField(
        max_length=50,
        choices=OPERATION_TYPES,
        verbose_name='操作类型',
        help_text='离线操作的类型'
    )
    
    operation_data = models.JSONField(
        verbose_name='操作数据',
        help_text='操作相关的数据（JSON格式）',
        default=dict
    )
    
    priority = models.IntegerField(
        default=0,
        verbose_name='优先级',
        help_text='操作优先级（数字越大优先级越高）'
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='状态',
        help_text='操作当前状态'
    )
    
    retry_count = models.IntegerField(
        default=0,
        verbose_name='重试次数',
        help_text='操作重试次数'
    )
    
    result = models.JSONField(
        null=True,
        blank=True,
        verbose_name='操作结果',
        help_text='操作执行结果（JSON格式）'
    )
    
    error_message = models.TextField(
        null=True,
        blank=True,
        verbose_name='错误信息',
        help_text='操作失败时的错误信息'
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间',
        help_text='操作创建时间'
    )
    
    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='完成时间',
        help_text='操作完成时间'
    )
    
    class Meta:
        db_table = 'health_offline_operation'
        verbose_name = '离线操作'
        verbose_name_plural = '离线操作'
        ordering = ['-priority', 'created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['operation_type', 'status']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.get_operation_type_display()} - {self.get_status_display()}"

class HealthDataAuditLog(models.Model):
    """
    健康数据审计日志模型
    🔥 BOSS需求：记录所有健康数据相关操作的完整审计日志
    """
    OPERATION_TYPES = [
        ('session_init', '会话初始化'),
        ('session_continuity_check', '会话连续性检查'),
        ('cross_day', '跨天处理'),
        ('sync', '数据同步'),
        ('baseline_reset', '基线重置'),
        ('permission_change', '权限变更'),
        ('data_recovery', '数据恢复'),
        ('integrity_check', '完整性检查'),
        ('offline_queue', '离线队列处理'),
    ]
    
    SEVERITY_LEVELS = [
        ('info', '信息'),
        ('warning', '警告'), 
        ('error', '错误'),
        ('critical', '严重'),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name='用户',
        help_text='执行操作的用户'
    )
    
    device_id = models.CharField(
        max_length=100,
        verbose_name='设备ID',
        help_text='操作设备的唯一标识',
        null=True,
        blank=True
    )
    
    operation_type = models.CharField(
        max_length=50,
        choices=OPERATION_TYPES,
        verbose_name='操作类型',
        help_text='操作的类型分类'
    )
    
    operation_name = models.CharField(
        max_length=200,
        verbose_name='操作名称',
        help_text='具体操作的描述性名称'
    )
    
    before_state = models.JSONField(
        null=True,
        blank=True,
        verbose_name='操作前状态',
        help_text='操作执行前的数据状态'
    )
    
    after_state = models.JSONField(
        null=True,
        blank=True,
        verbose_name='操作后状态',
        help_text='操作执行后的数据状态'
    )
    
    health_data = models.JSONField(
        null=True,
        blank=True,
        verbose_name='健康数据',
        help_text='操作涉及的健康数据'
    )
    
    operation_success = models.BooleanField(
        verbose_name='操作成功',
        help_text='操作是否成功执行'
    )
    
    error_message = models.TextField(
        null=True,
        blank=True,
        verbose_name='错误信息',
        help_text='操作失败时的错误详情'
    )
    
    severity = models.CharField(
        max_length=20,
        choices=SEVERITY_LEVELS,
        default='info',
        verbose_name='严重程度',
        help_text='操作的严重程度级别'
    )
    
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name='IP地址',
        help_text='操作发起的IP地址'
    )
    
    user_agent = models.TextField(
        null=True,
        blank=True,
        verbose_name='用户代理',
        help_text='操作的用户代理信息'
    )
    
    request_id = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name='请求ID',
        help_text='关联的请求唯一标识'
    )
    
    session_id = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name='会话ID',
        help_text='关联的用户会话ID'
    )
    
    # 通用关联字段，可以关联任何模型
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name='关联模型类型'
    )
    object_id = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='关联对象ID'
    )
    related_object = GenericForeignKey('content_type', 'object_id')
    
    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name='时间戳',
        help_text='操作发生的时间'
    )
    
    duration_ms = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='执行时长(毫秒)',
        help_text='操作执行的时长'
    )
    
    additional_data = models.JSONField(
        null=True,
        blank=True,
        verbose_name='附加数据',
        help_text='其他相关的上下文数据'
    )
    
    class Meta:
        db_table = 'health_audit_log'
        verbose_name = '健康数据审计日志'
        verbose_name_plural = '健康数据审计日志'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['operation_type', 'timestamp']),
            models.Index(fields=['device_id', 'timestamp']),
            models.Index(fields=['operation_success', 'severity']),
            models.Index(fields=['content_type', 'object_id']),
        ]
    
    def __str__(self):
        status = "成功" if self.operation_success else "失败"
        return f"{self.user.username} - {self.operation_name} - {status} - {self.timestamp}" 