"""
🔥 BOSS测试：用户权限状态和基线数据验证命令
Django管理命令：python manage.py test_user_permissions --user-email <EMAIL>

测试内容：
1. 检查用户的权限状态
2. 验证基线数据的正确性
3. 模拟部分权限授权场景
4. 测试基线管理逻辑

使用示例：
python manage.py test_user_permissions --user-email <EMAIL>
python manage.py test_user_permissions --user-email <EMAIL> --reset-baseline
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.contrib.auth import get_user_model
from decimal import Decimal
import pytz
import logging

from users.models import UnifiedUserSession
from api.health.baseline_manager import BaselineManager

logger = logging.getLogger(__name__)
User = get_user_model()

class Command(BaseCommand):
    help = '测试用户权限状态和基线数据'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--user-email',
            type=str,
            help='要测试的用户邮箱'
        )
        parser.add_argument(
            '--reset-baseline',
            action='store_true',
            help='重置基线数据'
        )
        parser.add_argument(
            '--test-partial-permissions',
            action='store_true',
            help='测试部分权限授权场景'
        )
        
    def handle(self, *args, **options):
        user_email = options.get('user_email')
        reset_baseline = options.get('reset_baseline', False)
        test_partial = options.get('test_partial_permissions', False)
        
        if not user_email:
            raise CommandError('请提供用户邮箱 --user-email')
        
        try:
            user = User.objects.get(email=user_email)
            self.stdout.write(f"🔍 测试用户: {user.email} (ID: {user.id})")
        except User.DoesNotExist:
            raise CommandError(f'用户不存在: {user_email}')
        
        # 1. 检查当前会话状态
        self.check_user_sessions(user)
        
        # 2. 测试部分权限授权
        if test_partial:
            self.test_partial_permission_scenarios(user)
        
        # 3. 重置基线（如果需要）
        if reset_baseline:
            self.reset_user_baseline(user)
        
        self.stdout.write(self.style.SUCCESS('✅ 测试完成'))
    
    def check_user_sessions(self, user):
        """检查用户的会话状态"""
        self.stdout.write("\n📊 检查用户会话状态:")
        
        sessions = UnifiedUserSession.objects.filter(user=user).order_by('-id')
        
        for session in sessions:
            status = "活跃" if session.is_active else "非活跃"
            self.stdout.write(f"\n会话 ID: {session.id} ({status})")
            self.stdout.write(f"  设备ID: {session.device_id}")
            
            if session.baseline_date:
                singapore_tz = pytz.timezone('Asia/Singapore')
                baseline_singapore = session.baseline_date.astimezone(singapore_tz)
                self.stdout.write(f"  基线日期: {baseline_singapore}")
            
            if session.session_start_time:
                singapore_tz = pytz.timezone('Asia/Singapore')
                start_singapore = session.session_start_time.astimezone(singapore_tz)
                self.stdout.write(f"  会话开始: {start_singapore}")
            
            self.stdout.write(f"  基线数据:")
            self.stdout.write(f"    步数: {session.session_baseline_steps}")
            self.stdout.write(f"    距离: {session.session_baseline_distance}")
            self.stdout.write(f"    卡路里: {session.session_baseline_calories}")
            
            # 分析权限状态
            authorized_permissions = []
            if session.session_baseline_steps is not None:
                authorized_permissions.append('steps')
            if session.session_baseline_distance is not None:
                authorized_permissions.append('distance')
            if session.session_baseline_calories is not None:
                authorized_permissions.append('calories')
            
            self.stdout.write(f"  已授权权限: {', '.join(authorized_permissions) if authorized_permissions else '无'}")
    
    def test_partial_permission_scenarios(self, user):
        """测试部分权限授权场景"""
        self.stdout.write("\n🧪 测试部分权限授权场景:")
        
        test_scenarios = [
            {
                'name': '仅步数权限',
                'permissions': {'steps': True, 'distance': False, 'calories': False},
                'device_id': 'test_device_steps_only'
            },
            {
                'name': '步数+距离权限',
                'permissions': {'steps': True, 'distance': True, 'calories': False},
                'device_id': 'test_device_steps_distance'
            },
            {
                'name': '完整权限',
                'permissions': {'steps': True, 'distance': True, 'calories': True},
                'device_id': 'test_device_full'
            }
        ]
        
        for scenario in test_scenarios:
            self.stdout.write(f"\n测试场景: {scenario['name']}")
            self.stdout.write(f"权限状态: {scenario['permissions']}")
            
            try:
                result = BaselineManager.initialize_user_baseline(
                    user=user,
                    device_id=scenario['device_id'],
                    permissions_status=scenario['permissions'],
                    force_create_new_session=True,
                    reason=f"测试场景: {scenario['name']}"
                )
                
                self.stdout.write(f"✅ 基线初始化成功:")
                self.stdout.write(f"  会话ID: {result['session_id']}")
                self.stdout.write(f"  已授权权限: {', '.join(result['authorized_permissions'])}")
                self.stdout.write(f"  基线值: {result['baseline_values']}")
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"❌ 基线初始化失败: {str(e)}"))
    
    def reset_user_baseline(self, user):
        """重置用户基线"""
        self.stdout.write("\n🔄 重置用户基线:")
        
        # 将所有会话设为非活跃
        affected_count = UnifiedUserSession.objects.filter(
            user=user,
            is_active=True
        ).update(is_active=False)
        
        self.stdout.write(f"✅ 已重置 {affected_count} 个活跃会话")
        
        # 测试仅步数权限的基线初始化
        self.stdout.write("\n🧪 重新初始化基线（仅步数权限）:")
        
        try:
            result = BaselineManager.initialize_user_baseline(
                user=user,
                device_id='reset_test_device',
                permissions_status={'steps': True, 'distance': False, 'calories': False},
                force_create_new_session=True,
                reason="重置测试 - 仅步数权限"
            )
            
            self.stdout.write(f"✅ 基线重置成功:")
            self.stdout.write(f"  会话ID: {result['session_id']}")
            self.stdout.write(f"  已授权权限: {', '.join(result['authorized_permissions'])}")
            self.stdout.write(f"  基线值: {result['baseline_values']}")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ 基线重置失败: {str(e)}")) 