"""
SweatMint 会话时间字段修复命令
🔥 BOSS需求：修复旧会话的session_start_time和baseline_date字段为None的问题

使用方法：
python manage.py fix_session_time_fields [--dry-run] [--user-id USER_ID]
"""
import logging
from datetime import timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from users.models import UnifiedUserSession

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = "修复UnifiedUserSession中session_start_time和baseline_date字段为None的问题"
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示需要修复的数据，不实际修改',
        )
        parser.add_argument(
            '--user-id',
            type=int,
            help='只修复指定用户ID的会话',
        )
        parser.add_argument(
            '--limit',
            type=int,
            default=1000,
            help='限制处理的记录数量（默认1000）',
        )
    
    def handle(self, *args, **options):
        dry_run = options['dry_run']
        user_id = options['user_id']
        limit = options['limit']
        
        self.stdout.write(
            self.style.SUCCESS(
                "🔧 开始修复UnifiedUserSession时间字段..."
            )
        )
        
        # 构建查询条件
        queryset = UnifiedUserSession.objects.filter(
            session_start_time__isnull=True
        ).select_related('user')
        
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        queryset = queryset[:limit]
        
        sessions_to_fix = list(queryset)
        
        if not sessions_to_fix:
            self.stdout.write(
                self.style.WARNING("未发现需要修复的会话记录")
            )
            return
        
        self.stdout.write(
            f"📊 发现 {len(sessions_to_fix)} 个需要修复的会话记录"
        )
        
        if dry_run:
            self._show_dry_run_results(sessions_to_fix)
            return
        
        # 执行修复
        with transaction.atomic():
            fixed_count = 0
            error_count = 0
            
            for session in sessions_to_fix:
                try:
                    self._fix_session_time_fields(session)
                    fixed_count += 1
                    
                    if fixed_count % 100 == 0:
                        self.stdout.write(f"✅ 已修复 {fixed_count} 个会话...")
                        
                except Exception as e:
                    error_count += 1
                    self.stdout.write(
                        self.style.ERROR(
                            f"❌ 修复会话 {session.id} 失败: {str(e)}"
                        )
                    )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"🎉 修复完成！成功: {fixed_count}，失败: {error_count}"
                )
            )
    
    def _show_dry_run_results(self, sessions):
        """显示dry-run结果"""
        self.stdout.write(
            self.style.WARNING("📋 Dry-run模式，以下是需要修复的会话：")
        )
        
        for i, session in enumerate(sessions[:10]):  # 只显示前10个
            proposed_start_time = session.login_time or timezone.now()
            proposed_baseline_date = session.login_time or timezone.now()
            
            self.stdout.write(
                f"  {i+1}. 会话ID: {session.id}, 用户: {session.user.email}\n"
                f"     当前session_start_time: {session.session_start_time}\n"
                f"     建议session_start_time: {proposed_start_time}\n"
                f"     当前baseline_date: {session.baseline_date}\n"
                f"     建议baseline_date: {proposed_baseline_date}\n"
                f"     登录时间: {session.login_time}\n"
            )
        
        if len(sessions) > 10:
            self.stdout.write(f"     ... 还有 {len(sessions) - 10} 个会话需要修复")
    
    def _fix_session_time_fields(self, session):
        """修复单个会话的时间字段"""
        now = timezone.now()
        fixed_fields = []
        
        # 修复session_start_time
        if session.session_start_time is None:
            session.session_start_time = session.login_time or now
            fixed_fields.append('session_start_time')
        
        # 修复baseline_date
        if session.baseline_date is None:
            session.baseline_date = session.session_start_time or session.login_time or now
            fixed_fields.append('baseline_date')
        
        # 修复last_sync_time（如果为None）
        if session.last_sync_time is None:
            session.last_sync_time = session.session_start_time or session.login_time or now
            fixed_fields.append('last_sync_time')
        
        if fixed_fields:
            session.save(update_fields=fixed_fields + ['last_activity'])
            logger.info(
                f"修复会话 {session.id} (用户: {session.user.email}) "
                f"的字段: {', '.join(fixed_fields)}"
            )
        
        return fixed_fields 