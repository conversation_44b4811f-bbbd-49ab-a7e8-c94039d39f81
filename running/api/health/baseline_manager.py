"""
SweatMint 统一基线管理服务
🔥 BOSS修复：严格遵循v13.0规范，确保基线准确性和跨天处理完整性
整合所有基线相关操作，消除代码冗余
"""
import time
import logging
from decimal import Decimal
from datetime import timedelta, datetime
from django.utils import timezone
import pytz
from django.db import transaction

from users.models import UnifiedUserSession, DailyHealthSnapshot
from core.utils.lock_utils import user_lock
from core.utils.offline_cache import enqueue_offline_request

logger = logging.getLogger(__name__)


class BaselineManager:
    """
    🔥 BOSS修复：统一的健康数据基线管理服务 - 严格遵循v13.0规范
    
    核心修复：
    1. 基线时间计算：startDate=新加坡时间0:00，endDate=会话开始时间
    2. 权限独立性：未授权权限保持null，不设为0
    3. 会话连续性：严格4小时无活动阈值
    4. 跨天处理：完整的数据归档和任务更新
    
    职责：
    1. 统一基线初始化逻辑（整合HealthSessionService）
    2. 统一基线重置逻辑（整合HealthBaselineService）
    3. 统一权限处理和基线计算
    4. 消除重复代码，提供单一入口
    """
    
    @staticmethod
    def get_singapore_timezone():
        """获取新加坡时区对象"""
        return pytz.timezone('Asia/Singapore')
    
    @staticmethod
    def get_singapore_today_start():
        """获取新加坡时间今天0:00"""
        singapore_tz = BaselineManager.get_singapore_timezone()
        # 先获取当前新加坡时间
        singapore_now = timezone.now().astimezone(singapore_tz)
        # 获取新加坡时间今天0:00
        singapore_today_start = singapore_now.replace(hour=0, minute=0, second=0, microsecond=0)
        # 转换回UTC存储
        return singapore_today_start.astimezone(timezone.utc)
    
    @staticmethod
    def initialize_user_baseline(user, device_id, permissions_status, force_create_new_session=False, reason="正常会话创建", session_start_time=None, baseline_data=None):
        """
        🔥 BOSS修复：统一的用户基线初始化（支持部分权限授权）- 时区统一修复版

        Args:
            user: 用户对象
            device_id: 设备ID
            permissions_status: 权限状态字典 {'steps': bool, 'distance': bool, 'calories': bool}
            force_create_new_session: 是否强制创建新会话
            reason: 创建原因
            session_start_time: 会话开始时间（前端传递的新加坡时间），如果为None则使用当前时间
            baseline_data: 前端传递的基线数据字典 {'steps': float, 'distance': float, 'calories': float}

        Returns:
            dict: 基线初始化结果
        """
        with user_lock(user.id):
            try:
                logger.info(f"🔥 [BaselineManager] 开始为用户 {user.email} 初始化基线 (原因: {reason})")
                logger.info(f"📱 权限状态: {permissions_status}")
                
                # 🔥 BOSS修复：强制创建新会话处理
                if force_create_new_session:
                    logger.info(f"🔄 强制创建新会话: {reason}")
                    BaselineManager._force_create_new_session(user, device_id, reason)
                
                # 检查是否存在活跃会话
                singapore_tz = pytz.timezone('Asia/Singapore')
                session = UnifiedUserSession.objects.filter(
                    user=user,
                    device_id=device_id,
                    is_active=True
                ).first()
                
                if session:
                    # 🔥 BOSS修复：会话连续性检查（遵循v2.0核心概念：app启动应创建新会话）
                    now = timezone.now()
                    if session.last_sync_time:
                        time_since_last_sync = now - session.last_sync_time

                        # 🔥 修复：严格的会话连续性判断 - 超过30分钟或app重启强制创建新会话
                        # 根据会话核心概念v2.0："用户启动App时，立即创建一个新会话"
                        if (time_since_last_sync > timedelta(minutes=30) or
                            reason == "会话初始化API调用"):  # API调用通常表示app重启

                            if time_since_last_sync > timedelta(hours=4):
                                logger.info(f"⏰ 会话间隔超过4小时 ({time_since_last_sync})，创建新会话")
                                logout_reason = 'session_timeout'
                            elif time_since_last_sync > timedelta(minutes=30):
                                logger.info(f"🔄 会话间隔超过30分钟 ({time_since_last_sync})，遵循v2.0概念创建新会话")
                                logout_reason = 'session_break'
                            else:
                                logger.info(f"🆕 检测到app重启（API调用），遵循v2.0概念强制创建新会话")
                                logout_reason = 'app_restart'

                            session.is_active = False
                            session.logout_time = timezone.now()
                            session.logout_reason = logout_reason
                            session.save()
                            session = None
                        else:
                            logger.info(f"✅ 会话连续性检查通过，间隔: {time_since_last_sync}")
                
                # 创建或更新会话
                if not session:
                    logger.info("🆕 创建新的用户会话")
                    
                    # 🔥 BOSS核心修复：在创建新会话前，先强制结束所有活跃会话
                    existing_active_sessions = UnifiedUserSession.objects.filter(
                        user=user,
                        is_active=True
                    )
                    
                    if existing_active_sessions.exists():
                        # 强制结束所有活跃会话，记录结束原因
                        updated_count = existing_active_sessions.update(
                            is_active=False,
                            logout_time=timezone.now(),
                            logout_reason='app_restart'
                        )
                        logger.info(f"🔥 BOSS修复：强制结束用户 {user.email} 的 {updated_count} 个活跃会话，避免会话冲突")
                    
                    # 🔥 BOSS修复：计算新加坡时间的基线时间
                    now_singapore = timezone.now().astimezone(singapore_tz)
                    baseline_date_singapore = now_singapore.replace(hour=0, minute=0, second=0, microsecond=0)
                    
                    # 🔥 BOSS核心修复：转换为UTC存储（数据库要求UTC时间）
                    baseline_date_utc = baseline_date_singapore.astimezone(timezone.utc)
                    
                    # 🔥 BOSS核心修复：使用前端传递的会话开始时间，确保时间一致性
                    if session_start_time:
                        # 前端传递了会话开始时间，使用它（应该已经是新加坡时间）
                        if isinstance(session_start_time, str):
                            # 如果是字符串，解析为datetime
                            from dateutil import parser
                            session_start_time_singapore = parser.parse(session_start_time)
                            # 确保时区正确
                            if session_start_time_singapore.tzinfo is None:
                                session_start_time_singapore = singapore_tz.localize(session_start_time_singapore)
                            else:
                                session_start_time_singapore = session_start_time_singapore.astimezone(singapore_tz)
                        else:
                            # 如果是datetime对象，转换为新加坡时区
                            session_start_time_singapore = session_start_time.astimezone(singapore_tz)

                        logger.info(f"🕐 使用前端传递的会话开始时间(新加坡): {session_start_time_singapore.strftime('%Y-%m-%d %H:%M:%S %Z')}")
                    else:
                        # 没有传递会话开始时间，使用当前时间
                        session_start_time_singapore = timezone.now().astimezone(singapore_tz)
                        logger.info(f"🕐 使用当前时间作为会话开始时间(新加坡): {session_start_time_singapore.strftime('%Y-%m-%d %H:%M:%S %Z')}")

                    # 🔥 关键修复：在使用前定义session_start_time_utc变量
                    session_start_time_utc = session_start_time_singapore.astimezone(timezone.utc)

                    # 🔥 BOSS核心修复：正确设置基线时间字段（遵循v2.0核心概念）
                    session_data = {
                        'user': user,
                        'device_id': device_id,
                        'baseline_date': baseline_date_utc,  # 🔥 v2.0修复：新加坡时间当天00:00（startDate）
                        'session_start_time': session_start_time_utc,  # 🔥 使用前端传递的时间
                        'session_baseline_end_time': session_start_time_utc,  # 🔥 v2.0修复：会话开始时间（endDate，用于基线计算）
                        'last_sync_time': timezone.now(),
                        'login_time': timezone.now(),
                        'is_active': True
                    }

                    # 🔥 BOSS核心修复：基于权限状态设置基线值（权限独立性原则）
                    # 🔥 关键：HKStatisticsQuery请求startDate=新加坡0:00，endDate=会话开始时间
                    # 🔥 原则：已授权权限有基线，未授权权限为null！！！
                    
                    if permissions_status.get('steps', False):
                        # 🔥 BOSS核心修复：使用前端传递的实际基线数据（v2.0规范）
                        baseline_steps = baseline_data.get('steps') if baseline_data else None
                        if baseline_steps is not None:
                            session_data['session_baseline_steps'] = int(baseline_steps)
                            logger.info(f"✅ 步数权限已授权，使用前端实际基线: {session_data['session_baseline_steps']}")
                        else:
                            session_data['session_baseline_steps'] = 0
                            logger.warning("⚠️ 步数权限已授权但前端未提供基线数据，使用默认值0")
                    else:
                        session_data['session_baseline_steps'] = None
                        logger.info("❌ 步数权限未授权，基线保持null")
                    
                    if permissions_status.get('distance', False):
                        # 🔥 BOSS核心修复：使用前端传递的实际基线数据（v2.0规范）
                        baseline_distance = baseline_data.get('distance') if baseline_data else None
                        if baseline_distance is not None:
                            session_data['session_baseline_distance'] = Decimal(str(baseline_distance))
                            logger.info(f"✅ 距离权限已授权，使用前端实际基线: {session_data['session_baseline_distance']}")
                        else:
                            session_data['session_baseline_distance'] = Decimal('0.0')
                            logger.warning("⚠️ 距离权限已授权但前端未提供基线数据，使用默认值0.0")
                    else:
                        session_data['session_baseline_distance'] = None
                        logger.info("❌ 距离权限未授权，基线保持null")

                    if permissions_status.get('calories', False):
                        # 🔥 BOSS核心修复：使用前端传递的实际基线数据（v2.0规范）
                        baseline_calories = baseline_data.get('calories') if baseline_data else None
                        if baseline_calories is not None:
                            session_data['session_baseline_calories'] = int(baseline_calories)
                            logger.info(f"✅ 卡路里权限已授权，使用前端实际基线: {session_data['session_baseline_calories']}")
                        else:
                            session_data['session_baseline_calories'] = 0
                            logger.warning("⚠️ 卡路里权限已授权但前端未提供基线数据，使用默认值0")
                    else:
                        session_data['session_baseline_calories'] = None
                        logger.info("❌ 卡路里权限未授权，基线保持null")
                    
                    session = UnifiedUserSession.objects.create(**session_data)
                    
                    logger.info(f"✅ 新会话创建成功 (ID: {session.id})")
                    logger.info(f"📅 基线日期（startDate-当天00:00）: {baseline_date_singapore}")
                    logger.info(f"🕐 会话开始时间（session_start_time）: {session.session_start_time}")
                    logger.info(f"⏰ 基线结束时间（endDate-用于基线计算）: {session.session_baseline_end_time}")
                    
                else:
                    # 🔥 BOSS修复：更新现有会话的权限状态
                    logger.info(f"🔄 更新现有会话 (ID: {session.id}) 的权限状态")
                    
                    # 🔥 BOSS核心修复：确保现有会话有正确的基线日期
                    now_singapore = timezone.now().astimezone(singapore_tz)
                    baseline_date_singapore = now_singapore.replace(hour=0, minute=0, second=0, microsecond=0)
                    baseline_date_utc = baseline_date_singapore.astimezone(timezone.utc)
                    
                    # 检查权限变化并相应更新基线
                    updated = False
                    
                    # 🔥 v2.0修复：检查并设置基线日期（startDate-当天00:00）
                    if session.baseline_date is None:
                        session.baseline_date = baseline_date_utc
                        updated = True
                        logger.info(f"✅ 设置会话基线日期（startDate-当天00:00）: {baseline_date_singapore}")
                    else:
                        # 检查基线日期是否是今天
                        existing_baseline_sg = session.baseline_date.astimezone(singapore_tz)
                        if existing_baseline_sg.date() != baseline_date_singapore.date():
                            session.baseline_date = baseline_date_utc
                            updated = True
                            logger.info(f"✅ 更新会话基线日期为今天（startDate-当天00:00）: {baseline_date_singapore}")

                    # 🔥 v2.0修复：确保基线结束时间（endDate-会话开始时间）存在
                    if session.session_baseline_end_time is None:
                        session.session_baseline_end_time = session.session_start_time or timezone.now()
                        updated = True
                        logger.info(f"✅ 设置基线结束时间（endDate-用于基线计算）: {session.session_baseline_end_time}")

                    # 🔥 BOSS修复：确保会话开始时间存在
                    if session.session_start_time is None:
                        session.session_start_time = timezone.now()
                        updated = True
                        logger.info(f"✅ 设置会话开始时间: {session.session_start_time}")
                    
                    # 步数权限检查
                    if permissions_status.get('steps', False) and session.session_baseline_steps is None:
                        session.session_baseline_steps = 0
                        updated = True
                        logger.info("✅ 步数权限新授权，设置基线为0")
                    elif not permissions_status.get('steps', False) and session.session_baseline_steps is not None:
                        session.session_baseline_steps = None
                        updated = True
                        logger.warning("❌ 步数权限被撤销，移除基线")
                    
                    # 距离权限检查
                    if permissions_status.get('distance', False) and session.session_baseline_distance is None:
                        session.session_baseline_distance = Decimal('0.0')
                        updated = True
                        logger.info("✅ 距离权限新授权，设置基线为0.0")
                    elif not permissions_status.get('distance', False) and session.session_baseline_distance is not None:
                        session.session_baseline_distance = None
                        updated = True
                        logger.warning("❌ 距离权限被撤销，移除基线")
                    
                    # 卡路里权限检查
                    if permissions_status.get('calories', False) and session.session_baseline_calories is None:
                        session.session_baseline_calories = 0
                        updated = True
                        logger.info("✅ 卡路里权限新授权，设置基线为0")
                    elif not permissions_status.get('calories', False) and session.session_baseline_calories is not None:
                        session.session_baseline_calories = None
                        updated = True
                        logger.warning("❌ 卡路里权限被撤销，移除基线")
                    
                    # 更新最后同步时间
                    session.last_sync_time = timezone.now()
                    updated = True
                    
                    if updated:
                        session.save()
                        logger.info("🔄 会话权限状态已更新")
                
                # 🔥 BOSS修复：记录操作到审计日志
                authorized_permissions = [k for k, v in permissions_status.items() if v]
                BaselineManager._log_baseline_operation(
                    user=user,
                    operation_type="baseline_init",
                    operation_name="初始化基线",
                    before_state={},
                    after_state={
                        'session_id': session.id,
                        'authorized_permissions': authorized_permissions,
                        'baseline_date': session.baseline_date.isoformat() if session.baseline_date else None,
                        'steps_baseline': session.session_baseline_steps,
                        'distance_baseline': float(session.session_baseline_distance) if session.session_baseline_distance else None,
                        'calories_baseline': session.session_baseline_calories
                    },
                    operation_success=True
                )
                
                result = {
                    'session_id': session.id,
                    'baseline_date': session.baseline_date,  # startDate-当天00:00
                    'session_baseline_end_time': session.session_baseline_end_time,  # endDate-用于基线计算
                    'authorized_permissions': authorized_permissions,
                    'baseline_values': {
                        'steps': session.session_baseline_steps,
                        'distance': float(session.session_baseline_distance) if session.session_baseline_distance else None,
                        'calories': session.session_baseline_calories
                    },
                    'session_start_time': session.session_start_time,
                    'last_sync_time': session.last_sync_time,
                    'message': f'基线初始化成功，已授权权限: {", ".join(authorized_permissions)}'
                }
                
                logger.info(f"✅ [BaselineManager] 基线初始化完成: {result['message']}")
                return result
                
            except Exception as e:
                logger.error(f"❌ [BaselineManager] 基线初始化失败: {str(e)}")
                
                # 记录失败操作
                BaselineManager._log_baseline_operation(
                    user=user,
                    operation_type="baseline_init",
                    operation_name="初始化基线",
                    before_state={},
                    after_state={},
                    operation_success=False,
                    error_message=str(e)
                )
                
                # 添加到离线处理队列
                enqueue_offline_request({
                    'type': 'baseline_init',
                    'user_id': user.id,
                    'device_id': device_id,
                    'permissions_status': permissions_status,
                    'reason': reason,
                    'timestamp': int(time.time())
                })
                
                raise Exception(f"基线初始化失败: {str(e)}")
    
    @staticmethod
    def reset_baseline_for_cross_day(user, new_totals, device_id, permissions=None):
        """
        🔥 BOSS修复：完整的跨天基线重置入口 - 严格遵循v13.0和离线处理规范
        集成权限新增基线确认和跨天任务状态更新
        
        核心修复：
        - 先处理昨天数据结算
        - 再重置今天基线为新加坡时间0:00到当前时间的数据
        - 只对已授权权限处理，未授权权限保持null
        
        Args:
            user: 用户对象
            new_totals: 新的健康数据总量（当前时间的HKStatisticsQuery结果）
            device_id: 设备唯一标识
            permissions: 权限状态
            
        Returns:
            dict: 重置结果
        """
        logger.info(f"🔥 BOSS修复：开始为用户 {user.email} 执行完整的跨天基线重置（v13.0+离线处理规范）")
        
        # 🔥 BOSS修复：获取新加坡时间今天0:00作为新基线时间
        singapore_today_start = BaselineManager.get_singapore_today_start()
        
        # 🔥 统一权限处理和基线计算逻辑
        baseline_values = BaselineManager._calculate_baseline_from_permissions(new_totals, permissions)
        
        logger.info(f"用户 {user.email} 跨天重置权限状态: {permissions or '无权限信息'}")
        logger.info(f"🔥 BOSS修复：新加坡时间0:00基线重置 - 步数: {baseline_values['steps']}, 距离: {baseline_values['distance']}, 卡路里: {baseline_values['calories']}")
        
        try:
            # 🔥 BOSS新增：步骤1 - 检测权限变化并补充基线
            logger.info(f"📋 步骤1：检测权限变化")
            permission_result = BaselineManager.check_permission_changes_and_update_baseline(
                user, device_id, permissions, new_totals
            )
            logger.info(f"权限变化检测结果: {permission_result.get('message', 'Unknown')}")
            
            # 🔥 BOSS新增：步骤2 - 跨天任务状态更新（基于昨天最终数据）
            logger.info(f"📋 步骤2：跨天任务状态更新")
            task_settlement_result = BaselineManager.process_cross_day_task_settlement(
                user, device_id, new_totals
            )
            logger.info(f"跨天任务更新结果: {task_settlement_result.get('message', 'Unknown')}")
            
            # 📋 步骤3：执行基线重置
            logger.info(f"📋 步骤3：执行基线重置")
            
            # 🔥 统一并发控制
            with user_lock(user.id):
                # 获取用户会话
                session = UnifiedUserSession.objects.filter(
                    user=user,
                    device_id=device_id,
                    is_active=True
                ).first()
                
                if not session:
                    logger.warning(f"用户 {user.email} 的健康数据会话不存在")
                    BaselineManager._enqueue_baseline_operation('reset', user.id, device_id, new_totals, permissions)
                    return {
                        'success': False,
                        'message': '健康数据会话不存在，请重新初始化 (已加入离线队列)',
                        'permission_changes': permission_result,
                        'task_settlement': task_settlement_result
                    }
                
                # 🔥 BOSS修复：统一归档处理（基于昨天23:59的最终数据）
                BaselineManager._archive_previous_day_data(user, session, new_totals)
                
                # 🔥 BOSS修复：统一基线更新（使用新加坡时间0:00作为基线时间）
                session.session_baseline_steps = baseline_values['steps']
                session.session_baseline_distance = baseline_values['distance']
                session.session_baseline_calories = baseline_values['calories']
                session.baseline_date = singapore_today_start  # 🔥 修复：使用新加坡时间0:00
                session.last_sync_time = timezone.now()
                session.save()
                
                logger.info(f"🔥 BOSS修复：用户 {user.email} 跨天基线重置成功（完整流程+v13.0规范）")
                
                return {
                    'success': True,
                    'message': '健康数据基线重置成功（含权限变化检测和任务状态更新+v13.0规范）',
                    'baseline_steps': session.session_baseline_steps,
                    'baseline_distance': session.session_baseline_distance,
                    'baseline_calories': session.session_baseline_calories,
                    'reset_date': session.baseline_date,
                    'singapore_today_start': singapore_today_start.isoformat(),
                    'permission_changes': permission_result,
                    'task_settlement': task_settlement_result
                }
            
        except Exception as e:
            logger.error(f"跨天基线重置失败，用户: {user.email}, 错误: {str(e)}", exc_info=True)
            BaselineManager._enqueue_baseline_operation('reset', user.id, device_id, new_totals, permissions)
            return {
                'success': False,
                'message': '健康数据基线重置失败，请稍后重试 (已加入离线队列)'
            }
    
    @staticmethod
    def check_baseline_status(user, device_id):
        """
        统一的基线状态检查
        
        Args:
            user: 用户对象
            device_id: 设备唯一标识
            
        Returns:
            dict: 基线状态信息
        """
        try:
            session = UnifiedUserSession.objects.filter(
                user=user,
                device_id=device_id,
                is_active=True
            ).first()
            
            if session:
                return {
                    'has_baseline': True,
                    'baseline_date': session.baseline_date,
                    'baseline_steps': session.session_baseline_steps,
                    'baseline_distance': session.session_baseline_distance,
                    'baseline_calories': session.session_baseline_calories,
                    'device_id': session.device_id
                }
            else:
                return {
                    'has_baseline': False,
                    'message': '未找到活跃的健康数据会话'
                }
                
        except Exception as e:
            logger.error(f"基线状态检查失败，用户: {user.email}, 错误: {str(e)}", exc_info=True)
            return {
                'has_baseline': False,
                'message': '基线状态检查失败'
            }
    
    @staticmethod
    def check_permission_changes_and_update_baseline(user, device_id, permissions, new_totals):
        """
        🔥 BOSS新增：检测权限变化并补充基线 - 严格遵循v13.0规范
        
        检测用户是否新授权了某些健康权限，如果是，则为这些新授权权限建立基线
        
        Args:
            user: 用户对象
            device_id: 设备ID
            permissions: 当前权限状态 {'steps': True, 'distance': True, 'calories': False}
            new_totals: 当前健康数据总量
            
        Returns:
            dict: 权限变化检测结果
        """
        logger.info(f"🔥 BOSS新增：检测用户 {user.email} 权限变化并补充基线（v13.0规范）")
        
        try:
            session = UnifiedUserSession.objects.filter(
                user=user,
                device_id=device_id,
                is_active=True
            ).first()
            
            if not session:
                return {
                    'success': False,
                    'message': '未找到活跃会话，无法检测权限变化',
                    'changes': []
                }
            
            changes_detected = []
            baseline_updated = False
            
            # 🔥 BOSS修复：检测步数权限变化
            if permissions.get('steps', False) and session.session_baseline_steps is None:
                session.session_baseline_steps = Decimal(str(new_totals.get('steps', 0)))
                changes_detected.append({
                    'permission': 'steps',
                    'action': 'newly_granted',
                    'baseline_set': session.session_baseline_steps
                })
                baseline_updated = True
                logger.info(f"🔥 v13.0规范：检测到步数权限新授权，设置基线: {session.session_baseline_steps}")
            
            # 🔥 BOSS修复：检测距离权限变化
            if permissions.get('distance', False) and session.session_baseline_distance is None:
                session.session_baseline_distance = Decimal(str(new_totals.get('distance', 0)))
                changes_detected.append({
                    'permission': 'distance',
                    'action': 'newly_granted',
                    'baseline_set': session.session_baseline_distance
                })
                baseline_updated = True
                logger.info(f"🔥 v13.0规范：检测到距离权限新授权，设置基线: {session.session_baseline_distance}")
            
            # 🔥 BOSS修复：检测卡路里权限变化
            if permissions.get('calories', False) and session.session_baseline_calories is None:
                session.session_baseline_calories = Decimal(str(new_totals.get('calories', 0)))
                changes_detected.append({
                    'permission': 'calories',
                    'action': 'newly_granted',
                    'baseline_set': session.session_baseline_calories
                })
                baseline_updated = True
                logger.info(f"🔥 v13.0规范：检测到卡路里权限新授权，设置基线: {session.session_baseline_calories}")
            
            # 保存变化
            if baseline_updated:
                session.save()
                logger.info(f"🔥 BOSS修复：用户 {user.email} 权限变化基线更新完成，变化数量: {len(changes_detected)}")
            
            return {
                'success': True,
                'message': f'权限变化检测完成，发现{len(changes_detected)}项变化',
                'changes': changes_detected,
                'baseline_updated': baseline_updated
            }
            
        except Exception as e:
            logger.error(f"权限变化检测失败，用户: {user.email}, 错误: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': '权限变化检测失败',
                'changes': []
            }

    @staticmethod
    def process_cross_day_task_settlement(user, device_id, yesterday_final_totals):
        """
        🔥 BOSS新增：跨天任务状态更新 - 严格遵循离线处理标准
        
        在新的一天开始时，基于昨天的最终健康数据更新任务完成状态
        
        Args:
            user: 用户对象
            device_id: 设备ID
            yesterday_final_totals: 昨天23:59的最终健康数据总量
            
        Returns:
            dict: 任务更新结果
        """
        logger.info(f"🔥 BOSS新增：处理用户 {user.email} 跨天任务状态更新（离线处理标准）")
        
        try:
            # 1. 获取昨天的日期
            yesterday = timezone.now().date() - timedelta(days=1)
            
            # 2. 查找昨天未完成的任务
            from tasks.models import Task
            from tasks.services.task_service import TaskService
            
            yesterday_tasks = Task.objects.filter(
                user=user,
                date=yesterday,
                status__in=['pending', 'in_progress']
            )
            
            if not yesterday_tasks.exists():
                return {
                    'success': True,
                    'message': '昨天无待处理任务',
                    'tasks_updated': 0
                }
            
            # 3. 基于昨天最终数据更新任务状态
            updated_tasks = []
            for task in yesterday_tasks:
                try:
                    # 使用TaskService更新单个任务状态
                    update_result = TaskService.update_task_with_health_data(
                        task=task,
                        health_data=yesterday_final_totals,
                        is_cross_day_settlement=True
                    )
                    
                    if update_result.get('updated', False):
                        updated_tasks.append({
                            'task_id': task.id,
                            'task_type': task.task_type,
                            'old_status': task.status,
                            'new_status': update_result.get('new_status'),
                            'completion_progress': update_result.get('progress', 0)
                        })
                        
                except Exception as task_error:
                    logger.error(f"更新任务 {task.id} 失败: {str(task_error)}")
                    continue
            
            logger.info(f"🔥 BOSS修复：用户 {user.email} 跨天任务更新完成，更新任务数: {len(updated_tasks)}")
            
            return {
                'success': True,
                'message': f'跨天任务状态更新完成，更新{len(updated_tasks)}个任务',
                'tasks_updated': len(updated_tasks),
                'updated_task_details': updated_tasks,
                'settlement_date': yesterday.isoformat()
            }
            
        except Exception as e:
            logger.error(f"跨天任务状态更新失败，用户: {user.email}, 错误: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': '跨天任务状态更新失败，但基线重置将继续进行',
                'tasks_updated': 0
            }

    @staticmethod
    def _calculate_baseline_from_permissions(totals, permissions):
        """
        🔥 BOSS修复: 权限独立基线计算，严格遵循v13.0规范
        只对已授权的权限设置基线，未授权权限跳过处理（保持null）

        Args:
            totals (dict): 健康数据总量 e.g. {'steps': 100, 'distance': 1.2, 'calories': 50}
            permissions (dict): 权限状态 e.g. {'steps': True, 'distance': True, 'calories': False}

        Returns:
            dict: 计算后的基线值，未授权权限为None
        """
        # 如果前端没有提供权限信息，则所有基线都跳过处理
        if not permissions:
            logger.warning("🔥 BOSS修复：未提供权限信息，所有基线跳过处理（v13.0规范）")
            return {'steps': None, 'distance': None, 'calories': None}

        # 🔥 BOSS核心修复：只对已授权权限设置基线，未授权权限为None（数据库中保持null）
        baseline_values = {}
        
        # 步数基线处理
        if permissions.get('steps', False):
            baseline_values['steps'] = Decimal(str(totals.get('steps', 0)))
            logger.info(f"🔥 v13.0规范：步数权限已授权，设置基线: {baseline_values['steps']}")
        else:
            baseline_values['steps'] = None
            logger.info("🔥 v13.0规范：步数权限未授权，跳过基线设置（保持null）")
            
        # 距离基线处理  
        if permissions.get('distance', False):
            baseline_values['distance'] = Decimal(str(totals.get('distance', 0)))
            logger.info(f"🔥 v13.0规范：距离权限已授权，设置基线: {baseline_values['distance']}")
        else:
            baseline_values['distance'] = None
            logger.info("🔥 v13.0规范：距离权限未授权，跳过基线设置（保持null）")
            
        # 卡路里基线处理
        if permissions.get('calories', False):
            baseline_values['calories'] = Decimal(str(totals.get('calories', 0)))
            logger.info(f"🔥 v13.0规范：卡路里权限已授权，设置基线: {baseline_values['calories']}")
        else:
            baseline_values['calories'] = None
            logger.info("🔥 v13.0规范：卡路里权限未授权，跳过基线设置（保持null）")
            
        return baseline_values
    
    @staticmethod
    def _handle_device_session(user, device_id, platform, health_source, baseline_values, 
                              singapore_today_start, session_start_time):
        """
        🔥 BOSS修复：统一设备会话处理 - 严格遵循v13.0规范时间管理
        """
        try:
            # 🔥 BOSS核心修复：严谨判断是否需要新会话
            is_new_session_needed = BaselineManager._is_new_session_required(
                user, device_id, platform, health_source
            )
            
            # 🔥 BOSS修复：检查并强制结束所有活跃会话，避免unique constraint违反
            existing_active_sessions = UnifiedUserSession.objects.filter(
                user=user,
                is_active=True
            )
            
            if existing_active_sessions.exists():
                # 强制结束所有活跃会话，记录结束原因
                updated_count = existing_active_sessions.update(
                    is_active=False,
                    logout_time=timezone.now(),
                    logout_reason='app_restart'
                )
                logger.info(f"🔥 BOSS修复：强制结束用户 {user.email} 的 {updated_count} 个活跃会话，避免会话冲突")
            
            # 🔥 BOSS修复：根据会话状态决定时间设置（严格遵循v13.0规范）
            if is_new_session_needed:
                logger.info(f"🆕 用户 {user.email} 开始新会话（v13.0规范）")
                
                # 🔥 BOSS修复：创建新会话（严格遵循v13.0时间规范）
                session = UnifiedUserSession.objects.create(
                    user=user,
                    device_id=device_id,
                    platform=platform,
                    health_source=health_source,
                    is_active=True,
                    session_baseline_steps=baseline_values['steps'],
                    session_baseline_distance=baseline_values['distance'],
                    session_baseline_calories=baseline_values['calories'],
                    baseline_date=singapore_today_start,  # 🔥 修复：新加坡时间0:00
                    session_start_time=session_start_time,  # 🔥 修复：会话开始时间
                    last_sync_time=timezone.now()
                )
                created = True
                
            else:
                # 会话延续：获取现有会话（此时应该只有一个活跃会话）
                session = UnifiedUserSession.objects.filter(
                    user=user,
                    device_id=device_id,
                    is_active=True
                ).first()
                
                if not session:
                    # 如果没有活跃会话，创建新会话
                    logger.info(f"🆕 用户 {user.email} 无活跃会话，创建新会话")
                    session = UnifiedUserSession.objects.create(
                        user=user,
                        device_id=device_id,
                        platform=platform,
                        health_source=health_source,
                        is_active=True,
                        session_baseline_steps=baseline_values['steps'],
                        session_baseline_distance=baseline_values['distance'],
                        session_baseline_calories=baseline_values['calories'],
                        baseline_date=singapore_today_start,
                        session_start_time=session_start_time,
                        last_sync_time=timezone.now()
                    )
                    created = True
                else:
                    # 🔥 BOSS修复：确保会话延续时时间字段有正确的值（v13.0规范）
                    now = timezone.now()
                    needs_save = False
                    
                    # 🔥 修复：baseline_date应该是新加坡时间0:00，不是会话开始时间
                    if session.baseline_date is None:
                        session.baseline_date = singapore_today_start
                        needs_save = True
                        logger.info(f"🔥 修复会话 {session.id} 的baseline_date为新加坡时间0:00: {session.baseline_date}")
                    
                    if session.session_start_time is None:
                        session.session_start_time = session.login_time or now
                        needs_save = True
                        logger.info(f"🔥 修复会话 {session.id} 的session_start_time: {session.session_start_time}")
                    
                    session.last_sync_time = now
                    needs_save = True
                    
                    if needs_save:
                        session.save()
                    
                    created = False
                    logger.info(f"🔄 用户 {user.email} 会话延续（v13.0规范）")
            
            action = "创建新会话" if created else "延续会话"
            logger.info(f"✅ 已{action}用户 {user.email} 设备: {device_id}（v13.0规范）")
            logger.info(f"📅 会话开始时间: {session.session_start_time}")
            logger.info(f"⏰ 基线确定时间（新加坡0:00）: {session.baseline_date}")
            logger.info(f"🔄 最后同步时间: {session.last_sync_time}")
            
            return {
                'success': True,
                'session': session,
                'session_id': session.id,
                'baseline_date': session.baseline_date,
                'session_start_time': session.session_start_time,
                'is_new_session': created
            }
            
        except Exception as e:
            logger.error(f"设备会话处理失败，用户: {user.email}, 错误: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': '设备会话处理失败'
            }
    
    @staticmethod
    def _archive_previous_day_data(user, session, yesterday_final_totals):
        """
        🔥 BOSS修复：统一的数据归档逻辑 - 严格遵循离线处理规范
        """
        try:
            yesterday = timezone.now().date() - timedelta(days=1)
            
            # 检查是否已存在昨日的归档数据
            existing_archive = DailyHealthSnapshot.objects.filter(
                user=user,
                snapshot_date=yesterday
            ).first()
            
            if not existing_archive:
                # 🔥 BOSS修复：创建昨日数据归档（使用昨天23:59的最终数据）
                DailyHealthSnapshot.objects.create(
                    user=user,
                    snapshot_date=yesterday,
                    snapshot_steps=yesterday_final_totals.get('steps') if session.session_baseline_steps is not None else None,
                    snapshot_distance=yesterday_final_totals.get('distance') if session.session_baseline_distance is not None else None,
                    snapshot_calories=yesterday_final_totals.get('calories') if session.session_baseline_calories is not None else None,
                    device_id=session.device_id,
                    snapshot_type='cross_day_archive'
                )
                
                logger.info(f"🔥 BOSS修复：已归档用户 {user.email} 昨日健康数据（离线处理规范）")
            
        except Exception as e:
            logger.error(f"归档健康数据失败，用户: {user.email}, 错误: {str(e)}", exc_info=True) 
    
    @staticmethod
    def _enqueue_baseline_operation(operation_type, user_id, device_id, totals, permissions):
        """
        🔥 BOSS修复：统一的离线队列处理 - 严格遵循离线处理标准
        """
        try:
            enqueue_offline_request({
                'type': f'baseline_{operation_type}',
                'user_id': user_id,
                'device_id': device_id,
                'totals': totals,
                'permissions': permissions,
                'timestamp': int(time.time())
            })
            logger.info(f"🔥 BOSS修复：基线{operation_type}操作已加入离线队列（离线处理标准）")
        except Exception as e:
            logger.error(f"离线队列操作失败: {str(e)}", exc_info=True)

    @staticmethod
    def _is_new_session_required(user, device_id, platform=None, health_source=None):
        """
        🔥 BOSS修复：严谨判断是否需要创建新会话 - 严格遵循v13.0规范
        根据BOSS要求，会话开始/结束定义更加严格
        处理None值情况，确保APP重启正确创建新会话
        """
        try:
            # 查找用户的活跃会话
            existing_session = UnifiedUserSession.objects.filter(
                user=user,
                device_id=device_id,
                is_active=True
            ).first()
            
            if not existing_session:
                logger.info(f"🔥 用户 {user.email} 设备 {device_id} 无活跃会话，需要创建新会话")
                return True
            
            now = timezone.now()
            
            # 🔥 BOSS关键修复：处理旧会话时间字段为None的情况
            if existing_session.session_start_time is None:
                logger.warning(f"🔥 用户 {user.email} 会话开始时间为None，强制创建新会话")
                # 修复旧会话：设置会话开始时间为创建时间
                existing_session.session_start_time = existing_session.login_time or now
                existing_session.baseline_date = BaselineManager.get_singapore_today_start()  # 🔥 修复：新加坡时间0:00
                existing_session.is_active = False  # 标记为非活跃，创建新会话
                existing_session.logout_time = timezone.now()
                existing_session.logout_reason = 'session_time_field_missing'
                existing_session.save()
                return True
            
            if existing_session.last_sync_time is None:
                logger.warning(f"🔥 用户 {user.email} 最后同步时间为None，强制创建新会话")
                # 修复旧会话：设置最后同步时间
                existing_session.last_sync_time = existing_session.session_start_time or now
                existing_session.is_active = False  # 标记为非活跃，创建新会话
                existing_session.logout_time = timezone.now()
                existing_session.logout_reason = 'last_sync_time_missing'
                existing_session.save()
                return True
                
            # 检查会话是否过期（24小时自动过期）
            session_age = now - existing_session.session_start_time
            if session_age.total_seconds() > 24 * 3600:  # 24小时
                logger.warning(f"🔥 用户 {user.email} 会话已超过24小时 ({session_age.total_seconds():.0f}s)，强制创建新会话")
                # 标记旧会话为非活跃
                existing_session.is_active = False
                existing_session.logout_time = timezone.now()
                existing_session.logout_reason = 'session_expired'
                existing_session.save()
                return True
                
            # 🔥 BOSS核心修复：检查基线日期是否是今天（v13.0规范核心要求）
            singapore_tz = BaselineManager.get_singapore_timezone()
            today_start = BaselineManager.get_singapore_today_start()
            
            if existing_session.baseline_date:
                # 检查基线日期是否是今天（新加坡时间）
                baseline_date_sg = existing_session.baseline_date.astimezone(singapore_tz).date()
                today_sg = today_start.astimezone(singapore_tz).date()
                
                if baseline_date_sg != today_sg:
                    logger.warning(f"🔥 用户 {user.email} 基线日期({baseline_date_sg}) != 今天({today_sg})，创建新会话（v13.0规范）")
                    existing_session.is_active = False
                    existing_session.logout_time = timezone.now()
                    existing_session.logout_reason = 'baseline_date_mismatch'
                    existing_session.save()
                    return True
            else:
                logger.warning(f"🔥 用户 {user.email} 基线日期为None，创建新会话（v13.0规范）")
                existing_session.is_active = False
                existing_session.logout_time = timezone.now()
                existing_session.logout_reason = 'baseline_date_missing'
                existing_session.save()
                return True
            
            # 🔥 BOSS核心修复：检查会话连续性（遵循v2.0核心概念：严格的会话管理）
            inactive_duration = now - existing_session.last_sync_time

            # 🔥 修复：严格的会话连续性判断 - 超过30分钟强制创建新会话
            # 根据会话核心概念v2.0："用户启动App时，立即创建一个新会话"
            if inactive_duration.total_seconds() > 30 * 60:  # 30分钟
                if inactive_duration.total_seconds() > 4 * 3600:  # 4小时
                    logger.warning(f"🔥 用户 {user.email} 会话中断超过4小时 ({inactive_duration.total_seconds():.0f}s)，创建新会话（v2.0规范）")
                    logout_reason = 'session_timeout'
                else:
                    logger.warning(f"🔄 用户 {user.email} 会话中断超过30分钟 ({inactive_duration.total_seconds():.0f}s)，遵循v2.0概念创建新会话")
                    logout_reason = 'session_break'

                existing_session.is_active = False
                existing_session.logout_time = timezone.now()
                existing_session.logout_reason = logout_reason
                existing_session.save()
                return True
            
            # 检查设备信息变化（可能是APP重装或设备更换）
            if platform and existing_session.platform != platform:
                logger.info(f"🔥 用户 {user.email} 设备平台变化 {existing_session.platform} -> {platform}，创建新会话")
                existing_session.is_active = False
                existing_session.logout_time = timezone.now()
                existing_session.logout_reason = 'platform_change'
                existing_session.save()
                return True
                
            if health_source and existing_session.health_source != health_source:
                logger.info(f"🔥 用户 {user.email} 健康数据源变化 {existing_session.health_source} -> {health_source}，创建新会话")
                existing_session.is_active = False
                existing_session.logout_time = timezone.now()
                existing_session.logout_reason = 'health_source_change'
                existing_session.save()
                return True
            
            logger.info(f"🔥 用户 {user.email} 会话延续（v13.0规范），会话开始时间: {existing_session.session_start_time}，最后活动: {inactive_duration.total_seconds():.0f}s前")
            return False
            
        except Exception as e:
            logger.error(f"检查会话状态失败: {str(e)}", exc_info=True)
            # 出错时安全策略：创建新会话
            return True

    @staticmethod  
    def _force_create_new_session(user, device_id, reason="强制创建"):
        """
        强制创建新会话（用于APP重启等场景）
        """
        try:
            # 结束所有该用户的活跃会话
            affected_count = UnifiedUserSession.objects.filter(
                user=user,
                device_id=device_id,
                is_active=True
            ).update(
                is_active=False,
                logout_time=timezone.now(),
                logout_reason=f'force_create_session_{reason}'
            )
            
            logger.info(f"强制为用户 {user.email} 创建新会话，原因: {reason}，已结束 {affected_count} 个活跃会话")
            return True
            
        except Exception as e:
            logger.error(f"强制创建新会话失败: {str(e)}")
            return False

    @staticmethod
    def fix_session_time_fields(user=None, device_id=None):
        """
        🔥 BOSS新增：修复会话时间字段 - 批量修复历史数据
        
        Args:
            user: 可选，指定用户
            device_id: 可选，指定设备
            
        Returns:
            dict: 修复结果统计
        """
        logger.info(f"🔥 BOSS新增：开始修复会话时间字段（v13.0规范批量修复）")
        
        try:
            # 构建查询条件
            query_filter = {'is_active': True}
            if user:
                query_filter['user'] = user
            if device_id:
                query_filter['device_id'] = device_id
            
            sessions_to_fix = UnifiedUserSession.objects.filter(**query_filter)
            
            fixed_count = 0
            now = timezone.now()
            singapore_today_start = BaselineManager.get_singapore_today_start()
            
            for session in sessions_to_fix:
                needs_save = False
                fixed_fields = []
                
                # 修复session_start_time
                if session.session_start_time is None:
                    session.session_start_time = session.login_time or now
                    fixed_fields.append('session_start_time')
                    needs_save = True
                
                # 🔥 BOSS修复：baseline_date应该是新加坡时间0:00
                if session.baseline_date is None:
                    session.baseline_date = singapore_today_start
                    fixed_fields.append('baseline_date')
                    needs_save = True
                
                # 修复last_sync_time
                if session.last_sync_time is None:
                    session.last_sync_time = session.session_start_time or session.login_time or now
                    fixed_fields.append('last_sync_time')
                    needs_save = True
                
                if needs_save:
                    session.save()
                    fixed_count += 1
                    logger.info(f"🔥 修复会话 {session.id} (用户: {session.user.email}) 的字段: {', '.join(fixed_fields)}")
            
            logger.info(f"🔥 BOSS修复：会话时间字段修复完成，修复会话数: {fixed_count}")
            
            return {
                'success': True,
                'message': f'会话时间字段修复完成',
                'fixed_sessions': fixed_count,
                'singapore_today_start_used': singapore_today_start.isoformat()
            }
            
        except Exception as e:
            logger.error(f"会话时间字段修复失败: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': '会话时间字段修复失败',
                'fixed_sessions': 0
            }

    @staticmethod
    def _log_baseline_operation(user, operation_type, operation_name, before_state, after_state, operation_success, error_message=None):
        """
        记录基线操作到审计日志
        """
        try:
            # 这里可以添加到健康数据审计日志
            # 暂时使用标准日志记录
            log_entry = {
                'user_id': user.id,
                'operation_type': operation_type,
                'operation_name': operation_name,
                'before_state': before_state,
                'after_state': after_state,
                'operation_success': operation_success,
                'error_message': error_message
            }
            
            if operation_success:
                logger.info(f"[审计] {operation_name} 成功 - 用户: {user.email}, 状态: {after_state}")
            else:
                logger.error(f"[审计] {operation_name} 失败 - 用户: {user.email}, 错误: {error_message}")
            
        except Exception as e:
            logger.error(f"记录操作日志失败: {str(e)}")

    @staticmethod
    def _get_actual_baseline_from_context(permission_type, baseline_date_singapore, session_start_time_singapore):
        """
        🔥 v14.1核心修复：从前端上下文获取实际HKStatisticsQuery基线数据
        
        修复说明：
        - 原逻辑返回None导致显示"10步"问题（使用累计总量）
        - 新逻辑专注获取前端HKStatisticsQuery的实际结果
        - 不使用缓存，确保基线数据的实时性和准确性
        
        优先级：
        1. 线程本地存储（会话创建时前端传递的baseline_data）
        2. 当前请求上下文（request中的baseline_data）
        3. 返回保守默认值0而非None，避免显示累计总量
        
        Args:
            permission_type: 权限类型 ('steps', 'distance', 'calories')
            baseline_date_singapore: 基线日期（新加坡时间）
            session_start_time_singapore: 会话开始时间（新加坡时间）
            
        Returns:
            int/float: 实际基线值（绝不返回None）
        """
        try:
            # 🔥 优先级1：从线程本地存储获取前端HKStatisticsQuery结果
            import threading
            
            thread_local = getattr(threading.current_thread(), 'baseline_context', None)
            if thread_local and hasattr(thread_local, 'frontend_baseline_data'):
                frontend_data = thread_local.frontend_baseline_data
                if frontend_data and permission_type in frontend_data:
                    baseline_value = frontend_data[permission_type]
                    logger.info(f"🎯 v14.1修复：从前端HKStatisticsQuery获取{permission_type}基线: {baseline_value}")
                    return baseline_value
            
            # 🔥 优先级2：从当前请求上下文获取baseline_data
            try:
                current_request = getattr(threading.current_thread(), 'request', None)
                if current_request and hasattr(current_request, 'baseline_data'):
                    request_data = current_request.baseline_data
                    if request_data and permission_type in request_data:
                        baseline_value = request_data[permission_type]
                        logger.info(f"🎯 v14.1修复：从请求上下文获取{permission_type}基线: {baseline_value}")
                        return baseline_value
            except Exception as context_error:
                logger.debug(f"请求上下文获取失败: {context_error}")
            
            # 🔥 关键修复：返回0而非None，避免显示累计总量作为增量
            logger.warning(f"🔧 v14.1临时修复：{permission_type}基线使用默认值0，避免显示累计总量")
            logger.warning(f"   时间范围: {baseline_date_singapore} 至 {session_start_time_singapore}")
            logger.warning(f"   解决方案：前端应在会话创建时通过getSessionBaseline()提供baseline_data")
            
            return 0  # 🔥 核心修复：返回0而非None，确保显示会话增量而非累计总量
            
        except Exception as e:
            logger.error(f"获取实际基线数据失败: {str(e)}")
            return 0  # 🔥 异常情况也返回0，确保不显示累计总量

    @staticmethod
    def set_frontend_baseline_data(baseline_data):
        """
        🔥 v14.0核心修复：设置前端查询的基线数据到当前线程
        
        这个方法由前端调用基线查询API时使用，将实际查询的基线数据
        传递给后续的会话创建逻辑。
        
        Args:
            baseline_data: dict, 包含各类型基线数据
                {
                    'steps': 实际步数基线,
                    'distance': 实际距离基线,
                    'calories': 实际卡路里基线
                }
        """
        try:
            import threading
            
            # 获取当前线程
            current_thread = threading.current_thread()
            
            # 确保线程有baseline_context属性
            if not hasattr(current_thread, 'baseline_context'):
                current_thread.baseline_context = threading.local()
            
            # 设置前端基线数据
            current_thread.baseline_context.frontend_baseline_data = baseline_data
            
            logger.info(f"🔥 v14.0修复：已设置前端基线数据到线程: {baseline_data}")
            
        except Exception as e:
            logger.error(f"设置前端基线数据失败: {str(e)}")

    @staticmethod
    def clear_frontend_baseline_data():
        """
        🔥 v14.0核心修复：清除当前线程的基线数据
        
        在处理完会话创建后调用，避免数据污染。
        """
        try:
            import threading
            
            current_thread = threading.current_thread()
            if hasattr(current_thread, 'baseline_context'):
                current_thread.baseline_context.frontend_baseline_data = None
                logger.info("🔥 v14.0修复：已清除线程基线数据")
                
        except Exception as e:
            logger.error(f"清除前端基线数据失败: {str(e)}")

    # 🚫 v14.1合规性修复：移除基线数据缓存方法
    # 根据v14.1文档要求："健康数据不使用缓存，包括基线数据"
    # 此方法已被移除，确保基线数据的实时性和准确性 