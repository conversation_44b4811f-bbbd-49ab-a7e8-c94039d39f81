@staticmethod
def log_session_continuity_check(user, device_id, need_new_session, session_data, reason, duration_ms, ip_address=None, user_agent=None):
    """
    记录会话连续性检查操作
    🔥 BOSS需求：审计会话连续性检查的完整过程
    """
    try:
        audit_data = {
            'device_id': device_id,
            'need_new_session': need_new_session,
            'reason': reason,
            'session_data': session_data,
            'duration_ms': duration_ms,
            'ip_address': ip_address,
            'user_agent': user_agent
        }
        
        HealthDataAuditLog.objects.create(
            user=user,
            operation_type='session_continuity_check',
            operation_data=audit_data,
            result_data={'status': 'success'},
            ip_address=ip_address,
            user_agent=user_agent
        )
        
    except Exception as e:
        logger.error(f"记录会话连续性检查审计日志失败: {str(e)}") 