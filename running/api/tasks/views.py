from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from drf_spectacular.utils import extend_schema, inline_serializer, OpenApiResponse, OpenApiParameter
from rest_framework import serializers, status
from .services import TasksDashboardService
import logging
import time
import json
import os
import datetime
from django.core.cache import cache
from django.utils import timezone
from datetime import timedelta
from core.utils.api_utils import ApiResponse
from authentication.ratelimit import method_ratelimit

# 设置tasks专用日志
tasks_logger = logging.getLogger('tasks.api')

# 确保日志目录存在
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# 设置tasks日志文件处理器
tasks_log_file = os.path.join(log_dir, 'tasks_api.log')
file_handler = logging.FileHandler(tasks_log_file)
file_handler.setLevel(logging.DEBUG)

# 设置格式
formatter = logging.Formatter('%(levelname)s %(asctime)s %(name)s %(process)d %(thread)d %(message)s')
file_handler.setFormatter(formatter)

# 将处理器添加到logger
tasks_logger.addHandler(file_handler)
tasks_logger.setLevel(logging.DEBUG)

# 记录启动信息
tasks_logger.info(f"Tasks API module initialized at {datetime.datetime.now().isoformat()}")


class TasksDashboardViewSet(ViewSet):
    """
    Tasks页面聚合接口视图集
    
    提供Tasks页面所需的所有聚合数据，包括：
    - 顶部区域：用户基本信息和VIP状态
    - 倒计时区域：任务重置倒计时和动态提示
    - 每日任务区域：每日任务列表和进度
    - 附加任务区域：附加任务列表和激活状态
    - 今日摘要：收益统计和健康数据
    """
    permission_classes = [IsAuthenticated]

    @extend_schema(
        tags=["Tasks聚合API"],
        summary="获取Tasks页面聚合数据",
        description="""
        获取Tasks页面完整的聚合数据，一次性返回所有页面模块所需的数据。
        
        包含的数据模块：
        1. 顶部区域：用户头像、昵称、等级、VIP状态
        2. 倒计时区域：任务重置倒计时、动态提示文本
        3. 每日任务区域：任务列表、完成进度、奖励信息
        4. 附加任务区域：可激活任务、已激活任务、限制说明
        5. 今日摘要：收益统计、健康数据、连续天数
        """,
        parameters=[
            OpenApiParameter(
                name="include_meta", 
                description="是否包含元信息（数据完整性、处理时间等），用于调试", 
                required=False, 
                type=str
            ),
        ],
        responses={
            200: OpenApiResponse(
                response=inline_serializer(
                    name='TasksDashboardResponse',
                    fields={
                        'code': serializers.IntegerField(help_text="响应状态码"),
                        'message': serializers.CharField(help_text="响应消息"),
                        'data': inline_serializer(
                            name='TasksDashboardData',
                            fields={
                                'top_section': inline_serializer(
                                    name='TopSectionData',
                                    fields={
                                        'user_info': inline_serializer(
                                            name='UserInfo',
                                            fields={
                                                'user_id': serializers.CharField(),
                                                'avatar': serializers.CharField(allow_null=True),
                                                'username': serializers.CharField(),
                                                'member_level': inline_serializer(
                                                    name='MemberLevel',
                                                    fields={
                                                        'name': serializers.CharField(),
                                                        'level': serializers.IntegerField(),
                                                        'progress_percentage': serializers.FloatField(),
                                                    }
                                                ),
                                            }
                                        ),
                                        'vip_status': inline_serializer(
                                            name='VipStatus',
                                            fields={
                                                'has_vip': serializers.BooleanField(),
                                                'vip_level_name': serializers.CharField(allow_null=True),
                                                'vip_level': serializers.IntegerField(allow_null=True),
                                                'swmt_bonus_percentage': serializers.FloatField(allow_null=True),
                                                'exp_bonus_percentage': serializers.FloatField(allow_null=True),
                                            }
                                        ),
                                    }
                                ),
                                'countdown_section': inline_serializer(
                                    name='CountdownSectionData',
                                    fields={
                                        'countdown_info': inline_serializer(
                                            name='CountdownInfo',
                                            fields={
                                                'hours_until_reset': serializers.IntegerField(),
                                                'minutes_until_reset': serializers.IntegerField(),
                                                'seconds_until_reset': serializers.IntegerField(),
                                                'reset_time_singapore': serializers.DateTimeField(),
                                                'current_time_singapore': serializers.DateTimeField(),
                                            }
                                        ),
                                        'dynamic_tip': inline_serializer(
                                            name='DynamicTip',
                                            fields={
                                                'tip_text': serializers.CharField(),
                                                'tip_type': serializers.CharField(),
                                                'show_icon': serializers.BooleanField(),
                                            }
                                        ),
                                    }
                                ),
                                'daily_tasks_section': inline_serializer(
                                    name='DailyTasksSectionData',
                                    fields={
                                        'summary': inline_serializer(
                                            name='DailyTasksSummary',
                                            fields={
                                                'total_tasks': serializers.IntegerField(),
                                                'completed_tasks': serializers.IntegerField(),
                                                'completion_percentage': serializers.IntegerField(),
                                                'all_tasks_completed': serializers.BooleanField(),
                                            }
                                        ),
                                        'tasks': serializers.ListField(
                                            child=inline_serializer(
                                                name='DailyTask',
                                                fields={
                                                    'id': serializers.CharField(),
                                                    'task_id': serializers.CharField(),
                                                    'name': serializers.CharField(),
                                                    'description': serializers.CharField(),
                                                    'task_type': serializers.CharField(),
                                                    'status': serializers.CharField(),
                                                    'progress': serializers.IntegerField(),
                                                    'target': serializers.IntegerField(),
                                                    'progress_percentage': serializers.IntegerField(),
                                                    'icon_url': serializers.CharField(allow_null=True),
                                                    'rewards': inline_serializer(
                                                        name='TaskRewards',
                                                        fields={
                                                            'base_swmt': serializers.CharField(),
                                                            'base_exp': serializers.IntegerField(),
                                                            'vip_bonus_swmt': serializers.CharField(),
                                                            'vip_bonus_exp': serializers.IntegerField(),
                                                            'total_swmt': serializers.CharField(),
                                                            'total_exp': serializers.IntegerField(),
                                                        }
                                                    ),
                                                    'can_complete': serializers.BooleanField(),
                                                    'completion_time': serializers.DateTimeField(allow_null=True),
                                                }
                                            )
                                        ),
                                    }
                                ),
                                'addon_tasks_section': inline_serializer(
                                    name='AddonTasksSectionData',
                                    fields={
                                        'summary': inline_serializer(
                                            name='AddonTasksSummary',
                                            fields={
                                                'active_addon_tasks': serializers.IntegerField(),
                                                'max_addon_tasks': serializers.IntegerField(),
                                                'can_activate_more': serializers.BooleanField(),
                                            }
                                        ),
                                        'active_tasks': serializers.ListField(
                                            child=inline_serializer(
                                                name='ActiveAddonTask',
                                                fields={
                                                    'id': serializers.CharField(),
                                                    'task_id': serializers.CharField(),
                                                    'name': serializers.CharField(),
                                                    'description': serializers.CharField(),
                                                    'task_type': serializers.CharField(),
                                                    'status': serializers.CharField(),
                                                    'icon_url': serializers.CharField(allow_null=True),
                                                    'bonus_multiplier': serializers.FloatField(),
                                                    'activation_time': serializers.DateTimeField(),
                                                }
                                            )
                                        ),
                                        'available_tasks': serializers.ListField(
                                            child=inline_serializer(
                                                name='AvailableAddonTask',
                                                fields={
                                                    'id': serializers.CharField(),
                                                    'task_id': serializers.CharField(),
                                                    'name': serializers.CharField(),
                                                    'description': serializers.CharField(),
                                                    'task_type': serializers.CharField(),
                                                    'icon_url': serializers.CharField(allow_null=True),
                                                    'bonus_multiplier': serializers.FloatField(),
                                                    'can_activate': serializers.BooleanField(),
                                                }
                                            )
                                        ),
                                    }
                                ),
                                'today_summary_section': inline_serializer(
                                    name='TodaySummarySectionData',
                                    fields={
                                        'earned_today': inline_serializer(
                                            name='EarnedToday',
                                            fields={
                                                'swmt': serializers.CharField(),
                                                'exp': serializers.IntegerField(),
                                            }
                                        ),
                                        'health_data': inline_serializer(
                                            name='HealthData',
                                            fields={
                                                'steps': serializers.IntegerField(),
                                                'distance': serializers.FloatField(),
                                                'calories': serializers.IntegerField(),
                                            },
                                            allow_null=True
                                        ),
                                        'current_streak': serializers.IntegerField(),
                                        'next_tasks': serializers.ListField(
                                            child=inline_serializer(
                                                name='NextTask',
                                                fields={
                                                    'id': serializers.CharField(),
                                                    'name': serializers.CharField(),
                                                    'type': serializers.CharField(),
                                                    'icon': serializers.CharField(allow_null=True),
                                                    'total_swmt': serializers.CharField(),
                                                    'total_exp': serializers.IntegerField(),
                                                }
                                            )
                                        ),
                                        'completion_status': inline_serializer(
                                            name='CompletionStatus',
                                            fields={
                                                'all_tasks_completed': serializers.BooleanField(),
                                                'completion_percentage': serializers.IntegerField(),
                                                'total_tasks': serializers.IntegerField(),
                                                'completed_tasks': serializers.IntegerField(),
                                            }
                                        ),
                                    }
                                ),
                            }
                        ),
                        'timestamp': serializers.IntegerField(help_text="响应时间戳"),
                        '_meta': inline_serializer(
                            name='MetaInfo',
                            fields={
                                'data_completeness': serializers.CharField(),
                                'processing_time_ms': serializers.IntegerField(),
                                'cache_used': serializers.BooleanField(),
                            },
                            allow_null=True
                        ),
                    }
                ),
                description="Tasks页面聚合数据获取成功"
            ),
            500: OpenApiResponse(
                description="服务器内部错误",
                response=inline_serializer(
                    name='TasksDashboardErrorResponse',
                    fields={
                        'code': serializers.IntegerField(),
                        'message': serializers.CharField(),
                        'details': serializers.CharField(),
                        'timestamp': serializers.IntegerField(),
                    }
                )
            )
        }
    )
    def list(self, request):
        """
        获取Tasks页面的聚合数据
        
        这个接口聚合了Tasks页面所有模块需要的数据，避免前端发起多个API请求。
        """
        user = request.user
        request_id = getattr(request, 'request_id', '-')  # 假设有RequestIDMiddleware
        start_time = time.time()
        
        # 增强日志，添加更多追踪信息
        tasks_logger.info(f"[{request_id}] TasksDashboardViewSet: Tasks dashboard data requested by user {user.id} ({user.email}). IP: {request.META.get('REMOTE_ADDR')}")
        
        try:
            # 传递整个request对象给服务层
            dashboard_data = TasksDashboardService.get_tasks_dashboard_data(request)
            
            # 数据完整性检查和详细记录
            data_quality_report = {
                'top_section_complete': bool(dashboard_data.get('top_section', {}).get('user_info', {}).get('user_id')),
                'countdown_section_complete': 'countdown_info' in dashboard_data.get('countdown_section', {}),
                'daily_tasks_section_complete': 'summary' in dashboard_data.get('daily_tasks_section', {}),
                'addon_tasks_section_complete': 'summary' in dashboard_data.get('addon_tasks_section', {}),
                'today_summary_section_complete': 'earned_today' in dashboard_data.get('today_summary_section', {}),
            }
            
            complete_modules = sum(data_quality_report.values())
            total_modules = len(data_quality_report)
            
            tasks_logger.info(f"[{request_id}] Data quality: {complete_modules}/{total_modules} modules complete - {data_quality_report}")
            
            # 检查earned_today数据
            if 'today_summary_section' in dashboard_data and 'earned_today' in dashboard_data['today_summary_section']:
                earned_today = dashboard_data['today_summary_section']['earned_today']
                tasks_logger.info(f"[{request_id}] TasksDashboardViewSet: User {user.email} earned_today data: SWMT={earned_today.get('swmt', '0')}, XP={earned_today.get('exp', 0)}")
            else:
                tasks_logger.warning(f"[{request_id}] Missing earned_today data in response")
            
            # 检查任务数据
            daily_tasks = dashboard_data.get('daily_tasks_section', {}).get('summary', {})
            addon_tasks = dashboard_data.get('addon_tasks_section', {}).get('summary', {})
            tasks_logger.info(f"[{request_id}] Tasks summary: Daily={daily_tasks.get('completed_tasks', 0)}/{daily_tasks.get('total_tasks', 0)}, Addon={addon_tasks.get('active_addon_tasks', 0)}/{addon_tasks.get('max_addon_tasks', 0)}")
            
            # 标准成功响应结构
            response_payload = {
                "code": 200,
                "message": "操作成功",
                "data": dashboard_data,
                "timestamp": int(time.time()),
                # 添加数据质量元信息（可选，用于客户端调试）
                "_meta": {
                    "data_completeness": f"{complete_modules}/{total_modules}",
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "cache_used": dashboard_data.get('_cache_used', False),  # 从服务层获取缓存使用状态
                    "service_processing_time_ms": dashboard_data.get('_processing_time_ms', 0),  # 服务层处理时间
                } if getattr(request, 'GET', {}).get('include_meta', '').lower() == 'true' else None
            }
            
            # 如果数据不完整，添加警告头
            if complete_modules < total_modules:
                response_payload["_warnings"] = [f"部分数据模块不完整: {total_modules - complete_modules} 个模块缺失或错误"]
            
            end_time = time.time()
            # 添加响应时间日志
            tasks_logger.info(f"[{request_id}] TasksDashboardViewSet: Tasks dashboard data served to user {user.id} in {end_time - start_time:.3f}s")
            
            # 记录关键数据到debug级别日志
            try:
                # 记录earned_today以确认数据正确
                if 'today_summary_section' in dashboard_data and 'earned_today' in dashboard_data['today_summary_section']:
                    earned_data = dashboard_data['today_summary_section']['earned_today']
                    tasks_logger.debug(f"[{request_id}] TasksDashboardViewSet Response earned_today: {json.dumps(earned_data)}")
                
                # 只记录关键字段，避免日志过大
                key_data = {
                    'top_section': {
                        'user_info': {'user_id': dashboard_data.get('top_section', {}).get('user_info', {}).get('user_id')},
                        'vip_status': {'has_vip': dashboard_data.get('top_section', {}).get('vip_status', {}).get('has_vip')}
                    },
                    'daily_tasks_summary': dashboard_data.get('daily_tasks_section', {}).get('summary', {}),
                    'addon_tasks_summary': dashboard_data.get('addon_tasks_section', {}).get('summary', {}),
                    'today_summary': {
                        'earned_today': dashboard_data.get('today_summary_section', {}).get('earned_today'),
                        'completion_status': dashboard_data.get('today_summary_section', {}).get('completion_status')
                    },
                    'data_quality': data_quality_report
                }
                tasks_logger.debug(f"[{request_id}] TasksDashboardViewSet Response key data: {json.dumps(key_data)}")
            except Exception as json_error:
                tasks_logger.warning(f"[{request_id}] Failed to log response data: {str(json_error)}")
                
            return Response(response_payload)
            
        except Exception as e:
            end_time = time.time()
            tasks_logger.error(f"[{request_id}] Error fetching tasks dashboard data for user {user.id} after {end_time - start_time:.3f}s: {str(e)}", exc_info=True)
            
            # 标准错误响应结构
            return Response({
                "code": 500,
                "message": "获取Tasks页面数据失败，请稍后重试。",
                "details": str(e),
                "timestamp": int(time.time()),
                "_meta": {
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "error_type": type(e).__name__,
                } if getattr(request, 'GET', {}).get('include_meta', '').lower() == 'true' else None
            }, status=500)

    @action(detail=False, methods=['post'], url_path='manual-health-sync')
    @method_ratelimit(key='user', rate='10/60m', method='POST')  # 每小时最多10次手动同步
    def manual_health_sync(self, request):
        """
        手动健康数据同步
        
        提供用户主动触发健康数据同步的功能，包含速率保护
        """
        try:
            user = request.user
            
            # 检查用户级别的速率限制（每30秒最多1次）
            user_cache_key = f'manual_health_sync:{user.id}'
            if cache.get(user_cache_key):
                return ApiResponse(
                    code=429,
                    message="手动同步过于频繁，请30秒后再试",
                    data={
                        'retry_after_seconds': 30,
                        'sync_type': 'manual'
                    },
                    status=status.HTTP_429_TOO_MANY_REQUESTS
                )
            
            # 验证请求数据
            health_data = request.data
            if not health_data:
                return ApiResponse(
                    code=400,
                    message="健康数据不能为空",
                    data=None,
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # ⚠️ 旧 HealthDataSyncView 已废弃，Tasks 聚合API不再直接调用旧视图
            
            # 调用现有的健康数据同步逻辑
            # health_sync_view = HealthDataSyncView()
            # health_sync_view.request = request
            # health_sync_view.format_kwarg = None
            
            # 执行同步
            # sync_response = health_sync_view.post(request)
            
            # 如果同步成功，设置速率限制缓存
            if sync_response.status_code == 200:
                cache.set(user_cache_key, True, 30)  # 30秒限制
                
                # 清除Tasks聚合数据缓存
                TasksDashboardService.invalidate_user_cache(
                    user.id, 
                    trigger_context='manual_health_sync'
                )
                
                # 增强响应数据，添加手动同步特定信息
                response_data = sync_response.data.get('data', {})
                response_data['sync_type'] = 'manual'
                response_data['next_sync_available_at'] = (
                    timezone.now() + timedelta(seconds=30)
                ).isoformat()
                
                return ApiResponse(
                    code=200,
                    message="手动健康数据同步成功",
                    data=response_data,
                    status=status.HTTP_200_OK
                )
            else:
                return sync_response
                
        except Exception as e:
            tasks_logger.error(f"手动健康数据同步失败: {str(e)}", exc_info=True)
            return ApiResponse(
                code=500,
                message="手动同步失败，请稍后再试",
                data=None,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 