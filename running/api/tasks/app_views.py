from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import IsAuthenticated
import jwt
from django.conf import settings
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class WebSocketTokenView(APIView):
    """
    WebSocket连接Token获取端点
    为前端WebSocket连接提供临时认证Token
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """获取WebSocket连接Token"""
        try:
            # 生成WebSocket专用Token（短期有效）
            expiry_seconds = settings.WEBSOCKET_CONFIG.get('SYNC_TOKEN_EXPIRY', 300)
            payload = {
                'user_id': request.user.id,
                'username': request.user.username,
                'type': 'websocket_sync',
                'exp': datetime.utcnow() + timedelta(seconds=expiry_seconds),
                'iat': datetime.utcnow(),
            }
            
            token = jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')
            
            logger.info(f"用户 {request.user.id} 获取WebSocket Token成功")
            
            return Response({
                'token': token,
                'expires_in': expiry_seconds,
                'websocket_url': 'wss://api.sweatmint.com/ws/sync',
            })
            
        except Exception as e:
            logger.error(f"获取WebSocket Token失败: {str(e)}")
            return Response(
                {'error': 'Token生成失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 