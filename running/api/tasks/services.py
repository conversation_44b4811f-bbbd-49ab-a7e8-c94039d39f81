"""
Tasks页面聚合API服务层

该服务层专门为Tasks页面提供聚合数据，将原本分散在多个API的数据统一到一个响应中。
基于现有的以下API和功能：
- 每日任务 API (tasks/app_views.py - DailyTaskViewSet)
- 附加任务 API (tasks/app_views.py - AddonTaskViewSet)
- 健康数据 API (tasks/app_views.py - HealthDataRetrieveView)
- 用户资料 API (users/views_app.py - UserProfileView)
- VIP状态 API (vip/app_views.py - UserVIPStatusView)
- 今日摘要 API (tasks/app_views.py - today_summary)

按照项目规则，聚合API优于多个分散API调用，减少数据库查询次数和网络往返时间。
"""

import logging
import pytz
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
from django.db.models import Sum, F
from django.core.cache import cache
from django.db import transaction
from concurrent.futures import ThreadPoolExecutor

# 导入现有的模型和服务
from users.models import User, MemberLevel
from vip.models import UserVIP
from tasks.models import UserTask, Task
# 🔥 BOSS修复：正确导入tasks.app_views中的必要函数
from tasks.app_views import get_earned_today, get_user_health_data, get_user_streak_count, get_next_tasks
from vip.app_views import UserVIPStatusView
from users.services.level_upgrade_service import LevelUpgradeService
from config.models import SystemConfig
from core.utils.cache_utils import get_cache, set_cache

# 使用tasks专用logger
tasks_logger = logging.getLogger('tasks.api')


class TasksDashboardService:
    """Tasks页面聚合数据服务"""
    
    @classmethod
    def get_tasks_dashboard_data(cls, request):
        """
        获取Tasks页面的完整聚合数据
        
        Args:
            request: Django请求对象，包含用户信息
            
        Returns:
            dict: 包含所有Tasks页面模块数据的字典
        """
        user = request.user
        start_time = timezone.now()
        
        # 获取新加坡时区的当前时间和日期
        sg_timezone = pytz.timezone('Asia/Singapore')
        current_time_sg = timezone.now().astimezone(sg_timezone)
        today_sg = current_time_sg.date()
        
        request_id = getattr(request, 'request_id', '-')
        tasks_logger.info(f"[{request_id}] TasksDashboardService: Starting aggregation for user {user.id} at {current_time_sg.isoformat()}")
        
        # 🔥 添加缓存机制
        cache_key = f'tasks_dashboard:{user.id}:{today_sg.isoformat()}'
        # 🔧 修复：兼容不同类型的request对象
        request_get_params = getattr(request, 'GET', getattr(request, 'query_params', {}))
        skip_cache = request_get_params.get('skip_cache', 'false').lower() == 'true'
        cache_used = False
        
        if not skip_cache:
            cached_data = cache.get(cache_key)
            if cached_data:
                tasks_logger.info(f"[{request_id}] Cache hit for tasks_dashboard:{user.id}")
                # 验证缓存数据的完整性
                required_keys = ['top_section', 'countdown_section', 'daily_tasks_section', 'addon_tasks_section', 'today_summary_section']
                if all(key in cached_data for key in required_keys):
                    tasks_logger.info(f"[{request_id}] Returning valid cached data for user {user.id}")
                    return {**cached_data, '_cache_used': True}
                else:
                    tasks_logger.warning(f"[{request_id}] Cached data incomplete for user {user.id}, fetching fresh data")
                    cache.delete(cache_key)
        
        try:
            # 🔥 添加并行处理，提升性能
            with ThreadPoolExecutor(max_workers=5) as executor:
                # 1. 获取顶部区域数据
                future_top_section = executor.submit(cls._get_top_section_data, user)
                
                # 2. 获取倒计时区域数据
                future_countdown_section = executor.submit(cls._get_countdown_section_data, current_time_sg, today_sg)
                
                # 3. 获取每日任务区域数据
                future_daily_tasks_section = executor.submit(cls._get_daily_tasks_section_data, user, today_sg)
                
                # 4. 获取附加任务区域数据
                future_addon_tasks_section = executor.submit(cls._get_addon_tasks_section_data, user, today_sg)
                
                # 5. 获取今日摘要数据
                future_today_summary_section = executor.submit(cls._get_today_summary_section_data, user, today_sg)
                
                # 等待所有任务完成
                top_section_data = future_top_section.result()
                countdown_section_data = future_countdown_section.result()
                daily_tasks_section_data = future_daily_tasks_section.result()
                addon_tasks_section_data = future_addon_tasks_section.result()
                today_summary_section_data = future_today_summary_section.result()
                
            end_time = timezone.now()
            processing_time = (end_time - start_time).total_seconds()
            
            tasks_logger.info(f"[{request_id}] TasksDashboardService: Aggregation completed for user {user.id} in {processing_time:.3f}s")
            
            # 组装聚合数据
            dashboard_data = {
                'top_section': top_section_data,
                'countdown_section': countdown_section_data,
                'daily_tasks_section': daily_tasks_section_data,
                'addon_tasks_section': addon_tasks_section_data,
                'today_summary_section': today_summary_section_data,
                '_cache_used': cache_used,
                '_processing_time_ms': int(processing_time * 1000)
            }
            
            # 🔥 智能缓存策略
            try:
                # 检查关键数据是否存在
                has_critical_data = (
                    top_section_data and 'user_info' in top_section_data and
                    daily_tasks_section_data and 'summary' in daily_tasks_section_data and
                    today_summary_section_data and 'earned_today' in today_summary_section_data
                )
                
                if has_critical_data:
                    # 数据完整，缓存较长时间
                    cache_timeout = 600  # 10分钟，Tasks数据变化相对频繁
                    cache.set(cache_key, dashboard_data, timeout=cache_timeout)
                    tasks_logger.info(f"[{request_id}] Cached complete tasks dashboard data for user {user.id} for {cache_timeout}s")
                else:
                    # 数据不完整，缓存较短时间
                    cache_timeout = 120  # 2分钟
                    cache.set(cache_key, dashboard_data, timeout=cache_timeout)
                    tasks_logger.warning(f"[{request_id}] Cached incomplete tasks dashboard data for user {user.id} for {cache_timeout}s")
                    
            except Exception as cache_error:
                tasks_logger.error(f"[{request_id}] Failed to cache tasks dashboard data for user {user.id}: {cache_error}")
            
            return dashboard_data
            
        except Exception as e:
            tasks_logger.error(f"[{request_id}] TasksDashboardService: Error aggregating data for user {user.id}: {str(e)}", exc_info=True)
            raise e
    
    @classmethod
    def _get_top_section_data(cls, user):
        """
        获取顶部区域数据：用户信息和VIP状态
        
        基于现有的：
        - users/views_app.py - UserProfileView
        - vip/app_views.py - UserVIPStatusView
        """
        try:
            # 获取详细的用户经验和等级信息
            level_info = cls._get_detailed_level_info(user)
            
            # 获取用户基本信息
            user_info = {
                'user_id': str(user.id),
                'avatar': getattr(user, 'avatar', None),
                'username': user.username or user.email.split('@')[0],
                'member_level': {
                    'name': user.member_level.name if user.member_level else "新手",
                    'level': user.member_level.level if user.member_level else 1,
                    'progress_percentage': level_info['progress_percentage'],
                },
                # 🔥 新增：详细的经验信息用于UI显示
                'experience_info': {
                    'current_exp': level_info['current_exp'],
                    'current_level_min_exp': level_info['current_level_min_exp'],
                    'current_level_max_exp': level_info['current_level_max_exp'],
                    'next_level_min_exp': level_info['next_level_min_exp'],
                    'exp_needed_for_next_level': level_info['exp_needed_for_next_level'],
                    'is_max_level': level_info['is_max_level'],
                    'next_level_info': level_info['next_level_info'],
                }
            }
            
            # 获取VIP状态 - 基于现有的UserVIPStatusView逻辑
            vip_status = cls._get_vip_status_data(user)
            
            return {
                'user_info': user_info,
                'vip_status': vip_status
            }
            
        except Exception as e:
            tasks_logger.error(f"Error getting top section data for user {user.id}: {str(e)}", exc_info=True)
            return {
                'user_info': {
                    'user_id': str(user.id),
                    'avatar': None,
                    'username': user.email.split('@')[0],
                    'member_level': {'name': "Level 1", 'level': 1, 'progress_percentage': 0.0},
                    'experience_info': {
                        'current_exp': 0,
                        'current_level_min_exp': 0,
                        'current_level_max_exp': 0,
                        'next_level_min_exp': None,
                        'exp_needed_for_next_level': None,
                        'is_max_level': False,
                        'next_level_info': None,
                    }
                },
                'vip_status': {'has_vip': False, 'vip_level_name': None, 'vip_level': None, 'swmt_bonus_percentage': None, 'exp_bonus_percentage': None}
            }
    
    @classmethod
    def _get_vip_status_data(cls, user):
        """获取完整VIP状态数据（包含下一级VIP信息、可升级等级列表等）"""
        try:
            # 检查VIP系统是否启用
            if not SystemConfig.get_vip_status():
                return {
                    'has_vip': False,
                    'vip_level_name': None,
                    'vip_level': None,
                    'swmt_bonus_percentage': None,
                    'exp_bonus_percentage': None,
                    'is_max_level': False,
                    'next_level_info': None,
                    'complete_vip_data': {},
                }
            
            try:
                # 🔥 使用VIP购买API获取完整的VIP数据（包括availableLevels、availableLevel等）
                from api.vip_purchase.services import VipPurchaseService
                from django.http import HttpRequest
                
                # 创建模拟请求对象
                mock_request = HttpRequest()
                mock_request.user = user
                mock_request.method = 'GET'
                mock_request.META = {}
                
                # 获取完整的VIP数据
                complete_vip_data = VipPurchaseService.get_vip_purchase_data(mock_request)
                
                # 提取基础VIP信息
                has_vip = complete_vip_data.get('hasVip', False)
                vip_info = complete_vip_data.get('vipInfo', {})
                available_level = complete_vip_data.get('availableLevel')
                available_levels = complete_vip_data.get('availableLevels', [])
                
                # 当前VIP等级信息
                current_level = vip_info.get('level', 1) if has_vip else 1
                current_level_name = vip_info.get('name', f'VIP {current_level}') if has_vip else 'VIP 1'
                current_swmt_bonus = float(round((vip_info.get('swmtBonusRate', 1.0) - 1.0) * 100, 1)) if has_vip else 0.0
                current_exp_bonus = float(round((vip_info.get('expBonusRate', 1.0) - 1.0) * 100, 1)) if has_vip else 0.0
                
                # 🔥 计算下一级VIP信息和是否最高等级
                next_level_info = None
                is_max_level = True  # 默认假设是最高等级
                
                if available_level:
                    # 有下一级可升级
                    is_max_level = False
                    next_level_info = {
                        "level": available_level.get('level'),
                        "name": available_level.get('name', f"VIP {available_level.get('level')}"),
                        "swmt_bonus_rate": available_level.get('swmt_bonus_rate', 1.0),
                        "exp_bonus_rate": available_level.get('exp_bonus_rate', 1.0),
                        "swmt_bonus_percentage": float(round((available_level.get('swmt_bonus_rate', 1.0) - 1.0) * 100, 1)),
                        "exp_bonus_percentage": float(round((available_level.get('exp_bonus_rate', 1.0) - 1.0) * 100, 1)),
                    }
                elif available_levels:
                    # 从available_levels中找下一级
                    next_levels = [level for level in available_levels if level.get('level', 0) > current_level]
                    if next_levels:
                        is_max_level = False
                        next_level = min(next_levels, key=lambda x: x.get('level', 0))
                        next_level_info = {
                            "level": next_level.get('level'),
                            "name": next_level.get('name', f"VIP {next_level.get('level')}"),
                            "swmt_bonus_rate": next_level.get('swmt_bonus_rate', 1.0),
                            "exp_bonus_rate": next_level.get('exp_bonus_rate', 1.0),
                            "swmt_bonus_percentage": float(round((next_level.get('swmt_bonus_rate', 1.0) - 1.0) * 100, 1)),
                            "exp_bonus_percentage": float(round((next_level.get('exp_bonus_rate', 1.0) - 1.0) * 100, 1)),
                        }
                        
                # 🔥 返回完整的VIP信息
                return {
                    # 当前VIP基础信息（兼容原有API）
                    'has_vip': has_vip,
                    'vip_level_name': current_level_name,
                    'vip_level': current_level,
                    'swmt_bonus_percentage': current_swmt_bonus,
                    'exp_bonus_percentage': current_exp_bonus,
                    
                    # 🔥 新增：下一级VIP信息
                    'is_max_level': is_max_level,
                    'next_level_info': next_level_info,
                    
                    # 🔥 新增：完整的VIP数据（供前端VIP卡片使用）
                    'complete_vip_data': complete_vip_data,
                }
                
            except Exception as e:
                tasks_logger.error(f"Error getting complete VIP status for user {user.id}: {str(e)}", exc_info=True)
                
                # 🔧 降级到简化的VIP数据获取
                try:
                    user_vip = UserVIP.objects.get(user=user, is_active=True)
                    return {
                        'has_vip': True,
                        'vip_level_name': user_vip.vip_level.name,
                        'vip_level': user_vip.vip_level.level,
                        'swmt_bonus_percentage': float(round((user_vip.vip_level.swmt_bonus_rate - 1) * 100, 1)),
                        'exp_bonus_percentage': float(round((user_vip.vip_level.exp_bonus_rate - 1) * 100, 1)),
                        # 简化数据：无法确定下一级信息
                        'is_max_level': False,  # 保守假设不是最高等级
                        'next_level_info': None,
                        'complete_vip_data': {},
                    }
                except UserVIP.DoesNotExist:
                    return {
                        'has_vip': False,
                        'vip_level_name': None,
                        'vip_level': None,
                        'swmt_bonus_percentage': None,
                        'exp_bonus_percentage': None,
                        'is_max_level': False,
                        'next_level_info': None,
                        'complete_vip_data': {},
                    }
                
        except Exception as e:
            tasks_logger.error(f"Error getting VIP status for user {user.id}: {str(e)}", exc_info=True)
            return {
                'has_vip': False,
                'vip_level_name': None,
                'vip_level': None,
                'swmt_bonus_percentage': None,
                'exp_bonus_percentage': None,
                'is_max_level': False,
                'next_level_info': None,
                'complete_vip_data': {},
            }
    
    @classmethod
    def _get_detailed_level_info(cls, user):
        """获取详细的用户等级和经验信息"""
        try:
            from users.models import MemberLevel
            
            if not user.member_level:
                return {
                    'current_exp': user.exp or 0,
                    'current_level_min_exp': 0,
                    'current_level_max_exp': 0,
                    'next_level_min_exp': None,
                    'exp_needed_for_next_level': None,
                    'is_max_level': False,
                    'next_level_info': None,
                    'progress_percentage': 0.0,
                }
            
            current_level = user.member_level
            current_exp = user.exp or 0
            
            # 获取下一级别
            next_level = MemberLevel.objects.filter(
                level=current_level.level + 1
            ).first()
            
            # 计算当前等级的经验范围
            current_level_min_exp = current_level.min_exp
            current_level_max_exp = current_level.max_exp
            
            if not next_level:
                # 已达到最高等级
                return {
                    'current_exp': current_exp,
                    'current_level_min_exp': current_level_min_exp,
                    'current_level_max_exp': current_level_max_exp,
                    'next_level_min_exp': None,
                    'exp_needed_for_next_level': None,
                    'is_max_level': True,
                    'next_level_info': None,
                    'progress_percentage': 100.0,
                }
            
            # 计算升级所需经验
            next_level_min_exp = next_level.min_exp
            exp_needed_for_next_level = max(0, next_level_min_exp - current_exp)
            
            # 计算进度百分比（基于当前等级范围）
            if current_level_max_exp <= current_level_min_exp:
                progress_percentage = 100.0
            else:
                progress = (current_exp - current_level_min_exp) / (current_level_max_exp - current_level_min_exp) * 100
                progress_percentage = min(100.0, max(0.0, progress))
            
            # 下一等级信息
            next_level_info = {
                'name': next_level.name,
                'level': next_level.level,
                'min_exp': next_level.min_exp,
                'max_exp': next_level.max_exp,
                            'daily_task_limit': getattr(next_level, 'daily_task_count', None),
            'addon_task_limit': getattr(next_level, 'extra_task_count', None),
            }
            
            return {
                'current_exp': current_exp,
                'current_level_min_exp': current_level_min_exp,
                'current_level_max_exp': current_level_max_exp,
                'next_level_min_exp': next_level_min_exp,
                'exp_needed_for_next_level': exp_needed_for_next_level,
                'is_max_level': False,
                'next_level_info': next_level_info,
                'progress_percentage': progress_percentage,
            }
            
        except Exception as e:
            tasks_logger.error(f"Error getting detailed level info for user {user.id}: {str(e)}", exc_info=True)
            return {
                'current_exp': user.exp or 0,
                'current_level_min_exp': 0,
                'current_level_max_exp': 0,
                'next_level_min_exp': None,
                'exp_needed_for_next_level': None,
                'is_max_level': False,
                'next_level_info': None,
                'progress_percentage': 0.0,
            }
    
    @classmethod
    def _calculate_level_progress(cls, user):
        """计算用户等级进度百分比（保留向后兼容性）"""
        level_info = cls._get_detailed_level_info(user)
        return level_info['progress_percentage']
    
    @classmethod
    def _get_countdown_section_data(cls, current_time_sg, today_sg):
        """
        获取倒计时区域数据：任务重置倒计时和动态提示
        """
        try:
            # 计算距离下一次任务重置的时间（每日00:00重置）
            reset_time_today = current_time_sg.replace(hour=0, minute=0, second=0, microsecond=0)
            
            # 如果当前时间已过今日00:00，则重置时间为明日00:00
            if current_time_sg >= reset_time_today:
                reset_time_next = reset_time_today + timedelta(days=1)
            else:
                reset_time_next = reset_time_today
            
            # 计算倒计时
            time_until_reset = reset_time_next - current_time_sg
            hours_until_reset = int(time_until_reset.total_seconds() // 3600)
            minutes_until_reset = int((time_until_reset.total_seconds() % 3600) // 60)
            seconds_until_reset = int(time_until_reset.total_seconds() % 60)
            
            # 生成动态提示
            dynamic_tip = cls._generate_dynamic_tip(hours_until_reset, current_time_sg)
            
            return {
                'countdown_info': {
                    'hours_until_reset': hours_until_reset,
                    'minutes_until_reset': minutes_until_reset,
                    'seconds_until_reset': seconds_until_reset,
                    'reset_time_singapore': reset_time_next,
                    'current_time_singapore': current_time_sg,
                },
                'dynamic_tip': dynamic_tip
            }
            
        except Exception as e:
            tasks_logger.error(f"Error getting countdown section data: {str(e)}", exc_info=True)
            return {
                'countdown_info': {
                    'hours_until_reset': 0,
                    'minutes_until_reset': 0,
                    'seconds_until_reset': 0,
                    'reset_time_singapore': current_time_sg,
                    'current_time_singapore': current_time_sg,
                },
                'dynamic_tip': {
                    'tip_text': "加油完成今日任务！",
                    'tip_type': "default",
                    'show_icon': True,
                }
            }
    
    @classmethod
    def _generate_dynamic_tip(cls, hours_until_reset, current_time_sg):
        """根据时间生成动态提示文本"""
        try:
            current_hour = current_time_sg.hour
            
            if hours_until_reset <= 2:
                return {
                    'tip_text': "任务即将重置，抓紧时间完成！",
                    'tip_type': "urgent",
                    'show_icon': True,
                }
            elif current_hour >= 6 and current_hour < 12:
                return {
                    'tip_text': "早上好！开始今日的挑战吧～",
                    'tip_type': "morning",
                    'show_icon': True,
                }
            elif current_hour >= 12 and current_hour < 18:
                return {
                    'tip_text': "午后继续加油，离目标更近一步！",
                    'tip_type': "afternoon",
                    'show_icon': True,
                }
            elif current_hour >= 18 and current_hour < 23:
                return {
                    'tip_text': "晚上好！坚持完成今日任务～",
                    'tip_type': "evening",
                    'show_icon': True,
                }
            else:
                return {
                    'tip_text': "夜深了，记得早点休息！",
                    'tip_type': "night",
                    'show_icon': True,
                }
                
        except Exception as e:
            tasks_logger.error(f"Error generating dynamic tip: {str(e)}", exc_info=True)
            return {
                'tip_text': "加油完成今日任务！",
                'tip_type': "default",
                'show_icon': True,
            }
    
    @classmethod
    def _get_daily_tasks_section_data(cls, user, today_sg):
        """
        获取每日任务区域数据
        
        基于现有的：
        - tasks/app_views.py - DailyTaskViewSet.list()
        """
        try:
            # 获取今日每日任务
            daily_tasks = UserTask.objects.filter(
                user=user,
                assignment_date=today_sg,
                task__category='daily'
            ).select_related('task').order_by('created_at')
            
            # 统计摘要信息
            total_tasks = daily_tasks.count()
            completed_tasks = daily_tasks.filter(status='completed').count()
            completion_percentage = int((completed_tasks / total_tasks) * 100) if total_tasks > 0 else 0
            all_tasks_completed = completed_tasks == total_tasks and total_tasks > 0
            
            # 序列化任务数据
            tasks_data = []
            for user_task in daily_tasks:
                task_data = cls._serialize_daily_task(user_task)
                tasks_data.append(task_data)
            
            return {
                'summary': {
                    'total_tasks': total_tasks,
                    'completed_tasks': completed_tasks,
                    'completion_percentage': completion_percentage,
                    'all_tasks_completed': all_tasks_completed,
                },
                'tasks': tasks_data
            }
            
        except Exception as e:
            tasks_logger.error(f"Error getting daily tasks section data for user {user.id}: {str(e)}", exc_info=True)
            return {
                'summary': {
                    'total_tasks': 0,
                    'completed_tasks': 0,
                    'completion_percentage': 0,
                    'all_tasks_completed': False,
                },
                'tasks': []
            }
    
    @classmethod
    def _serialize_daily_task(cls, user_task):
        """序列化单个每日任务数据"""
        try:
            task = user_task.task
            
            # 计算进度百分比（对于广告任务，使用广告观看进度）
            progress_percentage = 0
            current_progress = 0
            target_progress = 1
            
            if task.task_type == 'ad':
                # 广告任务使用广告观看数量作为进度
                current_progress = user_task.current_ad_views
                target_progress = task.required_ad_views or 1
                if target_progress > 0:
                    progress_percentage = min(100, int((current_progress / target_progress) * 100))
            elif task.task_type == 'steps':
                # 🔧 修复：步数任务从健康数据实时计算进度
                target_progress = getattr(task, 'required_steps', 1)
                current_progress = cls._get_user_health_progress(user_task.user, 'steps', user_task.assignment_date)
                if target_progress > 0:
                    progress_percentage = min(100, int((current_progress / target_progress) * 100))
            elif task.task_type == 'distance':
                # 🔧 修复：距离任务从健康数据实时计算进度
                target_progress = float(getattr(task, 'required_distance', 1.0))  # 转换为浮点数
                current_progress = cls._get_user_health_progress(user_task.user, 'distance', user_task.assignment_date)
                if target_progress > 0:
                    progress_percentage = min(100, int((current_progress / target_progress) * 100))
            elif task.task_type == 'referral':
                # 🔧 修复：拉新任务使用required_referrals字段
                current_progress = getattr(user_task, 'current_count', 0)
                target_progress = getattr(task, 'required_referrals', 1)
                if target_progress > 0:
                    progress_percentage = min(100, int((current_progress / target_progress) * 100))
            else:
                # 其他任务类型的进度计算（如果有相关字段的话）
                current_progress = getattr(user_task, 'current_count', 0)
                target_progress = getattr(user_task, 'target_count', 1)
                if target_progress > 0:
                    progress_percentage = min(100, int((current_progress / target_progress) * 100))
            
            # 计算奖励
            total_swmt = user_task.base_swmt_reward + user_task.vip_bonus_swmt + user_task.additional_bonus_swmt
            total_exp = user_task.base_exp_reward + user_task.vip_bonus_exp + user_task.additional_bonus_exp
            
            # 基础任务数据
            task_data = {
                'id': str(user_task.id),
                'task_id': str(task.id),
                'name': task.name,
                'description': task.description or "",
                'task_type': task.task_type,
                'status': user_task.status,
                'progress': current_progress,  # 使用计算出的当前进度
                'target': target_progress,     # 使用计算出的目标进度
                'progress_percentage': progress_percentage,
                'icon_url': getattr(task, 'icon_url', None),
                'rewards': {
                    'base_swmt': str(user_task.base_swmt_reward),
                    'base_exp': user_task.base_exp_reward,
                    'vip_bonus_swmt': str(user_task.vip_bonus_swmt),
                    'vip_bonus_exp': user_task.vip_bonus_exp,
                    'total_swmt': str(total_swmt),
                    'total_exp': total_exp,
                },
                'can_complete': user_task.status == 'in_progress' and current_progress >= target_progress,
                'completion_time': user_task.completed_at,
            }
            
            # 如果是广告任务，添加广告相关字段
            if task.task_type == 'ad':
                # 广告进度百分比计算（基于广告观看数量，而不是general的progress）
                ad_progress_percentage = 0
                if task.required_ad_views > 0:
                    ad_progress_percentage = min(100, int((user_task.current_ad_views / task.required_ad_views) * 100))
                
                task_data.update({
                    # 广告任务专用字段
                    'ad_url': task.ad_url,  # 广告链接
                    'ad_duration': task.ad_duration,  # 每个广告观看时长（秒）
                    'required_ad_views': task.required_ad_views,  # 需要观看的广告数量
                    'current_ad_views': user_task.current_ad_views,  # 已观看的广告数量
                    'ad_progress_percentage': ad_progress_percentage,  # 广告进度百分比
                    'can_watch_ad': user_task.status == 'in_progress' and user_task.current_ad_views < task.required_ad_views,
                })
            
            return task_data
            
        except Exception as e:
            tasks_logger.error(f"Error serializing daily task {user_task.id}: {str(e)}", exc_info=True)
            fallback_data = {
                'id': str(user_task.id),
                'task_id': str(user_task.task.id),
                'name': user_task.task.name,
                'description': "",
                'task_type': user_task.task.task_type,
                'status': user_task.status,
                'progress': 0,
                'target': 1,
                'progress_percentage': 0,
                'icon_url': None,
                'rewards': {
                    'base_swmt': "0.00",
                    'base_exp': 0,
                    'vip_bonus_swmt': "0.00",
                    'vip_bonus_exp': 0,
                    'total_swmt': "0.00",
                    'total_exp': 0,
                },
                'can_complete': False,
                'completion_time': None,
            }
            
            # 如果是广告任务，添加广告相关字段的默认值
            if hasattr(user_task, 'task') and user_task.task.task_type == 'ad':
                fallback_data.update({
                    'ad_url': getattr(user_task.task, 'ad_url', ''),
                    'ad_duration': getattr(user_task.task, 'ad_duration', 30),
                    'required_ad_views': getattr(user_task.task, 'required_ad_views', 1),
                    'current_ad_views': getattr(user_task, 'current_ad_views', 0),
                    'ad_progress_percentage': 0,
                    'can_watch_ad': False,
                })
            
            return fallback_data
    
    @classmethod
    def _get_addon_tasks_section_data(cls, user, today_sg):
        """
        获取附加任务区域数据
        
        基于现有的：
        - tasks/app_views.py - AddonTaskViewSet.list()
        """
        try:
            # 获取系统配置的最大附加任务数
            max_addon_tasks = 3  # 默认值，可以从SystemConfig获取
            
            # 获取今日已激活的附加任务
            active_addon_tasks = UserTask.objects.filter(
                user=user,
                assignment_date=today_sg,
                task__category='additional',
                status__in=['pending', 'in_progress', 'completed']
            ).select_related('task')
            
            active_count = active_addon_tasks.count()
            can_activate_more = active_count < max_addon_tasks
            
            # 序列化已激活的附加任务
            active_tasks_data = []
            for user_task in active_addon_tasks:
                task_data = cls._serialize_addon_task(user_task, is_active=True)
                active_tasks_data.append(task_data)
            
            # 获取可激活的附加任务（未激活的）
            available_tasks = Task.objects.filter(
                category='additional',
                is_active=True
            ).exclude(
                id__in=active_addon_tasks.values_list('task_id', flat=True)
            )
            
            # 序列化可激活的附加任务
            available_tasks_data = []
            for task in available_tasks:
                task_data = cls._serialize_available_addon_task(task, can_activate_more)
                available_tasks_data.append(task_data)
            
            return {
                'summary': {
                    'active_addon_tasks': active_count,
                    'max_addon_tasks': max_addon_tasks,
                    'can_activate_more': can_activate_more,
                },
                'active_tasks': active_tasks_data,
                'available_tasks': available_tasks_data
            }
            
        except Exception as e:
            tasks_logger.error(f"Error getting addon tasks section data for user {user.id}: {str(e)}", exc_info=True)
            return {
                'summary': {
                    'active_addon_tasks': 0,
                    'max_addon_tasks': 3,
                    'can_activate_more': True,
                },
                'active_tasks': [],
                'available_tasks': []
            }
    
    @classmethod
    def _serialize_addon_task(cls, user_task, is_active=True):
        """序列化附加任务数据"""
        try:
            task = user_task.task
            
            # 🔧 修复：保持Decimal精度，避免转换为float导致精度丢失
            # 使用 quantize 确保精确的小数位数，然后转换为字符串再转换为float
            # 这样可以避免直接 float(Decimal) 导致的精度问题
            from decimal import ROUND_HALF_UP
            
            swmt_multiplier_decimal = getattr(task, 'swmt_multiplier', Decimal('1.0'))
            exp_multiplier_decimal = getattr(task, 'exp_multiplier', Decimal('1.0'))
            
            # 🔧 使用 quantize 保持2位小数精度，然后通过字符串转换避免精度丢失
            swmt_multiplier = float(str(swmt_multiplier_decimal.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)))
            exp_multiplier = float(str(exp_multiplier_decimal.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)))
            
            # 🆕 添加详细的调试日志
            tasks_logger.info(f"🔧 序列化附加任务: {task.name} (ID: {task.id})")
            tasks_logger.info(f"🔧 原始SWMT倍率: {task.swmt_multiplier} (类型: {type(task.swmt_multiplier)})")
            tasks_logger.info(f"🔧 原始XP倍率: {task.exp_multiplier} (类型: {type(task.exp_multiplier)})")
            tasks_logger.info(f"🔧 处理后SWMT倍率: {swmt_multiplier}")
            tasks_logger.info(f"🔧 处理后XP倍率: {exp_multiplier}")
            
            # 基础附加任务数据
            task_data = {
                'id': str(user_task.id),
                'task_id': str(task.id),
                'name': task.name,
                'description': task.description or "",
                'task_type': task.task_type,
                'status': user_task.status,
                'icon_url': getattr(task, 'icon_url', None),
                # 🔧 修复：分别返回SWMT和XP倍率
                'swmt_bonus_rate': swmt_multiplier,
                'exp_bonus_rate': exp_multiplier,
                'activation_time': user_task.created_at,
            }
            
            # 🆕 添加返回数据的日志
            tasks_logger.info(f"🔧 返回的任务数据: swmt_bonus_rate={task_data['swmt_bonus_rate']}, exp_bonus_rate={task_data['exp_bonus_rate']}")
            
            # 如果是广告类型的附加任务，添加广告相关字段
            if task.task_type == 'ad':
                # 广告进度百分比计算
                ad_progress_percentage = 0
                if task.required_ad_views > 0:
                    ad_progress_percentage = min(100, int((user_task.current_ad_views / task.required_ad_views) * 100))
                
                task_data.update({
                    # 广告任务专用字段
                    'ad_url': task.ad_url,  # 广告链接
                    'ad_duration': task.ad_duration,  # 每个广告观看时长（秒）
                    'required_ad_views': task.required_ad_views,  # 需要观看的广告数量
                    'current_ad_views': user_task.current_ad_views,  # 已观看的广告数量
                    'ad_progress_percentage': ad_progress_percentage,  # 广告进度百分比
                    'can_watch_ad': user_task.status == 'in_progress' and user_task.current_ad_views < task.required_ad_views,
                })
            
            return task_data
            
        except Exception as e:
            tasks_logger.error(f"Error serializing addon task {user_task.id}: {str(e)}", exc_info=True)
            fallback_data = {
                'id': str(user_task.id),
                'task_id': str(user_task.task.id),
                'name': user_task.task.name,
                'description': "",
                'task_type': user_task.task.task_type,
                'status': user_task.status,
                'icon_url': None,
                'swmt_bonus_rate': 1.0,
                'exp_bonus_rate': 1.0,
                'activation_time': user_task.created_at,
            }
            
            # 如果是广告任务，添加广告相关字段的默认值
            if hasattr(user_task, 'task') and user_task.task.task_type == 'ad':
                fallback_data.update({
                    'ad_url': getattr(user_task.task, 'ad_url', ''),
                    'ad_duration': getattr(user_task.task, 'ad_duration', 30),
                    'required_ad_views': getattr(user_task.task, 'required_ad_views', 1),
                    'current_ad_views': getattr(user_task, 'current_ad_views', 0),
                    'ad_progress_percentage': 0,
                    'can_watch_ad': False,
                })
            
            return fallback_data
    
    @classmethod
    def _serialize_available_addon_task(cls, task, can_activate):
        """序列化可激活的附加任务数据"""
        try:
            # 🔧 修复：保持Decimal精度，避免转换为float导致精度丢失
            from decimal import ROUND_HALF_UP
            
            swmt_multiplier_decimal = getattr(task, 'swmt_multiplier', Decimal('1.0'))
            exp_multiplier_decimal = getattr(task, 'exp_multiplier', Decimal('1.0'))
            
            # 🔧 使用 quantize 保持2位小数精度，然后通过字符串转换避免精度丢失
            swmt_multiplier = float(str(swmt_multiplier_decimal.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)))
            exp_multiplier = float(str(exp_multiplier_decimal.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)))
            
            # 基础可激活任务数据
            task_data = {
                'id': str(task.id),
                'task_id': str(task.id),
                'name': task.name,
                'description': task.description or "",
                'task_type': task.task_type,
                'icon_url': getattr(task, 'icon_url', None),
                # 🔧 修复：分别返回SWMT和XP倍率
                'swmt_bonus_rate': swmt_multiplier,
                'exp_bonus_rate': exp_multiplier,
                'can_activate': can_activate,
            }
            
            # 如果是广告类型的任务，添加广告相关字段（用于前端显示任务要求）
            if task.task_type == 'ad':
                task_data.update({
                    # 广告任务要求信息
                    'ad_url': task.ad_url,  # 广告链接
                    'ad_duration': task.ad_duration,  # 每个广告观看时长（秒）
                    'required_ad_views': task.required_ad_views,  # 需要观看的广告数量
                })
            
            return task_data
            
        except Exception as e:
            tasks_logger.error(f"Error serializing available addon task {task.id}: {str(e)}", exc_info=True)
            fallback_data = {
                'id': str(task.id),
                'task_id': str(task.id),
                'name': task.name,
                'description': "",
                'task_type': task.task_type,
                'icon_url': None,
                'swmt_bonus_rate': 1.0,
                'exp_bonus_rate': 1.0,
                'can_activate': can_activate,
            }
            
            # 如果是广告任务，添加广告相关字段的默认值
            if hasattr(task, 'task_type') and task.task_type == 'ad':
                fallback_data.update({
                    'ad_url': getattr(task, 'ad_url', ''),
                    'ad_duration': getattr(task, 'ad_duration', 30),
                    'required_ad_views': getattr(task, 'required_ad_views', 1),
                })
            
            return fallback_data
    
    @classmethod
    def _get_today_summary_section_data(cls, user, today_sg):
        """
        获取今日摘要数据
        
        基于现有的：
        - tasks/app_views.py - today_summary
        - tasks/app_views.py - get_earned_today
        - tasks/app_views.py - get_user_streak_count
        - tasks/app_views.py - get_next_tasks
        - tasks/app_views.py - get_user_health_data
        """
        try:
            # 获取今日收益 - 使用现有函数
            earned_today = get_earned_today(user, today_sg)
            
            # 获取健康数据 - 使用现有函数
            health_data = get_user_health_data(user, today_sg)
            
            # 获取连续天数 - 使用现有函数
            current_streak = get_user_streak_count(user, today_sg)
            
            # 获取下一个任务 - 使用现有函数
            next_tasks = get_next_tasks(user, today_sg)
            
            # 获取完成状态摘要
            completion_status = cls._get_completion_status(user, today_sg)
            
            return {
                'earned_today': earned_today,
                'health_data': health_data,
                'current_streak': current_streak,
                'next_tasks': next_tasks,
                'completion_status': completion_status,
            }
            
        except Exception as e:
            tasks_logger.error(f"Error getting today summary section data for user {user.id}: {str(e)}", exc_info=True)
            return {
                'earned_today': {'swmt': '0.00', 'exp': 0},
                'health_data': None,
                'current_streak': 0,
                'next_tasks': [],
                'completion_status': {
                    'all_tasks_completed': False,
                    'completion_percentage': 0,
                    'total_tasks': 0,
                    'completed_tasks': 0,
                },
            }
    
    @classmethod
    def _get_completion_status(cls, user, today_sg):
        """获取今日任务完成状态摘要"""
        try:
            # 获取今日所有任务
            today_tasks = UserTask.objects.filter(
                user=user,
                assignment_date=today_sg
            )
            
            total_tasks = today_tasks.count()
            completed_tasks = today_tasks.filter(status='completed').count()
            completion_percentage = int((completed_tasks / total_tasks) * 100) if total_tasks > 0 else 0
            all_tasks_completed = completed_tasks == total_tasks and total_tasks > 0
            
            return {
                'all_tasks_completed': all_tasks_completed,
                'completion_percentage': completion_percentage,
                'total_tasks': total_tasks,
                'completed_tasks': completed_tasks,
            }
            
        except Exception as e:
            tasks_logger.error(f"Error getting completion status for user {user.id}: {str(e)}", exc_info=True)
            return {
                'all_tasks_completed': False,
                'completion_percentage': 0,
                'total_tasks': 0,
                'completed_tasks': 0,
            }
    
    @classmethod
    def invalidate_user_cache(cls, user_id, trigger_context=None):
        """
        使指定用户的Tasks聚合数据缓存失效
        
        Args:
            user_id: 用户ID
            trigger_context: 触发上下文，用于日志记录
        """
        try:
            # 获取今天的日期（新加坡时区）
            sg_timezone = pytz.timezone('Asia/Singapore')
            today_sg = timezone.now().astimezone(sg_timezone).date()
            
            cache_key = f'tasks_dashboard:{user_id}:{today_sg.isoformat()}'
            
            # 删除缓存
            cache_deleted = cache.delete(cache_key)
            
            # 记录缓存失效日志
            trigger_info = f" (trigger: {trigger_context})" if trigger_context else ""
            if cache_deleted:
                tasks_logger.info(f"TasksDashboardService: Cache invalidated for user {user_id}{trigger_info}")
            else:
                tasks_logger.info(f"TasksDashboardService: No cache to invalidate for user {user_id}{trigger_info}")
                
            return cache_deleted
            
        except Exception as e:
            tasks_logger.error(f"TasksDashboardService: Error invalidating cache for user {user_id}: {str(e)}", exc_info=True)
            return False
    
    @classmethod
    def batch_invalidate_cache(cls, user_ids, trigger_context=None):
        """
        批量使用户缓存失效，用于系统级事件（如每日重置）
        
        Args:
            user_ids: 用户ID列表
            trigger_context: 触发上下文
        """
        try:
            invalidated_count = 0
            for user_id in user_ids:
                if cls.invalidate_user_cache(user_id, trigger_context):
                    invalidated_count += 1
            
            tasks_logger.info(f"TasksDashboardService: Batch cache invalidation completed. {invalidated_count}/{len(user_ids)} caches invalidated (trigger: {trigger_context})")
            return invalidated_count
            
        except Exception as e:
            tasks_logger.error(f"TasksDashboardService: Error in batch cache invalidation: {str(e)}", exc_info=True)
            return 0
    
    @classmethod
    def _get_user_health_progress(cls, user, task_type, assignment_date):
        """
        从健康数据中获取用户的任务进度
        
        Args:
            user: 用户对象
            task_type: 任务类型 ('steps' 或 'distance')
            assignment_date: 任务分配日期
            
        Returns:
            int/float: 当前进度值
        """
        try:
            # 使用新的统一会话架构
            from users.models import UnifiedUserSession
            
            # 获取用户的活跃会话
            active_session = UnifiedUserSession.objects.filter(
                user=user,
                is_active=True
            ).first()
            
            if not active_session:
                return 0
            
            # TODO: 这里需要实现获取当前设备数据并计算净增量的逻辑
            # 暂时返回0，等待完整的健康数据同步逻辑实现
            if task_type == 'steps':
                return 0  # active_session.calculate_increment(current_data)['steps']
            elif task_type == 'distance':
                return 0  # active_session.calculate_increment(current_data)['distance']
            else:
                return 0
                
        except Exception as e:
            tasks_logger.error(f"❌ 获取用户健康进度失败: 用户={user.id}, 任务类型={task_type}, 错误={str(e)}")
            return 0 