from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import DashboardViewSet

router = DefaultRouter()
# The endpoint will be /api/app/v1/dashboard/ (if included with prefix 'dashboard/' in main urls)
# or just /dashboard/ if registered at root of 'api.dashboard.urls'
router.register(r'', DashboardViewSet, basename='dashboard') # Registering at the root of this app's URLs

app_name = 'api_dashboard' # Added app_name for namespacing if needed

urlpatterns = [
    path('', include(router.urls)),
] 