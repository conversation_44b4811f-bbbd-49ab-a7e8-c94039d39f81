import time
import logging
from concurrent.futures import ThreadPoolExecutor

# Django Core
from django.core.cache import cache # 启用缓存
from django.http import HttpRequest # 导入 HttpRequest

# Import specific views from other apps
from users.views_app import UserProfileView
from vip.app_views import UserVIPStatusView
# 🔥 BOSS核心修复：重新导入必须的类和函数，否则dashboard数据加载失败
from tasks.app_views import DailyTaskViewSet, AddonTaskViewSet, today_summary as get_today_summary_view
# 🔥 新增：导入健康数据API服务
# from api.health.services import HealthService as BackendHealthService

# from django.core.cache import cache # 缓存后续添加

# 假设这些服务/视图方法可以从各自的模块导入
# 例如:
# from users.app_views import UserProfileView # 或者 users.services.UserService
# from vip.app_views import VipStatusView     # 或者 vip.services.VipService
# from tasks.app_views import TodaySummaryView, DailyTaskListView, AddonTaskListView # 或者 tasks.services.TaskService

# !! Placeholder: Actual imports will depend on how existing services are structured and accessible !!
# !! For now, we'll define placeholder functions that mimic fetching data.
# !! These need to be replaced with actual calls to existing service/view methods.

logger = logging.getLogger('dashboard.api') # Get logger specific to dashboard operations

# --- Placeholder Data Fetching Functions ---
# These should be replaced by actual calls to your existing services/views
# For example, if UserProfileView has a method get_profile_data(user):
# from users.app_views import UserProfileView
# def _get_user_profile_data(user):
#     return UserProfileView().get_profile_data(user)

def _get_user_profile_placeholder(user):
    logger.debug(f"Fetching placeholder user profile for {user.username}")
    return {
        "userId": str(user.id),
        "username": user.username,
        "email": user.email,
        "avatar_url": user.profile.avatar_url if hasattr(user, 'profile') and hasattr(user.profile, 'avatar_url') else None,
        # Ensure UserProfileDto structure is matched
        "memberLevel": {"name": "示例等级", "level": 1} # Example, match actual DTO
    }

def _get_vip_status_placeholder(user):
    logger.debug(f"Fetching placeholder VIP status for {user.username}")
    return {
        "has_vip": False,
        "vipLevelName": "非VIP",
        # Ensure VipStatusDto structure is matched
    }

def _get_today_summary_placeholder(user):
    logger.debug(f"Fetching placeholder today summary for {user.username}")
    return {
        "earnedToday": {"swmt": "0.0", "exp": 0},
        # Ensure TodaySummaryDto structure is matched
    }

def _get_daily_tasks_placeholder(user):
    logger.debug(f"Fetching placeholder daily tasks for {user.username}")
    return {
        "completedTasks": 0,
        "totalTasks": 5,
        "tasks": [],
        # Ensure DailyTaskListDto structure is matched
    }

def _get_addon_tasks_placeholder(user):
    logger.debug(f"Fetching placeholder addon tasks for {user.username}")
    return {
        "activeAddonTasks": 0,
        "maxAddonTasks": 2,
        "addonTasks": [],
        # Ensure AddonTaskListDto structure is matched
    }
# --- End Placeholder Data Fetching Functions ---

# --- Helper functions to call actual app services/views ---

def _get_user_profile_data(request_mock):
    """Fetches user profile data using UserProfileView."""
    try:
        user = request_mock.user 
        
        # 🔥 关键修复：确保使用最新的用户数据，特别是在任务完成后
        # 刷新用户对象以获取最新的exp、swmt_balance等字段
        user.refresh_from_db()
        logger.info(f"🔄 用户对象已刷新以获取最新数据: user={user.email}, exp={user.exp}, swmt_balance={user.swmt_balance}")
        
        view = UserProfileView()
        
        # 处理不同类型的request对象
        # 在DRF ViewSet中，request是rest_framework.request.Request对象，有._request属性
        # 在Django TestCase中，request是django.http.HttpRequest对象，没有._request属性
        if hasattr(request_mock, '_request'):
            # DRF request对象
            actual_request = request_mock._request
            view.request = request_mock  # DRF view需要DRF request对象
            response = view.get(request_mock)
        else:
            # Django HttpRequest对象（通常在测试中）
            actual_request = request_mock
            view.request = request_mock
            response = view.get(request_mock)
        
        if hasattr(response, 'data') and response.data.get('code') == 200:
            return response.data.get('data') 
            
        logger.warning(f"Failed to get user profile data for {user.username}. Response: {getattr(response, 'data', 'No data attribute')}")
        
        # 📍 关键修复：在fallback情况下，返回用户真实的exp值，而不是硬编码的0
        # 直接从用户模型获取最新的exp值
        user.refresh_from_db()  # 确保获取最新数据
        logger.info(f"UserProfileView失败，使用fallback数据，用户真实exp值: {user.exp}")
        
        # 返回基本的用户信息结构，但使用真实的exp值
        return {
            "user_id": str(user.user_id),  # 使用正确的字段名
            "email": user.email,
            "username": user.username,
            "avatar": user.avatar,
            "member_level": {
                "id": user.member_level.id if user.member_level else 1,
                "name": user.member_level.name if user.member_level else "Level 1",
                "level": user.member_level.level if user.member_level else 1,
                "min_exp": user.member_level.min_exp if user.member_level else 0,
                "max_exp": user.member_level.max_exp if user.member_level else 999,
                "daily_task_count": user.member_level.daily_task_count if user.member_level else 3,
                "extra_task_count": user.member_level.extra_task_count if user.member_level else 1,
                "benefits": f"Complete {user.member_level.daily_task_count if user.member_level else 3} daily tasks, activate {user.member_level.extra_task_count if user.member_level else 1} extra tasks"
            },
            "exp": user.exp,  # 📍 使用真实的经验值
            "swmt_balance": float(user.swmt_balance),  # 使用真实的SWMT余额
            "usdt_balance": str(user.usdt_balance),
            "total_commission": float(user.total_commission),
            "is_agent": user.is_agent,
            "is_active_member": user.is_active_member,
            "referral_code": user.referral_code,
            "referral_count": user.referral_count,
            "account_status": "Active" if user.is_active else ("Frozen" if user.is_frozen else "Inactive"),
            "can_withdraw_status": "Withdrawal allowed" if user.can_withdraw else (user.withdraw_disabled_reason or "Withdrawal disabled"),
            "agent_id": user.agent_id if user.is_agent else None
        }
    except Exception as e:
        logger.error(f"Error fetching user profile for {request_mock.user.username}: {e}", exc_info=True)
        # 返回基本的用户信息结构，避免前端解析失败
        user = request_mock.user
        
        # 📍 关键修复：即使在异常情况下，也尝试获取用户真实数据
        try:
            user.refresh_from_db()
            logger.info(f"异常处理中，用户真实exp值: {user.exp}")
        except Exception as refresh_error:
            logger.error(f"刷新用户数据失败: {refresh_error}")
        
        return {
            "user_id": str(user.user_id),  # 使用正确的字段名
            "email": user.email,
            "username": user.username,
            "avatar": user.avatar,
            "member_level": {
                "id": user.member_level.id if user.member_level else 1,
                "name": user.member_level.name if user.member_level else "Level 1",
                "level": user.member_level.level if user.member_level else 1,
                "min_exp": user.member_level.min_exp if user.member_level else 0,
                "max_exp": user.member_level.max_exp if user.member_level else 999,
                "daily_task_count": user.member_level.daily_task_count if user.member_level else 3,
                "extra_task_count": user.member_level.extra_task_count if user.member_level else 1,
                "benefits": f"Complete {user.member_level.daily_task_count if user.member_level else 3} daily tasks, activate {user.member_level.extra_task_count if user.member_level else 1} extra tasks"
            },
            "exp": user.exp,  # 📍 使用真实的经验值
            "swmt_balance": float(user.swmt_balance),  # 使用真实的SWMT余额
            "usdt_balance": str(user.usdt_balance),
            "total_commission": float(user.total_commission),
            "is_agent": user.is_agent,
            "is_active_member": user.is_active_member,
            "referral_code": user.referral_code,
            "referral_count": user.referral_count,
            "account_status": "Active" if user.is_active else ("Frozen" if user.is_frozen else "Inactive"),
            "can_withdraw_status": "Withdrawal allowed" if user.can_withdraw else (user.withdraw_disabled_reason or "Withdrawal disabled"),
            "agent_id": user.agent_id if user.is_agent else None
        }

def _get_vip_status_data(request_mock):
    """Fetches VIP status data using UserVIPStatusView."""
    try:
        user = request_mock.user
        view = UserVIPStatusView()
        
        # 处理不同类型的request对象（与_get_user_profile_data保持一致）
        if hasattr(request_mock, '_request'):
            # DRF request对象
            view.request = request_mock
            response = view.get(request_mock)
        else:
            # Django HttpRequest对象（通常在测试中）
            view.request = request_mock
            response = view.get(request_mock)
        
        if hasattr(response, 'data') and response.data.get('code') == 200:
            return response.data.get('data')
        logger.warning(f"Failed to get VIP status for {user.username}. Response: {getattr(response, 'data', 'No data attribute')}")
        # 返回默认的非VIP状态结构
        return {
            "has_vip": False,
            "vip_info": None,
            "features": [],
            "refund_active": False,
            "available_level": {
                "id": 1,
                "name": "Bronze VIP",
                "level": 1,
                "price": {"amount": "10.00", "currency": "USDT"},
                "swmt_bonus_rate": 1.6,
                "exp_bonus_rate": 1.8
            },
            "available_levels": []
        }
    except Exception as e:
        logger.error(f"Error fetching VIP status for {request_mock.user.username}: {e}", exc_info=True)
        # 返回默认的非VIP状态结构
        return {
            "has_vip": False,
            "vip_info": None,
            "features": [],
            "refund_active": False,
            "available_level": {
                "id": 1,
                "name": "Bronze VIP",
                "level": 1,
                "price": {"amount": "10.00", "currency": "USDT"},
                "swmt_bonus_rate": 1.6,
                "exp_bonus_rate": 1.8
            },
            "available_levels": []
        }

def _get_today_summary_data(request_mock):
    """Fetches today's summary data using the today_summary function view."""
    try:
        user = request_mock.user
        logger.info(f"正在获取用户 {user.email} 的今日摘要数据")
        
        # 确保传递的是 HttpRequest 对象
        actual_http_request = request_mock._request if hasattr(request_mock, '_request') else request_mock
        
        # 直接调用导入的 today_summary 函数
        response = get_today_summary_view(actual_http_request) 
        
        if hasattr(response, 'data'):
            logger.debug(f"today_summary 原始响应: {response.data}")
            if isinstance(response.data, dict) and 'data' in response.data and \
               isinstance(response.data['data'], dict) and 'earned_today' in response.data['data']:
                earned_today = response.data['data']['earned_today']
                logger.info(f"用户 {user.email} 今日收益: SWMT={earned_today.get('swmt', '0')}, XP={earned_today.get('exp', 0)}")
            else:
                logger.warning(f"today_summary响应数据格式不符合预期: {response.data}")
        
        if hasattr(response, 'data') and isinstance(response.data, dict) and response.data.get('code') == 200:
            today_data = response.data.get('data')
            
            # 🔥 统一架构：移除健康数据注入逻辑，Overview模块应直接调用 /health API
            # 这样实现职责分离：Dashboard专注聚合，Health API专注健康数据
            logger.info(f"✅ Dashboard API专注聚合任务数据，健康数据由独立 /health API 提供")
            
            return today_data
            
        logger.warning(f"从 today_summary 获取数据失败或格式不正确. Response: {getattr(response, 'data', 'No data attribute')}")
        
        # 🔥 统一架构：fallback情况下也不获取健康数据
        # 返回默认的今日摘要结构
        from datetime import datetime
        return {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "total_tasks": 0,
            "completed_tasks": 0,
            "completion_percentage": 0,
            "earned_today": {
                "swmt": "0",
                "exp": 0
            },
            "current_streak": 0,
            "next_tasks": [],
            "all_tasks_completed": False
        }
    except Exception as e:
        logger.error(f"Error fetching today summary for {request_mock.user.email}: {e}", exc_info=True)
        
        # 🔥 统一架构：异常情况下也不处理健康数据
        # 返回默认的今日摘要结构
        from datetime import datetime
        return {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "total_tasks": 0,
            "completed_tasks": 0,
            "completion_percentage": 0,
            "earned_today": {
                "swmt": "0",
                "exp": 0
            },
            "current_streak": 0,
            "next_tasks": [],
            "all_tasks_completed": False
        }

def _get_daily_tasks_data(request_mock):
    """Fetches daily tasks list using DailyTaskViewSet's list action."""
    try:
        user = request_mock.user
        view = DailyTaskViewSet()
        # 确保传递的是 HttpRequest
        view.request = request_mock._request if hasattr(request_mock, '_request') else request_mock
        view.action = 'list'
        view.format_kwarg = None
        response = view.list(request_mock._request if hasattr(request_mock, '_request') else request_mock)
        
        if hasattr(response, 'data') and response.data.get('code') == 200:
            return response.data.get('data')
        logger.warning(f"Failed to get daily tasks for {user.username}. Response: {getattr(response, 'data', 'No data attribute')}")
        # 返回默认的每日任务结构
        return {
            "total_tasks": 0,
            "completed_tasks": 0,
            "completion_percentage": 0,
            "user_level": {
                "name": "普通会员",
                "level": 1
            },
            "user_vip": {
                "has_vip": False,
                "name": None,
                "level": None
            },
            "vip_refund": None,
            "tasks": []
        }
    except Exception as e:
        logger.error(f"Error fetching daily tasks for {request_mock.user.username}: {e}", exc_info=True)
        # 返回默认的每日任务结构
        return {
            "total_tasks": 0,
            "completed_tasks": 0,
            "completion_percentage": 0,
            "user_level": {
                "name": "普通会员",
                "level": 1
            },
            "user_vip": {
                "has_vip": False,
                "name": None,
                "level": None
            },
            "vip_refund": None,
            "tasks": []
        }

def _get_addon_tasks_data(request_mock):
    """Fetches addon tasks list using AddonTaskViewSet's list action."""
    try:
        user = request_mock.user
        view = AddonTaskViewSet()
        # 确保传递的是 HttpRequest
        view.request = request_mock._request if hasattr(request_mock, '_request') else request_mock
        view.action = 'list'
        view.format_kwarg = None
        response = view.list(request_mock._request if hasattr(request_mock, '_request') else request_mock)
        
        if hasattr(response, 'data') and response.data.get('code') == 200:
            return response.data.get('data')
        logger.warning(f"Failed to get addon tasks for {user.username}. Response: {getattr(response, 'data', 'No data attribute')}")
        # 返回默认的附加任务结构
        return {
            "active_addon_tasks": 0,
            "max_addon_tasks": 2,
            "available_slots": 2,
            "current_member_level": {
                "name": "普通会员",
                "level": 1
            },
            "addon_tasks": []
        }
    except Exception as e:
        logger.error(f"Error fetching addon tasks for {request_mock.user.username}: {e}", exc_info=True)
        # 返回默认的附加任务结构
        return {
            "active_addon_tasks": 0,
            "max_addon_tasks": 2,
            "available_slots": 2,
            "current_member_level": {
                "name": "普通会员",
                "level": 1
            },
            "addon_tasks": []
        }

# --- End Helper functions ---

class DashboardService:
    """
    首页聚合数据服务
    
    🔥 BOSS架构统一要求：
    - Dashboard API专注于聚合用户资料、VIP状态、任务数据等
    - 健康数据由独立的 /api/app/v1/health/status/ API提供
    - Overview模块应直接调用 /health API获取健康数据
    - 这样实现职责分离，减少冗余，保持代码统一
    """
    @staticmethod
    def get_home_dashboard_data(user_request_object):
        """
        获取首页聚合数据。
        
        🔥 架构说明：
        - 聚合用户资料、VIP状态、任务摘要、每日任务、附加任务
        - 不包含健康数据，健康数据由前端直接调用 /health API获取
        - 实现职责分离：Dashboard专注聚合，Health API专注健康数据
        
        The user_request_object is the actual Django request object.
        """
        user = user_request_object.user
        request_id = getattr(user_request_object, 'request_id', '-') 
        logger.info(f"[{request_id}] DashboardService: Starting data aggregation for user {user.id} ({user.username})")
        start_time = time.time()

        cache_key = f'home_dashboard:{user.id}'
        # 重新启用缓存机制，但在开发/调试时可以通过参数跳过缓存
        skip_cache = getattr(user_request_object, 'GET', {}).get('skip_cache', 'false').lower() == 'true'
        
        if not skip_cache:
            cached_data = cache.get(cache_key)
            if cached_data:
                logger.info(f"[{request_id}] Cache hit for home_dashboard:{user.id}")
                # 验证缓存数据的完整性
                required_keys = ['user_profile', 'vip_status', 'today_summary', 'daily_tasks', 'addon_tasks']
                if all(key in cached_data for key in required_keys):
                    logger.info(f"[{request_id}] Returning valid cached data for user {user.id}")
                    return cached_data
                else:
                    logger.warning(f"[{request_id}] Cached data incomplete for user {user.id}, fetching fresh data")
                    cache.delete(cache_key)

        with ThreadPoolExecutor(max_workers=5) as executor:
            # 所有辅助函数现在都应该期望接收 DRF Request 对象 (request_mock)
            # 并在内部提取 _request 属性传递给需要 HttpRequest 的地方
            future_user_profile = executor.submit(_get_user_profile_data, user_request_object)
            future_vip_status = executor.submit(_get_vip_status_data, user_request_object)
            future_today_summary = executor.submit(_get_today_summary_data, user_request_object)
            future_daily_tasks = executor.submit(_get_daily_tasks_data, user_request_object)
            future_addon_tasks = executor.submit(_get_addon_tasks_data, user_request_object)

            user_profile_data = future_user_profile.result()
            vip_status_data = future_vip_status.result()
            today_summary_data = future_today_summary.result()
            daily_tasks_data = future_daily_tasks.result()
            addon_tasks_data = future_addon_tasks.result()
            
        dashboard_data = {
            "user_profile": user_profile_data,
            "vip_status": vip_status_data,
            "today_summary": today_summary_data,
            "daily_tasks": daily_tasks_data,
            "addon_tasks": addon_tasks_data,
        }

        # 验证聚合数据的完整性再缓存
        try:
            # 检查关键数据是否存在 - 修复字段名不一致问题
            has_critical_data = (
                user_profile_data and 'user_id' in user_profile_data and
                vip_status_data and 'has_vip' in vip_status_data and
                today_summary_data and 'earned_today' in today_summary_data
            )
            
            if has_critical_data:
                # 动态调整缓存时间：如果数据完整且正常，缓存较长时间
                cache_timeout = 300  # 5分钟
                cache.set(cache_key, dashboard_data, timeout=cache_timeout)
                logger.info(f"[{request_id}] Cached complete dashboard data for user {user.id} for {cache_timeout}s")
            else:
                # 如果数据不完整，缓存较短时间，便于快速重试
                cache_timeout = 60  # 1分钟
                cache.set(cache_key, dashboard_data, timeout=cache_timeout)
                logger.warning(f"[{request_id}] Cached incomplete dashboard data for user {user.id} for {cache_timeout}s")
                
        except Exception as cache_error:
            logger.error(f"[{request_id}] Failed to cache dashboard data for user {user.id}: {cache_error}")

        logger.info(f"[{request_id}] DashboardService: Data aggregation for user {user.id} completed in {time.time() - start_time:.4f} seconds.")
        
        return dashboard_data 