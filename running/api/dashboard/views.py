from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .services import DashboardService
import logging
import time
import json
import os
import datetime

# 设置dashboard专用日志
dashboard_logger = logging.getLogger('dashboard.api')

# 确保日志目录存在
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# 设置dashboard日志文件处理器
dashboard_log_file = os.path.join(log_dir, 'dashboard.log')
file_handler = logging.FileHandler(dashboard_log_file)
file_handler.setLevel(logging.DEBUG)

# 设置格式
formatter = logging.Formatter('%(levelname)s %(asctime)s %(name)s %(process)d %(thread)d %(message)s')
file_handler.setFormatter(formatter)

# 将处理器添加到logger
dashboard_logger.addHandler(file_handler)
dashboard_logger.setLevel(logging.DEBUG)

# 记录启动信息
dashboard_logger.info(f"Dashboard API module initialized at {datetime.datetime.now().isoformat()}")

class DashboardViewSet(ViewSet):
    """
    聚合接口视图集
    目前主要用于提供首页所需的聚合数据。
    """
    permission_classes = [IsAuthenticated]

    def list(self, request):
        """
        获取首页的聚合数据。
        对应前端的 Home Dashboard。
        """
        user = request.user
        request_id = getattr(request, 'request_id', '-') # Assuming RequestIDMiddleware is in place
        start_time = time.time()
        
        # 增强日志，添加更多追踪信息
        dashboard_logger.info(f"[{request_id}] DashboardViewSet: Home dashboard data requested by user {user.id} ({user.email}). IP: {request.META.get('REMOTE_ADDR')}")
        
        try:
            # 传递整个request对象给服务层，因为服务层需要request.user以及其他request属性
            dashboard_data = DashboardService.get_home_dashboard_data(request)
            
            # 数据完整性检查和详细记录
            data_quality_report = {
                'user_profile_complete': bool(dashboard_data.get('user_profile', {}).get('user_id')),
                'vip_status_complete': 'has_vip' in dashboard_data.get('vip_status', {}),
                'today_summary_complete': bool(dashboard_data.get('today_summary', {}).get('earned_today')),
                'daily_tasks_complete': 'total_tasks' in dashboard_data.get('daily_tasks', {}),
                'addon_tasks_complete': 'max_addon_tasks' in dashboard_data.get('addon_tasks', {}),
            }
            
            complete_modules = sum(data_quality_report.values())
            total_modules = len(data_quality_report)
            
            dashboard_logger.info(f"[{request_id}] Data quality: {complete_modules}/{total_modules} modules complete - {data_quality_report}")
            
            # 检查earned_today数据，确保前端能正确显示
            if 'today_summary' in dashboard_data and 'earned_today' in dashboard_data['today_summary']:
                earned_today = dashboard_data['today_summary']['earned_today']
                dashboard_logger.info(f"[{request_id}] DashboardViewSet: User {user.email} earned_today data: SWMT={earned_today.get('swmt', '0')}, XP={earned_today.get('exp', 0)}")
            else:
                dashboard_logger.warning(f"[{request_id}] Missing earned_today data in response")
            
            # 检查任务数据
            daily_tasks = dashboard_data.get('daily_tasks', {})
            addon_tasks = dashboard_data.get('addon_tasks', {})
            dashboard_logger.info(f"[{request_id}] Tasks summary: Daily={daily_tasks.get('completed_tasks', 0)}/{daily_tasks.get('total_tasks', 0)}, Addon={addon_tasks.get('active_addon_tasks', 0)}/{addon_tasks.get('max_addon_tasks', 0)}")
            
            # Standard success response structure from backend rules
            response_payload = {
                "code": 200,
                "message": "操作成功", # "Operation successful"
                "data": dashboard_data,
                "timestamp": int(time.time()),
                # 添加数据质量元信息（可选，用于客户端调试）
                "_meta": {
                    "data_completeness": f"{complete_modules}/{total_modules}",
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "cache_used": False,  # 这个值应该从DashboardService返回，目前先设为False
                } if request.GET.get('include_meta', '').lower() == 'true' else None
            }
            
            # 如果数据不完整，添加警告头
            if complete_modules < total_modules:
                response_payload["_warnings"] = [f"部分数据模块不完整: {total_modules - complete_modules} 个模块缺失或错误"]
            
            end_time = time.time()
            # 添加响应时间日志
            dashboard_logger.info(f"[{request_id}] DashboardViewSet: Home dashboard data served to user {user.id} in {end_time - start_time:.3f}s")
            
            # 将完整响应数据添加到debug级别日志，便于调试
            # 为避免日志过大，可以选择只记录关键字段或限制大小
            try:
                # 尝试记录earned_today以确认数据正确
                if 'today_summary' in dashboard_data and 'earned_today' in dashboard_data['today_summary']:
                    earned_data = dashboard_data['today_summary']['earned_today']
                    dashboard_logger.debug(f"[{request_id}] DashboardViewSet Response earned_today: {json.dumps(earned_data)}")
                
                # 只记录关键字段，避免日志过大
                key_data = {
                    'user_profile': {'avatar': dashboard_data.get('user_profile', {}).get('avatar_url')},
                    'vip_status': {'has_vip': dashboard_data.get('vip_status', {}).get('has_vip')},
                    'today_summary': {
                        'earned_today': dashboard_data.get('today_summary', {}).get('earned_today'),
                        'completion_percentage': dashboard_data.get('today_summary', {}).get('completion_percentage')
                    },
                    'data_quality': data_quality_report
                }
                dashboard_logger.debug(f"[{request_id}] DashboardViewSet Response key data: {json.dumps(key_data)}")
            except Exception as json_error:
                dashboard_logger.warning(f"[{request_id}] Failed to log response data: {str(json_error)}")
                
            # TODO: Add X-API-Degraded header if data is partially served from cache/fallback
            return Response(response_payload)
        except Exception as e:
            end_time = time.time()
            dashboard_logger.error(f"[{request_id}] Error fetching home dashboard data for user {user.id} after {end_time - start_time:.3f}s: {str(e)}", exc_info=True)
            # Standard error response structure
            return Response({
                "code": 500, # Or a more specific error code
                "message": "获取首页数据失败，请稍后重试。", # "Failed to fetch home data, please try again later."
                "details": str(e),
                "timestamp": int(time.time()),
                "_meta": {
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "error_type": type(e).__name__,
                } if request.GET.get('include_meta', '').lower() == 'true' else None
            }, status=500)

    # Potential future actions for other dashboards
    # @action(detail=False, methods=['get'], url_path='tasks-summary')
    # def tasks_dashboard(self, request):
    #     # ... logic for tasks specific dashboard ...
    #     return Response(...) 