import time
import logging
from concurrent.futures import ThreadPoolExecutor

# Django Core
from django.core.cache import cache  # 启用缓存
from django.http import HttpRequest  # 导入 HttpRequest

# Import specific views from VIP app
from vip.app_views import UserVIPStatusView, VIPLevelViewSet, VIPPurchaseView

logger = logging.getLogger('vip_purchase.api')  # Get logger specific to vip_purchase operations


def _get_current_vip_status_data(request_mock):
    """获取用户当前VIP状态数据，使用UserVIPStatusView"""
    try:
        user = request_mock.user
        view = UserVIPStatusView()
        # 确保传递的是 HttpRequest
        view.request = request_mock._request if hasattr(request_mock, '_request') else request_mock
        response = view.get(request_mock._request if hasattr(request_mock, '_request') else request_mock)
        
        if hasattr(response, 'data') and response.data.get('code') == 200:
            return response.data.get('data', {})
        else:
            logger.warning(f"VIP状态获取失败: {response.data if hasattr(response, 'data') else 'No response data'}")
            return {}
    except Exception as e:
        logger.error(f"获取VIP状态时发生错误: {str(e)}")
        return {}


def _get_available_vip_levels_data(request_mock):
    """获取可用VIP等级数据，使用VIPLevelViewSet"""
    try:
        user = request_mock.user
        viewset = VIPLevelViewSet()
        # 确保传递的是 HttpRequest
        viewset.request = request_mock._request if hasattr(request_mock, '_request') else request_mock
        response = viewset.list(request_mock._request if hasattr(request_mock, '_request') else request_mock)
        
        if hasattr(response, 'data') and response.data.get('code') == 200:
            return response.data.get('data', {})
        else:
            logger.warning(f"VIP等级列表获取失败: {response.data if hasattr(response, 'data') else 'No response data'}")
            return {}
    except Exception as e:
        logger.error(f"获取VIP等级列表时发生错误: {str(e)}")
        return {}


def _get_vip_rules_data():
    """获取VIP规则说明数据"""
    try:
        # VIP规则是静态数据，可以直接返回
        return {
            "rules": [
                {
                    "title": "Permanent VIP",
                    "description": "Once purchased, you keep that VIP level's SWMT & XP bonus forever."
                },
                {
                    "title": "No Interruption = Full Refund",
                    "description": "Each VIP tier has a 30-day (or specified) cycle. Complete all daily tasks for every one of those days—if you don't miss a single day, the fee is returned 100%."
                },
                {
                    "title": "Upgrading Mid-Cycle",
                    "description": "If your current VIP refund cycle is still valid (no missed days) and you upgrade to a higher VIP tier, a new 30-day cycle begins for the higher tier. Success in this new cycle = refund of all fees (the old + the new). If you break the chain at any point, you lose both refunds."
                },
                {
                    "title": "Failed Refund Cycle",
                    "description": "If your current VIP cycle was already broken, you can still upgrade, but you only get the new tier's fee refunded if you fulfill its entire cycle."
                },
                {
                    "title": "Different SWMT & XP Bonuses",
                    "description": "Each VIP tier offers distinct bonus rates. For example, VIP1 = +50% SWMT / +30% XP, while VIP2 = +80% SWMT / +50% XP, etc. Choose the one that suits your needs."
                },
                {
                    "title": "No Extra Fees",
                    "description": "No hidden charges. We only withhold your fee if you fail to maintain daily tasks."
                }
            ]
        }
    except Exception as e:
        logger.error(f"Error generating VIP rules data: {e}", exc_info=True)
        return {"rules": []}


def _get_purchase_benefits_data():
    """获取VIP购买权益数据"""
    try:
        # VIP购买权益是静态数据
        return {
            "benefits": [
                "Unlock Higher VIP Levels for Bigger SWMT & XP Boosts!",
                "Earn up to +120% SWMT and +100% XP!",
                "Keep your streak for 30 days and get a full refund!"
            ]
        }
    except Exception as e:
        logger.error(f"Error generating purchase benefits data: {e}", exc_info=True)
        return {"benefits": []}


class VipPurchaseService:
    """VIP购买页面聚合服务"""
    
    @staticmethod
    def get_vip_purchase_data(request):
        """
        获取VIP购买页面所需的聚合数据
        
        Args:
            request: Django HttpRequest对象
            
        Returns:
            dict: 包含VIP购买页面所需的所有数据，格式与前端DTO匹配
        """
        start_time = time.time()
        logger.info(f"VIP购买聚合数据请求开始 - 用户: {request.user.username if request.user.is_authenticated else 'Anonymous'}")
        
        # 创建请求模拟对象，用于调用其他视图
        class RequestMock:
            def __init__(self, original_request):
                self._request = original_request
                self.user = original_request.user
                self.META = original_request.META
                self.method = original_request.method
        
        request_mock = RequestMock(request)
        
        # 并发获取数据
        with ThreadPoolExecutor(max_workers=2) as executor:
            # 提交并发任务
            vip_status_future = executor.submit(_get_current_vip_status_data, request_mock)
            vip_levels_future = executor.submit(_get_available_vip_levels_data, request_mock)
            
            # 获取结果
            vip_status_data = vip_status_future.result()
            vip_levels_data = vip_levels_future.result()
        
        # ✅ 修复：正确映射字段名，确保与前端DTO一致
        has_vip = vip_status_data.get('has_vip', False)
        raw_vip_info = vip_status_data.get('vip_info') if has_vip else None
        features = vip_status_data.get('features', [])
        refund_active = vip_status_data.get('refund_active', False)
        available_level = vip_status_data.get('available_level')
        additional_stats = vip_status_data.get('additional_stats')
        
        # 🔧 修复：正确映射vipInfo字段，确保前端DTO能正确解析
        vip_info = None
        if raw_vip_info:
            vip_info = {
                # 基础VIP信息
                'level_id': raw_vip_info.get('level_id'),
                'name': raw_vip_info.get('name'),
                'level': raw_vip_info.get('level'),
                'upgrade_time': raw_vip_info.get('upgrade_time'),
                
                # 🔧 核心修复：正确映射返还相关字段到前端期望的字段名
                'refundActive': raw_vip_info.get('refund_active', False),
                'totalValidDays': raw_vip_info.get('refund_required_days', 30),
                'refundRequiredDays': raw_vip_info.get('refund_required_days', 30),
                'completedDays': raw_vip_info.get('completed_days', 0),
                'refundProgress': raw_vip_info.get('refund_progress', 0),
                'refundStatusLabel': raw_vip_info.get('refund_status_label', ''),
                'activePlansCount': raw_vip_info.get('active_plans_count', 0),
                
                # VIP等级详情
                'vipLevel': raw_vip_info.get('vip_level'),
                
                # 加成比例（从vip_level中提取）
                'swmtBonusRate': raw_vip_info.get('vip_level', {}).get('swmt_bonus_rate'),
                'expBonusRate': raw_vip_info.get('vip_level', {}).get('exp_bonus_rate'),
            }
        
        # 处理VIP等级列表数据
        available_levels = vip_levels_data.get('levels', [])
        
        # 为每个VIP等级添加必要的字段
        processed_levels = []
        for level in available_levels:
            # 确保每个等级都有必要的字段
            processed_level = {
                'id': level.get('id'),
                'name': level.get('name'),
                'level': level.get('level'),
                'price': level.get('price', {}),
                'features': level.get('features', []),
                'refund_plan': level.get('refund_plan'),
            }
            
            # 添加加成比例字段（从features中提取或设置默认值）
            swmt_bonus_rate = 1.0
            exp_bonus_rate = 1.0
            
            for feature in level.get('features', []):
                if feature.get('name') == '任务加成':
                    swmt_bonus_rate = 1.0 + feature.get('bonus_rate', 0)
                elif feature.get('name') == '经验加成':
                    exp_bonus_rate = 1.0 + feature.get('bonus_rate', 0)
            
            processed_level['swmt_bonus_rate'] = swmt_bonus_rate
            processed_level['exp_bonus_rate'] = exp_bonus_rate
            
            processed_levels.append(processed_level)
        
        # ✅ 修复：组装聚合数据，使用前端期望的字段名
        aggregated_data = {
            'hasVip': has_vip,                    # ✅ 前端期望的字段名
            'vipInfo': vip_info,                  # ✅ 前端期望的字段名
            'features': features,
            'refundActive': refund_active,        # ✅ 前端期望的字段名
            'availableLevel': available_level,
            'availableLevels': processed_levels,   # ✅ 前端期望的字段名
            'additionalStats': additional_stats,   # 🔧 新增：添加VIP额外收益数据
        }
        
        # 数据质量检查
        data_quality = {
            'vip_status_complete': bool(vip_status_data),
            'available_levels_complete': bool(processed_levels),
        }
        
        aggregated_data['data_quality'] = data_quality
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        logger.info(f"VIP购买聚合数据请求完成 - 用户: {request.user.username}, 耗时: {processing_time:.3f}秒")
        logger.info(f"返回数据: hasVip={has_vip}, availableLevels数量={len(processed_levels)}")
        logger.debug(f"数据质量: {data_quality}")
        
        return aggregated_data

    @staticmethod
    def purchase_vip(user_request_object, vip_level_id):
        """
        处理VIP购买逻辑
        
        Args:
            user_request_object: Django HttpRequest对象，包含用户信息
            vip_level_id: 要购买的VIP等级ID
            
        Returns:
            dict: 购买结果数据
        """
        user = user_request_object.user
        start_time = time.time()
        
        logger.info(f"开始为用户 {user.email} 处理VIP等级 {vip_level_id} 的购买请求")
        
        try:
            # ✅ 修复：正确调用现有的VIP购买视图
            view = VIPPurchaseView()
            view.request = user_request_object._request if hasattr(user_request_object, '_request') else user_request_object
            
            # ✅ 修复：构造正确的购买请求数据，使用DRF的方式
            from django.http import QueryDict
            from rest_framework.request import Request
            from rest_framework.parsers import JSONParser
            import io
            import json
            
            # 构造POST数据
            purchase_data = {
                'level_id': vip_level_id,
                'payment_method': 'usdt_balance'  # 默认使用USDT余额支付
            }
            
            # ✅ 修复：正确模拟DRF Request对象
            original_request = user_request_object._request if hasattr(user_request_object, '_request') else user_request_object
            
            # 创建新的请求数据
            data_json = json.dumps(purchase_data)
            stream = io.BytesIO(data_json.encode())
            
            # 模拟POST请求
            mock_request = type(original_request)(original_request.META.copy())
            mock_request.method = 'POST'
            mock_request._body = data_json.encode()
            mock_request.content_type = 'application/json'
            mock_request.user = user
            
            # 创建DRF Request对象
            drf_request = Request(mock_request, parsers=[JSONParser()])
            drf_request._full_data = purchase_data
            
            response = view.post(drf_request)
            
            if hasattr(response, 'data') and response.data.get('code') == 200:
                purchase_result = response.data.get('data')
                end_time = time.time()
                logger.info(f"用户 {user.email} VIP等级 {vip_level_id} 购买成功，耗时 {end_time - start_time:.3f}s")
                
                # ✅ 购买成功后，重新获取用户VIP状态
                class RequestMock:
                    def __init__(self, original_request):
                        self._request = original_request
                        self.user = original_request.user
                        self.META = original_request.META
                        self.method = original_request.method
                
                request_mock = RequestMock(original_request)
                updated_vip_status = _get_current_vip_status_data(request_mock)
                
                return {
                    "purchase_success": True,
                    "purchase_result": purchase_result,
                    "updated_vip_status": updated_vip_status,
                    "message": "VIP购买成功"
                }
            else:
                error_msg = "未知错误"
                if hasattr(response, 'data'):
                    error_msg = response.data.get('message', '购买失败')
                
                logger.warning(f"用户 {user.email} VIP等级 {vip_level_id} 购买失败: {error_msg}")
                return {
                    "purchase_success": False,
                    "error": error_msg,
                    "message": error_msg
                }
                
        except Exception as e:
            end_time = time.time()
            logger.error(f"用户 {user.email} VIP等级 {vip_level_id} 购买处理失败，耗时 {end_time - start_time:.3f}s: {str(e)}", exc_info=True)
            return {
                "purchase_success": False,
                "error": str(e),
                "message": "VIP购买失败，请稍后重试"
            } 