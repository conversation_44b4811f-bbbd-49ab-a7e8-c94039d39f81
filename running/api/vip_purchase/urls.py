from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON>er
from .views import VipPurchaseViewSet

router = DefaultRouter()
# The endpoint will be /api/app/v1/vip/purchase-data/ (if included with prefix 'vip/purchase-data/' in main urls)
router.register(r'', VipPurchaseViewSet, basename='vip_purchase')

app_name = 'api_vip_purchase'  # Added app_name for namespacing if needed

urlpatterns = [
    path('', include(router.urls)),
] 