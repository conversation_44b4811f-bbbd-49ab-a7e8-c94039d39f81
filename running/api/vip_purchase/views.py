from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from .services import VipPurchaseService
import logging
import time
import json
import os
import datetime

# 设置vip_purchase专用日志
vip_purchase_logger = logging.getLogger('vip_purchase.api')

# 确保日志目录存在
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# 设置vip_purchase日志文件处理器
vip_purchase_log_file = os.path.join(log_dir, 'vip_purchase.log')
file_handler = logging.FileHandler(vip_purchase_log_file)
file_handler.setLevel(logging.DEBUG)

# 设置格式
formatter = logging.Formatter('%(levelname)s %(asctime)s %(name)s %(process)d %(thread)d %(message)s')
file_handler.setFormatter(formatter)

# 将处理器添加到logger
vip_purchase_logger.addHandler(file_handler)
vip_purchase_logger.setLevel(logging.DEBUG)

# 记录启动信息
vip_purchase_logger.info(f"VIP Purchase API module initialized at {datetime.datetime.now().isoformat()}")


class VipPurchaseViewSet(ViewSet):
    """
    VIP购买页面聚合接口视图集
    提供VIP购买页面所需的聚合数据，包括用户VIP状态、可购买VIP等级等信息
    """
    permission_classes = [IsAuthenticated]

    def list(self, request):
        """
        获取VIP购买页面的聚合数据
        对应前端的 VIP Purchase Page
        
        返回数据包括：
        - 用户当前VIP状态
        - 可购买的VIP等级列表
        - VIP权益信息
        - 购买规则说明
        """
        user = request.user
        request_id = getattr(request, 'request_id', '-')  # Assuming RequestIDMiddleware is in place
        start_time = time.time()
        
        # 增强日志，添加更多追踪信息
        vip_purchase_logger.info(f"[{request_id}] VipPurchaseViewSet: VIP purchase data requested by user {user.id} ({user.email}). IP: {request.META.get('REMOTE_ADDR')}")
        
        try:
            # 传递整个request对象给服务层
            vip_purchase_data = VipPurchaseService.get_vip_purchase_data(request)
            
            # 数据完整性检查和详细记录
            data_quality_report = {
                'vip_status_complete': bool(vip_purchase_data.get('hasVip') is not None),
                'available_levels_complete': bool(vip_purchase_data.get('availableLevels')),
                'data_quality_from_service': vip_purchase_data.get('data_quality', {}),
            }
            
            complete_modules = sum(bool(v) for v in data_quality_report.values() if isinstance(v, bool))
            total_modules = len([v for v in data_quality_report.values() if isinstance(v, bool)])
            
            vip_purchase_logger.info(f"[{request_id}] Data quality: {complete_modules}/{total_modules} modules complete - {data_quality_report}")
            
            # 检查可购买VIP等级数据
            available_levels = vip_purchase_data.get('availableLevels', [])
            vip_purchase_logger.info(f"[{request_id}] VipPurchaseViewSet: User {user.email} has {len(available_levels)} available VIP levels")
            
            # 检查用户当前VIP状态
            has_vip = vip_purchase_data.get('hasVip', False)
            vip_info = vip_purchase_data.get('vipInfo')
            vip_purchase_logger.info(f"[{request_id}] VipPurchaseViewSet: User {user.email} current VIP status: hasVip={has_vip}, vipInfo={'present' if vip_info else 'null'}")
            
            # Standard success response structure from backend rules
            response_payload = {
                "code": 200,
                "message": "操作成功",  # "Operation successful"
                "data": vip_purchase_data,
                "timestamp": int(time.time()),
                # 添加数据质量元信息（可选，用于客户端调试）
                "_meta": {
                    "data_completeness": f"{complete_modules}/{total_modules}",
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "cache_used": False,  # 这个值应该从VipPurchaseService返回，目前先设为False
                } if request.GET.get('include_meta', '').lower() == 'true' else None
            }
            
            # 如果数据不完整，添加警告头
            if complete_modules < total_modules:
                response_payload["_warnings"] = [f"部分数据模块不完整: {total_modules - complete_modules} 个模块缺失或错误"]
            
            end_time = time.time()
            # 添加响应时间日志
            vip_purchase_logger.info(f"[{request_id}] VipPurchaseViewSet: VIP purchase data served to user {user.id} in {end_time - start_time:.3f}s")
            
            # 将完整响应数据添加到debug级别日志，便于调试
            try:
                # 只记录关键字段，避免日志过大
                key_data = {
                    'current_vip_status': {'hasVip': has_vip},
                    'available_levels_count': len(available_levels),
                    'data_quality': data_quality_report
                }
                vip_purchase_logger.debug(f"[{request_id}] VipPurchaseViewSet Response key data: {json.dumps(key_data)}")
            except Exception as json_error:
                vip_purchase_logger.warning(f"[{request_id}] Failed to log response data: {str(json_error)}")
                
            return Response(response_payload)
            
        except Exception as e:
            end_time = time.time()
            vip_purchase_logger.error(f"[{request_id}] Error fetching VIP purchase data for user {user.id} after {end_time - start_time:.3f}s: {str(e)}", exc_info=True)
            # Standard error response structure
            return Response({
                "code": 500,  # Or a more specific error code
                "message": "获取VIP购买数据失败，请稍后重试。",  # "Failed to fetch VIP purchase data, please try again later."
                "details": str(e),
                "timestamp": int(time.time()),
                "_meta": {
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "error_type": type(e).__name__,
                } if request.GET.get('include_meta', '').lower() == 'true' else None
            }, status=500)

    @action(detail=False, methods=['post'], url_path='purchase')
    def purchase_vip(self, request):
        """
        购买VIP等级
        
        请求参数：
        - vip_level_id: VIP等级ID
        
        返回：
        - 购买结果
        - 更新后的VIP状态
        """
        user = request.user
        request_id = getattr(request, 'request_id', '-')
        start_time = time.time()
        
        vip_level_id = request.data.get('vip_level_id')
        
        vip_purchase_logger.info(f"[{request_id}] VipPurchaseViewSet: VIP purchase requested by user {user.id} ({user.email}) for level {vip_level_id}")
        
        try:
            # 验证参数
            if not vip_level_id:
                return Response({
                    "code": 400,
                    "message": "缺少必填参数：vip_level_id",
                    "timestamp": int(time.time()),
                }, status=400)
            
            # 调用服务层处理购买逻辑
            purchase_result = VipPurchaseService.purchase_vip(request, vip_level_id)
            
            end_time = time.time()
            vip_purchase_logger.info(f"[{request_id}] VipPurchaseViewSet: VIP purchase completed for user {user.id} in {end_time - start_time:.3f}s")
            
            return Response({
                "code": 200,
                "message": "VIP购买成功",
                "data": purchase_result,
                "timestamp": int(time.time()),
            })
            
        except Exception as e:
            end_time = time.time()
            vip_purchase_logger.error(f"[{request_id}] Error processing VIP purchase for user {user.id} after {end_time - start_time:.3f}s: {str(e)}", exc_info=True)
            return Response({
                "code": 500,
                "message": "VIP购买失败，请稍后重试。",
                "details": str(e),
                "timestamp": int(time.time()),
            }, status=500) 