from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from rest_framework_simplejwt.tokens import RefreshToken
from django.core.cache import cache
from django.utils import timezone
from .models import UserToken, TokenBlacklist
import logging
import json

logger = logging.getLogger(__name__)

class CustomJWTAuthentication(JWTAuthentication):
    """自定义JWT认证 - 支持双 Token (Access/Refresh)，不依赖数据库"""
    TOKEN_CACHE_VERSION = 1
    TOKEN_CACHE_TIMEOUT = 300  # 5分钟
    TOKEN_BLACKLIST_CACHE_TIMEOUT = 1800  # 30分钟

    def authenticate(self, request):
        """认证请求"""
        try:
            if not request or not hasattr(request, 'META'):
                return None
            
            header = self.get_header(request)
            if header is None:
                return None

            raw_token = self.get_raw_token(header)
            if raw_token is None:
                return None

            # 🔥 核心修复：在使用缓存前，必须先检查黑名单状态
            # 检查token是否在黑名单中（这是必需的安全检查）
            if self._is_token_blacklisted(raw_token.decode()):
                raise InvalidToken('Token已被禁用')

            # 检查缓存中的认证结果（黑名单检查通过后才能使用缓存）
            cache_key = f'auth_result_{raw_token.decode()}'
            cached_result = cache.get(cache_key, version=self.TOKEN_CACHE_VERSION)
            
            if cached_result:
                user = self._get_user_from_cache(cached_result)
                if user:
                    # 🔥 核心修复：Access/Refresh Token 不需要更新数据库，只记录访问日志
                    self._log_token_usage(raw_token.decode(), user, request)
                    return user, raw_token.decode()

            # 验证token
            validated_token = self.get_validated_token(raw_token)
            
            # 获取并验证用户
            user = self.get_user_by_token(validated_token)
            if not user or not user.is_active:
                raise InvalidToken('用户不存在或已被禁用')

            # 🔥 核心修复：AccessToken是自包含的JWT，不需要数据库验证
            # 只要JWT验证通过、用户有效、token未被列入黑名单即可
            # 完全移除对UserToken表的依赖

            # 记录token使用情况（仅用于日志，不依赖数据库）
            self._log_token_usage(raw_token.decode(), user, request)
            
            # 缓存认证结果
            self._cache_auth_result(raw_token.decode(), user)
            
            return user, raw_token.decode()

        except TokenError as e:
            self._log_auth_error(e, request)
            raise InvalidToken(str(e))
        except InvalidToken as e:
            # 🔥 修复：直接重新抛出InvalidToken，保持原始错误消息
            self._log_auth_error(e, request)
            raise e
        except Exception as e:
            self._log_auth_error(e, request)
            raise InvalidToken({
                'detail': '认证失败',
                'code': 'token_not_valid'
            })

    def get_user_by_token(self, validated_token):
        """
        🔥 关键修复：正确处理JWT Token中的字符串用户ID

        JWT Token中存储的user_id可能是字符串格式（如'S0401025'），
        但Django User模型的id字段是整数。我们需要使用user_id字段而不是id字段来查找用户。
        """
        try:
            # 从JWT Token中获取用户ID
            user_id = validated_token.get('user_id')
            if not user_id:
                raise InvalidToken('Token中缺少用户ID')

            # 🔥 关键修复：使用user_id字段查找用户，而不是id字段
            # user_id字段存储字符串ID（如'S0401025'），id字段是数据库自增整数
            from users.models import User
            user = User.objects.get(user_id=user_id)

            return user

        except User.DoesNotExist:
            raise InvalidToken(f'用户不存在: {user_id}')
        except Exception as e:
            raise InvalidToken(f'获取用户失败: {str(e)}')

    def _is_token_blacklisted(self, token):
        """
        检查token是否在黑名单中。
        直接调用 TokenBlacklist 模型中的 is_blacklisted 方法，以确保逻辑统一。
        """
        try:
            # 直接使用TokenBlacklist模型的类方法，该方法已包含缓存逻辑
            is_blacklisted = TokenBlacklist.is_blacklisted(token)
            if is_blacklisted:
                 logger.warning(f"检测到黑名单中的Token: {token[:16]}...")
            return is_blacklisted
        except Exception as e:
            # 记录异常，并默认为非黑名单，以防检查逻辑本身出错导致服务不可用
            logger.error(f"检查token黑名单时发生未知错误: {str(e)}", exc_info=True)
            return False

    def _log_token_usage(self, token, user, request):
        """记录token使用情况（仅日志，不操作数据库）"""
        try:
            # 🔥 核心修复：Access/Refresh Token 不需要更新数据库，只记录访问日志
            device_info = self._get_device_info(request)
            logger.info(
                f"JWT访问记录 - "
                f"用户ID: {user.id}, "
                f"用户名: {user.username}, "
                f"IP: {device_info['ip']}, "
                f"路径: {device_info['path']}, "
                f"Token: {token[:16]}..., "
                f"时间: {timezone.now().isoformat()}"
            )
        except Exception as e:
            logger.warning(
                f"记录token使用日志失败: {str(e)}, "
                f"token={token[:32]}..., user_id={user.id}",
                exc_info=True
            )

    def _get_device_info(self, request):
        """获取设备信息"""
        return {
            'ip': request.META.get('REMOTE_ADDR', 'unknown'),
            'user_agent': request.META.get('HTTP_USER_AGENT', 'unknown'),
            'path': request.path,
            'method': request.method
        }

    def _cache_auth_result(self, token, user):
        """缓存认证结果"""
        cache_key = f'auth_result_{token}'
        cache_data = {
            'user_id': user.user_id,  # 🔥 修复：使用字符串user_id而不是数字id
            'username': user.username,
            'is_active': user.is_active,
            'cached_at': timezone.now().timestamp()
        }
        cache.set(
            cache_key,
            json.dumps(cache_data),
            self.TOKEN_CACHE_TIMEOUT,
            version=self.TOKEN_CACHE_VERSION
        )

    def _get_user_from_cache(self, cached_data):
        """从缓存中获取用户"""
        try:
            data = json.loads(cached_data)
            # 🔥 修复：使用user_id字段查询用户
            user = self.user_model.objects.get(
                user_id=data['user_id'],  # 使用字符串user_id字段
                is_active=data['is_active']
            )
            return user if user.is_active else None
        except Exception:
            return None

    def _log_auth_error(self, error, request):
        """记录认证错误"""
        log_data = {
            'error': str(error),
            'path': request.path,
            'method': request.method,
            'ip': request.META.get('REMOTE_ADDR', 'unknown'),
            'user_agent': request.META.get('HTTP_USER_AGENT', 'unknown')
        }
        logger.error(
            f"认证失败: {json.dumps(log_data, ensure_ascii=False)}",
            exc_info=True
        )

    def get_raw_token(self, header):
        """获取原始令牌字符串"""
        try:
            parts = header.decode().split()
        except UnicodeError:
            return None

        if len(parts) == 0:
            return None

        if parts[0] not in ('Bearer', 'Token'):
            return None

        if len(parts) != 2:
            return None

        return parts[1].encode() 