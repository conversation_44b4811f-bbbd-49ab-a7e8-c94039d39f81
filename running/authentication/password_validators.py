# running/authentication/password_validators.py
import re
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

class PasswordComplexityValidator:
    """
    验证密码是否满足复杂度要求（至少包含大写、小写、数字、特殊符号中的三类）。
    """
    def __init__(self, min_categories=3):
        self.min_categories = min_categories
        # 定义特殊符号集合，可以根据需要调整
        # 注意：避免包含可能引起问题的字符，如引号、反斜杠等，除非做好了转义处理
        self.special_characters = r"!@#$%^&*()_+-=[]{}|;:,.<>/?~" 

    def validate(self, password, user=None):
        categories_met = 0
        errors = []

        # 1. 检查小写字母
        if re.search(r'[a-z]', password):
            categories_met += 1
        # else:
            # errors.append(_("密码必须包含至少一个小写字母。")) # 先不单独提示，最后统一提示

        # 2. 检查大写字母
        if re.search(r'[A-Z]', password):
            categories_met += 1
        # else:
            # errors.append(_("密码必须包含至少一个大写字母。"))

        # 3. 检查数字
        if re.search(r'[0-9]', password):
            categories_met += 1
        # else:
            # errors.append(_("密码必须包含至少一个数字。"))

        # 4. 检查特殊符号
        # 对特殊字符集合中的每个字符进行转义，以安全地用于正则表达式
        escaped_special_chars = re.escape(self.special_characters)
        if re.search(f'[{escaped_special_chars}]', password):
             categories_met += 1
        # else:
            # # 如果少于3类且特殊符号缺失，才提示需要特殊符号
            # if categories_met < self.min_categories:
            #      errors.append(_("密码必须包含至少一个特殊符号 (例如 !@#$%)。"))

        # 最终检查：是否满足至少 N 类
        if categories_met < self.min_categories:
            # 为了简洁，统一提示复杂度不足
            raise ValidationError(
                 _("Password does not meet complexity requirements. It must contain at least three of the following: uppercase letters, lowercase letters, numbers, and special symbols (%s)." % self.special_characters),
                 code='password_insufficient_complexity'
             )

    def get_help_text(self):
        return _(
            "Your password must contain at least %(min_categories)d of the following character types: uppercase letters, lowercase letters, numbers, and special symbols (%(special_chars)s)."
        ) % {'min_categories': self.min_categories, 'special_chars': self.special_characters} 