# Generated by Django 4.2.11 on 2025-04-09 16:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0003_historicalusertoken_token_type_usertoken_token_type_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='historicaltokenblacklist',
            name='token',
            field=models.Char<PERSON>ield(db_index=True, max_length=2000, verbose_name='令牌'),
        ),
        migrations.AlterField(
            model_name='historicalusertoken',
            name='token',
            field=models.Char<PERSON><PERSON>(db_index=True, max_length=2000, verbose_name='令牌'),
        ),
        migrations.AlterField(
            model_name='tokenblacklist',
            name='token',
            field=models.Char<PERSON>ield(db_index=True, max_length=2000, unique=True, verbose_name='令牌'),
        ),
        migrations.AlterField(
            model_name='usertoken',
            name='token',
            field=models.<PERSON><PERSON><PERSON><PERSON>(db_index=True, max_length=2000, unique=True, verbose_name='令牌'),
        ),
    ]
