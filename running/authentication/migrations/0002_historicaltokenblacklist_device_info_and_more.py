# Generated by Django 4.2.11 on 2025-02-17 05:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicaltokenblacklist',
            name='device_info',
            field=models.JSONField(blank=True, default=dict, verbose_name='设备信息'),
        ),
        migrations.AddField(
            model_name='historicaltokenblacklist',
            name='user_id',
            field=models.IntegerField(blank=True, db_index=True, null=True, verbose_name='用户ID'),
        ),
        migrations.AddField(
            model_name='historicalusertoken',
            name='device_info',
            field=models.JSONField(blank=True, default=dict, verbose_name='设备信息'),
        ),
        migrations.AddField(
            model_name='tokenblacklist',
            name='device_info',
            field=models.JSONField(blank=True, default=dict, verbose_name='设备信息'),
        ),
        migrations.AddField(
            model_name='tokenblacklist',
            name='user_id',
            field=models.IntegerField(blank=True, db_index=True, null=True, verbose_name='用户ID'),
        ),
        migrations.AddField(
            model_name='usertoken',
            name='device_info',
            field=models.JSONField(blank=True, default=dict, verbose_name='设备信息'),
        ),
        migrations.AlterField(
            model_name='historicaltokenblacklist',
            name='blacklisted_at',
            field=models.DateTimeField(blank=True, db_index=True, editable=False, verbose_name='加入黑名单时间'),
        ),
        migrations.AlterField(
            model_name='historicalusertoken',
            name='created_at',
            field=models.DateTimeField(blank=True, db_index=True, editable=False, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='historicalusertoken',
            name='expires_at',
            field=models.DateTimeField(db_index=True, verbose_name='过期时间'),
        ),
        migrations.AlterField(
            model_name='historicalusertoken',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否有效'),
        ),
        migrations.AlterField(
            model_name='tokenblacklist',
            name='blacklisted_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='加入黑名单时间'),
        ),
        migrations.AlterField(
            model_name='tokenblacklist',
            name='token',
            field=models.CharField(db_index=True, max_length=255, unique=True, verbose_name='令牌'),
        ),
        migrations.AlterField(
            model_name='usertoken',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='usertoken',
            name='expires_at',
            field=models.DateTimeField(db_index=True, verbose_name='过期时间'),
        ),
        migrations.AlterField(
            model_name='usertoken',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否有效'),
        ),
        migrations.AlterField(
            model_name='usertoken',
            name='token',
            field=models.CharField(db_index=True, max_length=255, unique=True, verbose_name='令牌'),
        ),
        migrations.AddIndex(
            model_name='tokenblacklist',
            index=models.Index(fields=['blacklisted_at'], name='authenticat_blackli_2054b3_idx'),
        ),
        migrations.AddIndex(
            model_name='tokenblacklist',
            index=models.Index(fields=['user_id', 'blacklisted_at'], name='authenticat_user_id_97b221_idx'),
        ),
        migrations.AddIndex(
            model_name='usertoken',
            index=models.Index(fields=['user', 'is_active', 'expires_at'], name='authenticat_user_id_529ed6_idx'),
        ),
        migrations.AddIndex(
            model_name='usertoken',
            index=models.Index(fields=['token', 'is_active'], name='authenticat_token_e82232_idx'),
        ),
    ]
