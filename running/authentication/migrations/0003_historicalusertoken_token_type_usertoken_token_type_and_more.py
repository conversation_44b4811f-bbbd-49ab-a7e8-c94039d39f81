# Generated by Django 4.2.11 on 2025-04-03 14:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0002_historicaltokenblacklist_device_info_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalusertoken',
            name='token_type',
            field=models.CharField(choices=[('access', '访问令牌'), ('refresh', '刷新令牌')], default='access', max_length=20, verbose_name='令牌类型'),
        ),
        migrations.AddField(
            model_name='usertoken',
            name='token_type',
            field=models.CharField(choices=[('access', '访问令牌'), ('refresh', '刷新令牌')], default='access', max_length=20, verbose_name='令牌类型'),
        ),
        migrations.AddIndex(
            model_name='usertoken',
            index=models.Index(fields=['token_type', 'is_active'], name='authenticat_token_t_bf5e0c_idx'),
        ),
    ]
