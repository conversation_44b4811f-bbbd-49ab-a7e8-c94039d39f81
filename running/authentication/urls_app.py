from django.urls import path
from . import views_app
from rest_framework_simplejwt.views import TokenVerifyView

app_name = 'authentication_app'

urlpatterns = [
    # 前端API认证路径
    path('login/', views_app.AppLoginView.as_view(), name='login'),
    path('register/', views_app.AppRegisterView.as_view(), name='register'),
    path('verify-code/', views_app.AppVerifyCodeView.as_view(), name='verify-code'),
    path('reset-password/', views_app.AppResetPasswordView.as_view(), name='reset-password'),
    path('token/refresh/', views_app.AppTokenRefreshView.as_view(), name='token-refresh'),
    path('token/verify/', TokenVerifyView.as_view(), name='token-verify'),
    path('logout/', views_app.AppLogoutView.as_view(), name='logout'),
    path('check-token/', views_app.AppCheckTokenView.as_view(), name='check-token'),
] 