"""
API请求频率限制工具

为API视图提供请求频率限制装饰器
"""

import time
from functools import wraps
from django.core.cache import cache
from django.conf import settings
from rest_framework.exceptions import Throttled
from rest_framework import status
import logging

from core.utils.api_utils import ApiResponse

logger = logging.getLogger(__name__)

def method_ratelimit(key='ip', rate='5/m', method=None):
    """
    基于方法的请求频率限制装饰器
    
    Args:
        key: 限制键类型，可选 'ip', 'user'
        rate: 频率限制，格式为 "次数/时间单位"，时间单位可为 s(秒), m(分), h(小时), d(天)
        method: 限制的HTTP方法，如 'GET', 'POST'，None表示所有方法
    
    Returns:
        装饰后的视图方法
    """
    # 解析频率限制
    count, period = _parse_rate(rate)
    
    def decorator(view_method):
        @wraps(view_method)
        def _wrapped_view(self, request, *args, **kwargs):
            # 检查方法是否需要限制
            if method and request.method != method:
                return view_method(self, request, *args, **kwargs)
            
            # 生成缓存键
            cache_key = _get_cache_key(key, request)
            
            # 获取当前计数
            current_count = cache.get(cache_key, 0)
            
            # 如果超过限制，则抛出异常
            if current_count >= count:
                logger.warning(
                    f"请求频率限制: {cache_key} 在 {period}秒内已请求 {current_count}次，超过限制 {count}次"
                )
                return ApiResponse(
                    code=429,
                    message="Too many requests. Please try again later.",
                    data=None,
                    status=status.HTTP_429_TOO_MANY_REQUESTS
                )
            
            # 更新计数
            if current_count == 0:
                cache.set(cache_key, 1, period)
            else:
                cache.incr(cache_key)
            
            # 执行原始视图方法
            return view_method(self, request, *args, **kwargs)
        
        return _wrapped_view
    
    return decorator

def _parse_rate(rate):
    """解析频率限制字符串"""
    count, period = rate.split('/')
    count = int(count)
    
    # 解析时间单位
    time_unit = period[-1]
    if time_unit == 's':
        seconds = int(period[:-1])
    elif time_unit == 'm':
        seconds = int(period[:-1]) * 60
    elif time_unit == 'h':
        seconds = int(period[:-1]) * 3600
    elif time_unit == 'd':
        seconds = int(period[:-1]) * 86400
    elif time_unit == 'w':
        seconds = int(period[:-1]) * 604800
    else:
        # 默认为秒
        seconds = int(period)
    
    return count, seconds

def _get_cache_key(key_type, request):
    """根据键类型生成缓存键"""
    view_path = request.resolver_match.view_name
    method = request.method
    
    if key_type == 'user':
        # 使用用户ID作为键
        if request.user.is_authenticated:
            key_id = f"user_{request.user.id}"
        else:
            # 未认证用户使用IP
            key_id = f"ip_{_get_client_ip(request)}"
    else:
        # 默认使用IP
        key_id = f"ip_{_get_client_ip(request)}"
    
    # 生成唯一键
    return f"ratelimit:{key_id}:{view_path}:{method}"

def _get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        return x_forwarded_for.split(',')[0].strip()
    return request.META.get('REMOTE_ADDR', 'unknown') 