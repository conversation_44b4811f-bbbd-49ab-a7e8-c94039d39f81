from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from django.core.cache import cache
from django.utils import timezone
import logging
import jwt
from django.conf import settings
import json
import base64
import hashlib
from core.utils.api_utils import ApiResponse  # 导入 ApiResponse 类
import re

logger = logging.getLogger(__name__)

# 添加兼容函数，内部调用 ApiResponse
def api_response(data=None, message="success", code=200, response_status=status.HTTP_200_OK):
    """
    统一API响应格式（兼容函数，内部使用ApiResponse）
    
    Args:
        data: 响应数据
        message: 响应消息
        code: 业务状态码
        response_status: HTTP状态码
    
    Returns:
        Response: 统一格式的响应对象
    """
    return ApiResponse(
        data=data,
        message=message,
        code=code,
        status=response_status
    )

def custom_exception_handler(exc, context):
    """自定义异常处理"""
    response = exception_handler(exc, context)
    
    if response is None:
        return response

    error_data = {
        'code': response.status_code,
        'message': str(exc),
        'data': {
            'error_type': exc.__class__.__name__,
            'error_detail': response.data if hasattr(response, 'data') else str(exc)
        }
    }

    # 添加请求上下文信息到日志
    request = context.get('request')
    if request:
        error_data['data'].update({
            'path': request.path,
            'method': request.method,
            'ip': request.META.get('REMOTE_ADDR', 'unknown'),
            'user_agent': request.META.get('HTTP_USER_AGENT', 'unknown')
        })

    # 特殊处理认证相关错误
    if response.status_code in [401, 403]:
        error_data['data'].update({
            'should_relogin': True,
            'auth_error': True
        })

    logger.error(
        f"API异常: {json.dumps(error_data, ensure_ascii=False)}",
        exc_info=True
    )
    
    return Response(error_data, status=response.status_code)

def decode_jwt_token(token, verify=True):
    """解码JWT令牌"""
    try:
        # 检查缓存
        cache_key = f'decoded_token_{token}'
        cached_result = cache.get(cache_key)
        
        if cached_result:
            return json.loads(cached_result)
        
        # 解码令牌
        decoded = jwt.decode(
            token,
            settings.SIMPLE_JWT['SIGNING_KEY'],
            algorithms=[settings.SIMPLE_JWT['ALGORITHM']] if verify else None,
            verify=verify
        )
        
        # 缓存结果
        cache.set(cache_key, json.dumps(decoded), timeout=300)  # 缓存5分钟
        return decoded
        
    except jwt.ExpiredSignatureError:
        logger.warning(f"令牌已过期: {token[:32]}...")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning(f"无效的令牌: {str(e)}, token={token[:32]}...")
        return None
    except Exception as e:
        logger.error(f"解码令牌时出错: {str(e)}", exc_info=True)
        return None

def get_token_from_request(request):
    """从请求中获取令牌"""
    # 1. 尝试从Authorization头获取
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')
    if auth_header.startswith('Bearer '):
        return auth_header.split(' ')[1]
    
    # 2. 尝试从X-Auth-Token头获取
    x_auth_token = request.META.get('HTTP_X_AUTH_TOKEN')
    if x_auth_token:
        return x_auth_token
    
    # 3. 尝试从cookie获取
    return request.COOKIES.get('jwt_access_token')

def clear_user_token_cache(user_id):
    """清理用户相关的令牌缓存"""
    try:
        from .models import UserToken
        # 获取用户的所有令牌
        tokens = UserToken.objects.filter(
            user_id=user_id,
            is_active=True
        ).values_list('token', flat=True)
        
        # 准备要清理的缓存键
        cache_keys = []
        for token in tokens:
            cache_keys.extend([
                f'token_valid_{token}',
                f'token_blacklist_{token}',
                f'auth_result_{token}',
                f'decoded_token_{token}'
            ])
        cache_keys.append(f'user_token_{user_id}')
        
        # 批量删除缓存
        if cache_keys:
            cache.delete_many(cache_keys)
            logger.info(f"已清理用户{user_id}的{len(cache_keys)}个缓存项")
        
        return True
    except Exception as e:
        logger.error(
            f"清理用户令牌缓存失败: user_id={user_id}, error={str(e)}",
            exc_info=True
        )
        return False

def is_token_expired(token_data):
    """检查令牌是否过期"""
    try:
        exp_timestamp = token_data.get('exp', 0)
        current_timestamp = timezone.now().timestamp()
        return exp_timestamp <= current_timestamp
    except Exception as e:
        logger.error(f"检查令牌过期状态时出错: {str(e)}", exc_info=True)
        return True

def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        return x_forwarded_for.split(',')[0].strip()
    return request.META.get('REMOTE_ADDR', 'unknown')

def get_device_info(request):
    """
    从请求中提取设备信息
    
    Args:
        request: Django/DRF请求对象
        
    Returns:
        dict: 设备信息字典
    """
    if not request:
        return {'device_type': 'unknown', 'os': 'unknown', 'browser': 'unknown'}
        
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    
    # 初始化设备信息
    device_info = {
        'user_agent': user_agent,
        'device_type': 'unknown',
        'os': 'unknown',
        'browser': 'unknown'
    }
    
    # 检测设备类型
    if re.search(r'Mobile|Android|iPhone|iPad|iPod', user_agent, re.I):
        device_info['device_type'] = 'mobile'
    else:
        device_info['device_type'] = 'desktop'
    
    # 检测操作系统
    if re.search(r'Windows', user_agent, re.I):
        device_info['os'] = 'Windows'
    elif re.search(r'Mac OS X', user_agent, re.I):
        device_info['os'] = 'macOS'
    elif re.search(r'iPhone|iPad|iPod', user_agent, re.I):
        device_info['os'] = 'iOS'
    elif re.search(r'Android', user_agent, re.I):
        device_info['os'] = 'Android'
    elif re.search(r'Linux', user_agent, re.I):
        device_info['os'] = 'Linux'
    
    # 检测浏览器
    if re.search(r'Chrome', user_agent, re.I) and not re.search(r'Chromium|OPR|Edge', user_agent, re.I):
        device_info['browser'] = 'Chrome'
    elif re.search(r'Firefox', user_agent, re.I):
        device_info['browser'] = 'Firefox'
    elif re.search(r'Safari', user_agent, re.I) and not re.search(r'Chrome|Chromium', user_agent, re.I):
        device_info['browser'] = 'Safari'
    elif re.search(r'Edge', user_agent, re.I):
        device_info['browser'] = 'Edge'
    elif re.search(r'MSIE|Trident', user_agent, re.I):
        device_info['browser'] = 'Internet Explorer'
    
    return device_info

def verify_client_integrity(request):
    """验证客户端完整性"""
    try:
        # 获取应用签名
        app_signature = request.META.get('HTTP_X_APP_SIGNATURE')
        if not app_signature:
            return True  # 如果没有签名，不验证
        
        # 获取请求时间戳
        timestamp = request.META.get('HTTP_X_REQUEST_TIMESTAMP')
        if not timestamp:
            logger.warning("缺少请求时间戳，无法验证应用签名")
            return False
        
        # 检查时间戳是否在合理范围内（防止重放攻击）
        current_ts = int(timezone.now().timestamp())
        request_ts = int(timestamp)
        if abs(current_ts - request_ts) > 300:  # 允许5分钟时差
            logger.warning(f"请求时间戳过期: {timestamp}, 当前时间: {current_ts}")
            return False
        
        # 实际项目中，这里应该实现完整的签名验证逻辑
        # 例如：验证请求体的哈希值与签名是否匹配
        # 简化版本，仅做示例
        return True
    except Exception as e:
        logger.error(f"验证客户端完整性失败: {str(e)}", exc_info=True)
        return False

def sanitize_request_data(data):
    """
    清洗和验证请求数据，防止XSS攻击等安全问题
    
    Args:
        data: 请求数据（字典或列表）
        
    Returns:
        dict/list: 清洗后的数据
    """
    if isinstance(data, dict):
        result = {}
        for key, value in data.items():
            # 过滤键名中的特殊字符
            clean_key = re.sub(r'[^\w\-\.]', '', str(key))
            # 递归处理值
            result[clean_key] = sanitize_request_data(value)
        return result
    elif isinstance(data, list):
        return [sanitize_request_data(item) for item in data]
    elif isinstance(data, str):
        # 过滤HTML标签和危险字符
        value = re.sub(r'<[^>]*>', '', data)
        # 过滤常见的注入模式
        value = re.sub(r'javascript:', '', value, flags=re.I)
        return value
    else:
        # 其他类型（数字、布尔值等）直接返回
        return data

def encode_sensitive_data(data, key=None):
    """简单的数据编码，用于传输敏感信息"""
    if not key:
        key = settings.SECRET_KEY[:16]
    
    try:
        # 简单编码示例，实际项目中应使用更安全的加密方法
        json_data = json.dumps(data)
        encoded = base64.b64encode(json_data.encode('utf-8')).decode('utf-8')
        return encoded
    except Exception as e:
        logger.error(f"编码敏感数据失败: {str(e)}", exc_info=True)
        return None 