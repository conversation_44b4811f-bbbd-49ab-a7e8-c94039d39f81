from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from simple_history.models import HistoricalRecords
from django.utils import timezone
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)

class UserToken(models.Model):
    """用户令牌模型"""
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='auth_tokens',
        verbose_name=_('用户')
    )
    token = models.CharField(_('令牌'), max_length=2000, unique=True, db_index=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True, db_index=True)
    expires_at = models.DateTimeField(_('过期时间'), db_index=True)
    is_active = models.BooleanField(_('是否有效'), default=True, db_index=True)
    last_used_at = models.DateTimeField(_('最后使用时间'), null=True, blank=True)
    device_info = models.JSONField(_('设备信息'), default=dict, blank=True)
    token_type = models.CharField(_('令牌类型'), max_length=20, default='access', choices=[
        ('access', '访问令牌'),
        ('refresh', '刷新令牌'),
    ])
    history = HistoricalRecords()

    class Meta:
        verbose_name = _('用户令牌')
        verbose_name_plural = _('用户令牌')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_active', 'expires_at']),
            models.Index(fields=['token', 'is_active']),
            models.Index(fields=['token_type', 'is_active']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.token_type} - {self.created_at}"

    def is_expired(self):
        """检查令牌是否过期"""
        return self.expires_at <= timezone.now()

    def extend_expiry(self, minutes=30):
        """延长令牌有效期"""
        if self.is_active and not self.is_expired():
            self.expires_at = timezone.now() + timezone.timedelta(minutes=minutes)
            self.save(update_fields=['expires_at'])
            # 更新缓存
            cache_key = f'user_token_{self.user.id}'
            cache.delete(cache_key)
            return True
        return False

    def deactivate(self, reason='手动失效'):
        """使令牌失效"""
        try:
            self.is_active = False
            self.save(update_fields=['is_active'])
            
            # 添加到黑名单
            TokenBlacklist.objects.create(
                token=self.token,
                reason=reason,
                user_id=self.user.id
            )
            
            # 清理缓存
            cache_keys = [
                f'token_valid_{self.token}',
                f'user_token_{self.user.id}',
                f'token_blacklist_{self.token}'
            ]
            cache.delete_many(cache_keys)
            
            logger.info(
                f"令牌已失效: token={self.token[:32]}..., "
                f"user_id={self.user.id}, reason={reason}"
            )
            return True
        except Exception as e:
            logger.error(
                f"令牌失效操作失败: {str(e)}, "
                f"token={self.token[:32]}..., "
                f"user_id={self.user.id}",
                exc_info=True
            )
            return False

    @classmethod
    def cleanup_expired(cls, days=7):
        """清理过期的令牌记录"""
        try:
            expired_date = timezone.now() - timezone.timedelta(days=days)
            expired_tokens = cls.objects.filter(
                expires_at__lt=expired_date,
                is_active=True
            )
            
            # 批量失效
            count = 0
            for token in expired_tokens:
                if token.deactivate('自动清理'):
                    count += 1
                    
            logger.info(f"清理过期令牌完成: 处理了{count}个令牌")
            return count
        except Exception as e:
            logger.error(f"清理过期令牌失败: {str(e)}", exc_info=True)
            return 0

class TokenBlacklist(models.Model):
    """令牌黑名单"""
    token = models.CharField(_('令牌'), max_length=2000, unique=True, db_index=True)
    blacklisted_at = models.DateTimeField(_('加入黑名单时间'), auto_now_add=True, db_index=True)
    reason = models.CharField(_('原因'), max_length=255, blank=True)
    user_id = models.IntegerField(_('用户ID'), null=True, blank=True, db_index=True)
    device_info = models.JSONField(_('设备信息'), default=dict, blank=True)
    history = HistoricalRecords()

    class Meta:
        verbose_name = _('令牌黑名单')
        verbose_name_plural = _('令牌黑名单')
        ordering = ['-blacklisted_at']
        indexes = [
            models.Index(fields=['blacklisted_at']),
            models.Index(fields=['user_id', 'blacklisted_at']),
        ]

    def __str__(self):
        return f"{self.token[:32]}... - {self.blacklisted_at}"

    @classmethod
    def cleanup_expired(cls, days=30):
        """清理过期的黑名单记录"""
        try:
            expired_date = timezone.now() - timezone.timedelta(days=days)
            deleted_count = cls.objects.filter(
                blacklisted_at__lt=expired_date
            ).delete()[0]
            
            logger.info(f"清理过期黑名单记录完成: 删除了{deleted_count}条记录")
            return deleted_count
        except Exception as e:
            logger.error(f"清理过期黑名单记录失败: {str(e)}", exc_info=True)
            return 0

    @classmethod
    def is_blacklisted(cls, token):
        """检查令牌是否在黑名单中"""
        cache_key = f'token_blacklist_{token}'
        result = cache.get(cache_key)
        
        if result is None:
            result = cls.objects.filter(token=token).exists()
            cache.set(cache_key, result, timeout=1800)  # 缓存30分钟
            
        return result
