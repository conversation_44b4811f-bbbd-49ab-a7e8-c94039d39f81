from django.shortcuts import render
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db import transaction
from .models import UserToken, TokenBlacklist
from django.core.cache import cache
import logging
from .utils import get_device_info, sanitize_request_data
from .middleware import TokenBlacklistManager
from core.utils.ip_utils import get_client_ip
from users.models import LoginRecord
import redis
from django.conf import settings
import time
from rest_framework_simplejwt.token_blacklist.models import OutstandingToken, BlacklistedToken
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiTypes, inline_serializer
from rest_framework import serializers
from message_center.services import MessageService
from config.models import SystemConfig
from django.core import mail
from django.utils.html import strip_tags
from django.template import Template, Context
from message_center.models import EmailTemplate
from django.core.mail import EmailMultiAlternatives
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from tasks.services.task_assignment import TaskAssignmentService
from users.services.session_manager import unified_secure_login_and_create_session, unified_secure_logout

User = get_user_model()
logger = logging.getLogger(__name__)

# Generic Error Response
GenericErrorResponseSerializer = inline_serializer(
    name='AuthGenericErrorResponse',
    fields={
        'code': serializers.IntegerField(),
        'message': serializers.CharField(),
        'data': serializers.DictField(required=False, allow_null=True)
    }
)

class BaseAppTokenView(APIView):
    """App Token基础视图"""
    
    def handle_exception(self, exc):
        """统一异常处理"""
        logger.error(f"App Token operation failed: {str(exc)}", exc_info=True)
        
        if hasattr(exc, 'status_code'):
            status_code = exc.status_code
        else:
            status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            
        return Response({
            'code': status_code,
            'message': 'An error occurred, please try again later',
            'data': None
        }, status=status_code)
    
    def get_blacklist_manager(self):
        """获取Token黑名单管理器"""
        redis_client = redis.from_url(settings.CACHES['default']['LOCATION'])
        return TokenBlacklistManager(redis_client)
    
    def get_access_token_lifetime(self):
        """获取访问令牌有效期"""
        from django.conf import settings
        return settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME']
    
    def get_refresh_token_lifetime(self):
        """获取刷新令牌有效期"""
        from django.conf import settings
        return settings.SIMPLE_JWT['REFRESH_TOKEN_LIFETIME']
        
    def create_token(self, user, token_type, lifetime):
        """创建用户令牌
        
        Args:
            user: 用户对象
            token_type: 令牌类型，建议使用'sliding'
            lifetime: 令牌有效期
            
        Returns:
            UserToken: 创建的令牌对象
        """
        # 获取设备信息
        device_info = get_device_info(getattr(self, 'request', None))
        
        # ▶️ 改为使用RefreshToken双Token机制
        refresh_token_obj = RefreshToken.for_user(user)

        # 🔥 关键修复：添加自定义payload，确保Token包含正确的用户信息
        refresh_token_obj['user_id'] = user.user_id  # 使用字符串user_id而不是数字id
        refresh_token_obj['email'] = user.email      # 添加email字段
        refresh_token_obj['username'] = user.username # 添加username字段

        token_value = str(refresh_token_obj)
        
        # 🔥 BOSS核心修复：精确的Token清理逻辑
        # 不再一刀切清理所有Token，而是配合单设备登录机制
        # 这个清理应该在单设备登录逻辑完成后进行，或者由单设备登录逻辑来处理
        
        # 仅清理当前设备的旧Token（如果存在），避免影响其他正在登录的设备
        if hasattr(self, 'request') and self.request:
            current_device_id = getattr(self.request, '_current_device_id', None)
            if current_device_id:
                # 只清理当前设备的旧Token
                UserToken.objects.filter(
                    user=user,
                    token_type__in=['access', 'refresh', 'sliding'],
                    device_info__contains=current_device_id,
                    is_active=True
                ).update(is_active=False)
                logger.info(f"清理用户 {user.id} 在设备 {current_device_id} 上的旧Token")
            else:
                # 如果无法确定设备ID，保守处理：不清理任何Token
                # 让单设备登录逻辑来处理Token冲突
                logger.warning(f"无法确定设备ID，跳过Token清理，交由单设备登录逻辑处理")
        else:
            logger.warning(f"无request上下文，跳过Token清理")
        
        # 创建令牌记录 - 统一使用sliding类型
        user_token = UserToken.objects.create(
            user=user,
            token=token_value,
            token_type='refresh',  # 双Token机制存储refresh token
            expires_at=timezone.now() + lifetime,
            is_active=True,
            device_info=device_info,
            last_used_at=timezone.now()
        )
        
        return user_token

@extend_schema(
    tags=["App - Authentication"],
    summary="App 用户登录",
    description="使用邮箱和密码进行登录，成功后返回 Access Token 和 Refresh Token。支持设备验证和冲突检测。",
    request=inline_serializer(
        name='AppLoginRequest',
        fields={
            'email': serializers.EmailField(),
            'password': serializers.CharField(),
            'device_id': serializers.CharField(required=True, help_text="设备唯一标识，用于设备验证"),
            'health_data': serializers.JSONField(required=False, help_text="设备健康数据, e.g. {'steps': 1, 'distance': 1.0, 'calories': 1}")
        }
    ),
    responses={
        200: inline_serializer(
            name='AppLoginSuccessResponse',
            fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': inline_serializer(
                    name='AppLoginSuccessData',
                    fields={
                        'access_token': serializers.CharField(),
                        'refresh_token': serializers.CharField(),
                        'expires_in': serializers.IntegerField(),
                        'user': inline_serializer(
                            name='AppLoginUserInfo',
                            fields={
                                'id': serializers.IntegerField(),
                                'user_id': serializers.CharField(),
                                'email': serializers.EmailField(),
                                'username': serializers.CharField(),
                                'is_agent': serializers.BooleanField(),
                                'agent_id': serializers.CharField(allow_null=True),
                                'is_active_member': serializers.BooleanField(),
                            }
                        )
                    }
                )
            }
        ),
        400: GenericErrorResponseSerializer,
        401: GenericErrorResponseSerializer,
        403: GenericErrorResponseSerializer,
        409: inline_serializer(
            name='AppLoginDeviceConflictResponse',
            fields={
                'code': serializers.IntegerField(default=409),
                'message': serializers.CharField(default='Device conflict detected'),
                'data': inline_serializer(
                    name='DeviceConflictData',
                    fields={
                        'conflict_type': serializers.CharField(),
                        'current_device': serializers.DictField(),
                        'new_device': serializers.DictField(),
                    }
                )
            }
        ),
        500: GenericErrorResponseSerializer,
    }
)
class AppLoginView(BaseAppTokenView):
    """App登录视图"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        """用户登录"""
        try:
            # 参数校验
            email = request.data.get('email')
            password = request.data.get('password')
            device_id = request.data.get('device_id')
            health_data = request.data.get('health_data') # 接收健康数据
            
            if not email or not password or not device_id:
                return Response({
                    'code': 400,
                    'message': 'Email, password and device_id are required',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
                
            # 获取IP地址
            ip_address = get_client_ip(request)
                
            # 验证用户身份
            try:
                user = User.objects.get(email=email, is_active=True)
                
                # 验证密码
                if not user.check_password(password):
                    logger.warning(f"Login failed: Invalid password for {email} - IP: {ip_address}")
                    return Response({
                        'code': 401,
                        'message': 'Invalid email or password',
                        'data': None
                    }, status=status.HTTP_401_UNAUTHORIZED)
                    
                # 检查用户状态
                if user.is_frozen:
                    logger.warning(f"Login failed: User account frozen - {email} - IP: {ip_address}")
                    return Response({
                        'code': 403,
                        'message': 'Account has been frozen',
                        'data': None
                    }, status=status.HTTP_403_FORBIDDEN)
                    
            except User.DoesNotExist:
                logger.warning(f"Login failed: User not found - {email} - IP: {ip_address}")
                return Response({
                    'code': 401,
                    'message': 'Invalid email or password',
                    'data': None
                }, status=status.HTTP_401_UNAUTHORIZED)
                
            # 生成JWT Token
            with transaction.atomic():
                # 获取设备信息
                device_info = get_device_info(request)
                device_info['device_id'] = device_id
                device_info['ip'] = ip_address

                # 验证健康数据
                if not health_data:
                    health_data = {'steps': 0, 'distance': 0.0, 'calories': 0}
                    logger.warning(f"用户 {email} 登录时未提供 health_data，将使用零值创建会话。")
                
                # 🔥 v14.1核心修复：获取前端权限状态
                actual_permissions = request.data.get('permissions')
                if actual_permissions is None:
                    # 🔥 兼容性处理：如果前端未提供权限状态，设置默认值
                    logger.warning(f"⚠️ 用户 {email} 登录时未提供permissions，使用默认权限状态")
                    actual_permissions = {'steps': False, 'distance': False, 'calories': False}
                
                logger.info(f"🔍 用户 {email} 登录权限状态: {actual_permissions}")
                
                # 🆕 处理健康数据元信息
                health_data_meta = request.data.get('health_data_meta', {})
                if health_data_meta:
                    device_info['health_data_meta'] = health_data_meta
                    if health_data_meta.get('is_simulator', False):
                        logger.warning(f"🚨 用户 {email} 使用iOS模拟器登录，健康数据为模拟数据")
                
                # ▶️ 生成双 Token
                refresh_token_obj = RefreshToken.for_user(user)

                # 🔥 关键修复：添加自定义payload，确保Token包含正确的用户信息
                refresh_token_obj['user_id'] = user.user_id  # 使用字符串user_id而不是数字id
                refresh_token_obj['email'] = user.email      # 添加email字段
                refresh_token_obj['username'] = user.username # 添加username字段

                refresh_token_str = str(refresh_token_obj)
                access_token_str = str(refresh_token_obj.access_token)
                
                # 🔥 v14.1核心修复：统一处理会话创建，传递前端实际权限状态
                unified_secure_login_and_create_session(user, device_info, health_data, actual_permissions)
                
                # 记录 RefreshToken 以便后续设备冲突/黑名单审计
                logger.info(f"🔧 准备创建UserToken，用户: {user.id}, refresh_token长度: {len(refresh_token_str)}")
                user_token = UserToken.objects.create(
                    user=user,
                    token=refresh_token_str,
                    token_type='refresh',
                    expires_at=timezone.now() + self.get_refresh_token_lifetime(),
                    is_active=True,
                    device_info=device_info,
                    last_used_at=timezone.now()
                )
                
                # 创建登录记录
                LoginRecord.objects.create(
                    user=user,
                    ip_address=ip_address,
                    device_info=device_info
                )
                
                # 记录登录日志
                logger.info(f"User logged in successfully: user_id={user.id}, email={email}, IP={ip_address}, device_id={device_id[:8] + '...' if device_id else 'None'}")
                logger.info(f"🔧 登录流程即将返回响应，token_prefix: {access_token_str[:8]}… (hash={hash(access_token_str)})")
                
                return Response({
                    'code': 200,
                    'message': 'Login successful',
                    'data': {
                        'access_token': access_token_str,
                        'refresh_token': refresh_token_str,
                        'expires_in': int(self.get_access_token_lifetime().total_seconds()),
                        'user': {
                            'id': user.id,
                            'user_id': user.user_id,
                            'email': user.email,
                            'username': user.username,
                            'is_agent': user.is_agent,
                            'agent_id': user.agent_id,
                            'is_active_member': user.is_active_member
                        }
                    }
                }, status=status.HTTP_200_OK)
                
        except Exception as e:
            return self.handle_exception(e)

@extend_schema(
    tags=["App - Authentication"],
    summary="App 用户注册",
    description="使用邮箱、密码和验证码进行注册。",
    request=inline_serializer(
        name='AppRegisterRequest',
        fields={
            'email': serializers.EmailField(),
            'password': serializers.CharField(),
            'verification_code': serializers.CharField(),
            'referral_code': serializers.CharField(required=False, allow_blank=True),
        }
    ),
    responses={
        201: inline_serializer(
            name='AppRegisterSuccessResponse',
            fields={
                'code': serializers.IntegerField(default=201),
                'message': serializers.CharField(),
                'data': serializers.DictField()
            }
        ),
        400: GenericErrorResponseSerializer,
        500: GenericErrorResponseSerializer,
    }
)
class AppRegisterView(BaseAppTokenView):
    """App注册视图"""
    permission_classes = [AllowAny]
    
    @transaction.atomic
    def post(self, request):
        """用户注册"""
        # --- 新增：检查注册是否开放 ---
        if not SystemConfig.get_registration_status():
            logger.warning("Registration attempt failed: Registration is currently closed.")
            return Response({
                'code': 403,
                'message': 'Registration is currently closed.',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)
        # --- 结束检查 ---
        try:
            # 参数校验 - 添加 username
            email = request.data.get('email', '').strip().lower()
            password = request.data.get('password', '')
            username = request.data.get('username') # 获取 username
            verification_code = request.data.get('verification_code', '')
            referral_code_input = request.data.get('referral_code', '').strip().upper()
            
            # 更新必填项检查
            if not email or not password or not verification_code or not username:
                return Response({
                    'code': 400,
                    'message': 'Please provide complete registration information (Email, Username, Password, Verification Code)',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取IP地址
            ip_address = get_client_ip(request)
            
            # 验证密码强度 (基础长度检查)
            if len(password) < 8:
                return Response({
                    'code': 400,
                    'message': 'Password must be at least 8 characters long',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
                
            # --- 新增：调用 Django 密码验证器 ---
            try:
                validate_password(password, user=None) # 传入 None 因为用户还未创建
            except ValidationError as errors:
                # 将验证错误列表格式化为字符串
                error_list = list(errors)
                logger.warning(f"Registration failed: Weak password provided by {email} - {error_list}")
                return Response({
                    'code': 400,
                    'message': "Password validation failed: " + " ".join(error_list),
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            # --- 结束密码验证 ---

            # --- Email 标准化处理 ---
            normalized_email = email
            if '@' in normalized_email:
                local_part, domain_part = normalized_email.split('@', 1)
                # 移除 local_part 中的点 (主要针对 Gmail)
                local_part_normalized = local_part.replace('.', '')
                # TODO: 考虑是否移除 +tag (Gmail)
                # local_part_normalized = local_part_normalized.split('+', 1)[0]
                normalized_email = f"{local_part_normalized}@{domain_part}"
            else:
                # 如果邮箱格式不合法 (虽然前面校验过，以防万一)
                return Response({
                    'code': 400,
                    'message': 'Invalid email format provided for normalization.',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            # --- 结束 Email 标准化 ---
            
            # 验证验证码
            cache_key_verify = f"verify_code_{email}_register"
            cached_code_data = cache.get(cache_key_verify)
            if not cached_code_data or cached_code_data.get('code') != verification_code or not cached_code_data.get('verified'):
                logger.warning(f"Registration failed: Invalid verification code - Email: {email}, IP: {ip_address}")
                return Response({
                    'code': 400,
                    'message': 'Invalid or expired verification code',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 检查标准化后的邮箱是否已注册
            if User.objects.filter(email=normalized_email).exists(): # 使用 normalized_email 查询
                logger.warning(f"Registration failed: Normalized email already registered - Original: {email}, Normalized: {normalized_email}, IP: {ip_address}")
                return Response({
                    'code': 400,
                    'message': 'Email is already registered',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 检查用户名是否已注册
            if User.objects.filter(username=username).exists():
                logger.warning(f"Registration failed: Username already registered - {username}, IP: {ip_address}")
                return Response({
                    'code': 400,
                    'message': 'Username is already registered',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
                
            # 处理推荐码
            referrer = None
            if referral_code_input:
                try:
                    # 确保推荐码是以 'R' 开头
                    if not referral_code_input.startswith('R'):
                        logger.warning(f"注册时提供的推荐码格式无效: {referral_code_input} for email {email}")
                        # 可以选择忽略无效推荐码或返回错误，这里选择忽略
                    else:
                        referrer = User.objects.get(referral_code=referral_code_input, is_active=True)
                        if referrer.email == normalized_email: # 防止自己推荐自己
                            logger.warning(f"用户 {normalized_email} 尝试使用自己的推荐码 {referral_code_input} 进行注册。")
                            referrer = None # 无效化推荐人
                except User.DoesNotExist:
                    logger.warning(f"注册时提供的推荐码无效或推荐人不存在: {referral_code_input} for email {normalized_email}")
                    # 可以选择返回错误提示，或静默处理（不关联推荐人）
                    # return Response({'code': 400, 'message': _('Invalid referral code.'), 'data': None}, status=status.HTTP_400_BAD_REQUEST)
                except Exception as ref_exc:
                    logger.error(f"处理推荐码 {referral_code_input} 时发生未知错误: {str(ref_exc)}")
                    referrer = None

            # 创建新用户
            with transaction.atomic():
                # 创建用户 - 使用标准化后的 email
                user = User.objects.create_user(
                    email=normalized_email, # 使用标准化后的 email
                    username=username,
                    password=password
                )
                
                # 关联推荐人 - 新增
                if referrer:
                    user.referrer = referrer
                    user.save(update_fields=['referrer']) # 保存推荐人关系
                    
                    # 增加推荐人的推荐计数
                    referrer.referral_count += 1
                    referrer.save(update_fields=['referral_count'])
                    
                    # 检查推荐人是否需要升级为代理
                    referrer.check_and_upgrade_to_agent()
                
                # 生成访问令牌和刷新令牌
                # 使用RefreshToken统一管理 - 避免重复创建
                refresh_token_obj = RefreshToken.for_user(user)
                refresh_token_str = str(refresh_token_obj)
                access_token_str = str(refresh_token_obj.access_token)
                
                # 获取设备信息
                device_info = get_device_info(request)
                
                # 创建token记录
                user_token = UserToken.objects.create(
                    user=user,
                    token=refresh_token_str,
                    token_type='refresh',
                    expires_at=timezone.now() + self.get_refresh_token_lifetime(),
                    is_active=True,
                    device_info=device_info,
                    last_used_at=timezone.now()
                )
                
                # 清除验证码缓存
                cache.delete(cache_key_verify)

                # --- 新增：更新最后登录时间 ---
                user.last_login = timezone.now()
                user.save(update_fields=['last_login'])
                logger.info(f"Updated last_login for newly registered user: {user.email}")
                # --- 结束新增 ---
                
                # 记录注册日志
                logger.info(f"User registered successfully: user_id={user.id}, email={normalized_email}, username={username}, IP={ip_address}")
                
                # 为新用户分配初始任务
                try:
                    assignment_service = TaskAssignmentService(user=user)
                    daily_tasks_assigned = assignment_service.assign_daily_tasks()
                    additional_tasks_assigned = assignment_service.assign_additional_tasks()
                    
                    logger.info(f"为新用户 {user.email} 分配了 {len(daily_tasks_assigned)} 个每日任务和 {len(additional_tasks_assigned)} 个附加任务。")
                    
                except Exception as task_assign_exc:
                    # 任务分配失败不应阻塞注册流程，但需要记录严重错误
                    logger.critical(f"为新用户 {user.email} 分配初始任务时发生严重错误: {str(task_assign_exc)}", exc_info=True)

                # 注册成功后发送欢迎消息/邮件
                try:
                    MessageService.send_system_message(
                        recipient=user,
                        title_key="welcome_new_user_title", # 假设在EmailTemplate中有此key
                        content_key="welcome_new_user_content_app", # 假设在EmailTemplate中有此key
                        target_url="/profile/messages" 
                    )
                    # 异步发送欢迎邮件
                    # send_welcome_email_task.delay(user.id) 
                except Exception as e_msg:
                    logger.error(f"发送欢迎消息给用户 {user.email} 失败: {str(e_msg)}")
                
                return Response({
                    'code': 201,
                    'message': 'Registration successful',
                    'data': {
                        'access_token': access_token_str,
                        'refresh_token': refresh_token_str,
                        'expires_in': int(self.get_access_token_lifetime().total_seconds()),
                        'user': {
                            'id': user.id,
                            'user_id': user.user_id,
                            'email': user.email,
                            'username': user.username
                        }
                    }
                }, status=status.HTTP_201_CREATED) # 返回 201 Created 更合适
        except Exception as e:
            return self.handle_exception(e)

@extend_schema(
    tags=["App - Authentication"],
    summary="App 发送/验证 验证码",
    description="用于发送注册或重置密码的邮箱验证码，或验证用户输入的验证码。",
    request=inline_serializer(
        name='AppVerifyCodeRequest',
        fields={
            'email': serializers.EmailField(),
            'code_type': serializers.ChoiceField(choices=['register', 'reset_password']),
            'action': serializers.ChoiceField(choices=['send', 'verify']),
            'verification_code': serializers.CharField(required=False),
        }
    ),
    responses={
        200: inline_serializer(
            name='AppVerifyCodeSuccessResponse',
            fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': serializers.DictField(allow_null=True)
            }
        ),
        400: GenericErrorResponseSerializer,
        429: GenericErrorResponseSerializer,
        500: GenericErrorResponseSerializer,
    }
)
class AppVerifyCodeView(BaseAppTokenView):
    """App发送/验证验证码视图"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        """发送验证码"""
        try:
            # 参数校验
            email = request.data.get('email')
            
            # 修正：同时支持 'type' 和 'code_type' 参数，确保前端兼容性
            code_type = request.data.get('code_type')
            if code_type is None:  # 如果 code_type 不存在，尝试读取 type
                code_type = request.data.get('type', 'register')  # 默认为注册验证码
            
            # 参数统一化日志
            logger.debug(f"Verification code request: email={email}, code_type={code_type}, data={request.data}")
            
            # --- 新增：检查注册是否开放 (仅当类型为 register 时) ---
            if code_type == 'register' and not SystemConfig.get_registration_status():
                 logger.warning(f"Verification code request denied for {email}: Registration is currently closed.")
                 return Response({
                     'code': 403,
                     'message': 'Registration is currently closed.',
                     'data': None
                 }, status=status.HTTP_403_FORBIDDEN)
            # --- 结束检查 ---
            
            if not email:
                return Response({
                    'code': 400,
                    'message': 'Email address is required',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # --- 新增：注册前检查邮箱是否存在 ---
            if code_type == 'register':
                if User.objects.filter(email__iexact=email).exists(): # 使用 __iexact 忽略大小写
                    logger.warning(f"Verification code request denied: Email {email} already registered.")
                    return Response({
                        'code': 400,
                        'message': 'This email address is already registered.',
                        'data': None
                    }, status=status.HTTP_400_BAD_REQUEST)
            # --- 结束：邮箱检查 ---
            
            # 验证邮箱格式
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                return Response({
                    'code': 400,
                    'message': 'Invalid email format',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取IP地址
            ip_address = get_client_ip(request)
            
            # 校验验证码类型
            valid_types = ['register', 'reset_password', 'change_email']
            if code_type not in valid_types:
                return Response({
                    'code': 400,
                    'message': 'Invalid verification code type',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # --- 新增：重置密码前检查用户是否存在且激活 ---
            if code_type == 'reset_password':
                try:
                    user = User.objects.get(email__iexact=email)
                    if not user.is_active:
                        logger.warning(f"Password reset code request denied: User {email} is inactive.")
                        return Response({
                            'code': 400, # 或 403 Forbidden
                            'message': _('User account is inactive.'), # 使用翻译
                            'data': None
                        }, status=status.HTTP_400_BAD_REQUEST)
                except User.DoesNotExist:
                    logger.warning(f"Password reset code request denied: User {email} does not exist.")
                    # 注意：为了安全，通常不应明确提示用户是否存在，返回统一提示
                    return Response({
                        'code': 400,
                        'message': _('Check your email address.'), # 简化错误提示
                        'data': None
                    }, status=status.HTTP_400_BAD_REQUEST)
            # --- 结束：重置密码检查 ---
            
            # 检查频率限制 - IP级别
            ip_cache_key = f"verify_code_ip:{ip_address}"
            ip_count = cache.get(ip_cache_key, 0)
            
            if ip_count >= 10:  # 同一IP每小时最多10次
                logger.warning(f"Rate limit exceeded for IP {ip_address} - too many verification code requests")
                return Response({
                    'code': 429,
                    'message': 'Too many requests. Please try again later',
                    'data': None
                }, status=status.HTTP_429_TOO_MANY_REQUESTS)
            
            # 检查频率限制 - 邮箱级别
            email_cache_key = f"verify_code_email:{email}"
            email_count = cache.get(email_cache_key, 0)
            
            if email_count >= 5:  # 同一邮箱每小时最多5次
                logger.warning(f"Rate limit exceeded for email {email} - too many verification code requests")
                return Response({
                    'code': 429,
                    'message': 'Too many requests. Please try again later',
                    'data': None
                }, status=status.HTTP_429_TOO_MANY_REQUESTS)
            
            # 更新频率限制计数
            cache.set(ip_cache_key, ip_count + 1, 3600)  # 1小时过期
            cache.set(email_cache_key, email_count + 1, 3600)  # 1小时过期
            
            # 生成验证码
            import random
            code = str(random.randint(100000, 999999))
            
            # 存储验证码
            verification_cache_key = f'email_verification_code:{email}:{code_type}'
            cache_data = {
                'code': code,
                'created_at': time.time(),
                'attempts': 0
            }
            cache.set(verification_cache_key, cache_data, 300)  # 5分钟有效期
            
            # --- 添加：调用邮件发送服务 --- 
            try:
                # 注意：需要确保 code_type 的值与 MessageService 中定义的 trigger 对应
                # 例如，前端传来的 'register' 可能需要映射到 'user_register'
                trigger_map = {'register': 'user_register', 'reset_password': 'forgot_password'} # TODO: 确认映射关系
                email_trigger = trigger_map.get(code_type, code_type) # 获取对应的邮件模板 trigger
                
                # 调用 MessageService 发送邮件，并传入 code
                # MessageService.send_verification_code 现在内部生成 code，需要修改或使用其他方法
                # 改为调用 send_message，并传入 code
                context = {'verification_code': code} 
                # 尝试获取用户信息以丰富上下文
                try:
                    user = User.objects.get(email=email)
                    context['username'] = user.username
                except User.DoesNotExist:
                    context['username'] = email.split('@')[0] # 默认使用邮箱前缀
                
                # 查找用户对象（如果不存在也没关系，send_message 可能需要用户对象）
                user_obj = User.objects.filter(email=email).first()
                
                # 调用通用的发送服务，只发送邮件
                # 注意：send_message 需要 User 对象，如果用户不存在，需要特殊处理或修改服务
                if user_obj:
                    MessageService.send_message(
                        user=user_obj, 
                        trigger=email_trigger, 
                        context=context, 
                        send_email=True # 明确发送邮件
                    )
                    logger.info(f"Verification email initiated via MessageService for {email}, trigger: {email_trigger}")
                else:
                    # --- 修正：禁止给不存在的用户发送重置密码邮件 ---
                    if code_type == 'reset_password':
                        # 这里不应该到达，因为上面已经做了检查，加一道保险
                        logger.warning(f"Attempt to send reset password email to non-existent user: {email}")
                        return Response({
                            'code': 400,
                            'message': _('Unable to send verification code. Please check the email address.'),
                            'data': None
                        }, status=status.HTTP_400_BAD_REQUEST)
                    
                    # --- 修改：只为新用户注册发送邮件 ---
                    logger.info(f"Attempting to send verification email directly to new user: {email}") # 添加日志
                    system_config = SystemConfig.objects.first()
                    if not system_config:
                        logger.error(f"Cannot send verification email: SystemConfig not found.")
                        #可以选择抛出异常或静默失败
                        raise Exception("System email configuration not found.") 

                    email_config = system_config.get_email_config()
                    if not email_config:
                        logger.error(f"Cannot send verification email: Failed to load email config from SystemConfig.")
                        raise Exception("Failed to load system email configuration.")
                        
                    # 获取发件人地址 (可能包含名称)
                    from_email_config = email_config.get('DEFAULT_FROM_EMAIL')
                    if not from_email_config:
                         logger.error(f"Cannot send verification email: DEFAULT_FROM_EMAIL not configured.")
                         raise Exception("Default from email address is not configured.")

                    logger.debug(f"Looking for email template with trigger: {email_trigger}") # 添加日志
                    email_template = EmailTemplate.objects.filter(trigger=email_trigger, is_active=True).first()
                    
                    if email_template:
                        logger.info(f"Found active email template ID: {email_template.id} for trigger {email_trigger}") # 添加日志
                        try:
                            logger.debug(f"Rendering email template ID: {email_template.id} with context: {context}") # 添加日志
                            subject_template = Template(email_template.subject)
                            content_template = Template(email_template.content)
                            html_content = content_template.render(Context(context))
                            plain_content = strip_tags(html_content)
                            subject = subject_template.render(Context(context))
                            logger.info(f"Successfully rendered email template for {email}") # 添加日志
                        except Exception as render_error:
                            logger.error(f"Failed rendering email template ID {email_template.id} for {email}: {render_error}", exc_info=True)
                            raise # 重新抛出渲染错误，让外层捕获

                        try:
                            logger.info(f"Attempting to create mail connection using DB config for {email}") # 添加日志
                            # 创建使用数据库配置的连接
                            connection = mail.get_connection(
                                backend='django.core.mail.backends.smtp.EmailBackend',
                                host=email_config.get('EMAIL_HOST'),
                                port=email_config.get('EMAIL_PORT'),
                                username=email_config.get('EMAIL_HOST_USER'),
                                password=email_config.get('EMAIL_HOST_PASSWORD'),
                                use_tls=email_config.get('EMAIL_USE_TLS', False),
                                use_ssl=email_config.get('EMAIL_USE_SSL', False),
                                fail_silently=False,
                            )
                            
                            # 使用该连接发送邮件
                            # --- 再次修正 EmailMessage 调用方式 ---
                            msg = EmailMultiAlternatives(
                                subject=subject,
                                body=plain_content, # 纯文本内容
                                from_email=from_email_config, # 使用配置的From头
                                to=[email],
                                connection=connection
                            )
                            msg.attach_alternative(html_content, "text/html") # 附加 HTML 内容
                            sent_count = msg.send(fail_silently=False)
                            # --- 结束修正 ---

                            if sent_count == 1:
                                logger.info(f"Verification email sent successfully via EmailMessage using DB config for {email}")
                            else:
                                logger.warning(f"EmailMessage.send returned {sent_count} for {email}, expected 1.")
                                
                        except Exception as direct_mail_error:
                            logger.error(f"Failed sending email directly using DB config for {email}: {direct_mail_error}", exc_info=True)
                            # 此处可以选择是否继续向上抛出异常

                    else:
                         logger.warning(f"Active email template not found for trigger {email_trigger}, cannot send email directly.") # 修改日志
                    # --- 结束修改 ---

            except Exception as mail_error:
                # 记录邮件发送错误，但可能仍然返回成功给前端（避免用户困惑）
                logger.error(f"Failed to send verification email to {email}: {mail_error}", exc_info=True)
            # --- 结束：调用邮件发送服务 ---
            
            return Response({
                'code': 200,
                'message': 'Verification code has been sent',
                'data': {
                    'email': email,
                    'expires_in': 300  # 5分钟有效期
                }
            })
        except Exception as e:
            return self.handle_exception(e)

@extend_schema(
    tags=["App - Authentication"],
    summary="App 重置密码",
    description="使用邮箱、新密码和已验证的验证码（通过AppVerifyCodeView获取并验证）来重置密码。",
    request=inline_serializer(
        name='AppResetPasswordRequest',
        fields={
            'email': serializers.EmailField(),
            'new_password': serializers.CharField(),
            'verification_code': serializers.CharField(),
        }
    ),
    responses={
        200: inline_serializer(
            name='AppResetPasswordSuccessResponse',
            fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': serializers.DictField(allow_null=True)
            }
        ),
        400: GenericErrorResponseSerializer,
        500: GenericErrorResponseSerializer,
    }
)
class AppResetPasswordView(BaseAppTokenView):
    """App重置密码视图"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        """重置密码"""
        try:
            email = request.data.get('email')
            verification_code = request.data.get('verification_code')
            new_password = request.data.get('new_password')
            
            if not email or not verification_code or not new_password:
                return Response({
                    'code': 400,
                    'message': 'Please provide complete information',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证密码强度
            if len(new_password) < 8:
                return Response({
                    'code': 400,
                    'message': 'Password must be at least 8 characters long',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取IP地址用于安全日志
            ip_address = get_client_ip(request)
            
            # 验证验证码
            cache_key = f'email_verification_code:{email}:reset_password'
            stored_data = cache.get(cache_key)
            
            if not stored_data or stored_data.get('code') != verification_code:
                # 记录失败尝试
                logger.warning(
                    f"Password reset failed: Invalid verification code - "
                    f"Email: {email}, IP: {ip_address}"
                )
                return Response({
                    'code': 400,
                    'message': 'Invalid or expired verification code',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 清除验证码缓存
            cache.delete(cache_key)
            
            # 查找用户
            try:
                user = User.objects.get(email=email, is_active=True)
            except User.DoesNotExist:
                return Response({
                    'code': 404,
                    'message': 'User not found',
                    'data': None
                }, status=status.HTTP_404_NOT_FOUND)
            
            # 更新密码
            with transaction.atomic():
                user.set_password(new_password)
                user.save(update_fields=['password'])
                
                # 将该用户的所有Token失效
                tokens = UserToken.objects.filter(user=user, is_active=True)
                blacklist_manager = self.get_blacklist_manager()
                
                for token in tokens:
                    blacklist_manager.add_to_blacklist(
                        token.token, 
                        'Password reset', 
                        user.id
                    )
                    token.is_active = False
                    token.save(update_fields=['is_active'])
                
                # 记录密码重置
                logger.info(
                    f"App password reset successful: user_id={user.id}, email={user.email}, "
                    f"IP={ip_address}"
                )
                
                return Response({
                    'code': 200,
                    'message': 'Password has been reset successfully',
                    'data': None
                })
        except Exception as e:
            return self.handle_exception(e)

@extend_schema(
    tags=["App - Authentication"],
    summary="App 刷新 Access Token",
    description="使用有效的 Refresh Token 获取新的 Access Token 和 Refresh Token（如果开启了轮换）。支持设备验证和冲突检测。",
    request=inline_serializer(
        name='AppTokenRefreshRequest',
        fields={
            'refresh_token': serializers.CharField(),
            'device_id': serializers.CharField(required=False, help_text="设备唯一标识，用于设备验证"),
        }
    ),
    responses={
        200: inline_serializer(
            name='AppTokenRefreshSuccessResponse',
            fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': inline_serializer(
                    name='AppTokenRefreshSuccessData',
                    fields={
                        'access_token': serializers.CharField(),
                        'refresh_token': serializers.CharField(),
                        'expires_in': serializers.IntegerField(),
                    }
                )
            }
        ),
        400: GenericErrorResponseSerializer,
        401: GenericErrorResponseSerializer,
        409: inline_serializer(
            name='AppTokenRefreshDeviceConflictResponse',
            fields={
                'code': serializers.IntegerField(default=409),
                'message': serializers.CharField(default='Device conflict detected'),
                'data': inline_serializer(
                    name='DeviceConflictData',
                    fields={
                        'conflict_type': serializers.CharField(),
                        'current_device': serializers.DictField(),
                        'new_device': serializers.DictField(),
                    }
                )
            }
        ),
        500: GenericErrorResponseSerializer,
    }
)
class AppTokenRefreshView(BaseAppTokenView):
    """App Refresh Token 视图 - 支持设备验证和冲突检测"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        """刷新token"""
        try:
            # 参数校验
            refresh_token = request.data.get('refresh_token')
            device_id = request.data.get('device_id')  # 🆕 设备ID
            
            if not refresh_token:
                return Response({
                    'code': 400,
                    'message': 'Refresh token is required',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取IP地址和设备信息
            ip_address = get_client_ip(request)
            device_info = get_device_info(request)
            
            try:
                # 使用JWT的刷新令牌功能
                from rest_framework_simplejwt.tokens import RefreshToken
                # 验证sliding token
                refresh_token_obj = RefreshToken(refresh_token)
                
                # 🔥 关键修复：获取用户信息，使用user_id字段而不是id字段
                user_id = refresh_token_obj.get('user_id')
                if not user_id:
                    logger.warning(f"Token refresh failed: No user_id in token - IP: {ip_address}")
                    raise Exception("Invalid token payload: missing user_id")

                # ✅ 正确：使用user_id字段查询用户（字符串ID如'S6763903'）
                user = User.objects.get(user_id=user_id, is_active=True)
                
                # 检查用户状态
                if not user.is_active:
                    logger.warning(f"Token refresh failed: User is inactive - User ID: {user_id}, IP: {ip_address}")
                    return Response({
                        'code': 401,
                        'message': 'User account is inactive',
                        'data': None
                    }, status=status.HTTP_401_UNAUTHORIZED)
                
                # 🆕 设备验证和冲突检测
                if device_id:
                    conflict_response = self._check_device_conflict(user, device_id, device_info, ip_address)
                    if conflict_response:
                        return conflict_response
                
                # 🔥 生成新的双 Token
                new_refresh_token_obj = RefreshToken.for_user(user)

                # 🔥 关键修复：添加自定义payload，确保Token包含正确的用户信息
                new_refresh_token_obj['user_id'] = user.user_id  # 使用字符串user_id而不是数字id
                new_refresh_token_obj['email'] = user.email      # 添加email字段
                new_refresh_token_obj['username'] = user.username # 添加username字段

                new_refresh_token_str = str(new_refresh_token_obj)
                new_access_token_str = str(new_refresh_token_obj.access_token)
                
                # 🔥 关键修复：移除数据库操作，Refresh/Access Token 均为自包含JWT，验证仅依赖签名
                # 不需要在数据库中存储或更新token记录
                # Refresh/Access Token的验证完全基于JWT签名，不依赖数据库
                
                # 记录token刷新日志（仅用于审计）
                logger.info(f"Token refreshed: user_id={user.id}, IP={ip_address}, device_id={device_id}")
                
                return Response({
                    'code': 200,
                    'message': 'Token refreshed successfully',
                    'data': {
                        'access_token': new_access_token_str,
                        'refresh_token': new_refresh_token_str,  # Refresh/Access Token两者相同
                        'expires_in': int(self.get_access_token_lifetime().total_seconds())
                    }
                })
            
            except Exception as e:
                logger.warning(f"Token refresh failed: {str(e)} - IP: {ip_address}")
                return Response({
                    'code': 401,
                    'message': 'Invalid or expired refresh token',
                    'data': None
                }, status=status.HTTP_401_UNAUTHORIZED)
                
        except Exception as e:
            return self.handle_exception(e)
    
    def _check_device_conflict(self, user, device_id, device_info, ip_address):
        """🔧 检查设备冲突 - token刷新时检测是否被其他设备挤掉
        
        🔥 BOSS关键修复：设备会话不存在时重新创建，而不是返回409冲突
        这解决了token有效但设备会话过期导致的强制登出问题
        """
        try:
            from users.models import UnifiedUserSession
            
            # 查找当前设备的会话状态
            current_session = UnifiedUserSession.objects.filter(
                user=user,
                device_id=device_id,
                is_active=True
            ).first()
            
            # 🔥 关键修复：设备会话不存在时的处理逻辑
            if not current_session:
                logger.info(f"设备会话不存在，开始检查是否需要重新创建: user_id={user.id}, device_id={device_id[:8]}...")
                
                # 检查是否有其他活跃设备（真正的设备冲突）
                active_sessions = UnifiedUserSession.objects.filter(
                    user=user,
                    is_active=True
                ).exclude(device_id=device_id)
                
                if active_sessions.exists():
                    # 存在其他活跃设备，这是真正的设备冲突
                    other_session = active_sessions.first()
                    logger.warning(f"检测到真正的设备冲突: 当前设备 {device_id[:8]}... 被设备 {other_session.device_id[:8]}... 挤掉")
                    
                    conflict_data = {
                        'conflict_type': 'device_kicked_out',
                        'message': '您的账号已在其他设备上登录，当前设备已被登出。请重新登录。',
                        'action_required': True,
                        'suggested_action': 'relogin',
                        'current_device': {
                            'device_id': device_id[:8] + '...',
                            'status': 'logged_out'
                        },
                        'active_device': {
                            'device_id': other_session.device_id[:8] + '...',
                            'platform': other_session.device_fingerprint.get('platform', 'unknown'),
                            'last_active': other_session.last_activity.isoformat() if other_session.last_activity else None,
                        }
                    }
                    
                    return Response({
                        'code': 409,
                        'message': 'Device session conflict - please login again',
                        'data': conflict_data
                    }, status=status.HTTP_409_CONFLICT)
                else:
                    # 🔥 关键修复：没有其他活跃设备，说明是会话过期而不是设备冲突
                    # 重新创建设备会话，允许token刷新继续
                    logger.info(f"没有设备冲突，重新创建设备会话: user_id={user.id}, device_id={device_id[:8]}...")
                    
                    try:
                        # 重新创建设备会话（使用默认基线值）
                        new_session = UnifiedUserSession.objects.create(
                            user=user,
                            device_id=device_id,
                            device_fingerprint=device_info,
                            ip_address=ip_address,
                            # 设置默认基线值，避免必填字段错误
                            session_baseline_steps=0,
                            session_baseline_distance=0.0,
                            session_baseline_calories=0,
                            is_active=True,
                            last_activity=timezone.now(),
                        )
                        
                        logger.info(f"设备会话重新创建成功: session_id={new_session.id}, user_id={user.id}, device_id={device_id[:8]}...")
                        return None  # 允许token刷新继续
                        
                    except Exception as create_error:
                        logger.error(f"重新创建设备会话失败: user_id={user.id}, device_id={device_id[:8]}..., error={str(create_error)}")
                        # 创建失败也不阻止token刷新，只记录错误
                        return None
            else:
                # 设备会话存在且活跃，更新最后活跃时间
                current_session.last_activity = timezone.now()
                current_session.save(update_fields=['last_activity'])
                logger.debug(f"设备会话正常，更新最后活跃时间: session_id={current_session.id}")
                return None  # 允许token刷新继续
            
        except Exception as e:
            logger.error(f"设备冲突检查过程中出错，允许token刷新继续: user_id={user.id}, device_id={device_id[:8]}..., error={str(e)}", exc_info=True)
            return None  # 出错时不阻止刷新，记录日志即可
    
    # _update_device_session 方法已被移除，设备会话管理现在由 unified_secure_login_and_create_session 统一处理

@extend_schema(
    tags=["App - Authentication"],
    summary="App 用户登出",
    description="将当前用户使用的 Refresh Token 加入黑名单，使其失效。需要提供 Refresh Token。",
    request=inline_serializer(
        name='AppLogoutRequest',
        fields={
            'refresh_token': serializers.CharField(),
            'health_data': serializers.JSONField(required=False, help_text="登出时的设备健康数据, e.g. {'steps': 1, 'distance': 1.0, 'calories': 1}")
        }
    ),
    responses={
        200: inline_serializer(
            name='AppLogoutSuccessResponse',
            fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': serializers.DictField(allow_null=True)
            }
        ),
        400: GenericErrorResponseSerializer,
        401: GenericErrorResponseSerializer,
        500: GenericErrorResponseSerializer,
    }
)
class AppLogoutView(BaseAppTokenView):
    """App登出视图"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """用户登出"""
        try:
            refresh_token = request.data.get('refresh_token')
            health_data = request.data.get('health_data') # 接收健康数据
            
            if not refresh_token:
                return Response({'code': 400, 'message': 'Refresh token is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证健康数据
            if not health_data:
                health_data = {'steps': 0, 'distance': 0.0, 'calories': 0}
                logger.warning(f"用户 {request.user.email} 登出时未提供 health_data，将使用零值创建快照。")

            try:
                # 使用事务确保原子性
                with transaction.atomic():
                    # 1. 将JWT Token加入黑名单
                    # 注意：系统使用RefreshToken，需要按双Token黑名单处理
                    try:
                        from rest_framework_simplejwt.tokens import RefreshToken
                        from rest_framework_simplejwt.token_blacklist.models import BlacklistedToken, OutstandingToken
                        
                        # 验证token并获取jti
                        refresh_token_obj = RefreshToken(refresh_token)
                        jti = refresh_token_obj.get('jti')
                        
                        if jti:
                            # 查找或创建OutstandingToken记录
                            outstanding_token, created = OutstandingToken.objects.get_or_create(
                                jti=jti,
                                defaults={
                                    'token': refresh_token,
                                    'user': request.user,
                                    'created_at': timezone.now(),
                                    'expires_at': timezone.now() + refresh_token_obj.lifetime,
                                }
                            )
                            
                            # 将token加入黑名单
                            BlacklistedToken.objects.get_or_create(token=outstanding_token)
                            logger.info(f"RefreshToken {jti[:8]}... 已加入黑名单")
                        else:
                            logger.warning(f"无法从token中提取jti，跳过黑名单处理")
                            
                    except Exception as token_error:
                        logger.error(f"处理token黑名单时出错: {str(token_error)}")
                        # 即使token黑名单失败，也继续执行会话清理
                    
                    # 2. 结束用户的统一会话
                    active_session = request.user.sessions.filter(is_active=True).first()
                    if active_session:
                        unified_secure_logout(active_session, health_data, reason='user_logout')
                    else:
                        logger.warning(f"用户 {request.user.email} 登出时未找到活跃的会话。")

                    # 3. 更新用户最后活跃时间
                    user = request.user
                    user.last_active_time = timezone.now()
                    user.save(update_fields=['last_active_time'])
                    
                    logger.info(f"User {user.email} logged out successfully.")
                
                return Response({
                    'code': 200,
                    'message': 'Logout successful',
                    'data': None
                }, status=status.HTTP_200_OK)

            except Exception as e:
                logger.error(f"Logout failed for user {request.user.email}: {str(e)}", exc_info=True)
                return Response({'code': 400, 'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return self.handle_exception(e)

@extend_schema(
    tags=["App - Authentication"],
    summary="App 检查 Access Token 有效性",
    description="验证提供的 Access Token 是否仍然有效（未过期且未被列入可能的黑名单）。",
    request=inline_serializer(
        name='AppCheckTokenRequest',
        fields={
            'access_token': serializers.CharField(),
        }
    ),
    responses={
        200: inline_serializer(
            name='AppCheckTokenSuccessResponse',
            fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(default='Token is valid'),
                'data': inline_serializer(
                    name='AppCheckTokenSuccessData',
                    fields={
                        'is_valid': serializers.BooleanField(default=True)
                    }
                )
            }
        ),
        400: GenericErrorResponseSerializer,
        401: GenericErrorResponseSerializer,
        500: GenericErrorResponseSerializer,
    }
)
class AppCheckTokenView(BaseAppTokenView):
    """App检查Token有效性视图"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        """检查Token有效性"""
        try:
            # 🔧 修复：API文档定义的是access_token，但代码获取的是token
            token = request.data.get('token') or request.data.get('access_token')
            
            if not token:
                return Response({
                    'code': 400,
                    'message': 'Token is required',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取IP地址
            ip_address = get_client_ip(request)
            
            # 🔧 正确的JWT验证：解析token本身，不依赖数据库
            try:
                from rest_framework_simplejwt.tokens import RefreshToken
                from rest_framework_simplejwt.exceptions import TokenError
                from rest_framework_simplejwt.token_blacklist.models import BlacklistedToken, OutstandingToken
                
                # 验证RefreshToken
                logger.info(f"🔍 开始验证Token: {token[:50]}... IP: {ip_address}")
                refresh_token_obj = RefreshToken(token)
                logger.info(f"✅ Token解析成功, 用户ID: {refresh_token_obj.get('user_id')}, JTI: {refresh_token_obj.get('jti')}")
                
                # 检查是否在Django SimpleJWT黑名单中
                jti = refresh_token_obj.get('jti')
                is_blacklisted = False
                if jti:
                    try:
                        outstanding_token = OutstandingToken.objects.get(jti=jti)
                        is_blacklisted = BlacklistedToken.objects.filter(token=outstanding_token).exists()
                    except OutstandingToken.DoesNotExist:
                        is_blacklisted = False
                
                if is_blacklisted:
                    is_valid = False
                    logger.info(f"Token check failed: token is blacklisted - JTI: {jti}, IP: {ip_address}")
                else:
                    is_valid = True
                    user_id = refresh_token_obj.get('user_id')
                    logger.debug(f"Token check passed: valid Token - User ID: {user_id}, IP: {ip_address}")
                    
            except TokenError as e:
                is_valid = False
                logger.error(f"Token check failed: invalid or expired JWT - Error: {str(e)}, IP: {ip_address}")
            except Exception as e:
                is_valid = False
                logger.error(f"Token check failed: unexpected error - Error: {str(e)}, IP: {ip_address}", exc_info=True)
            
            return Response({
                'code': 200,
                'message': 'Token check completed',
                'data': {
                    'is_valid': is_valid
                }
            })
        except Exception as e:
            return self.handle_exception(e) 