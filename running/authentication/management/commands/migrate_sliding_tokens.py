from django.core.management.base import BaseCommand
from django.utils import timezone
from authentication.models import UserToken

class Command(BaseCommand):
    help = "将历史 token_type='sliding' 的 UserToken 标记为失效 (inactive=True)，避免遗留令牌误用。"

    def handle(self, *args, **options):
        now = timezone.now()
        sliding_qs = UserToken.objects.filter(token_type='sliding', is_active=True)
        total = sliding_qs.count()
        if total == 0:
            self.stdout.write(self.style.SUCCESS('无活跃 SlidingToken 记录，无需迁移。'))
            return

        updated = sliding_qs.update(is_active=False, updated_at=now)
        self.stdout.write(self.style.SUCCESS(f'成功将 {updated}/{total} 条 SlidingToken 记录标记为 inactive')) 