from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import UserToken, TokenBlacklist
from simple_history.admin import SimpleHistoryAdmin

@admin.register(UserToken)
class UserTokenAdmin(SimpleHistoryAdmin):
    list_display = ('user', 'token_preview', 'created_at', 'expires_at', 'is_active', 'last_used_at')
    list_filter = ('is_active', 'created_at', 'expires_at')
    search_fields = ('user__username', 'user__email', 'token')
    raw_id_fields = ('user',)
    readonly_fields = ('created_at', 'last_used_at')
    
    def token_preview(self, obj):
        return f"{obj.token[:32]}..."
    token_preview.short_description = _('令牌预览')

@admin.register(TokenBlacklist)
class TokenBlacklistAdmin(SimpleHistoryAdmin):
    list_display = ('token_preview', 'blacklisted_at', 'reason')
    list_filter = ('blacklisted_at',)
    search_fields = ('token', 'reason')
    readonly_fields = ('blacklisted_at',)
    
    def token_preview(self, obj):
        return f"{obj.token[:32]}..."
    token_preview.short_description = _('令牌预览')
