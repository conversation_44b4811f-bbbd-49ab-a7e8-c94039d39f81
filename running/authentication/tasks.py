from celery import shared_task
from django.utils import timezone
from .models import UserToken, TokenBlacklist
from django.core.cache import cache
from django.db import transaction
import logging
from datetime import timedelta
from celery.exceptions import MaxRetriesExceededError
from django.conf import settings

logger = logging.getLogger(__name__)

@shared_task(
    bind=True,
    max_retries=3,
    default_retry_delay=300,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_backoff_max=600,
    retry_jitter=True
)
def cleanup_expired_tokens(self, days=7, batch_size=1000):
    """清理过期的令牌"""
    try:
        logger.info("开始清理过期令牌...")
        expired_date = timezone.now() - timedelta(days=days)
        total_processed = 0
        total_blacklisted = 0
        
        while True:
            with transaction.atomic():
                # 获取一批过期令牌
                expired_tokens = UserToken.objects.filter(
                    expires_at__lt=expired_date,
                    is_active=True
                )[:batch_size]
                
                if not expired_tokens:
                    break
                
                # 批量处理
                token_ids = []
                blacklist_entries = []
                cache_keys = []
                
                for token in expired_tokens:
                    token_ids.append(token.id)
                    blacklist_entries.append(
                        TokenBlacklist(
                            token=token.token,
                            reason='令牌过期自动清理',
                            user_id=token.user_id
                        )
                    )
                    cache_keys.extend([
                        f'token_valid_{token.token}',
                        f'user_token_{token.user_id}',
                        f'token_blacklist_{token.token}',
                        f'auth_result_{token.token}'
                    ])
                
                # 批量更新数据库
                UserToken.objects.filter(id__in=token_ids).update(is_active=False)
                TokenBlacklist.objects.bulk_create(
                    blacklist_entries,
                    ignore_conflicts=True
                )
                
                # 批量清理缓存
                if cache_keys:
                    cache.delete_many(cache_keys)
                
                total_processed += len(token_ids)
                total_blacklisted += len(blacklist_entries)
                
        logger.info(
            f"清理过期令牌完成: 处理了{total_processed}个令牌, "
            f"加入黑名单{total_blacklisted}个"
        )
        return total_processed
        
    except Exception as e:
        logger.error(f"清理过期令牌失败: {str(e)}", exc_info=True)
        raise self.retry(exc=e)

@shared_task(
    bind=True,
    max_retries=3,
    default_retry_delay=300,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_backoff_max=600,
    retry_jitter=True
)
def cleanup_blacklist(self, days=30, batch_size=1000):
    """清理黑名单中的过期记录"""
    try:
        logger.info("开始清理黑名单...")
        expired_date = timezone.now() - timedelta(days=days)
        total_deleted = 0
        
        while True:
            with transaction.atomic():
                # 获取一批要删除的记录ID
                expired_records = TokenBlacklist.objects.filter(
                    blacklisted_at__lt=expired_date
                )[:batch_size]
                
                if not expired_records:
                    break
                
                record_ids = list(expired_records.values_list('id', flat=True))
                cache_keys = []
                
                for record in expired_records:
                    cache_keys.extend([
                        f'token_blacklist_{record.token}',
                        f'auth_result_{record.token}'
                    ])
                
                # 批量删除
                deleted_count = TokenBlacklist.objects.filter(
                    id__in=record_ids
                ).delete()[0]
                
                # 清理缓存
                if cache_keys:
                    cache.delete_many(cache_keys)
                
                total_deleted += deleted_count
                
        logger.info(f"清理黑名单完成: 删除了{total_deleted}条记录")
        return total_deleted
        
    except Exception as e:
        logger.error(f"清理黑名单失败: {str(e)}", exc_info=True)
        raise self.retry(exc=e)

@shared_task(
    bind=True,
    max_retries=3,
    default_retry_delay=300,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_backoff_max=600,
    retry_jitter=True
)
def invalidate_user_tokens(self, user_id, reason='用户注销'):
    """使指定用户的所有令牌失效"""
    try:
        logger.info(f"开始使用户{user_id}的令牌失效...")
        
        with transaction.atomic():
            # 获取用户的有效令牌
            active_tokens = UserToken.objects.filter(
                user_id=user_id,
                is_active=True
            )
            
            if not active_tokens:
                logger.info(f"用户{user_id}没有需要失效的令牌")
                return 0
            
            # 准备黑名单记录
            blacklist_entries = [
                TokenBlacklist(
                    token=token.token,
                    reason=reason,
                    user_id=user_id
                )
                for token in active_tokens
            ]
            
            # 批量更新和创建
            token_count = active_tokens.update(is_active=False)
            TokenBlacklist.objects.bulk_create(
                blacklist_entries,
                ignore_conflicts=True
            )
            
            # 清理缓存
            cache_keys = []
            for token in active_tokens:
                cache_keys.extend([
                    f'token_valid_{token.token}',
                    f'token_blacklist_{token.token}',
                    f'auth_result_{token.token}'
                ])
            cache_keys.append(f'user_token_{user_id}')
            
            if cache_keys:
                cache.delete_many(cache_keys)
            
            logger.info(
                f"用户{user_id}的令牌已失效: "
                f"处理了{token_count}个令牌"
            )
            return token_count
            
    except Exception as e:
        logger.error(
            f"使用户{user_id}的令牌失效失败: {str(e)}",
            exc_info=True
        )
        raise self.retry(exc=e) 