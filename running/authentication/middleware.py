from django.utils.deprecation import MiddlewareMixin
from django.utils import timezone
from rest_framework_simplejwt.tokens import AccessToken, RefreshToken
from django.conf import settings
from django.http import JsonResponse, HttpResponse
import jwt
from datetime import datetime, timedelta
from django.shortcuts import redirect
from django.contrib.auth import get_user_model
from .models import UserToken, TokenBlacklist
from django.core.cache import cache
import logging
import threading
import redis
from django.core.exceptions import PermissionDenied
import json
from django.db import transaction

logger = logging.getLogger(__name__)

class TokenBlacklistManager:
    """Token黑名单管理器"""
    def __init__(self, redis_client, cache_version=1):
        self.redis_client = redis_client
        self.cache_version = cache_version
        self.cache_timeout = 1800  # 30分钟

    def is_blacklisted(self, token):
        """检查token是否在黑名单中"""
        cache_key = f'token_blacklist_{token}'
        is_blacklisted = cache.get(cache_key, version=self.cache_version)
        
        if is_blacklisted is None:
            is_blacklisted = TokenBlacklist.objects.filter(token=token).exists()
            cache.set(cache_key, is_blacklisted, self.cache_timeout, version=self.cache_version)
        
        return is_blacklisted

    def add_to_blacklist(self, token, reason='', user_id=None):
        """将token加入黑名单"""
        try:
            with transaction.atomic():
                # 先检查是否已存在
                if not TokenBlacklist.objects.filter(token=token).exists():
                    TokenBlacklist.objects.create(token=token, reason=reason)
                    UserToken.objects.filter(token=token).update(is_active=False)
                
            cache_key = f'token_blacklist_{token}'
            cache.set(cache_key, True, self.cache_timeout, version=self.cache_version)
            
            if user_id:
                cache.delete(f'user_token_{user_id}', version=self.cache_version)
            
            return True
        except Exception as e:
            logger.error(f"将Token加入黑名单失败: {str(e)}", exc_info=True)
            return False

class JWTAuthenticationMiddleware(MiddlewareMixin):
    # 锁的超时时间（秒）
    LOCK_TIMEOUT = 5
    # Token验证缓存时间（秒）
    TOKEN_VALID_CACHE_TIMEOUT = 300  # 5分钟
    # Token数据缓存时间（秒）
    TOKEN_DATA_CACHE_TIMEOUT = 600   # 10分钟
    # Token缓存时间（秒）
    TOKEN_CACHE_TIMEOUT = 1800       # 30分钟
    # 重定向缓存时间（秒）
    REDIRECT_CACHE_TIMEOUT = 30
    # Token生成重试次数
    TOKEN_GEN_RETRY_COUNT = 3

    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.User = get_user_model()
        self._redis_client = redis.from_url(settings.CACHES['default']['LOCATION'])
        self.TOKEN_CACHE_VERSION = 1
        self.blacklist_manager = TokenBlacklistManager(self._redis_client, self.TOKEN_CACHE_VERSION)

    def _acquire_lock(self, lock_name, expire_time=None):
        """获取分布式锁，支持自定义超时时间"""
        expire_time = expire_time or self.LOCK_TIMEOUT
        lock_key = f'lock:{lock_name}'
        lock_value = f"{threading.current_thread().ident}_{timezone.now().timestamp()}"
        success = self._redis_client.set(
            lock_key,
            lock_value,
            nx=True,
            ex=expire_time
        )
        return lock_value if success else None

    def _release_lock(self, lock_name, lock_value):
        """安全释放分布式锁"""
        lock_key = f'lock:{lock_name}'
        script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
        """
        return self._redis_client.eval(script, 1, lock_key, lock_value)

    def _clear_token_caches(self, token, user_id=None):
        """清理token相关的所有缓存"""
        keys_to_delete = [
            f'token_valid_{token}',
            f'token_blacklist_{token}'
        ]
        if user_id:
            keys_to_delete.append(f'user_token_{user_id}')
        
        # 批量删除缓存
        cache.delete_many(keys_to_delete, version=self.TOKEN_CACHE_VERSION)

    def validate_token(self, token, request=None):
        """验证token的有效性"""
        if not token:
            return False
            
        try:
            # 检查token是否在黑名单中
            if TokenBlacklist.objects.filter(token=token).exists():
                self._log_blacklisted_token(token, request)
                return False

            # 解码并验证token
            decoded_token = self._decode_token(token)
            if not decoded_token:
                return False

            # 检查token是否过期
            if self._is_token_expired(decoded_token):
                return False

            # 检查token是否存在且有效
            user_token = UserToken.objects.filter(
                token=token,
                is_active=True,
                expires_at__gt=timezone.now()
            ).first()

            return user_token is not None

        except Exception as e:
            self._log_validation_error(e, token, request)
            return False

    def _handle_expired_token(self, token, user=None):
        """处理过期的token"""
        self._clear_token_caches(token, user.id if user else None)
        self.blacklist_manager.add_to_blacklist(token, '令牌过期', user.id if user else None)

    def _decode_token(self, token):
        """解码并验证token"""
        try:
            return jwt.decode(
                token,
                settings.SIMPLE_JWT['SIGNING_KEY'],
                algorithms=[settings.SIMPLE_JWT['ALGORITHM']]
            )
        except (jwt.ExpiredSignatureError, jwt.InvalidTokenError):
            return None

    def _validate_user(self, decoded_token):
        """验证token中的用户信息"""
        user_id = decoded_token.get('user_id')
        if not user_id:
            return None
            
        try:
            user = self.User.objects.get(id=user_id, is_active=True)
            return {'token': decoded_token, 'user': user}
        except self.User.DoesNotExist:
            return None

    def generate_token(self, user):
        """生成新的JWT token"""
        try:
            for attempt in range(self.TOKEN_GEN_RETRY_COUNT):
                lock_name = f'token_gen_{user.id}'
                lock_value = self._acquire_lock(lock_name)
                
                if not lock_value:
                    logger.warning(
                        f"获取token生成锁失败 (尝试 {attempt + 1}/{self.TOKEN_GEN_RETRY_COUNT}): "
                        f"user_id={user.id}"
                    )
                    continue
                    
                try:
                    token = self._generate_token_with_lock(user)
                    if token:
                        return token, self.TOKEN_CACHE_TIMEOUT
                finally:
                    self._release_lock(lock_name, lock_value)
                    
            logger.error(f"Token生成重试次数超限: user_id={user.id}")
            return None, 0
            
        except Exception as e:
            logger.error(f"Token生成失败: {str(e)}, user_id={user.id}", exc_info=True)
            return None, 0

    def _generate_token_with_lock(self, user, request=None):
        """使用锁机制生成token"""
        try:
            # 检查缓存中是否有有效token
            cached_token = cache.get(
                f'user_token_{user.id}',
                version=self.TOKEN_CACHE_VERSION
            )
            
            if cached_token and self.validate_token(cached_token['token']):
                return cached_token['token']
            
            # 生成新token
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            
            # 创建token记录
            expires_at = timezone.now() + refresh.access_token.lifetime
            UserToken.objects.filter(user=user).update(is_active=False)
            UserToken.objects.create(
                user=user,
                token=access_token,
                expires_at=expires_at,
                is_active=True
            )
            
            # 更新缓存
            token_data = {
                'token': access_token,
                'expires_at': expires_at.timestamp()
            }
            cache.set(
                f'user_token_{user.id}',
                token_data,
                self.TOKEN_CACHE_TIMEOUT,
                version=self.TOKEN_CACHE_VERSION
            )
            
            return access_token
            
        except Exception as e:
            log_data = f"生成Token失败: {str(e)}, user_id={user.id}"
            if request:
                log_data += (
                    f", path={request.path}, "
                    f"IP: {request.META.get('REMOTE_ADDR', 'N/A')}"
                )
            logger.error(log_data, exc_info=True)
            return None

    def _create_error_response(self, status_code, message, data=None):
        """创建统一的错误响应"""
        response_data = {
            'code': status_code,
            'message': message,
            'data': data or {}
        }
        
        if status_code == 401:
            response_data['data'].update({
                'error_type': 'authentication_failed',
                'error_detail': message,
                'should_relogin': True
            })
            
        return JsonResponse(response_data, status=status_code)

    def _log_blacklisted_token(self, token, request=None):
        """记录黑名单token的日志"""
        log_data = f"Token在黑名单中: {token[:32]}..."
        if request:
            log_data += (
                f" Path: {request.path}, "
                f"User-Agent: {request.META.get('HTTP_USER_AGENT', 'N/A')}, "
                f"IP: {request.META.get('REMOTE_ADDR', 'N/A')}"
            )
        logger.warning(log_data)

    def _log_validation_error(self, error, token, request=None):
        """记录token验证错误的日志"""
        log_data = f"Token验证失败: {str(error)}, Token: {token[:32]}..."
        if request:
            log_data += (
                f" Path: {request.path}, "
                f"User-Agent: {request.META.get('HTTP_USER_AGENT', 'N/A')}, "
                f"IP: {request.META.get('REMOTE_ADDR', 'N/A')}"
            )
        logger.error(log_data, exc_info=True)

    def _is_token_expired(self, decoded_token):
        """检查token是否过期"""
        try:
            exp_timestamp = decoded_token.get('exp', 0)
            current_timestamp = timezone.now().timestamp()
            return exp_timestamp <= current_timestamp
        except Exception:
            return True

    def process_request(self, request):
        """处理请求，统一处理JWT认证"""
        # 如果是登录页面、静态文件或不需要认证的API，不处理认证
        if (request.path.startswith('/admin/login/') or 
            request.path.startswith('/static/') or 
            request.path.startswith('/api/v1/auth/token/')):
            return

        # 如果是已认证用户
        if request.user.is_authenticated:
            # 获取当前token
            current_token = request.COOKIES.get('jwt_access_token')
            
            if not current_token:
                try:
                    # 先将该用户所有token设为无效
                    UserToken.objects.filter(user=request.user).update(is_active=False)
                    
                    # 生成新token
                    access_token = AccessToken.for_user(request.user)
                    token_str = str(access_token)
                    
                    # 创建新的token记录
                    UserToken.objects.create(
                        user=request.user,
                        token=token_str,
                        expires_at=timezone.now() + settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'],
                        is_active=True
                    )
                    
                    # 设置token到请求头
                    request.META['HTTP_AUTHORIZATION'] = f'Bearer {token_str}'
                    
                    # 获取response对象并设置cookie
                    response = self.get_response(request)
                    response.set_cookie(
                        'jwt_access_token',
                        token_str,
                        max_age=settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'].total_seconds(),
                        httponly=True,
                        secure=not settings.DEBUG,
                        samesite='Lax',
                        path='/'
                    )
                    return response
                except Exception as e:
                    logger.error(f"Token创建失败: {str(e)}", exc_info=True)
                    return self.get_response(request)
            else:
                # 验证当前token
                try:
                    decoded_token = jwt.decode(
                        current_token,
                        settings.SIMPLE_JWT['SIGNING_KEY'],
                        algorithms=[settings.SIMPLE_JWT['ALGORITHM']]
                    )
                    request.META['HTTP_AUTHORIZATION'] = f'Bearer {current_token}'
                    return
                except:
                    # token无效，删除cookie
                    response = self.get_response(request)
                    response.delete_cookie('jwt_access_token')
                    return response

        # 处理API请求
        elif request.path.startswith('/api/'):
            current_token = request.COOKIES.get('jwt_access_token')
            if current_token:
                request.META['HTTP_AUTHORIZATION'] = f'Bearer {current_token}'
            # 移除强制返回401状态码的处理，让请求流转到DRF的认证系统
            # DRF会适当处理未认证的请求，返回正确的状态码

    def process_response(self, request, response):
        """处理响应，统一处理JWT认证响应"""
        if hasattr(request, 'new_token'):
            self.set_token_cookie(response, request.new_token)
        return response

    def set_token_cookie(self, response, token, max_age):
        """设置JWT token到cookie"""
        if token and max_age > 0:
            response.set_cookie(
                'jwt_access_token',
                token,
                max_age=max_age,
                httponly=True,
                secure=not settings.DEBUG,
                samesite='Lax',
                path='/'
            )

class ApiTypeMiddleware:
    """API类型中间件，用于区分前端和后台API请求"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.logger = logging.getLogger(__name__)
    
    def __call__(self, request):
        """处理请求"""
        api_type = 'admin' if request.path.startswith('/admin') else 'app'
        request.api_type = api_type

        response = self.get_response(request) # 先处理请求，调用后续中间件和视图

        # === 🔧 修复：在响应返回前记录，此时DRF认证已完成 ===
        try:
            # 🔧 关键修复：从响应上下文中获取实际的用户信息
            # 对于DRF视图，用户信息在视图处理完成后才确定
            user_id = self._get_authenticated_user_id(request, response)
            ip_address = request.META.get('REMOTE_ADDR', 'unknown')
            
            log_message = (
                f"API请求处理完成: type={request.api_type}, user_id={user_id}, "
                f"method={request.method}, path={request.path}, ip={ip_address}, status={response.status_code}"
            )
            logger.info(log_message)
        except Exception as log_e:
            logger.error(f"记录API请求日志时出错: {log_e}", exc_info=True)

        return response

    def _get_authenticated_user_id(self, request, response):
        """
        🔧 新增方法：获取经过DRF认证后的真实用户ID

        DRF的认证是在视图层进行的，中间件层的request.user可能还是AnonymousUser
        需要通过多种方式尝试获取真实的用户ID
        """
        try:
            # 方法1：检查request.user（对于某些情况可能已被DRF设置）
            if hasattr(request, 'user') and request.user.is_authenticated:
                return request.user.id

            # 🔥 方法2：对于静态资源请求，从路径中提取用户信息
            if request.path.startswith('/media/avatars/'):
                # 从cookie或Authorization header中获取token
                user_id = self._extract_user_from_token(request)
                if user_id:
                    return user_id

            # 方法3：对于token刷新等认证端点，从Authorization header解析用户ID
            if request.path.endswith('/token/refresh/') or request.path.endswith('/logout/'):
                user_id = self._extract_user_from_token(request)
                if user_id:
                    return user_id

            # 方法4：检查响应数据中是否包含用户信息（某些API会返回用户数据）
            if hasattr(response, 'data') and isinstance(response.data, dict):
                # 检查响应中的用户信息
                if 'user' in response.data and isinstance(response.data['user'], dict):
                    return response.data['user'].get('id', 'anonymous')

                # 检查登录响应
                if 'data' in response.data and isinstance(response.data['data'], dict):
                    data = response.data['data']
                    if 'user' in data and isinstance(data['user'], dict):
                        return data['user'].get('id', 'anonymous')

            # 方法5：对于成功的认证请求，尝试从token中解析
            if response.status_code == 200 and self._is_auth_endpoint(request.path):
                user_id = self._extract_user_from_token(request)
                if user_id:
                    return user_id

            return 'anonymous'

        except Exception as e:
            logger.debug(f"提取用户ID时出错: {e}")
            return 'anonymous'
    
    def _extract_user_from_token(self, request):
        """从Authorization header或cookie中的JWT token提取用户ID"""
        try:
            token = None

            # 🔥 方法1：从Authorization header获取token
            auth_header = request.META.get('HTTP_AUTHORIZATION', '')
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]

            # 🔥 方法2：从cookie获取token（用于静态资源请求等）
            if not token:
                token = request.COOKIES.get('jwt_access_token')

            if not token:
                return None

            # 使用Django JWT库解码token
            import jwt
            from django.conf import settings

            # 解码token（不验证过期时间，只提取用户ID）
            decoded = jwt.decode(
                token,
                settings.SIMPLE_JWT['SIGNING_KEY'],
                algorithms=[settings.SIMPLE_JWT['ALGORITHM']],
                options={"verify_exp": False}  # 🔧 关键：不验证过期时间，只提取用户ID
            )

            # 从token中提取用户ID
            user_id = decoded.get(settings.SIMPLE_JWT.get('USER_ID_CLAIM', 'user_id'))
            return user_id

        except Exception as e:
            logger.debug(f"从token提取用户ID失败: {e}")
            return None
    
    def _is_auth_endpoint(self, path):
        """判断是否是认证相关的端点"""
        auth_endpoints = [
            '/authentication/login/',
            '/authentication/token/refresh/',
            '/authentication/logout/',
            '/dashboard/',  # Dashboard API通常需要认证
        ]
        
        return any(endpoint in path for endpoint in auth_endpoints)

class AppApiRateLimitMiddleware:
    """前端API请求频率限制中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.logger = logging.getLogger(__name__)
        self.redis_client = redis.from_url(settings.CACHES['default']['LOCATION'])
        
        # 频率限制配置
        self.rate_limits = {
            # 路径: (最大请求数, 时间窗口(秒))
            # 可根据实际情况配置不同接口的限制
            'default': (100, 60),  # 默认限制：每分钟100次
            'authentication/login': (20, 60),  # 登录接口：每分钟20次
            'authentication/verify-code': (10, 60),  # 验证码接口：每分钟10次
        }
    
    def __call__(self, request):
        """处理请求"""
        # 只处理前端API请求
        if not hasattr(request, 'api_type') or request.api_type != 'app':
            return self.get_response(request)
        
        # 获取客户端标识
        from .utils import get_client_ip
        client_ip = get_client_ip(request)
        
        # 获取路径部分
        path = request.path.replace('/api/app/v1/', '')
        
        # 确定适用的频率限制
        limit_key = None
        for key in self.rate_limits.keys():
            if path.startswith(key):
                limit_key = key
                break
        
        # 使用默认限制
        if not limit_key:
            limit_key = 'default'
        
        # 应用频率限制
        max_requests, window = self.rate_limits[limit_key]
        
        # 检查是否超过限制
        if not self._check_rate_limit(client_ip, path, max_requests, window):
            self.logger.warning(
                f"API请求频率超限: ip={client_ip}, path={path}, "
                f"limit={max_requests}/{window}s"
            )
            
            response_data = {
                'code': 429,
                'message': '请求频率超限，请稍后再试',
                'data': None
            }
            
            return JsonResponse(response_data, status=429)
        
        # 处理请求
        return self.get_response(request)
    
    def _check_rate_limit(self, client_ip, path, max_requests, window):
        """检查是否超过频率限制"""
        try:
            # 构造Redis键
            key = f"rate_limit:{client_ip}:{path}"
            
            # 获取当前计数
            count = self.redis_client.get(key)
            
            # 如果键不存在，创建并设置过期时间
            if count is None:
                self.redis_client.set(key, 1, ex=window)
                return True
            
            # 转换为整数
            count = int(count)
            
            # 检查是否超过限制
            if count >= max_requests:
                return False
            
            # 增加计数
            self.redis_client.incr(key)
            
            return True
        except Exception as e:
            self.logger.error(f"检查API请求频率限制失败: {str(e)}", exc_info=True)
            return True  # 出错时默认允许请求 