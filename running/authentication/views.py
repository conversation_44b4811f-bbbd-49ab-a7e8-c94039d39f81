from django.shortcuts import render
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils import timezone
from .models import UserToken, TokenBlacklist
from django.core.cache import cache
from rest_framework.throttling import UserRateThrottle
import logging
from django.conf import settings
from .utils import get_client_ip
from users.models import User
from drf_spectacular.utils import extend_schema, inline_serializer, OpenApiResponse
from rest_framework import serializers

logger = logging.getLogger(__name__)

class TokenRateThrottle(UserRateThrottle):
    """Token请求频率限制"""
    rate = '10/minute'  # 每分钟10次

class BaseTokenView(APIView):
    """Token基础视图"""
    throttle_classes = [TokenRateThrottle]
    
    def handle_exception(self, exc):
        """统一异常处理"""
        logger.error(f"Token操作失败: {str(exc)}", exc_info=True)
        
        if hasattr(exc, 'status_code'):
            status_code = exc.status_code
        else:
            status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            
        return Response({
            'code': status_code,
            'message': str(exc),
            'data': {
                'error_type': exc.__class__.__name__,
                'error_detail': str(exc)
            }
        }, status=status_code)

@extend_schema(
    tags=["令牌管理"],
    summary="用户令牌 (Deprecated)",
    description="已弃用 - 请改用 /authentication/token/ (Pair) 端点"
)
class TokenView(BaseTokenView):
    """(已弃用) SlidingToken 相关逻辑已被移除"""
    permission_classes = [AllowAny]
    pass

@extend_schema(
    tags=["令牌管理"],
    summary="刷新令牌 (Deprecated)",
    description="已弃用 - 请改用 /authentication/token/refresh/ (Pair)" 
)
class TokenRefreshView(BaseTokenView):
    """(已弃用) SlidingToken 刷新逻辑移除"""
    permission_classes = [AllowAny]
    pass

@extend_schema(
    tags=["管理员令牌"],
    summary="管理员令牌",
    description="为管理员创建专用令牌"
)
class AdminTokenView(BaseTokenView):
    """管理员专用Token视图，使用当前会话用户生成JWT令牌"""
    permission_classes = [IsAuthenticated]

    @extend_schema(
        summary="创建管理员令牌",
        description="为当前登录的管理员用户创建专用令牌",
        responses={
            200: inline_serializer(
                name="ApiResponseAdminTokenCreate",
                fields={
                    "code": serializers.IntegerField(),
                    "message": serializers.CharField(),
                    "data": inline_serializer(
                        name="AdminTokenData",
                        fields={
                            "access": serializers.CharField(),
                            "refresh": serializers.CharField(),
                            "expires_at": serializers.DateTimeField()
                        }
                    )
                }
            ),
            403: inline_serializer(
                name="ApiResponseAdminTokenError",
                fields={
                    "code": serializers.IntegerField(),
                    "message": serializers.CharField()
                }
            )
        }
    )
    def post(self, request):
        """为管理员创建新令牌，使用当前Django会话中的用户"""
        try:
            # 确保当前用户是管理员
            if not request.user.is_staff:
                return Response({
                    'code': 403,
                    'message': '此端点仅供管理员使用',
                    'data': None
                }, status=status.HTTP_403_FORBIDDEN)

            # 生成新的JWT令牌
            refresh = RefreshToken.for_user(request.user)
            access_token = str(refresh.access_token)

            # 使用事务创建用户令牌记录
            expires_at = timezone.now() + refresh.access_token.lifetime
            UserToken.objects.filter(user=request.user).update(is_active=False)
            token = UserToken.objects.create(
                user=request.user,
                token=access_token,
                expires_at=expires_at
            )

            # 记录日志
            logger.info(f"管理员令牌已创建: user={request.user.id}, username={request.user.username}")

            return Response({
                'code': 200,
                'message': '管理员令牌创建成功',
                'data': {
                    'access': access_token,
                    'refresh': str(refresh),
                    'expires_at': expires_at
                }
            })
        except Exception as e:
            logger.error(f"管理员令牌创建失败: {str(e)}", exc_info=True)
            return self.handle_exception(e)

class LoginView(BaseTokenView):
    """用户登录视图"""
    permission_classes = [AllowAny]

    def post(self, request):
        """处理用户登录请求"""
        try:
            # 获取登录凭证
            email = request.data.get('email')
            password = request.data.get('password')
            
            if not email or not password:
                return Response({
                    'code': 400,
                    'message': '请提供邮箱和密码',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证用户
            try:
                user = User.objects.get(email=email, is_active=True)
                if not user.check_password(password):
                    # 记录登录失败
                    logger.info(f"User login failed - wrong password: {email}")
                    return Response({
                        'code': 401,
                        'message': '邮箱或密码错误',
                        'data': None
                    }, status=status.HTTP_401_UNAUTHORIZED)
            except User.DoesNotExist:
                # 记录登录失败
                logger.info(f"User login failed - user not found: {email}")
                return Response({
                    'code': 401,
                    'message': '邮箱或密码错误',
                    'data': None
                }, status=status.HTTP_401_UNAUTHORIZED)
            
            # 记录登录成功
            logger.info(f"User successfully logged in: {email}")
            
            # 生成JWT令牌
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            
            # 记录设备信息
            client_ip = get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', 'unknown')
            
            # 创建令牌记录
            expires_at = timezone.now() + refresh.access_token.lifetime
            token = UserToken.objects.create(
                user=user,
                token=access_token,
                expires_at=expires_at,
                ip_address=client_ip,
                user_agent=user_agent
            )
            
            # 设置Session Cookie
            response = Response({
                'code': 200,
                'message': '登录成功',
                'data': {
                    'access': access_token,
                    'refresh': str(refresh),
                    'expires_at': expires_at,
                    'user_id': user.id,
                    'email': user.email,
                    'username': user.username,
                    'is_staff': user.is_staff,
                    'is_admin': user.is_superuser
                }
            }, status=status.HTTP_200_OK)  # 明确设置200状态码
            
            # 设置JWT令牌Cookie
            max_age = refresh.access_token.lifetime.total_seconds()
            response.set_cookie(
                'jwt_access_token',
                access_token,
                max_age=max_age,
                httponly=True,
                secure=not settings.DEBUG,
                samesite='Lax',
                path='/'
            )
            
            return response
        except Exception as e:
            return self.handle_exception(e)
