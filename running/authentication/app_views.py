from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.db import transaction

from .models import AppUser, LoginRecord
from .utils import api_response, generate_token, verify_token
from .app_serializers import UserSerializer, UserProfileSerializer
from config.models import SystemConfig

class RegisterView(APIView):
    """用户注册视图"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        """处理用户注册请求"""
        # 检查是否允许注册
        if not SystemConfig.get_registration_status():
            return api_response(
                code=403,
                message="Registration is currently disabled",
                response_status=status.HTTP_403_FORBIDDEN
            )
            
        # 获取请求数据
        username = request.data.get('username', '').strip()
        password = request.data.get('password', '')
        email = request.data.get('email', '').strip()
        phone = request.data.get('phone', '').strip()
        
        # 验证输入数据
        if not username or not password:
            return api_response(code=400, message="Username and password are required")
            
        if len(password) < 8:
            return api_response(code=400, message="Password must be at least 8 characters")
            
        # 检查用户名是否已存在
        if User.objects.filter(username=username).exists():
            return api_response(code=400, message="Username already exists")

class LoginView(APIView):
    """用户登录视图"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        """处理用户登录请求"""
        username = request.data.get('username', '')
        password = request.data.get('password', '')
        
        if not username or not password:
            return api_response(code=400, message="Username and password are required")
            
        # 验证用户
        user = authenticate(username=username, password=password)
        if not user:
            return api_response(code=401, message="Invalid username or password") 