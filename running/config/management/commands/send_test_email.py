# running/config/management/commands/send_test_email.py
import logging
from django.core.management.base import BaseCommand, CommandError
from django.core import mail
from django.conf import settings
from smtplib import SMTPException, SMTPAuthenticationError
from config.models import SystemConfig # 导入 SystemConfig 模型
import re # 导入 re 用于提取邮箱地址

# 配置日志
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

class Command(BaseCommand):
    help = 'Sends a test email using SMTP configuration from SystemConfig in the database.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--recipient',
            required=True,
            help='The email address to send the test email to.'
        )
        parser.add_argument(
            '--subject',
            default='SweatMint Live SMTP Config Test',
            help='Subject for the test email.'
        )
        parser.add_argument(
            '--message',
            default='This is a test email sent via management command using live SystemConfig settings.',
            help='Body content for the test email.'
        )

    def handle(self, *args, **options):
        recipient = options['recipient']
        subject = options['subject']
        message = options['message']

        logger.info(f"Attempting to send test email to: {recipient}")

        # 1. 从数据库加载实时邮件配置
        try:
            system_config = SystemConfig.objects.first() # 或者使用更精确的查询
            if not system_config:
                raise CommandError("SystemConfig not found in the database. Please configure it in the admin panel.")

            email_config = system_config.get_email_config()
            if not email_config:
                raise CommandError("Failed to get email config from SystemConfig model.")

            logger.info("--- Using Live DB Configuration ---")
            logger.info(f"Host: {email_config.get('EMAIL_HOST')}")
            logger.info(f"Port: {email_config.get('EMAIL_PORT')}")
            logger.info(f"User: {email_config.get('EMAIL_HOST_USER')}")
            logger.info(f"TLS: {email_config.get('EMAIL_USE_TLS')}")
            logger.info(f"SSL: {email_config.get('EMAIL_USE_SSL')}")
            # 密码不记录

            # 提取纯邮箱地址作为发件人
            from_email_config = email_config.get('EMAIL_HOST_USER')
            effective_from_email = from_email_config # 默认值
            if from_email_config:
                match = re.search(r'<(.+?)>', from_email_config)
                if match:
                    # <AUTHOR> <EMAIL>" 格式, send_mail 会自动处理
                    # 但为了日志清晰和可能的直接SMTP调用，我们保留原始格式
                    logger.info(f"Effective From Header: {from_email_config}")
                else:
                    # 如果只是纯邮箱
                     logger.info(f"Effective From Header: {from_email_config}")
            else:
                 raise CommandError("EMAIL_HOST_USER not configured in SystemConfig.")


        except Exception as e:
            raise CommandError(f"Error fetching email config from database: {e}")

        # 2. 尝试发送邮件 (注意：这里直接使用配置，不通过 override_settings)
        try:
            # 使用 Django 的邮件发送功能，它会查找 settings 中的配置
            # 为了确保使用数据库配置，我们需要一个能直接传入配置的邮件后端，或者确保 settings 能动态加载
            # 简单起见，我们直接使用 smtplib，但这绕过了 Django 的一些封装

            # ！！！或者更 Django 的方式：获取邮件连接并手动设置属性 ！！！
            connection = mail.get_connection(
                backend='django.core.mail.backends.smtp.EmailBackend',
                host=email_config.get('EMAIL_HOST'),
                port=email_config.get('EMAIL_PORT'),
                username=email_config.get('EMAIL_HOST_USER'), # 注意：这里应该是纯用户名或完整邮箱，取决于SMTP服务器
                password=email_config.get('EMAIL_HOST_PASSWORD'),
                use_tls=email_config.get('EMAIL_USE_TLS', False),
                use_ssl=email_config.get('EMAIL_USE_SSL', False),
                fail_silently=False,
            )
            
            # 使用获取的连接发送邮件
            sent_count = mail.EmailMessage(
                subject=subject,
                body=message,
                from_email=from_email_config, # 使用数据库中配置的原始格式
                to=[recipient],
                connection=connection,
            ).send(fail_silently=False)


            # sent_count = mail.send_mail(
            #     subject,
            #     message,
            #     from_email_config, # Django send_mail 需要 From 头
            #     [recipient],
            #     fail_silently=False,
            #     # auth_user=email_config.get('EMAIL_HOST_USER'), # 可能需要显式提供认证用户和密码
            #     # auth_password=email_config.get('EMAIL_HOST_PASSWORD')
            #     # send_mail 不直接支持传入 host/port 等，它依赖 settings
            # )

            if sent_count == 1:
                self.stdout.write(self.style.SUCCESS(f"Successfully sent test email to {recipient} using live DB config."))
                logger.info(f"Test email sent successfully to {recipient} using live DB config.")
            else:
                 raise CommandError(f"send_mail returned {sent_count}, expected 1.")


        except SMTPAuthenticationError as e:
            logger.error(f"SMTP Authentication Failed: {e.smtp_code} - {e.smtp_error.decode() if isinstance(e.smtp_error, bytes) else e.smtp_error}", exc_info=True)
            raise CommandError(f"SMTP Authentication Error: {e}. Please check EMAIL_HOST_USER and EMAIL_HOST_PASSWORD in SystemConfig.")
        except ConnectionRefusedError as e:
             logger.error(f"SMTP Connection Refused: Host={email_config.get('EMAIL_HOST')}, Port={email_config.get('EMAIL_PORT')}. Error: {e}", exc_info=True)
             raise CommandError(f"Connection Refused Error: {e}. Check SystemConfig SMTP server/port and reachability.")
        except SMTPException as e: # 更通用的 SMTP 错误
             logger.error(f"SMTP Error: {e}", exc_info=True)
             raise CommandError(f"An SMTP error occurred: {e}")
        except Exception as e:
            logger.error(f"Failed to send test email using live DB config: {e}", exc_info=True)
            raise CommandError(f"Email sending failed with an unexpected error: {e}") 