# Generated by Django 4.2.11 on 2025-03-24 15:25

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('config', '0006_historicalsystemconfig_google_fit_api_key_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicalsystemconfig',
            name='google_fit_api_key',
            field=models.CharField(blank=True, max_length=255, verbose_name='Google Fit API Key'),
        ),
        migrations.AlterField(
            model_name='historicalsystemconfig',
            name='google_fit_client_id',
            field=models.CharField(blank=True, max_length=255, verbose_name='Google Fit Client ID'),
        ),
        migrations.AlterField(
            model_name='historicalsystemconfig',
            name='google_fit_client_secret',
            field=models.CharField(blank=True, max_length=255, verbose_name='Google Fit Client Secret'),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name='historicalsystemconfig',
            name='healthkit_api_key',
            field=models.CharField(blank=True, max_length=255, verbose_name='Apple HealthKit API Key'),
        ),
        migrations.AlterField(
            model_name='historicalsystemconfig',
            name='max_distance_per_day',
            field=models.FloatField(default=100.0, help_text='每天允许的最大运动距离，单位：公里', validators=[django.core.validators.MinValueValidator(0.0)], verbose_name='每日最大运动距离(公里)'),
        ),
        migrations.AlterField(
            model_name='historicalsystemconfig',
            name='max_speed_kmh',
            field=models.FloatField(default=30.0, help_text='允许的最大运动速度，单位：公里/小时', validators=[django.core.validators.MinValueValidator(0.0)], verbose_name='最大运动速度(公里/小时)'),
        ),
        migrations.AlterField(
            model_name='historicalsystemconfig',
            name='max_steps_per_day',
            field=models.PositiveIntegerField(default=100000, help_text='每天允许的最大步数', validators=[django.core.validators.MinValueValidator(0)], verbose_name='每日最大步数'),
        ),
        migrations.AlterField(
            model_name='historicalsystemconfig',
            name='max_steps_per_minute',
            field=models.PositiveIntegerField(default=200, help_text='每分钟允许的最大步数', validators=[django.core.validators.MinValueValidator(0)], verbose_name='每分钟最大步数'),
        ),
        migrations.AlterField(
            model_name='historicalsystemconfig',
            name='max_sync_days',
            field=models.PositiveIntegerField(default=7, help_text='允许同步的最大历史天数', validators=[django.core.validators.MinValueValidator(1)], verbose_name='最大同步天数'),
        ),
        migrations.AlterField(
            model_name='historicalsystemconfig',
            name='sync_interval',
            field=models.PositiveIntegerField(default=300, help_text='两次同步之间的最小间隔时间，单位：秒', validators=[django.core.validators.MinValueValidator(60)], verbose_name='同步间隔(秒)'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='google_fit_api_key',
            field=models.CharField(blank=True, max_length=255, verbose_name='Google Fit API Key'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='google_fit_client_id',
            field=models.CharField(blank=True, max_length=255, verbose_name='Google Fit Client ID'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='google_fit_client_secret',
            field=models.CharField(blank=True, max_length=255, verbose_name='Google Fit Client Secret'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='healthkit_api_key',
            field=models.CharField(blank=True, max_length=255, verbose_name='Apple HealthKit API Key'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='max_distance_per_day',
            field=models.FloatField(default=100.0, help_text='每天允许的最大运动距离，单位：公里', validators=[django.core.validators.MinValueValidator(0.0)], verbose_name='每日最大运动距离(公里)'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='max_speed_kmh',
            field=models.FloatField(default=30.0, help_text='允许的最大运动速度，单位：公里/小时', validators=[django.core.validators.MinValueValidator(0.0)], verbose_name='最大运动速度(公里/小时)'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='max_steps_per_day',
            field=models.PositiveIntegerField(default=100000, help_text='每天允许的最大步数', validators=[django.core.validators.MinValueValidator(0)], verbose_name='每日最大步数'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='max_steps_per_minute',
            field=models.PositiveIntegerField(default=200, help_text='每分钟允许的最大步数', validators=[django.core.validators.MinValueValidator(0)], verbose_name='每分钟最大步数'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='max_sync_days',
            field=models.PositiveIntegerField(default=7, help_text='允许同步的最大历史天数', validators=[django.core.validators.MinValueValidator(1)], verbose_name='最大同步天数'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='sync_interval',
            field=models.PositiveIntegerField(default=300, help_text='两次同步之间的最小间隔时间，单位：秒', validators=[django.core.validators.MinValueValidator(60)], verbose_name='同步间隔(秒)'),
        ),
    ]
