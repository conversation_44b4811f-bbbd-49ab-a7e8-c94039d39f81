# Generated by Django 4.2.11 on 2025-02-22 11:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('config', '0002_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalnetwork',
            name='network_type',
            field=models.CharField(choices=[('TRC20', 'TRC20'), ('ERC20', 'ERC20'), ('BSC', 'BSC'), ('OTHER', '其他')], default='OTHER', help_text='选择网络类型以启用相应的地址格式验证', max_length=10, verbose_name='网络类型'),
        ),
        migrations.AddField(
            model_name='network',
            name='network_type',
            field=models.CharField(choices=[('TRC20', 'TRC20'), ('ERC20', 'ERC20'), ('BSC', 'BSC'), ('OTHER', '其他')], default='OTHER', help_text='选择网络类型以启用相应的地址格式验证', max_length=10, verbose_name='网络类型'),
        ),
        migrations.AlterField(
            model_name='historicalnetwork',
            name='gas_fee',
            field=models.DecimalField(decimal_places=8, default=0, max_digits=18, verbose_name='Gas费用'),
        ),
        migrations.AlterField(
            model_name='network',
            name='gas_fee',
            field=models.DecimalField(decimal_places=8, default=0, max_digits=18, verbose_name='Gas费用'),
        ),
    ]
