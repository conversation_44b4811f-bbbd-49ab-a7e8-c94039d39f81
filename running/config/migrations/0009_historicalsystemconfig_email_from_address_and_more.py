# Generated by Django 4.2.11 on 2025-04-01 13:39

from django.db import migrations, models
import django_cryptography.fields


class Migration(migrations.Migration):

    dependencies = [
        ('config', '0008_alter_systemconfig_options'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='email_from_address',
            field=models.EmailField(blank=True, max_length=254, verbose_name='发件人邮箱'),
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='email_from_name',
            field=models.CharField(blank=True, max_length=50, verbose_name='发件人名称'),
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='email_password',
            field=django_cryptography.fields.encrypt(models.CharField(blank=True, max_length=100, verbose_name='邮箱密码')),
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='email_smtp_port',
            field=models.IntegerField(blank=True, default=587, verbose_name='SMTP端口'),
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='email_smtp_server',
            field=models.CharField(blank=True, max_length=100, verbose_name='SMTP服务器'),
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='email_use_ssl',
            field=models.BooleanField(default=False, verbose_name='使用SSL'),
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='email_use_tls',
            field=models.BooleanField(default=True, verbose_name='使用TLS'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='email_from_address',
            field=models.EmailField(blank=True, max_length=254, verbose_name='发件人邮箱'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='email_from_name',
            field=models.CharField(blank=True, max_length=50, verbose_name='发件人名称'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='email_password',
            field=django_cryptography.fields.encrypt(models.CharField(blank=True, max_length=100, verbose_name='邮箱密码')),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='email_smtp_port',
            field=models.IntegerField(blank=True, default=587, verbose_name='SMTP端口'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='email_smtp_server',
            field=models.CharField(blank=True, max_length=100, verbose_name='SMTP服务器'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='email_use_ssl',
            field=models.BooleanField(default=False, verbose_name='使用SSL'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='email_use_tls',
            field=models.BooleanField(default=True, verbose_name='使用TLS'),
        ),
    ]
