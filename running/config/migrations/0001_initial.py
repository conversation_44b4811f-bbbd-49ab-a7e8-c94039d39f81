# Generated by Django 4.2.19 on 2025-02-12 04:51

import django.core.validators
from django.db import migrations, models
import simple_history.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='HistoricalNetwork',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='网络名称')),
                ('code', models.CharField(db_index=True, max_length=20, verbose_name='网络代码')),
                ('gas_fee', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='GAS费用')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('remark', models.TextField(blank=True, verbose_name='备注')),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
            ],
            options={
                'verbose_name': '网络配置历史',
                'verbose_name_plural': 'historical 提现网络',
                'db_table': 'config_network_history',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalSystemConfig',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('allow_registration', models.BooleanField(default=True, verbose_name='是否开放注册')),
                ('allow_withdrawal', models.BooleanField(default=True, verbose_name='是否开放提现')),
                ('min_withdrawal', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='最低提现金额')),
                ('google_adsense_code', models.TextField(blank=True, verbose_name='Google AdSense代码')),
                ('enable_vip', models.BooleanField(default=True, verbose_name='是否开启VIP等级系统')),
                ('physical_fee_rate', models.PositiveIntegerField(default=0, help_text='0-100的整数，表示百分比', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='实物商品兑换手续费%')),
                ('virtual_fee_rate', models.PositiveIntegerField(default=0, help_text='0-100的整数，表示百分比', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='虚拟商品兑换手续费%')),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
            ],
            options={
                'verbose_name': '配置历史',
                'verbose_name_plural': 'historical 系统配置',
                'db_table': 'config_systemconfig_history',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='Network',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='网络名称')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='网络代码')),
                ('gas_fee', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='GAS费用')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('remark', models.TextField(blank=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '提现网络',
                'verbose_name_plural': '提现网络',
                'ordering': ['sort_order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('allow_registration', models.BooleanField(default=True, verbose_name='是否开放注册')),
                ('allow_withdrawal', models.BooleanField(default=True, verbose_name='是否开放提现')),
                ('min_withdrawal', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='最低提现金额')),
                ('google_adsense_code', models.TextField(blank=True, verbose_name='Google AdSense代码')),
                ('enable_vip', models.BooleanField(default=True, verbose_name='是否开启VIP等级系统')),
                ('physical_fee_rate', models.PositiveIntegerField(default=0, help_text='0-100的整数，表示百分比', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='实物商品兑换手续费%')),
                ('virtual_fee_rate', models.PositiveIntegerField(default=0, help_text='0-100的整数，表示百分比', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='虚拟商品兑换手续费%')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '系统配置',
                'verbose_name_plural': '系统配置',
            },
        ),
    ]
