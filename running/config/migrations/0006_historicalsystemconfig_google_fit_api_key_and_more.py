# Generated by Django 4.2.11 on 2025-03-24 14:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('config', '0005_alter_historicalnetwork_network_type_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='google_fit_api_key',
            field=models.CharField(blank=True, help_text='Google Fit集成的API密钥', max_length=255, verbose_name='Google Fit API Key'),
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='google_fit_client_id',
            field=models.Char<PERSON>ield(blank=True, help_text='Google Fit OAuth 2.0客户端ID', max_length=255, verbose_name='Google Fit Client ID'),
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='google_fit_client_secret',
            field=models.Char<PERSON><PERSON>(blank=True, help_text='Google Fit OAuth 2.0客户端密钥', max_length=255, verbose_name='Google Fit Client Secret'),
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='healthkit_api_key',
            field=models.CharField(blank=True, help_text='iOS Health Kit集成的API密钥', max_length=255, verbose_name='iOS Health Kit API Key'),
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='max_distance_per_day',
            field=models.FloatField(default=100.0, help_text='超过此值将被标记为可疑数据', verbose_name='每日最大距离(公里)'),
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='max_speed_kmh',
            field=models.FloatField(default=30.0, help_text='超过此值将被标记为可疑数据', verbose_name='最大速度(公里/小时)'),
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='max_steps_per_day',
            field=models.PositiveIntegerField(default=100000, help_text='超过此值将被标记为可疑数据', verbose_name='每日最大步数'),
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='max_steps_per_minute',
            field=models.PositiveIntegerField(default=200, help_text='超过此值将被标记为可疑数据', verbose_name='每分钟最大步数'),
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='max_sync_days',
            field=models.PositiveIntegerField(default=7, help_text='最多同步多少天前的数据', verbose_name='最大同步天数'),
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='sync_interval',
            field=models.PositiveIntegerField(default=300, help_text='健康数据同步的时间间隔', verbose_name='同步间隔(秒)'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='google_fit_api_key',
            field=models.CharField(blank=True, help_text='Google Fit集成的API密钥', max_length=255, verbose_name='Google Fit API Key'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='google_fit_client_id',
            field=models.CharField(blank=True, help_text='Google Fit OAuth 2.0客户端ID', max_length=255, verbose_name='Google Fit Client ID'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='google_fit_client_secret',
            field=models.CharField(blank=True, help_text='Google Fit OAuth 2.0客户端密钥', max_length=255, verbose_name='Google Fit Client Secret'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='healthkit_api_key',
            field=models.CharField(blank=True, help_text='iOS Health Kit集成的API密钥', max_length=255, verbose_name='iOS Health Kit API Key'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='max_distance_per_day',
            field=models.FloatField(default=100.0, help_text='超过此值将被标记为可疑数据', verbose_name='每日最大距离(公里)'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='max_speed_kmh',
            field=models.FloatField(default=30.0, help_text='超过此值将被标记为可疑数据', verbose_name='最大速度(公里/小时)'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='max_steps_per_day',
            field=models.PositiveIntegerField(default=100000, help_text='超过此值将被标记为可疑数据', verbose_name='每日最大步数'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='max_steps_per_minute',
            field=models.PositiveIntegerField(default=200, help_text='超过此值将被标记为可疑数据', verbose_name='每分钟最大步数'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='max_sync_days',
            field=models.PositiveIntegerField(default=7, help_text='最多同步多少天前的数据', verbose_name='最大同步天数'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='sync_interval',
            field=models.PositiveIntegerField(default=300, help_text='健康数据同步的时间间隔', verbose_name='同步间隔(秒)'),
        ),
    ]
