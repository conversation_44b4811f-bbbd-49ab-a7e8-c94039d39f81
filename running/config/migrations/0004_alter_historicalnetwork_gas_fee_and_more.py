# Generated by Django 4.2.11 on 2025-02-22 12:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('config', '0003_historicalnetwork_network_type_network_network_type_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicalnetwork',
            name='gas_fee',
            field=models.DecimalField(decimal_places=2, default=0, help_text='提现时收取的固定Gas费用，单位USDT', max_digits=10, verbose_name='Gas费用(USDT)'),
        ),
        migrations.AlterField(
            model_name='network',
            name='gas_fee',
            field=models.DecimalField(decimal_places=2, default=0, help_text='提现时收取的固定Gas费用，单位USDT', max_digits=10, verbose_name='Gas费用(USDT)'),
        ),
    ]
