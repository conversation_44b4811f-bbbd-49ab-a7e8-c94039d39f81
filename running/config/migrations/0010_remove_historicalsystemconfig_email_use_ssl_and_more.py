# Generated by Django 4.2.11 on 2025-04-01 14:45

from django.db import migrations, models
import django_cryptography.fields


class Migration(migrations.Migration):

    dependencies = [
        ('config', '0009_historicalsystemconfig_email_from_address_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='historicalsystemconfig',
            name='email_use_ssl',
        ),
        migrations.RemoveField(
            model_name='historicalsystemconfig',
            name='email_use_tls',
        ),
        migrations.RemoveField(
            model_name='systemconfig',
            name='email_use_ssl',
        ),
        migrations.RemoveField(
            model_name='systemconfig',
            name='email_use_tls',
        ),
        migrations.AddField(
            model_name='historicalsystemconfig',
            name='email_security',
            field=models.CharField(choices=[('TLS', 'TLS (端口587)'), ('SSL', 'SSL (端口465)'), ('NONE', '不加密 (端口25)')], default='TLS', help_text='选择邮件服务器的安全连接方式', max_length=10, verbose_name='安全连接方式'),
        ),
        migrations.AddField(
            model_name='systemconfig',
            name='email_security',
            field=models.CharField(choices=[('TLS', 'TLS (端口587)'), ('SSL', 'SSL (端口465)'), ('NONE', '不加密 (端口25)')], default='TLS', help_text='选择邮件服务器的安全连接方式', max_length=10, verbose_name='安全连接方式'),
        ),
        migrations.AlterField(
            model_name='historicalsystemconfig',
            name='email_from_name',
            field=models.CharField(blank=True, help_text='可选，用于显示的发件人名称', max_length=50, verbose_name='发件人名称'),
        ),
        migrations.AlterField(
            model_name='historicalsystemconfig',
            name='email_password',
            field=django_cryptography.fields.encrypt(models.CharField(blank=True, help_text='Gmail需要应用专用密码，QQ邮箱需要授权码，其他邮箱使用SMTP密码', max_length=100, verbose_name='邮箱密码/授权码')),
        ),
        migrations.AlterField(
            model_name='historicalsystemconfig',
            name='email_smtp_port',
            field=models.IntegerField(default=587, help_text='常用端口: 587(TLS), 465(SSL), 25(非加密)', verbose_name='SMTP端口'),
        ),
        migrations.AlterField(
            model_name='historicalsystemconfig',
            name='email_smtp_server',
            field=models.CharField(blank=True, help_text='例如: smtp.gmail.com, smtp.qq.com', max_length=100, verbose_name='SMTP服务器'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='email_from_name',
            field=models.CharField(blank=True, help_text='可选，用于显示的发件人名称', max_length=50, verbose_name='发件人名称'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='email_password',
            field=django_cryptography.fields.encrypt(models.CharField(blank=True, help_text='Gmail需要应用专用密码，QQ邮箱需要授权码，其他邮箱使用SMTP密码', max_length=100, verbose_name='邮箱密码/授权码')),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='email_smtp_port',
            field=models.IntegerField(default=587, help_text='常用端口: 587(TLS), 465(SSL), 25(非加密)', verbose_name='SMTP端口'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='email_smtp_server',
            field=models.CharField(blank=True, help_text='例如: smtp.gmail.com, smtp.qq.com', max_length=100, verbose_name='SMTP服务器'),
        ),
    ]
