/* 自定义管理界面样式 */
.sidebar-nav {
    background-color: #2c3e50;
}

.sidebar-nav li a {
    color: #ecf0f1;
}

.sidebar-nav li.active {
    background-color: #34495e;
}

.sidebar-nav li:hover {
    background-color: #34495e;
}

/* 主内容区域样式 */
.content-wrapper {
    background: #fff;
    padding: 60px;
    margin: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 表格样式优化 */
.table {
    background-color: #ffffff;
    border-radius: 5px;
    overflow: hidden;
}

.table th {
    background-color: #2c3e50;
    color: #ffffff;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

/* 表单容器样式 */
.form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 15px;
}

/* 输入框样式优化 */
.form-control {
    max-width: 300px;
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* 数字输入框特殊处理 */
input[type="number"].form-control {
    max-width: 150px;
}

/* 文本域特殊处理 */
textarea.form-control {
    max-width: 400px;
}

/* 复选框容器样式 */
.checkbox-container {
    margin: 10px 0;
    padding: 8px;
}

/* 表单组样式 */
.form-group {
    margin-bottom: 15px;
    padding: 8px;
}

/* 标签样式 */
label {
    display: block;
    margin-bottom: 6px;
    color: #2c3e50;
    font-weight: 500;
}

/* 响应式布局 */
@media (max-width: 992px) {
    .form-container {
        max-width: 100%;
        padding: 10px;
    }
    
    .form-control,
    input[type="number"].form-control,
    textarea.form-control {
        max-width: 100%;
    }
    
    .content-wrapper {
        margin: 10px;
        padding: 15px;
    }
    
    .card {
        padding: 10px !important;
    }
}

/* 卡片样式优化 */
.card {
    border: none !important;
    box-shadow: 0 2px 4px rgba(0,0,0,.05) !important;
    margin-bottom: 1.5rem !important;
    border-radius: 8px !important;
    background: #fff !important;
    padding: 15px !important;
}

.card-header {
    background: none !important;
    border-bottom: 1px solid #f0f0f0 !important;
    padding: 1rem !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    color: #1a1a1a !important;
}

.card-body {
    padding: 1rem !important;
}

/* 分组标题样式 */
.section-title {
    font-size: 1.2em;
    color: #2c3e50;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #eee;
}

/* 系统配置页面样式优化 */
.card {
    border: none !important;
    box-shadow: 0 2px 4px rgba(0,0,0,.05) !important;
    margin-bottom: 1.5rem !important;
    border-radius: 8px !important;
    background: #fff !important;
}

.card-header {
    background: none !important;
    border-bottom: 1px solid #f0f0f0 !important;
    padding: 1rem 1.5rem !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    color: #1a1a1a !important;
}

.card-body {
    padding: 1.5rem !important;
}

/* 站点配置页面样式 */
.site-config {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.site-config h2 {
    margin: 30px 0 20px;
    padding: 10px 20px;
    background: #f5f5f5;
    border-radius: 4px;
    font-size: 18px;
    color: #333;
}

.site-config .form-group {
    margin-bottom: 20px;
    padding: 0 20px;
}

.site-config .form-control {
    max-width: 300px;
    width: 100%;
}

.site-config .checkbox {
    margin: 10px 0;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .site-config {
        padding: 15px;
    }
    
    .site-config h2 {
        margin: 20px 0 15px;
        padding: 8px 15px;
        font-size: 16px;
    }
    
    .site-config .form-group {
        padding: 0 15px;
    }
    
    .site-config .form-control {
        max-width: 100%;
    }
}

/* 表单验证状态样式 */
.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(.375em + .1875rem) center;
    background-size: calc(.75em + .375rem) calc(.75em + .375rem);
}

.form-control.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(.375em + .1875rem) center;
    background-size: calc(.75em + .375rem) calc(.75em + .375rem);
}

/* 帮助文本样式 */
.help-text {
    font-size: 0.9em;
    color: #666;
    margin-top: 4px;
}

/* 必填字段标记 */
.required-field::after {
    content: "*";
    color: #dc3545;
    margin-left: 4px;
} 