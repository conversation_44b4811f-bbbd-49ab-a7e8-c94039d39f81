{% extends "admin/config/systemconfig/change_list.html" %}
{% load i18n admin_urls static admin_modify %}

{% block content %}
<div id="content-main">
    <form {% if has_file_field %}enctype="multipart/form-data" {% endif %}action="{{ form_url }}" method="post" id="{{ opts.model_name }}_form" novalidate>
        {% csrf_token %}
        {% if is_popup %}<input type="hidden" name="{{ is_popup_var }}" value="1">{% endif %}
        {% if to_field %}<input type="hidden" name="{{ to_field_var }}" value="{{ to_field }}">{% endif %}
        
        {% if errors %}
            <p class="errornote">
                {% blocktranslate count counter=errors|length %}Please correct the error below.{% plural %}Please correct the errors below.{% endblocktranslate %}
            </p>
            {{ adminform.form.non_field_errors }}
        {% endif %}
        
        {% for fieldset in adminform %}
            {% include "admin/includes/fieldset.html" %}
        {% endfor %}
        
        {% block submit_buttons_bottom %}
            <div class="submit-row">
                <input type="submit" value="{% trans '保存' %}" class="default" name="_save">
                <input type="button" value="测试邮件配置" id="test-email-btn" class="el-button el-button--primary" />
            </div>
        {% endblock %}
    </form>
</div>

<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function() {
    // 邮件测试按钮
    const testEmailBtn = document.getElementById('test-email-btn');
    if (testEmailBtn) {
        testEmailBtn.addEventListener('click', function() {
            // 禁用按钮，防止重复点击
            this.disabled = true;
            this.value = '正在测试...';
            
            // 发送测试请求
            fetch('{% url 'admin:test_email_config' %}')
                .then(function(response) { return response.json(); })
                .then(function(data) {
                    // 根据结果显示消息
                    if (data.status === 'success') {
                        // 使用SimpleUI的Success消息
                        parent.parent.app.$message({
                            type: 'success',
                            message: data.message
                        });
                    } else {
                        // 使用SimpleUI的Error消息
                        parent.parent.app.$message({
                            type: 'error',
                            message: data.message
                        });
                    }
                })
                .catch(function(error) {
                    // 发生错误时显示
                    parent.parent.app.$message({
                        type: 'error',
                        message: '请求失败: ' + error
                    });
                })
                .finally(function() {
                    // 无论成功失败，恢复按钮状态
                    testEmailBtn.disabled = false;
                    testEmailBtn.value = '测试邮件配置';
                });
        });
    }
});
</script>
{% endblock %} 