{% extends 'admin/change_form.html' %}
{% load i18n admin_urls static admin_list %}

{% block extrastyle %}
{{ block.super }}
<style>
    /* 移除返回按钮 */
    .breadcrumbs {
        display: none !important;
    }

    /* 修复标题样式 */
    #content > h1 {
        display: block !important;
        font-size: 20px;
        color: #333;
        padding: 16px 24px;
        margin: 0;
        background: #f8f9fa;
        border-bottom: 1px solid #eee;
    }

    /* 修复分组标题样式 */
    fieldset.module h2 {
        background: #79aec8 !important;
        color: white !important;
        padding: 8px 12px !important;
        font-size: 13px !important;
        font-weight: 400 !important;
        margin: 0 !important;
    }

    /* 表单样式 */
    .form-row {
        padding: 12px;
        border-bottom: 1px solid #eee;
    }

    /* 帮助文本样式 */
    .help {
        color: #666;
        font-size: 13px;
        margin-top: 4px;
    }

    /* 历史记录按钮 */
    .object-tools {
        float: right;
        margin-top: -48px;
        margin-right: 24px;
        position: relative;
        z-index: 1;
    }

    .object-tools a.historylink {
        background: #417690;
        padding: 8px 12px;
        border-radius: 4px;
        color: white;
        font-size: 12px;
        text-decoration: none;
    }

    .object-tools a.historylink:hover {
        background: #205067;
    }

    /* 保存按钮样式 */
    .submit-row {
        padding: 12px 24px;
        margin: 20px 0;
        text-align: right;
        background: #f8f9fa;
        border: 1px solid #eee;
    }

    .submit-row input[type="submit"] {
        background: #417690;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        color: white;
        cursor: pointer;
    }

    .submit-row input[type="submit"]:hover {
        background: #205067;
    }
</style>
{% endblock %}

{% block content %}
<div style="display: none">修改 系统配置</div>
<div id="content">
    <h1>修改 系统配置</h1>
    <div class="object-tools">
        {% if original %}
        <a href="{% url 'admin:config_systemconfig_history' original.pk %}" class="historylink">历史记录</a>
        {% endif %}
    </div>
    <form {% if has_file_field %}enctype="multipart/form-data" {% endif %}action="{{ form_url }}" method="post" id="{{ opts.model_name }}_form" novalidate>
        {% csrf_token %}
        {% for fieldset in adminform %}
            <fieldset class="module aligned {{ fieldset.classes }}">
                {% if fieldset.name %}
                    <h2>{{ fieldset.name }}</h2>
                {% endif %}
                {% for line in fieldset %}
                    {% for field in line %}
                        <div class="form-row{% if field.field.name %} field-{{ field.field.name }}{% endif %}">
                            {{ field.field.errors }}
                            {{ field.field.label_tag }}
                            {{ field.field }}
                            {% if field.field.help_text %}
                                <div class="help">{{ field.field.help_text|safe }}</div>
                            {% endif %}
                        </div>
                    {% endfor %}
                {% endfor %}
            </fieldset>
        {% endfor %}
        <div class="submit-row">
            <input type="submit" value="保存" class="default" name="_save">
        </div>
    </form>
</div>
{% endblock %} 