from django.shortcuts import render
from rest_framework import viewsets, status, permissions
from rest_framework.response import Response
from rest_framework.decorators import action
from django.utils.translation import gettext_lazy as _
from django.core.cache import cache
from rest_framework.views import APIView
from rest_framework.throttling import UserRateThrottle, AnonRateThrottle
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator
from .models import SystemConfig, Network
from .serializers import (
    SystemConfigSerializer, NetworkSerializer, NetworkListSerializer
)
# Add drf-spectacular imports
from drf_spectacular.utils import extend_schema, inline_serializer, OpenApiTypes
from rest_framework import serializers # Needed for inline_serializer

# 导入ApiResponse和消息字典
from core.utils.api_utils import ApiResponse
from core.utils.messages import get_message, CONFIG_MESSAGES

# 添加兼容函数，保持向后兼容
def api_response(data=None, message="success", code=200, response_status=status.HTTP_200_OK):
    """
    统一API响应格式（兼容函数，内部使用ApiResponse）
    
    Args:
        data: 响应数据
        message: 响应消息
        code: 业务状态码
        response_status: HTTP状态码
    
    Returns:
        Response: 统一格式的响应对象
    """
    return ApiResponse(
        data=data,
        message=message,
        code=code,
        status=response_status
    )


# 定义API访问频率限制
class ConfigUserRateThrottle(UserRateThrottle):
    """针对配置API的用户频率限制"""
    rate = '60/minute'  # 每分钟60次请求


class ConfigAnonRateThrottle(AnonRateThrottle):
    """针对配置API的匿名用户频率限制"""
    rate = '30/minute'  # 每分钟30次请求


class IsAdminUser(permissions.BasePermission):
    """仅管理员可访问"""
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_staff)


class SystemConfigViewSet(viewsets.GenericViewSet):
    """系统配置视图集"""
    serializer_class = SystemConfigSerializer
    permission_classes = [IsAdminUser]
    throttle_classes = [ConfigUserRateThrottle]

    def get_object(self):
        """获取系统配置（优先从缓存获取）"""
        return SystemConfig.get_config()

    def retrieve(self, request, *args, **kwargs):
        """获取系统配置"""
        instance = self.get_object()
        if not instance:
            return Response(
                {'detail': _('系统配置不存在')},
                status=status.HTTP_404_NOT_FOUND
            )
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def partial_update(self, request, *args, **kwargs):
        """更新系统配置"""
        instance = self.get_object()
        if not instance:
            return Response(
                {'detail': _('系统配置不存在')},
                status=status.HTTP_404_NOT_FOUND
            )
        
        serializer = self.get_serializer(
            instance,
            data=request.data,
            partial=True
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        
        # 记录操作日志（由simple_history自动处理）
        return Response(serializer.data)


class NetworkViewSet(viewsets.ModelViewSet):
    """提现网络视图集"""
    queryset = Network.objects.all()
    serializer_class = NetworkSerializer
    permission_classes = [IsAdminUser]
    throttle_classes = [ConfigUserRateThrottle]
    filterset_fields = ['is_active']
    search_fields = ['name', 'code']
    ordering_fields = ['sort_order', 'created_at']
    ordering = ['sort_order', 'created_at']

    def get_serializer_class(self):
        """根据action选择序列化器"""
        if self.action == 'list':
            return NetworkListSerializer
        return NetworkSerializer

    def perform_create(self, serializer):
        serializer.save()
        Network.clear_cache()

    def perform_update(self, serializer):
        serializer.save()
        Network.clear_cache()

    def perform_destroy(self, instance):
        instance.delete()
        Network.clear_cache()

    @action(detail=False, methods=['get'])
    def active(self, request):
        """获取所有启用的网络"""
        networks = Network.get_active_networks()
        serializer = NetworkListSerializer(networks, many=True)
        return Response(serializer.data)


# Reusable generic error serializer
GenericErrorResponseSerializer = inline_serializer(
    name='ConfigGenericErrorResponse',
    fields={
        'code': serializers.IntegerField(),
        'message': serializers.CharField(),
        'data': serializers.DictField(required=False, allow_null=True)
    }
)

@extend_schema(
    tags=["App - Config"],
    summary="获取对用户可见的系统配置",
    description="获取前端 App 需要的部分系统配置信息。",
    responses={
        200: inline_serializer(
            name='AppSystemConfigResponse',
            fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': inline_serializer(
                    name='AppSystemConfigData',
                    fields={
                        'allow_registration': serializers.BooleanField(),
                        'allow_withdrawal': serializers.BooleanField(),
                        'min_withdrawal': serializers.DecimalField(max_digits=18, decimal_places=8),
                        'enable_vip': serializers.BooleanField(),
                        'physical_fee_rate': serializers.DecimalField(max_digits=5, decimal_places=2),
                        'virtual_fee_rate': serializers.DecimalField(max_digits=5, decimal_places=2),
                    }
                )
            }
        ),
        404: GenericErrorResponseSerializer, # Config not found
        500: GenericErrorResponseSerializer, # Server Error
    }
)
class SystemConfigView(APIView):
    """前端用户获取系统配置视图"""
    permission_classes = [permissions.IsAuthenticated]
    
    @method_decorator(cache_page(60 * 5))  # 缓存5分钟
    def get(self, request):
        """获取用户可见的系统配置"""
        config = SystemConfig.get_config()
        if not config:
            return ApiResponse(
                code=404,
                message=get_message('config_not_found'),
                data=None,
                status=status.HTTP_404_NOT_FOUND
            )
        
        # 只返回非敏感的配置数据
        data = {
            'allow_registration': config.allow_registration,
            'allow_withdrawal': config.allow_withdrawal,
            'min_withdrawal': config.min_withdrawal,
            'enable_vip': config.enable_vip,
            'physical_fee_rate': config.physical_fee_rate,
            'virtual_fee_rate': config.virtual_fee_rate,
        }
        
        return ApiResponse(
            code=200,
            message=get_message('success'),
            data=data
        )


@extend_schema(
    tags=["App - Config"],
    summary="获取 App 版本信息",
    description="提供 App 的最新版本号、是否强制更新等信息。",
    responses={
        200: inline_serializer(
            name='AppVersionResponse',
            fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': inline_serializer(
                    name='AppVersionData',
                    fields={
                        'version': serializers.CharField(),
                        'force_update': serializers.BooleanField(),
                        'update_url': serializers.URLField(),
                        'update_notes': serializers.CharField(),
                    }
                )
            }
        ),
        500: GenericErrorResponseSerializer, # Server Error
    }
)
class AppVersionView(APIView):
    """APP版本信息视图"""
    permission_classes = [permissions.AllowAny]
    throttle_classes = [ConfigAnonRateThrottle]
    
    @method_decorator(cache_page(60 * 10))  # 缓存10分钟
    def get(self, request):
        """获取APP版本信息"""
        data = {
            'version': '1.0.0',
            'force_update': False,
            'update_url': 'https://www.example.com/download',
            'update_notes': 'Update notes'
        }
        
        return ApiResponse(
            code=200,
            message=get_message('success'),
            data=data
        )


@extend_schema(
    tags=["App - Config"],
    summary="获取支持的语言列表",
    description="返回系统支持的多语言选项。",
    responses={
        200: inline_serializer(
            name='SupportedLanguagesResponse',
            fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': serializers.ListField(
                    child=inline_serializer(
                        name='LanguageItem',
                        fields={
                            'code': serializers.CharField(),
                            'name': serializers.CharField(),
                        }
                    )
                )
            }
        ),
        500: GenericErrorResponseSerializer, # Server Error
    }
)
class SupportedLanguagesView(APIView):
    """支持的语言列表视图"""
    permission_classes = [permissions.AllowAny]
    throttle_classes = [ConfigAnonRateThrottle]
    
    @method_decorator(cache_page(60 * 60))  # 缓存60分钟
    def get(self, request):
        """获取支持的语言列表"""
        languages = [
            {'code': 'zh-hans', 'name': '简体中文'},
            {'code': 'en', 'name': 'English'},
        ]
        
        try:
            return ApiResponse(
                code=200,
                message="success",
                data=languages
            )
        except NameError:
            return api_response(
                code=200,
                message="success",
                data=languages
            )


@extend_schema(
    tags=["App - Config"],
    summary="获取地区配置列表",
    description="返回系统支持的地区选项。",
    responses={
        200: inline_serializer(
            name='RegionsResponse',
            fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': serializers.ListField(
                    child=inline_serializer(
                        name='RegionItem',
                        fields={
                            'code': serializers.CharField(),
                            'name': serializers.CharField(),
                        }
                    )
                )
            }
        ),
        500: GenericErrorResponseSerializer, # Server Error
    }
)
class RegionsView(APIView):
    """地区配置视图"""
    permission_classes = [permissions.AllowAny]
    throttle_classes = [ConfigAnonRateThrottle]
    
    @method_decorator(cache_page(60 * 60))  # 缓存60分钟
    def get(self, request):
        """获取地区配置"""
        regions = [
            {'code': 'sg', 'name': 'Singapore'},
            {'code': 'cn', 'name': 'China'},
            {'code': 'us', 'name': 'United States'},
        ]
        
        try:
            return ApiResponse(
                code=200,
                message="success",
                data=regions
            )
        except NameError:
            return api_response(
                code=200,
                message="success",
                data=regions
            )


@extend_schema(
    tags=["App - Config"],
    summary="获取活跃的提现网络列表",
    description="返回当前启用的所有提现网络及其基本信息。",
    responses={
        200: inline_serializer(
            name='ActiveNetworksResponse',
            fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': NetworkListSerializer(many=True) # Use existing serializer
            }
        ),
        500: GenericErrorResponseSerializer, # Server Error
    }
)
class NetworksView(APIView):
    """提现网络列表视图"""
    permission_classes = [permissions.IsAuthenticated]
    throttle_classes = [ConfigUserRateThrottle]
    
    @method_decorator(cache_page(60 * 5))  # 缓存5分钟
    def get(self, request):
        """获取可用的提现网络列表"""
        networks = Network.get_active_networks()
        data = []
        for network in networks:
            data.append({
                'id': network.id,
                'name': network.name,
                'code': network.code,
                'gas_fee': network.gas_fee
            })
        
        try:
            return ApiResponse(
                code=200,
                message="success",
                data=data
            )
        except NameError:
            return api_response(
                code=200,
                message="success",
                data=data
            )


@extend_schema(
    tags=["App - Config"],
    summary="获取当前汇率信息",
    description="返回当前 SWMT 到 USDT 的汇率等信息。",
    responses={
        200: inline_serializer(
            name='ExchangeRatesResponse',
            fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': inline_serializer(
                    name='ExchangeRatesData',
                    fields={
                        'swmt_to_usdt': serializers.DecimalField(max_digits=18, decimal_places=8),
                        # Add other rates if needed
                    }
                )
            }
        ),
        500: GenericErrorResponseSerializer, # Server Error
    }
)
class ExchangeRatesView(APIView):
    """汇率信息视图"""
    permission_classes = [permissions.IsAuthenticated]
    throttle_classes = [ConfigUserRateThrottle]
    
    @method_decorator(cache_page(60 * 5))  # 缓存5分钟
    def get(self, request):
        """获取当前汇率信息"""
        rates = {
            'SWMT_TO_USDT': 0.01,  # 1 SWMT = 0.01 USDT
            'updated_at': '2023-03-25T10:30:45Z'
        }
        
        try:
            return ApiResponse(
                code=200,
                message="success",
                data=rates
            )
        except NameError:
            return api_response(
                code=200,
                message="success",
                data=rates
            )
