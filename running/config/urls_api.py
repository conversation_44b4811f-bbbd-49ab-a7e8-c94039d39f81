from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import SystemConfigViewSet, NetworkViewSet

# 设置API路由
router = DefaultRouter()
router.register(r'system', SystemConfigViewSet, basename='systemconfig')
router.register(r'networks', NetworkViewSet, basename='network')

app_name = 'config_api'

urlpatterns = [
    # 管理后台API
    path('', include(router.urls)),
] 