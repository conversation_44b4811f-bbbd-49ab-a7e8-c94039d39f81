from django.urls import path
from .views import (
    SystemConfigView, AppVersionView, SupportedLanguagesView,
    RegionsView, NetworksView, ExchangeRatesView
)

app_name = 'app_config'

urlpatterns = [
    # 系统配置API
    path('system', SystemConfigView.as_view(), name='app-config-system'),
    # APP版本信息API
    path('app-version', AppVersionView.as_view(), name='app-config-app-version'),
    # 支持的语言列表API
    path('languages', SupportedLanguagesView.as_view(), name='app-config-languages'),
    # 地区配置API
    path('regions', RegionsView.as_view(), name='app-config-regions'),
    # 提现网络列表API
    path('networks', NetworksView.as_view(), name='app-config-networks'),
    # 汇率信息API
    path('exchange-rates', ExchangeRatesView.as_view(), name='app-config-exchange-rates'),
] 