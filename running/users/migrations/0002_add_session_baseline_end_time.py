# Generated manually for session baseline end time field
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='unifiedusersession',
            name='session_baseline_end_time',
            field=models.DateTimeField(blank=True, help_text='会话基线结束时间（即会话开始时间，用于基线计算的endDate）', null=True),
        ),
        migrations.AlterField(
            model_name='unifiedusersession',
            name='baseline_date',
            field=models.DateTimeField(blank=True, help_text='基线设置日期（新加坡时间当天00:00）', null=True),
        ),
        # 数据迁移：将现有session_start_time复制到session_baseline_end_time
        migrations.RunSQL(
            "UPDATE users_unifiedusersession SET session_baseline_end_time = session_start_time WHERE session_start_time IS NOT NULL;",
            reverse_sql="UPDATE users_unifiedusersession SET session_baseline_end_time = NULL;"
        ),
    ]
