from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_protect
from django.db import transaction
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import Sum, Count, Q
from .models import AgentLevel, CommissionRecord, SettlementPeriod, AgentRelationship, SettlementWarning
from users.models import User
from stats.models import SystemLog
import logging
import json
from rest_framework import viewsets, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from .serializers import SettlementWarningSerializer
from django.views.generic import ListView
from django.contrib.auth.mixins import UserPassesTestMixin
from django.contrib import messages

logger = logging.getLogger(__name__)

# Create your views here.

@csrf_protect
@require_http_methods(["GET"])
def get_team_stats(request):
    """获取团队业绩统计"""
    try:
        user = request.user
        if not user.is_agent:
            return JsonResponse({
                'code': 403,
                'message': '非代理用户无法查看团队业绩'
            })
        
        # 获取直接下线
        level1_users = User.objects.filter(referrer=user)
        # 获取二级下线
        level2_users = User.objects.filter(referrer__referrer=user)
        # 获取三级下线
        level3_users = User.objects.filter(referrer__referrer__referrer=user)
        
        # 计算本周期SWMT产出
        current_period = timezone.now().isocalendar()[1]  # 获取当前周数
        
        data = {
            'team_size': {
                'level1': level1_users.count(),
                'level2': level2_users.count(),
                'level3': level3_users.count(),
                'total': level1_users.count() + level2_users.count() + level3_users.count()
            },
            'current_period': {
                'level1_swmt': level1_users.aggregate(
                    total=Sum('taskcompletion__total_swmt', 
                            filter=Q(taskcompletion__created_at__week=current_period))
                )['total'] or 0,
                'level2_swmt': level2_users.aggregate(
                    total=Sum('taskcompletion__total_swmt',
                            filter=Q(taskcompletion__created_at__week=current_period))
                )['total'] or 0,
                'level3_swmt': level3_users.aggregate(
                    total=Sum('taskcompletion__total_swmt',
                            filter=Q(taskcompletion__created_at__week=current_period))
                )['total'] or 0,
            }
        }
        
        # 获取代理等级信息
        try:
            agent_relationship = AgentRelationship.objects.get(user=user, is_active=True)
            data['agent_level'] = {
                'name': agent_relationship.agent_level.name,
                'level': agent_relationship.agent_level.level,
                'commission_rates': {
                    'level1': float(agent_relationship.agent_level.level1_commission_rate),
                    'level2': float(agent_relationship.agent_level.level2_commission_rate),
                    'level3': float(agent_relationship.agent_level.level3_commission_rate),
                }
            }
        except AgentRelationship.DoesNotExist:
            data['agent_level'] = None
        
        return JsonResponse({
            'code': 200,
            'message': '获取团队业绩成功',
            'data': data
        })
    except Exception as e:
        logger.error(f"获取团队业绩失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': f'系统错误: {str(e)}'
        })

@csrf_protect
@require_http_methods(["GET"])
def get_commission_history(request):
    """获取佣金结算历史"""
    try:
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        
        commission_records = CommissionRecord.objects.filter(
            agent=request.user
        ).select_related('settlement_period', 'agent_level').order_by('-created_at')
        
        paginator = Paginator(commission_records, page_size)
        current_page = paginator.page(page)
        
        data = [{
            'id': record.id,
            'period_start': record.settlement_period.start_date.isoformat(),
            'period_end': record.settlement_period.end_date.isoformat(),
            'agent_level': {
                'name': record.agent_level.name,
                'level': record.agent_level.level
            },
            'commissions': {
                'level1': {
                    'members': record.level1_members,
                    'swmt': float(record.level1_swmt),
                    'commission': float(record.level1_commission)
                },
                'level2': {
                    'members': record.level2_members,
                    'swmt': float(record.level2_swmt),
                    'commission': float(record.level2_commission)
                },
                'level3': {
                    'members': record.level3_members,
                    'swmt': float(record.level3_swmt),
                    'commission': float(record.level3_commission)
                }
            },
            'total_commission': float(record.total_commission),
            'status': record.settlement_period.status,
            'settlement_time': record.created_at.isoformat()
        } for record in current_page]
        
        return JsonResponse({
            'code': 200,
            'message': 'success',
            'data': {
                'results': data,
                'total': paginator.count,
                'total_pages': paginator.num_pages,
                'current_page': page
            }
        })
    except Exception as e:
        logger.error(f"获取佣金结算历史失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': str(e)
        })

@csrf_protect
@require_http_methods(["GET"])
def get_downline_tree(request):
    """获取下线关系树"""
    try:
        user = request.user
        if not user.is_agent:
            return JsonResponse({
                'code': 403,
                'message': '非代理用户无法查看下线关系'
            })
        
        def get_user_info(user):
            return {
                'user_id': user.user_id,
                'email': user.email,
                'is_agent': user.is_agent,
                'is_active_member': user.is_active_member,
                'created_at': user.created_at.isoformat()
            }
        
        def get_children(parent, level=1, max_level=3):
            if level > max_level:
                return []
            
            children = User.objects.filter(referrer=parent)
            return [{
                'user': get_user_info(child),
                'children': get_children(child, level + 1, max_level)
            } for child in children]
        
        data = {
            'user': get_user_info(user),
            'children': get_children(user)
        }
        
        return JsonResponse({
            'code': 200,
            'message': 'success',
            'data': data
        })
    except Exception as e:
        logger.error(f"获取下线关系树失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': str(e)
        })

@csrf_protect
@require_http_methods(["GET"])
def get_downline_list(request):
    """获取下线列表（分页）"""
    try:
        level = int(request.GET.get('level', 1))  # 1, 2, 或 3
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        
        user = request.user
        if not user.is_agent:
            return JsonResponse({
                'code': 403,
                'message': '非代理用户无法查看下线列表'
            })
        
        # 根据level选择查询
        if level == 1:
            downlines = User.objects.filter(referrer=user)
        elif level == 2:
            downlines = User.objects.filter(referrer__referrer=user)
        else:  # level == 3
            downlines = User.objects.filter(referrer__referrer__referrer=user)
        
        # 添加排序和筛选
        downlines = downlines.order_by('-created_at')
        
        paginator = Paginator(downlines, page_size)
        current_page = paginator.page(page)
        
        data = [{
            'user_id': user.user_id,
            'email': user.email,
            'is_agent': user.is_agent,
            'is_active_member': user.is_active_member,
            'created_at': user.created_at.isoformat(),
            'current_period_swmt': user.taskcompletion_set.filter(
                created_at__week=timezone.now().isocalendar()[1]
            ).aggregate(total=Sum('total_swmt'))['total'] or 0
        } for user in current_page]
        
        return JsonResponse({
            'code': 200,
            'message': 'success',
            'data': {
                'results': data,
                'total': paginator.count,
                'total_pages': paginator.num_pages,
                'current_page': page
            }
        })
    except Exception as e:
        logger.error(f"获取下线列表失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': str(e)
        })

@csrf_protect
@require_http_methods(["GET"])
def get_current_commission(request):
    """获取当前代理的佣金信息"""
    try:
        user = request.user
        if not user.is_agent:
            return JsonResponse({
                'code': 403,
                'message': '非代理用户无法查看佣金信息'
            })

        # 获取当前佣金记录
        current_commission = CommissionRecord.objects.filter(agent=user).order_by('-created_at').first()

        if not current_commission:
            return JsonResponse({
                'code': 404,
                'message': '没有找到佣金记录'
            })

        data = {
            'id': current_commission.id,
            'total_commission': float(current_commission.total_commission),
            'status': current_commission.settlement_period.status,
            'settlement_time': current_commission.created_at.isoformat()
        }

        return JsonResponse({
            'code': 200,
            'message': 'success',
            'data': data
        })
    except Exception as e:
        logger.error(f"获取当前佣金信息失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': str(e)
        })

@csrf_protect
@require_http_methods(["GET"])
def get_agent_levels(request):
    """获取所有代理等级"""
    try:
        levels = AgentLevel.objects.filter(is_active=True).order_by('level')
        data = [{
            'id': level.id,
            'name': level.name,
            'level': level.level,
            'commission_rate': float(level.commission_rate)
        } for level in levels]

        return JsonResponse({
            'code': 200,
            'message': 'success',
            'data': data
        })
    except Exception as e:
        logger.error(f"获取代理等级失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': str(e)
        })

@csrf_protect
@require_http_methods(["POST"])
@transaction.atomic
def upgrade_agent_level(request):
    """升级代理等级 (仅限管理员)"""
    try:
        if not request.user.is_staff:
            return JsonResponse({
                'code': 403,
                'message': '权限被拒绝。需要管理员访问。'
            }, status=403)

        user_id = request.POST.get('user_id')
        if not user_id:
            return JsonResponse({
                'code': 400,
                'message': '用户ID是必需的。'
            }, status=400)

        user = User.objects.get(id=user_id)

        # 直接将用户提升为代理
        user.is_agent = True
        user.agent_id = f"A{user.user_id[1:]}"  # 使用用户ID生成代理ID
        user.save()

        return JsonResponse({
            'code': 200,
            'message': '用户已成功提升为代理。',
            'data': {'user_id': user.id}
        })
    except User.DoesNotExist:
        return JsonResponse({
            'code': 404,
            'message': '用户未找到。'
        }, status=404)
    except Exception as e:
        logger.error(f"代理等级升级失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': '在升级代理等级时发生错误。'
        }, status=500)

@csrf_protect
@require_http_methods(["GET"])
def admin_settlements(request):
    """管理员查看结算记录"""
    try:
        if not request.user.is_staff:
            return JsonResponse({
                'code': 403,
                'message': '权限被拒绝。需要管理员访问。'
            }, status=403)

        # 获取所有结算记录
        settlements = SettlementPeriod.objects.all().order_by('-start_date')

        data = [{
            'id': settlement.id,
            'start_date': settlement.start_date.isoformat(),
            'end_date': settlement.end_date.isoformat(),
            'status': settlement.status,
            'total_commission': float(settlement.total_commission),
            'agent_count': settlement.agent_count,
            'created_at': settlement.created_at.isoformat()
        } for settlement in settlements]

        return JsonResponse({
            'code': 200,
            'message': 'success',
            'data': data
        })
    except Exception as e:
        logger.error(f"获取结算记录失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': str(e)
        })

@csrf_protect
@require_http_methods(["POST"])
@transaction.atomic
def approve_settlement(request, settlement_id):
    """管理员批准结算记录"""
    try:
        if not request.user.is_staff:
            return JsonResponse({
                'code': 403,
                'message': '权限被拒绝。需要管理员访问。'
            }, status=403)

        # 获取结算记录
        settlement = SettlementPeriod.objects.get(id=settlement_id)
        settlement.status = 'completed'
        settlement.save()

        return JsonResponse({
            'code': 200,
            'message': '结算记录已成功批准。',
            'data': {'settlement_id': settlement.id}
        })
    except SettlementPeriod.DoesNotExist:
        return JsonResponse({
            'code': 404,
            'message': '结算记录未找到。'
        }, status=404)
    except Exception as e:
        logger.error(f"批准结算记录失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': '在批准结算记录时发生错误。'
        }, status=500)

@csrf_protect
@require_http_methods(["POST"])
def reject_settlement(request, settlement_id):
    """管理员拒绝结算记录"""
    try:
        if not request.user.is_staff:
            return JsonResponse({
                'code': 403,
                'message': '权限被拒绝。需要管理员访问。'
            }, status=403)

        # 获取结算记录
        settlement = SettlementPeriod.objects.get(id=settlement_id)
        settlement.status = 'rejected'
        settlement.save()

        return JsonResponse({
            'code': 200,
            'message': '结算记录已成功拒绝。',
            'data': {'settlement_id': settlement.id}
        })
    except SettlementPeriod.DoesNotExist:
        return JsonResponse({
            'code': 404,
            'message': '结算记录未找到。'
        }, status=404)
    except Exception as e:
        logger.error(f"拒绝结算记录失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': '在拒绝结算记录时发生错误。'
        }, status=500)

@csrf_protect
@require_http_methods(["POST"])
@transaction.atomic
def stop_agent_settlement(request, user_id):
    """停止代理结算"""
    try:
        if not request.user.is_staff:
            return JsonResponse({
                'code': 403,
                'message': '权限被拒绝。需要管理员访问。'
            }, status=403)

        # 获取代理关系
        try:
            agent_relationship = AgentRelationship.objects.get(
                user_id=user_id,
                is_active=True
            )
        except AgentRelationship.DoesNotExist:
            return JsonResponse({
                'code': 404,
                'message': '代理关系不存在。'
            }, status=404)

        # 获取停止原因
        reason = request.POST.get('reason')
        if not reason:
            return JsonResponse({
                'code': 400,
                'message': '需要提供停止结算的原因。'
            }, status=400)

        # 停止结算
        agent_relationship.stop_settlement(reason)

        # 记录操作日志
        SystemLog.objects.create(
            type='agent',
            level='info',
            user_id=str(request.user.id),
            action='stop_agent_settlement',
            description=f"停止用户 ID:{user_id} 的代理结算，原因：{reason}",
            data={
                'reason': reason,
                'stopped_at': timezone.now().isoformat()
            }
        )

        return JsonResponse({
            'code': 200,
            'message': '代理结算已停止。',
            'data': {
                'user_id': user_id,
                'stopped_at': agent_relationship.settlement_stopped_at.isoformat()
            }
        })

    except Exception as e:
        logger.error(f"停止代理结算失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': '停止代理结算时发生错误。'
        }, status=500)

@csrf_protect
@require_http_methods(["POST"])
@transaction.atomic
def restore_agent_settlement(request, user_id):
    """恢复代理结算"""
    try:
        if not request.user.is_staff:
            return JsonResponse({
                'code': 403,
                'message': '权限被拒绝。需要管理员访问。'
            }, status=403)

        # 获取代理关系
        try:
            agent_relationship = AgentRelationship.objects.get(
                user_id=user_id,
                is_active=True
            )
        except AgentRelationship.DoesNotExist:
            return JsonResponse({
                'code': 404,
                'message': '代理关系不存在。'
            }, status=404)

        # 获取恢复原因
        reason = request.POST.get('reason')
        if not reason:
            return JsonResponse({
                'code': 400,
                'message': '需要提供恢复结算的原因。'
            }, status=400)

        # 恢复结算
        agent_relationship.restore_settlement(reason)

        # 记录操作日志
        SystemLog.objects.create(
            type='agent',
            level='info',
            user_id=str(request.user.id),
            action='restore_agent_settlement',
            description=f"恢复用户 ID:{user_id} 的代理结算，原因：{reason}",
            data={
                'reason': reason,
                'restored_at': timezone.now().isoformat()
            }
        )

        return JsonResponse({
            'code': 200,
            'message': '代理结算已恢复。',
            'data': {
                'user_id': user_id,
                'restored_at': agent_relationship.settlement_restored_at.isoformat()
            }
        })

    except Exception as e:
        logger.error(f"恢复代理结算失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': '恢复代理结算时发生错误。'
        }, status=500)

@csrf_protect
@require_http_methods(["GET"])
def get_agent_settlement_status(request, user_id):
    """获取代理结算状态"""
    try:
        if not request.user.is_staff:
            return JsonResponse({
                'code': 403,
                'message': '权限被拒绝。需要管理员访问。'
            }, status=403)

        # 获取代理关系
        try:
            agent_relationship = AgentRelationship.objects.get(
                user_id=user_id,
                is_active=True
            )
        except AgentRelationship.DoesNotExist:
            return JsonResponse({
                'code': 404,
                'message': '代理关系不存在。'
            }, status=404)

        return JsonResponse({
            'code': 200,
            'message': 'success',
            'data': {
                'user_id': user_id,
                'settlement_status': agent_relationship.settlement_status,
                'stopped_at': agent_relationship.settlement_stopped_at.isoformat() if agent_relationship.settlement_stopped_at else None,
                'restored_at': agent_relationship.settlement_restored_at.isoformat() if agent_relationship.settlement_restored_at else None,
                'reason': agent_relationship.settlement_status_reason
            }
        })

    except Exception as e:
        logger.error(f"获取代理结算状态失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': '获取代理结算状态时发生错误。'
        }, status=500)

@csrf_protect
@require_http_methods(["POST"])
@transaction.atomic
def create_agent_relationship(request):
    """创建代理关系(管理员接口)"""
    try:
        # 验证权限 - 需要管理员权限
        if not request.user.is_staff:
            return JsonResponse({
                'code': 403,
                'message': '权限不足，仅管理员可创建代理关系'
            })
        
        # 解析请求数据
        data = json.loads(request.body)
        user_id = data.get('user_id')
        referrer_id = data.get('referrer_id')
        
        # 验证必要参数
        if not user_id:
            return JsonResponse({
                'code': 400,
                'message': '缺少必要参数：user_id'
            })
        
        # 获取用户
        try:
            user = User.objects.get(user_id=user_id)
        except User.DoesNotExist:
            return JsonResponse({
                'code': 404,
                'message': f'用户不存在: {user_id}'
            })
        
        # 获取推荐人(可选)
        referrer = None
        if referrer_id:
            try:
                referrer = User.objects.get(user_id=referrer_id)
            except User.DoesNotExist:
                return JsonResponse({
                    'code': 404,
                    'message': f'推荐人不存在: {referrer_id}'
                })
        
        # 使用最低等级代理等级 (自动投档机制将在结算时调整等级)
        default_agent_level = AgentLevel.objects.filter(
            level=1,  # 假设1是最低等级
            is_active=True
        ).first()
        
        if not default_agent_level:
            return JsonResponse({
                'code': 500,
                'message': '系统中无可用的代理等级配置'
            })
        
        # 检查用户是否已有代理关系
        existing_relationship = AgentRelationship.objects.filter(user=user).first()
        if existing_relationship:
            return JsonResponse({
                'code': 400,
                'message': f'用户 {user.email} 已有代理关系记录'
            })
        
        # 计算直接下线数量
        direct_member_count = User.objects.filter(referrer=user).count()
        
        # 创建代理关系
        with transaction.atomic():
            # 更新用户为代理状态
            if not user.is_agent:
                user.is_agent = True
                if not user.agent_id:
                    user.agent_id = f"A{user.user_id[1:]}"
                user.save(update_fields=['is_agent', 'agent_id'])
            
            # 创建代理关系记录
            agent_relationship = AgentRelationship.objects.create(
                user=user,
                referrer=referrer or user.referrer,
                agent_level=default_agent_level,  # 初始赋值为最低等级，后续结算时会自动投档
                is_active=True,
                settlement_status='active',
                direct_member_count=direct_member_count
            )
            
            # 记录系统日志
            try:
                SystemLog.objects.create(
                    type='agent',
                    level='info',
                    user_id=str(request.user.id),
                    action='create_agent_relationship',
                    description=f"创建了用户 {user.email} 的代理关系，初始等级设为 {default_agent_level.name}（将在结算时自动投档）",
                    data={
                        'user_email': user.email,
                        'user_id': user.user_id,
                        'agent_id': user.agent_id,
                        'agent_level': default_agent_level.name,
                        'referrer': referrer.email if referrer else (user.referrer.email if user.referrer else None)
                    }
                )
            except Exception as log_error:
                # 记录系统日志失败，但不应影响主要功能
                logger.error(f"记录系统日志失败，但代理关系已创建成功: {str(log_error)}")
        
        return JsonResponse({
            'code': 200,
            'message': '创建代理关系成功',
            'data': {
                'id': agent_relationship.id,
                'user': user.email,
                'agent_id': user.agent_id,
                'agent_level': default_agent_level.name,
                'referrer': referrer.email if referrer else (user.referrer.email if user.referrer else None),
                'created_at': agent_relationship.created_at.isoformat(),
                'note': '初始等级设为最低等级，将在下一个结算周期根据团队规模自动投档'
            }
        })
    except Exception as e:
        logger.error(f"创建代理关系失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': f'系统错误: {str(e)}'
        })

@csrf_protect
@require_http_methods(["GET"])
def get_all_agents(request):
    """获取所有代理用户(管理员接口)"""
    try:
        # 验证权限 - 需要管理员权限
        if not request.user.is_staff:
            return JsonResponse({
                'code': 403,
                'message': '权限不足，仅管理员可查看所有代理'
            })
        
        # 获取请求参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 10))
        search = request.GET.get('search', '')
        is_active = request.GET.get('is_active')
        status = request.GET.get('status')
        
        # 构建查询
        query = AgentRelationship.objects.select_related('user', 'referrer', 'agent_level')
        
        # 应用过滤条件
        if search:
            query = query.filter(
                Q(user__email__icontains=search) | 
                Q(user__user_id__icontains=search) | 
                Q(user__agent_id__icontains=search)
            )
        
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            query = query.filter(is_active=is_active)
            
        if status:
            query = query.filter(settlement_status=status)
            
        # 统计总数
        total_count = query.count()
        
        # 排序和分页
        query = query.order_by('-created_at')
        paginator = Paginator(query, page_size)
        agents = paginator.get_page(page)
        
        # 构建响应数据
        results = []
        for agent in agents:
            results.append({
                'id': agent.id,
                'user': {
                    'id': agent.user.id,
                    'user_id': agent.user.user_id,
                    'email': agent.user.email,
                    'agent_id': agent.user.agent_id,
                },
                'referrer': {
                    'user_id': agent.referrer.user_id if agent.referrer else None,
                    'email': agent.referrer.email if agent.referrer else None,
                } if agent.referrer else None,
                'agent_level': {
                    'id': agent.agent_level.id,
                    'name': agent.agent_level.name,
                    'level': agent.agent_level.level,
                } if agent.agent_level else None,
                'is_active': agent.is_active,
                'settlement_status': agent.settlement_status,
                'direct_member_count': agent.direct_member_count,
                'total_commission': str(agent.total_commission),
                'created_at': agent.created_at.isoformat(),
            })
        
        return JsonResponse({
            'code': 200,
            'message': '获取代理列表成功',
            'data': {
                'total': total_count,
                'page': page,
                'page_size': page_size,
                'results': results
            }
        })
    except Exception as e:
        logger.error(f"获取代理列表失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': f'系统错误: {str(e)}'
        })

@csrf_protect
@require_http_methods(["POST"])
@transaction.atomic
def delete_agent_relationship(request, relationship_id):
    """删除代理关系(管理员接口)"""
    try:
        # 验证权限 - 需要管理员权限
        if not request.user.is_staff:
            return JsonResponse({
                'code': 403,
                'message': '权限不足，仅管理员可删除代理关系'
            })
        
        # 查找代理关系
        try:
            relationship = AgentRelationship.objects.get(id=relationship_id)
        except AgentRelationship.DoesNotExist:
            return JsonResponse({
                'code': 404,
                'message': f'代理关系不存在: ID {relationship_id}'
            })
        
        # 获取关联用户信息用于日志记录
        user_email = relationship.user.email
        user_id = relationship.user.user_id
        agent_id = relationship.user.agent_id
        
        # 检查是否可以删除
        # 如果有佣金记录，建议不要直接删除
        commission_count = CommissionRecord.objects.filter(agent=relationship.user).count()
        if commission_count > 0:
            # 建议改为停用而不是删除
            relationship.is_active = False
            relationship.settlement_status = 'terminated'
            relationship.settlement_status_reason = f'管理员操作：终止代理关系 (由{request.user.email}操作)'
            relationship.settlement_stopped_at = timezone.now()
            relationship.save(update_fields=[
                'is_active', 
                'settlement_status',
                'settlement_status_reason',
                'settlement_stopped_at'
            ])
            
            # 记录系统日志
            SystemLog.objects.create(
                type='agent',
                level='info',
                user_id=str(request.user.id),
                action='terminate_agent_relationship',
                description=f"终止了用户 {user_email} 的代理关系，因为该代理已有佣金记录，所以仅停用而非删除",
                data={
                    'user_email': user_email,
                    'user_id': relationship.user.user_id,
                    'agent_id': agent_id,
                    'commission_count': commission_count
                }
            )
            
            return JsonResponse({
                'code': 200,
                'message': '代理关系已停用',
                'data': {
                    'id': relationship.id,
                    'user': user_email,
                    'agent_id': agent_id,
                    'is_active': False,
                    'status': 'terminated'
                }
            })
        else:
            # 无佣金记录，可以安全删除
            relationship.delete()
            
            # 记录系统日志
            SystemLog.objects.create(
                type='agent',
                level='info',
                user_id=str(request.user.id),
                action='delete_agent_relationship',
                description=f"删除了用户 {user_email} 的代理关系",
                data={
                    'user_email': user_email,
                    'user_id': relationship.user.user_id,
                    'agent_id': agent_id
                }
            )
            
            return JsonResponse({
                'code': 200,
                'message': '代理关系已删除',
                'data': {
                    'user': user_email,
                    'agent_id': agent_id
                }
            })
    except Exception as e:
        logger.error(f"删除代理关系失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': f'系统错误: {str(e)}'
        })

@csrf_protect
@require_http_methods(["GET"])
def get_agent_relationship_detail(request, relationship_id):
    """获取代理关系详情(管理员接口)"""
    try:
        # 验证权限 - 需要管理员权限
        if not request.user.is_staff:
            return JsonResponse({
                'code': 403,
                'message': '权限不足，仅管理员可查看代理关系详情'
            })
        
        # 查找代理关系
        try:
            relationship = AgentRelationship.objects.select_related(
                'user', 'referrer', 'agent_level'
            ).get(id=relationship_id)
        except AgentRelationship.DoesNotExist:
            return JsonResponse({
                'code': 404,
                'message': f'代理关系不存在: ID {relationship_id}'
            })
        
        # 获取佣金历史
        commission_records = CommissionRecord.objects.filter(
            agent=relationship.user
        ).select_related('settlement_period').order_by('-created_at')[:5]  # 获取最近5条
        
        # 获取团队规模
        level1_count = User.objects.filter(referrer=relationship.user).count()
        level2_count = User.objects.filter(referrer__referrer=relationship.user).count()
        level3_count = User.objects.filter(referrer__referrer__referrer=relationship.user).count()
        
        # 获取结算状态历史
        settlement_logs = SystemLog.objects.filter(
            type='agent',
            action__in=['stop_agent_settlement', 'restore_agent_settlement'],
            data__contains={'user_id': relationship.user.user_id}
        ).order_by('-created_at')[:10]  # 获取最近10条
        
        # 构建响应数据
        data = {
            'id': relationship.id,
            'user': {
                'id': relationship.user.id,
                'user_id': relationship.user.user_id,
                'email': relationship.user.email,
                'agent_id': relationship.user.agent_id,
                'created_at': relationship.user.created_at.isoformat(),
                'is_active': relationship.user.is_active
            },
            'referrer': {
                'id': relationship.referrer.id,
                'user_id': relationship.referrer.user_id,
                'email': relationship.referrer.email
            } if relationship.referrer else None,
            'agent_level': {
                'id': relationship.agent_level.id,
                'name': relationship.agent_level.name,
                'level': relationship.agent_level.level,
                'commission_rates': {
                    'level1': float(relationship.agent_level.level1_commission_rate),
                    'level2': float(relationship.agent_level.level2_commission_rate),
                    'level3': float(relationship.agent_level.level3_commission_rate)
                }
            } if relationship.agent_level else None,
            'team_size': {
                'level1': level1_count,
                'level2': level2_count,
                'level3': level3_count,
                'total': level1_count + level2_count + level3_count
            },
            'statistics': {
                'direct_member_count': relationship.direct_member_count,
                'total_commission': str(relationship.total_commission)
            },
            'settlement_status': {
                'status': relationship.settlement_status,
                'reason': relationship.settlement_status_reason,
                'stopped_at': relationship.settlement_stopped_at.isoformat() if relationship.settlement_stopped_at else None,
                'restored_at': relationship.settlement_restored_at.isoformat() if relationship.settlement_restored_at else None,
                'logs': [{
                    'action': log.action,
                    'created_at': log.created_at.isoformat(),
                    'level': log.level,
                    'description': log.description,
                    'data': log.data
                } for log in settlement_logs]
            },
            'recent_commissions': [{
                'id': record.id,
                'period': {
                    'start_date': record.settlement_period.start_date.isoformat(),
                    'end_date': record.settlement_period.end_date.isoformat(),
                    'status': record.settlement_period.status
                },
                'total_commission': str(record.total_commission),
                'created_at': record.created_at.isoformat()
            } for record in commission_records],
            'is_active': relationship.is_active,
            'created_at': relationship.created_at.isoformat(),
            'updated_at': relationship.updated_at.isoformat() if relationship.updated_at else None
        }
        
        return JsonResponse({
            'code': 200,
            'message': '获取代理关系详情成功',
            'data': data
        })
    except Exception as e:
        logger.error(f"获取代理关系详情失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'code': 500,
            'message': f'系统错误: {str(e)}'
        })

# 添加修复代理ID的功能
@csrf_protect
@require_http_methods(["POST"])
@transaction.atomic
def fix_agent_ids(request):
    """修复已创建代理的代理ID
    
    检查所有已标记为代理但没有代理ID的用户，为其设置代理ID
    """
    if not request.user.is_superuser and not request.user.is_staff:
        return JsonResponse({
            'status': 'error',
            'code': 403,
            'message': '权限不足',
            'data': None
        }, status=403)
    
    # 查找所有已标记为代理但没有代理ID的用户
    agent_users = User.objects.filter(is_agent=True, agent_id__isnull=True) | User.objects.filter(is_agent=True, agent_id='')
    
    fixed_count = 0
    result_details = []
    
    for user in agent_users:
        old_agent_id = user.agent_id
        # 设置代理ID
        user.agent_id = f"A{user.user_id[1:]}"
        user.save(update_fields=['agent_id'])
        
        fixed_count += 1
        result_details.append({
            'user_id': user.user_id,
            'email': user.email,
            'old_agent_id': old_agent_id,
            'new_agent_id': user.agent_id
        })
        
        # 记录日志
        SystemLog.objects.create(
            type='agent',
            level='info',
            user_id=str(request.user.id),
            action='fix_agent_id',
            description=f"修复用户 {user.email} 的代理ID：从 '{old_agent_id}' 更新为 '{user.agent_id}'",
            data={
                'user_id': user.user_id,
                'email': user.email,
                'old_agent_id': old_agent_id,
                'new_agent_id': user.agent_id
            }
        )
    
    # 查找所有有代理关系但未标记为代理的用户
    relationships = AgentRelationship.objects.filter(
        user__is_agent=False,
        is_active=True
    ).select_related('user')
    
    for relationship in relationships:
        user = relationship.user
        old_is_agent = user.is_agent
        old_agent_id = user.agent_id
        
        # 更新用户为代理
        user.is_agent = True
        if not user.agent_id:
            user.agent_id = f"A{user.user_id[1:]}"
        user.save(update_fields=['is_agent', 'agent_id'])
        
        fixed_count += 1
        result_details.append({
            'user_id': user.user_id,
            'email': user.email,
            'old_is_agent': old_is_agent,
            'old_agent_id': old_agent_id,
            'new_agent_id': user.agent_id
        })
        
        # 记录日志
        SystemLog.objects.create(
            type='agent',
            level='info',
            user_id=str(request.user.id),
            action='fix_agent_status',
            description=f"修复用户 {user.email} 的代理状态：设置is_agent=True，代理ID：'{user.agent_id}'",
            data={
                'user_id': user.user_id,
                'email': user.email,
                'old_is_agent': old_is_agent,
                'old_agent_id': old_agent_id,
                'new_agent_id': user.agent_id
            }
        )
    
    return JsonResponse({
        'status': 'success',
        'code': 200,
        'message': f'成功修复 {fixed_count} 个代理用户',
        'data': {
            'fixed_count': fixed_count,
            'details': result_details
        }
    })

class SettlementWarningViewSet(viewsets.ReadOnlyModelViewSet):
    """结算预警ViewSet"""
    serializer_class = SettlementWarningSerializer
    permission_classes = [permissions.IsAdminUser]  # 只有管理员可以查看
    
    def get_queryset(self):
        """获取结算预警列表，支持多种过滤条件"""
        queryset = SettlementWarning.objects.all().order_by('-created_at')
        
        # 过滤条件
        status = self.request.query_params.get('status')
        level = self.request.query_params.get('level')
        warning_type = self.request.query_params.get('type')
        agent_id = self.request.query_params.get('agent_id')
        period_id = self.request.query_params.get('period_id')
        
        if status:
            queryset = queryset.filter(status=status)
        if level:
            queryset = queryset.filter(level=level)
        if warning_type:
            queryset = queryset.filter(type=warning_type)
        if agent_id:
            queryset = queryset.filter(agent_id=agent_id)
        if period_id:
            queryset = queryset.filter(period_id=period_id)
        
        return queryset
    
    @action(detail=True, methods=['post'])
    def resolve(self, request, pk=None):
        """将预警标记为已解决"""
        warning = self.get_object()
        warning.resolve(operator=request.user.username)
        return Response({'status': 'success', 'message': '预警已标记为已解决'})
    
    @action(detail=True, methods=['post'])
    def ignore(self, request, pk=None):
        """将预警标记为已忽略"""
        warning = self.get_object()
        warning.ignore(operator=request.user.username)
        return Response({'status': 'success', 'message': '预警已标记为已忽略'})

class SettlementWarningListView(UserPassesTestMixin, ListView):
    """结算预警管理页面"""
    model = SettlementWarning
    template_name = 'agents/settlement_warnings.html'
    context_object_name = 'warnings'
    paginate_by = 20
    
    def test_func(self):
        return self.request.user.is_staff
    
    def get_queryset(self):
        queryset = SettlementWarning.objects.all().order_by('-created_at')
        
        # 过滤条件
        status = self.request.GET.get('status')
        level = self.request.GET.get('level')
        warning_type = self.request.GET.get('type')
        
        if status:
            queryset = queryset.filter(status=status)
        if level:
            queryset = queryset.filter(level=level)
        if warning_type:
            queryset = queryset.filter(warning_type=warning_type)
            
        return queryset

def resolve_warning(request, warning_id):
    """标记预警为已解决"""
    if not request.user.is_staff:
        messages.error(request, "您没有权限执行此操作")
        return redirect('admin:index')
        
    warning = get_object_or_404(SettlementWarning, id=warning_id)
    warning.resolve(handler=request.user)
    messages.success(request, f"预警 #{warning_id} 已标记为已解决")
    return redirect('agents:warning_list')
    
def ignore_warning(request, warning_id):
    """标记预警为已忽略"""
    if not request.user.is_staff:
        messages.error(request, "您没有权限执行此操作")
        return redirect('admin:index')
        
    warning = get_object_or_404(SettlementWarning, id=warning_id)
    warning.ignore(handler=request.user)
    messages.success(request, f"预警 #{warning_id} 已标记为已忽略")
    return redirect('agents:warning_list')

def warning_list_redirect(request):
    """将旧的预警列表URL重定向到SimpleUI中的预警管理页面"""
    return redirect('/admin/agents/settlementwarning/')