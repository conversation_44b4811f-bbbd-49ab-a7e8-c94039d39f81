from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from rangefilter.filters import DateRange<PERSON>ilter
from django.template.response import TemplateResponse
from django.urls import path
from django.shortcuts import redirect
from .models import AgentLevel, AgentRelationship, SettlementPeriod, CommissionRecord, SettlementWarning
from users.models import User
from stats.models import SystemLog
from django.db import transaction
from django.contrib import messages
from django.utils import timezone
import json
import logging
from django.db.models import Sum, Count
from tasks.models import UserTask
from django import forms
from .utils import format_decimal
from django.utils.html import format_html

logger = logging.getLogger(__name__)

# 通用格式化方法
def formatted_commission(value, field_name):
    """通用佣金格式化方法
    
    Args:
        value: 要格式化的对象
        field_name: 字段名
        
    Returns:
        str: 格式化后的值，如"123.45"
    """
    field_value = getattr(value, field_name, None)
    return format_decimal(field_value)

@admin.register(AgentLevel)
class AgentLevelAdmin(admin.ModelAdmin):
    list_display = ('name', 'level', 'required_direct_members', 'min_swmt_output', 'max_swmt_output',
                   'level1_commission_rate', 'level2_commission_rate', 'level3_commission_rate',
                   'is_active')
    list_filter = ('is_active',)
    search_fields = ('name',)
    ordering = ('level',)
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('基本信息', {
            'fields': ['name', 'level', 'is_active']
        }),
        ('要求条件', {
            'fields': ['required_direct_members', 'min_swmt_output', 'max_swmt_output']
        }),
        ('佣金比例', {
            'fields': ['level1_commission_rate', 'level2_commission_rate', 'level3_commission_rate']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at']
        })
    )

@admin.register(AgentRelationship)
class AgentRelationshipAdmin(admin.ModelAdmin):
    list_display = ('user', 'referrer', 'agent_level', 'is_active', 'settlement_status', 'direct_member_count', 'formatted_total_commission', 'created_at')
    list_filter = ('is_active', 'settlement_status', 'agent_level')
    search_fields = ('user__email', 'user__user_id', 'referrer__email')
    raw_id_fields = ('user', 'referrer')
    readonly_fields = ('user', 'referrer', 'agent_level', 'settlement_stopped_at', 'settlement_restored_at', 'created_at', 'updated_at', 'direct_member_count', 'total_commission')
    change_form_template = 'admin/agents/agentrelationship/change_form.html'
    
    # 禁用默认的添加按钮
    def has_add_permission(self, request):
        return False
        
    # 添加代理关系一致性检查操作
    @admin.action(description="检查代理关系一致性")
    def check_agent_relationship_consistency(self, request, queryset):
        inconsistent_count = 0
        for relationship in queryset:
            # 检查User表中的referrer和AgentRelationship表中的referrer是否一致
            if relationship.referrer != relationship.user.referrer:
                inconsistent_count += 1
                self.message_user(
                    request,
                    f"发现不一致: 用户 {relationship.user.email} 的推荐人不一致。" 
                    f"User表: {relationship.user.referrer.email if relationship.user.referrer else 'None'}, "
                    f"代理关系表: {relationship.referrer.email if relationship.referrer else 'None'}",
                    messages.WARNING
                )
        
        if inconsistent_count == 0:
            self.message_user(request, "所有选中的代理关系均一致", messages.SUCCESS)
        else:
            self.message_user(request, f"发现 {inconsistent_count} 条不一致记录", messages.ERROR)
    
    # 添加修复代理关系不一致的操作
    @admin.action(description="修复代理关系不一致")
    def fix_agent_relationship_consistency(self, request, queryset):
        fixed_count = 0
        for relationship in queryset:
            if relationship.referrer != relationship.user.referrer:
                # 以User表为准
                relationship.referrer = relationship.user.referrer
                relationship.save(update_fields=['referrer'])
                fixed_count += 1
        
        if fixed_count > 0:
            self.message_user(request, f"成功修复 {fixed_count} 条不一致记录", messages.SUCCESS)
        else:
            self.message_user(request, "所有选中记录均已一致，无需修复", messages.SUCCESS)
    
    # 添加操作到操作列表
    actions = [check_agent_relationship_consistency, fix_agent_relationship_consistency]

    fieldsets = (
        ('代理信息', {
            'fields': ('user', 'referrer', 'agent_level'),
            'classes': ('collapse',),
        }),
        ('结算状态', {
            'fields': ('is_active', 'settlement_status', 'settlement_status_reason', 
                      'settlement_stopped_at', 'settlement_restored_at'),
            'classes': ('collapse',),
        }),
        ('统计数据', {
            'fields': ('direct_member_count', 'total_commission'),
            'classes': ('collapse',),
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )
    
    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('add-agent-relationship/', self.admin_site.admin_view(self.add_agent_relationship_view), name='add_agent_relationship'),
        ]
        return custom_urls + urls
    
    def changelist_view(self, request, extra_context=None):
        """添加自定义按钮到列表页"""
        extra_context = extra_context or {}
        extra_context['custom_button'] = {
            'url': 'add-agent-relationship/',
            'label': '增加代理关系'
        }
        return super().changelist_view(request, extra_context=extra_context)

    def add_agent_relationship_view(self, request):
        """自定义添加代理关系视图"""
        if request.method == 'POST':
            user_id = request.POST.get('user_id')
            is_active = request.POST.get('is_active') == 'on'
            
            try:
                # 检查用户是否存在
                try:
                    user = User.objects.get(user_id=user_id)
                except User.DoesNotExist:
                    messages.error(request, f'用户不存在: {user_id}')
                    return redirect('admin:agents_agentrelationship_changelist')
                
                # 使用AgentService创建代理关系
                from agents.services import AgentService
                success, message, relationship = AgentService.create_agent_relationship(
                    user=user,
                    is_active=is_active,
                    admin_user=request.user,
                    reason="管理员手动创建"
                )
                
                if success:
                    messages.success(request, f'成功为用户 {user.email} 创建代理关系')
                    return redirect('admin:agents_agentrelationship_change', relationship.id)
                else:
                    messages.error(request, message)
                    return redirect('admin:agents_agentrelationship_changelist')
                
            except Exception as e:
                messages.error(request, f'创建代理关系失败: {str(e)}')
                return redirect('admin:agents_agentrelationship_changelist')
        
        # GET请求显示表单
        context = {
            'title': '增加代理关系',
            'opts': self.model._meta,
            'app_label': self.model._meta.app_label,
        }
        return TemplateResponse(request, 'admin/create_agent_form.html', context)

    def get_form(self, request, obj=None, **kwargs):
        """返回定制的表单类，不再使用动态方法替换"""
        # 创建一个自定义ModelForm类
        class AgentRelationshipAdminForm(forms.ModelForm):
            class Meta:
                model = AgentRelationship
                fields = '__all__'
                
            def clean(self):
                """自定义验证，确保状态变更时必须提供原因"""
                cleaned_data = super().clean()
                
                # 只有在编辑现有对象时才检查
                if obj:
                    # 获取表单中的值
                    new_status = cleaned_data.get('settlement_status')
                    new_active = cleaned_data.get('is_active')
                    reason = cleaned_data.get('settlement_status_reason', '')
                    
                    # 对比原始值
                    old_status = obj.settlement_status
                    old_active = obj.is_active
                    
                    # 如果有状态变更但没有填写原因，添加错误
                    if ((old_status != new_status or old_active != new_active) and 
                        not reason):
                        self.add_error('settlement_status_reason', 
                                      '修改状态时必须填写变更原因')
                
                return cleaned_data
                
        # 设置自定义表单类
        kwargs['form'] = AgentRelationshipAdminForm
        return super().get_form(request, obj, **kwargs)
    
    def _calculate_current_period_info(self):
        """计算当前结算周期信息"""
        # 获取当前时间
        now = timezone.now()
        # 结算周期是每周一00:00，计算下一个结算日期
        days_until_monday = (7 - now.weekday()) % 7
        if days_until_monday == 0 and now.hour == 0 and now.minute == 0:
            # 如果现在刚好是周一00:00，结算日期就是现在
            next_settlement_date = now
        else:
            # 否则计算下一个周一00:00
            next_settlement_date = (now + timezone.timedelta(days=days_until_monday)).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
        
        return next_settlement_date

    def change_view(self, request, object_id, form_url='', extra_context=None):
        """自定义详情页视图，添加额外的上下文数据"""
        extra_context = extra_context or {}
        
        try:
            # 获取当前代理关系
            obj = self.get_object(request, object_id)
            if obj:
                # 获取直接下线用户
                direct_members = User.objects.filter(referrer=obj.user).order_by('-created_at')[:10]
                
                # 获取这些用户的SWMT产出数据
                for member in direct_members:
                    # 计算最近30天的任务SWMT
                    member.total_swmt = UserTask.objects.filter(
                        user=member,
                        status='completed',
                        completed_at__gte=timezone.now() - timezone.timedelta(days=30)
                    ).aggregate(
                        total=Sum('base_swmt_reward') + 
                               Sum('vip_bonus_swmt') + 
                               Sum('additional_bonus_swmt')
                    )['total'] or 0
                
                extra_context['direct_members'] = direct_members
                
                # 获取最近的佣金记录，包括最近一次有效结算（佣金>0）
                recent_commission_records = CommissionRecord.objects.filter(
                    agent=obj.user
                ).order_by('-created_at')[:10]
                extra_context['recent_commission_records'] = recent_commission_records
                
                # 寻找最近一次有效结算（佣金>0）
                last_valid_commission = CommissionRecord.objects.filter(
                    agent=obj.user,
                    total_commission__gt=0
                ).order_by('-created_at').first()
                
                if last_valid_commission:
                    extra_context['last_valid_commission'] = last_valid_commission
                    extra_context['last_commission_amount'] = last_valid_commission.total_commission
                    extra_context['last_settlement_date'] = last_valid_commission.created_at
                
                # 获取当前佣金周期信息
                next_settlement_date = self._calculate_current_period_info()
                extra_context['next_settlement_date'] = next_settlement_date
                
                # 计算当前周期的SWMT产出（从上次结算到现在）
                from django.db.models import Sum
                from tasks.models import UserTask
                from django.utils import timezone
                
                # 确定当前周期开始时间（上一个周一00:00）
                current_week_start = (timezone.now() - timezone.timedelta(days=timezone.now().weekday())).replace(
                    hour=0, minute=0, second=0, microsecond=0
                )
                
                # 计算本周期一级下线已产生的SWMT
                level1_users = User.objects.filter(referrer=obj.user)
                current_period_swmt = 0
                
                if level1_users.exists():
                    current_period_swmt = UserTask.objects.filter(
                        user__in=level1_users,
                        status='completed',
                        completed_at__gte=current_week_start
                    ).aggregate(
                        total_swmt=Sum(
                            models.F('base_swmt_reward') + 
                            models.F('vip_bonus_swmt') + 
                            models.F('additional_bonus_swmt')
                        )
                    )['total_swmt'] or 0
                
                extra_context['current_period_swmt'] = current_period_swmt
                
                # 预测投档等级
                # 获取所有代理等级，根据直接下线人数和SWMT预测投档等级
                from .models import AgentLevel
                
                direct_downlines_count = User.objects.filter(referrer=obj.user).count()
                
                # 首先根据下线人数筛选可能的等级
                possible_levels = AgentLevel.objects.filter(
                    is_active=True,
                    required_direct_members__lte=direct_downlines_count
                ).order_by('-level')
                
                predicted_level = None
                
                if possible_levels.exists():
                    # 然后根据SWMT产出确定最终等级
                    for level in possible_levels:
                        if (level.min_swmt_output <= current_period_swmt and 
                            (level.max_swmt_output >= current_period_swmt or level.max_swmt_output == 0)):
                            predicted_level = level
                            break
                    
                    # 如果没有找到匹配的等级，使用可能等级中最低的
                    if not predicted_level:
                        predicted_level = possible_levels.last()
                
                extra_context['predicted_level'] = predicted_level
                
                # 获取团队数据（如果模型中没有直接提供）
                obj.user.level1_members_count = User.objects.filter(referrer=obj.user).count()
                obj.user.level2_members_count = User.objects.filter(
                    referrer__referrer=obj.user,
                    referrer__isnull=False
                ).count()
                obj.user.level3_members_count = User.objects.filter(
                    referrer__referrer__referrer=obj.user,
                    referrer__referrer__isnull=False,
                    referrer__isnull=False
                ).count()
                
                # 计算SWMT产出数据
                try:
                    # 尝试导入utils中的计算函数
                    from .utils import calculate_level_swmt
                    from django.utils import timezone
                    import datetime
                    
                    # 计算最近30天的SWMT产出
                    end_date = timezone.now()
                    start_date = end_date - datetime.timedelta(days=30)
                    
                    # 计算不同级别的SWMT产出
                    obj.user.level1_swmt = calculate_level_swmt(obj.user, 1, start_date, end_date) or 0
                    obj.user.level2_swmt = calculate_level_swmt(obj.user, 2, start_date, end_date) or 0
                    obj.user.level3_swmt = calculate_level_swmt(obj.user, 3, start_date, end_date) or 0
                    
                    logger.info(f"计算代理 {obj.user.email} 的SWMT产出成功: L1={obj.user.level1_swmt}, L2={obj.user.level2_swmt}, L3={obj.user.level3_swmt}")
                except Exception as e:
                    logger.error(f"计算SWMT产出时出错: {str(e)}")
                    obj.user.level1_swmt = 0
                    obj.user.level2_swmt = 0 
                    obj.user.level3_swmt = 0
                
        except Exception as e:
            logger.error(f"获取代理详情页数据出错: {str(e)}")
            
        return super().change_view(
            request, object_id, form_url, extra_context=extra_context
        )
    
    def save_model(self, request, obj, form, change):
        """重写保存方法，记录状态变更并处理激活状态变更"""
        if change:  # 如果是修改操作
            # 获取原始对象
            try:
                original_obj = self.model.objects.get(pk=obj.pk)
                
                # 检查是否修改了结算状态
                if original_obj.settlement_status != obj.settlement_status:
                    # 根据新状态设置停止/恢复时间
                    if obj.settlement_status == 'stopped':
                        obj.settlement_stopped_at = timezone.now()
                    elif obj.settlement_status == 'active':
                        obj.settlement_restored_at = timezone.now()
                    
                    # 记录日志
                    SystemLog.objects.create(
                        type='agent',
                        level='warning',
                        user_id=str(request.user.id),
                        action='change_settlement_status',
                        description=f"管理员修改了代理 {obj.user.email} 的结算状态: "
                                    f"从 {original_obj.settlement_status} 改为 {obj.settlement_status}，"
                                    f"原因: {obj.settlement_status_reason}",
                        data={
                            'user_email': obj.user.email,
                            'user_id': obj.user.user_id,
                            'old_status': original_obj.settlement_status,
                            'new_status': obj.settlement_status,
                            'reason': obj.settlement_status_reason
                        }
                    )
                
                # 检查是否修改了激活状态
                if original_obj.is_active != obj.is_active:
                    with transaction.atomic():
                        # 如果设为不激活，同时要修改用户的代理状态
                        if not obj.is_active:
                            obj.user.is_agent = False
                            obj.user.save(update_fields=['is_agent'])
                        else:
                            # 如果恢复激活，确保用户是代理
                            obj.user.is_agent = True
                            obj.user.save(update_fields=['is_agent'])
                        
                        # 记录日志
                        SystemLog.objects.create(
                            type='agent',
                            level='warning',
                            user_id=str(request.user.id),
                            action='change_agent_active_status',
                            description=f"管理员修改了代理 {obj.user.email} 的激活状态: "
                                        f"从 {original_obj.is_active} 改为 {obj.is_active}，"
                                        f"原因: {obj.settlement_status_reason}",
                            data={
                                'user_email': obj.user.email,
                                'user_id': obj.user.user_id,
                                'old_active': original_obj.is_active,
                                'new_active': obj.is_active,
                                'reason': obj.settlement_status_reason
                            }
                        )
            
            except self.model.DoesNotExist:
                pass
        
        super().save_model(request, obj, form, change)

    # 格式化佣金显示，使用通用方法
    def formatted_total_commission(self, obj):
        return format_decimal(obj.total_commission)
    formatted_total_commission.short_description = "累计佣金"
    formatted_total_commission.admin_order_field = 'total_commission'

@admin.register(SettlementPeriod)
class SettlementPeriodAdmin(admin.ModelAdmin):
    list_display = ('start_date', 'end_date', 'status', 'formatted_total_commission', 'agent_count',
                   'created_at')
    list_filter = ('status', ('start_date', DateRangeFilter))
    search_fields = ('id',)
    ordering = ('-start_date',)
    readonly_fields = ('created_at', 'updated_at', 'start_date', 'end_date', 'formatted_total_commission', 'agent_count')
    
    # 禁用添加权限
    def has_add_permission(self, request):
        return False
        
    # 禁用删除权限
    def has_delete_permission(self, request, obj=None):
        return False
    
    # 格式化佣金显示，使用通用方法
    def formatted_total_commission(self, obj):
        return format_decimal(obj.total_commission)
    formatted_total_commission.short_description = "总佣金"  
    formatted_total_commission.admin_order_field = 'total_commission'
    
    fieldsets = (
        ('基本信息', {
            'fields': ['start_date', 'end_date', 'status']
        }),
        ('结算信息', {
            'fields': ['formatted_total_commission', 'agent_count']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at']
        })
    )

@admin.register(CommissionRecord)
class CommissionRecordAdmin(admin.ModelAdmin):
    list_display = ('agent', 'settlement_period', 'agent_level', 'level1_members',
                   'level2_members', 'level3_members', 'formatted_total_commission', 'created_at')
    list_filter = ('agent_level', ('created_at', DateRangeFilter))
    search_fields = ('agent__email',)
    raw_id_fields = ('agent', 'settlement_period', 'agent_level')
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'agent', 'settlement_period', 'agent_level', 
                      'level1_members', 'level2_members', 'level3_members',
                      'level1_swmt', 'level2_swmt', 'level3_swmt',
                      'formatted_level1_commission', 'formatted_level2_commission', 'formatted_level3_commission',
                      'formatted_total_commission')
    
    # 禁用添加权限
    def has_add_permission(self, request):
        return False
        
    # 禁用删除权限
    def has_delete_permission(self, request, obj=None):
        return False
        
    # 禁用修改权限
    def has_change_permission(self, request, obj=None):
        # 允许查看但不允许修改
        return True

    # 格式化佣金显示，使用通用方法
    def formatted_total_commission(self, obj):
        return format_decimal(obj.total_commission)
    formatted_total_commission.short_description = "总佣金"  
    formatted_total_commission.admin_order_field = 'total_commission'
    
    def formatted_level1_commission(self, obj):
        return format_decimal(obj.level1_commission)
    formatted_level1_commission.short_description = "一级佣金"
    
    def formatted_level2_commission(self, obj):
        return format_decimal(obj.level2_commission)
    formatted_level2_commission.short_description = "二级佣金"
    
    def formatted_level3_commission(self, obj):
        return format_decimal(obj.level3_commission)
    formatted_level3_commission.short_description = "三级佣金"

    fieldsets = (
        (None, {
            'fields': ('agent', 'settlement_period', 'agent_level')
        }),
        (_('团队数据'), {
            'fields': (('level1_members', 'level2_members', 'level3_members'),
                      ('level1_swmt', 'level2_swmt', 'level3_swmt'))
        }),
        (_('佣金数据'), {
            'fields': (('formatted_level1_commission', 'formatted_level2_commission', 'formatted_level3_commission'),
                      'formatted_total_commission')
        }),
        (_('时间信息'), {
            'fields': ('created_at',)
        })
    )

@admin.register(SettlementWarning)
class SettlementWarningAdmin(admin.ModelAdmin):
    """结算预警管理"""
    list_display = ('id', 'warning_type', 'level', 'status', 'formatted_title', 
                    'agent_email', 'settlement_period', 'is_notified', 
                    'handler', 'created_at')
    list_filter = ('warning_type', 'level', 'status', 'is_notified', 
                  ('created_at', DateRangeFilter))
    search_fields = ('title', 'description', 'agent__email', 'handling_notes')
    readonly_fields = ('warning_type', 'level', 'created_at', 'updated_at', 
                      'agent', 'settlement_period', 'title', 'description', 
                      'formatted_data', 'is_notified', 
                      'notification_sent_at')
    raw_id_fields = ('handler',)
    fieldsets = (
        (_('预警信息'), {
            'fields': (('warning_type', 'level'), 'title', 'description', 
                      ('agent', 'settlement_period'), 
                      ('is_notified'), 'notification_sent_at', 
                      'formatted_data')
        }),
        (_('处理信息'), {
            'fields': (('status', 'handler'), 'handling_notes', 'resolved_at')
        }),
        (_('时间信息'), {
            'fields': (('created_at', 'updated_at'),)
        })
    )
    actions = ['mark_as_resolved', 'mark_as_ignored', 'resend_notification']
    
    def agent_email(self, obj):
        """显示代理邮箱"""
        return obj.agent.email if obj.agent else _('系统级预警')
    agent_email.short_description = _('代理')
    
    def formatted_title(self, obj):
        """格式化标题，添加颜色标识"""
        color_map = {
            'critical': 'red',
            'warning': 'orange',
            'info': 'blue'
        }
        color = color_map.get(obj.level, 'black')
        return format_html('<span style="color: {};">{}</span>', color, obj.title)
    formatted_title.short_description = _('预警标题')
    formatted_title.admin_order_field = 'title'
    
    def formatted_data(self, obj):
        """格式化展示JSON数据"""
        if not obj.data:
            return '-'
        
        # 将数据转换为可读形式
        import json
        from django.utils.safestring import mark_safe
        
        formatted = json.dumps(obj.data, indent=4, ensure_ascii=False)
        return mark_safe(f'<pre>{formatted}</pre>')
    formatted_data.short_description = _('相关数据')
    
    def mark_as_resolved(self, request, queryset):
        """将选中的预警标记为已解决"""
        count = 0
        for warning in queryset.filter(status__in=['pending', 'processing']):
            warning.resolve(request.user, _('由管理员批量操作解决'))
            count += 1
        
        self.message_user(request, _('成功将 %d 个预警标记为已解决') % count, messages.SUCCESS)
    mark_as_resolved.short_description = _('标记为已解决')
    
    def mark_as_ignored(self, request, queryset):
        """将选中的预警标记为已忽略"""
        count = 0
        for warning in queryset.filter(status__in=['pending', 'processing']):
            warning.ignore(request.user, _('由管理员批量操作忽略'))
            count += 1
        
        self.message_user(request, _('成功将 %d 个预警标记为已忽略') % count, messages.SUCCESS)
    mark_as_ignored.short_description = _('标记为已忽略')
    
    def resend_notification(self, request, queryset):
        """重新发送通知"""
        from agents.services.warning_service import SettlementWarningService
        warning_service = SettlementWarningService()
        
        count = 0
        for warning in queryset:
            # 先将状态重置为未通知
            warning.is_notified = False
            warning.save(update_fields=['is_notified'])
            
            # 重新发送通知
            if warning_service.send_warning_notification(warning):
                count += 1
        
        self.message_user(request, _('成功重新发送 %d 个预警通知') % count, messages.SUCCESS)
    resend_notification.short_description = _('重新发送通知')
    
    def save_model(self, request, obj, form, change):
        """保存模型，自动处理状态变更"""
        if change and 'status' in form.changed_data:
            # 如果状态从未处理/处理中变为已解决
            if obj.status == 'resolved' and form.initial.get('status') in ['pending', 'processing']:
                obj.resolve(request.user, obj.handling_notes or _('管理员标记为已解决'))
            # 如果状态从未处理/处理中变为已忽略
            elif obj.status == 'ignored' and form.initial.get('status') in ['pending', 'processing']:
                obj.ignore(request.user, obj.handling_notes or _('管理员标记为已忽略'))
            else:
                super().save_model(request, obj, form, change)
        else:
            super().save_model(request, obj, form, change)
            
    def has_add_permission(self, request):
        """禁止手动添加预警"""
        return False
