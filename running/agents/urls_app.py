from django.urls import path
from . import app_views

app_name = 'app_agents'

urlpatterns = [
    # 团队数据概览
    path('team-stats/', app_views.TeamStatsView.as_view(), name='agent-team-stats'),
    
    # 团队结构树形图
    path('team-tree/', app_views.TeamTreeView.as_view(), name='agent-team-tree'),
    
    # 团队成员列表（分页）
    path('team-list/', app_views.TeamListView.as_view(), name='agent-team-list'),
    
    # 佣金历史记录
    path('commission-history/', app_views.CommissionHistoryView.as_view(), name='agent-commission-history'),
    
    # 当前结算周期佣金预估
    path('current-commission/', app_views.CurrentCommissionView.as_view(), name='agent-current-commission'),
    
    # 代理等级列表
    path('levels/', app_views.AgentLevelListView.as_view(), name='agent-levels'),
    
    # 邀请码和分享信息
    path('invite-code/', app_views.InviteCodeView.as_view(), name='agent-invite-code'),
] 