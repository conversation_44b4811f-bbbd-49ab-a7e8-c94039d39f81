from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'agents'

router = DefaultRouter()

urlpatterns = [
    # 代理相关的URL配置将在这里添加
    # 团队统计相关
    path('team/stats/', views.get_team_stats, name='team_stats'),
    path('team/tree/', views.get_downline_tree, name='downline_tree'),
    path('team/list/', views.get_downline_list, name='downline_list'),
    
    # 佣金相关
    path('commission/history/', views.get_commission_history, name='commission_history'),
    path('commission/current/', views.get_current_commission, name='current_commission'),
    
    # 代理等级相关
    path('levels/', views.get_agent_levels, name='agent_levels'),
    path('upgrade/', views.upgrade_agent_level, name='upgrade_agent_level'),
    
    # 管理员接口
    path('admin/settlements/', views.admin_settlements, name='admin_settlements'),
    path('admin/settlement/<int:settlement_id>/approve/', views.approve_settlement, name='approve_settlement'),
    path('admin/settlement/<int:settlement_id>/reject/', views.reject_settlement, name='reject_settlement'),
    path('admin/agent/create/', views.create_agent_relationship, name='create_agent_relationship'),
    path('admin/agents/', views.get_all_agents, name='get_all_agents'),
    path('admin/agent/<int:relationship_id>/delete/', views.delete_agent_relationship, name='delete_agent_relationship'),
    path('admin/agent/<int:relationship_id>/detail/', views.get_agent_relationship_detail, name='get_agent_relationship_detail'),
    
    # 代理结算状态管理
    path('admin/agent/<int:user_id>/settlement/stop/', views.stop_agent_settlement, name='stop_agent_settlement'),
    path('admin/agent/<int:user_id>/settlement/restore/', views.restore_agent_settlement, name='restore_agent_settlement'),
    path('admin/agent/<int:user_id>/settlement/status/', views.get_agent_settlement_status, name='get_agent_settlement_status'),
    
    # 代理系统维护
    path('admin/maintenance/fix-agent-ids/', views.fix_agent_ids, name='fix_agent_ids'),
    
    # 预警管理相关
    path('admin/warnings/', views.SettlementWarningListView.as_view(), name='warning_list'),
    path('admin/warnings/<int:warning_id>/resolve/', views.resolve_warning, name='resolve_warning'),
    path('admin/warnings/<int:warning_id>/ignore/', views.ignore_warning, name='ignore_warning'),
    
    # API路由
    path('api/', include(router.urls)),
]

router.register(r'warnings', views.SettlementWarningViewSet, basename='settlement-warnings')
