from rest_framework import serializers
from .models import SettlementWarning
from django.contrib.auth import get_user_model

User = get_user_model()

class SettlementWarningSerializer(serializers.ModelSerializer):
    """结算预警序列化器"""
    agent_email = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    level_display = serializers.SerializerMethodField()
    type_display = serializers.SerializerMethodField()
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    
    class Meta:
        model = SettlementWarning
        fields = ['id', 'title', 'warning_type', 'type_display', 'level', 'level_display', 
                  'status', 'status_display', 'data', 'agent', 'agent_email', 
                  'settlement_period', 'created_at']
        read_only_fields = ['id', 'title', 'warning_type', 'type_display', 'level', 'level_display',
                           'data', 'agent', 'agent_email', 'settlement_period', 'created_at']
    
    def get_agent_email(self, obj):
        if obj.agent:
            return obj.agent.email
        return "系统级别"
    
    def get_status_display(self, obj):
        return obj.get_status_display()
    
    def get_level_display(self, obj):
        return obj.get_level_display()
    
    def get_type_display(self, obj):
        return obj.get_warning_type_display()
