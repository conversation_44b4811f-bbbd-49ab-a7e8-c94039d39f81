import logging
from django.core.management.base import BaseCommand
from django.db import transaction
from users.models import User
from agents.models import AgentRelationship
from agents.services import AgentService
from stats.models import SystemLog

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '代理系统修复工具: 修复代理ID、代理状态和代理关系一致性问题'

    def add_arguments(self, parser):
        parser.add_argument(
            '--mode',
            type=str,
            choices=['all', 'ids', 'relationships'],
            default='all',
            help='修复模式: ids=仅修复代理ID问题, relationships=仅修复代理关系不一致问题, all=全部修复'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只检查不修复，列出所有问题记录'
        )
        parser.add_argument(
            '--user-email',
            type=str,
            help='只处理指定用户邮箱的代理'
        )

    def handle(self, *args, **options):
        mode = options.get('mode', 'all')
        dry_run = options.get('dry_run', False)
        user_email = options.get('user_email')
        
        if mode in ['all', 'ids']:
            self.fix_agent_ids(dry_run, user_email)
            
        if mode in ['all', 'relationships']:
            self.fix_agent_relationships(dry_run, user_email)
            
        self.stdout.write(self.style.SUCCESS("修复操作完成"))
    
    def fix_agent_ids(self, dry_run, user_email=None):
        """修复代理ID相关问题"""
        self.stdout.write("=== 开始代理ID修复 ===")
        
        # 1. 修复已标记为代理但没有代理ID的用户
        self.stdout.write("开始检查已标记为代理但没有代理ID的用户...")
        
        query = User.objects.filter(is_agent=True, agent_id__isnull=True) | User.objects.filter(is_agent=True, agent_id='')
        if user_email:
            query = query.filter(email=user_email)
            
        agent_users = query
        self.stdout.write(f"找到 {agent_users.count()} 个标记为代理但没有代理ID的用户")
        
        count_updated_agent_id = 0
        
        for user in agent_users:
            old_agent_id = user.agent_id
            new_agent_id = f"A{user.user_id[1:]}"
            
            if not dry_run:
                with transaction.atomic():
                    user.agent_id = new_agent_id
                    user.save(update_fields=['agent_id'])
                    count_updated_agent_id += 1
                    self.stdout.write(self.style.SUCCESS(
                        f"用户 {user.email} 的代理ID已从 '{old_agent_id}' 更新为 '{new_agent_id}'"
                    ))
            else:
                self.stdout.write(
                    f"[DRY RUN] 将用户 {user.email} 的代理ID从 '{old_agent_id}' 更新为 '{new_agent_id}'"
                )
                count_updated_agent_id += 1
        
        # 2. 修复有代理关系但未标记为代理的用户
        self.stdout.write("\n开始检查有代理关系但未标记为代理的用户...")
        
        query = AgentRelationship.objects.filter(
            user__is_agent=False,
            is_active=True
        ).select_related('user')
        
        if user_email:
            query = query.filter(user__email=user_email)
            
        relationships = query
        
        self.stdout.write(f"找到 {relationships.count()} 个有代理关系但未标记为代理的用户")
        
        count_updated_agent_status = 0
        
        for relationship in relationships:
            user = relationship.user
            
            if not dry_run:
                with transaction.atomic():
                    user.is_agent = True
                    if not user.agent_id:
                        user.agent_id = f"A{user.user_id[1:]}"
                    user.save(update_fields=['is_agent', 'agent_id'])
                    count_updated_agent_status += 1
                    self.stdout.write(self.style.SUCCESS(
                        f"用户 {user.email} 已标记为代理，代理ID：'{user.agent_id}'"
                    ))
            else:
                self.stdout.write(
                    f"[DRY RUN] 将用户 {user.email} 标记为代理，代理ID：'A{user.user_id[1:]}'"
                )
                count_updated_agent_status += 1
        
        # 输出统计信息
        self.stdout.write("\n== ID修复统计 ==")
        self.stdout.write(f"已更新代理ID: {count_updated_agent_id}")
        self.stdout.write(f"已更新代理状态: {count_updated_agent_status}")
        self.stdout.write(f"总计修复: {count_updated_agent_id + count_updated_agent_status}")
        
        if dry_run:
            self.stdout.write(self.style.WARNING("这是DRY RUN模式，数据库没有实际更改"))
    
    def fix_agent_relationships(self, dry_run, user_email=None):
        """修复代理关系一致性问题"""
        self.stdout.write("\n=== 开始代理关系一致性修复 ===")
        
        # 获取所有代理关系
        relationships = AgentRelationship.objects.all()
        if user_email:
            relationships = relationships.filter(user__email=user_email)
            
        self.stdout.write(f"开始检查 {relationships.count()} 条代理关系的一致性...")
        
        inconsistent_count = 0
        fixed_count = 0
        
        # 收集所有不一致的记录
        inconsistent_records = []
        
        for relationship in relationships:
            # 使用AgentService检查一致性
            is_consistent, message = AgentService.check_agent_relationship_consistency(relationship.user)
            
            if not is_consistent:
                inconsistent_count += 1
                inconsistent_records.append({
                    'relationship_id': relationship.id,
                    'user_email': relationship.user.email,
                    'message': message,
                    'user_referrer': relationship.user.referrer.email if relationship.user.referrer else None,
                    'relationship_referrer': relationship.referrer.email if relationship.referrer else None
                })
        
        # 输出所有不一致记录
        if inconsistent_count > 0:
            self.stdout.write(self.style.WARNING(f"发现 {inconsistent_count} 条不一致记录:"))
            for record in inconsistent_records:
                self.stdout.write(self.style.WARNING(
                    f"用户: {record['user_email']}, "
                    f"问题: {record['message']}"
                ))
            
            # 如果不是只检查，执行修复
            if not dry_run:
                self.stdout.write("开始修复不一致记录...")
                
                for record in inconsistent_records:
                    relationship = AgentRelationship.objects.get(id=record['relationship_id'])
                    user = relationship.user
                    
                    # 使用AgentService修复一致性
                    success, message = AgentService.fix_agent_relationship_consistency(user)
                    
                    if success:
                        fixed_count += 1
                        self.stdout.write(self.style.SUCCESS(
                            f"用户 {user.email} 修复成功: {message}"
                        ))
                    else:
                        self.stdout.write(self.style.ERROR(
                            f"用户 {user.email} 修复失败: {message}"
                        ))
                
                self.stdout.write(self.style.SUCCESS(f"成功修复 {fixed_count} 条不一致记录"))
            else:
                self.stdout.write(self.style.WARNING("这是只检查模式，未执行任何修复"))
        else:
            self.stdout.write(self.style.SUCCESS("所有代理关系均一致，无需修复")) 