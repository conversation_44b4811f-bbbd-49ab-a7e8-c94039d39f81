# Generated by Django 4.2.19 on 2025-02-12 04:51

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AgentLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='等级名称')),
                ('level', models.PositiveIntegerField(unique=True, verbose_name='代理等级')),
                ('required_direct_members', models.PositiveIntegerField(verbose_name='所需直接下级数')),
                ('min_swmt_output', models.PositiveIntegerField(verbose_name='最小SWMT产出')),
                ('max_swmt_output', models.PositiveIntegerField(verbose_name='最大SWMT产出')),
                ('level1_commission_rate', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='一级佣金比例%')),
                ('level2_commission_rate', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='二级佣金比例%')),
                ('level3_commission_rate', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='三级佣金比例%')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '代理等级',
                'verbose_name_plural': '代理等级',
                'ordering': ['level'],
            },
        ),
        migrations.CreateModel(
            name='AgentRelationship',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('direct_member_count', models.PositiveIntegerField(default=0, verbose_name='直接下级数')),
                ('total_commission', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='累计佣金')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '代理关系',
                'verbose_name_plural': '代理关系',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CommissionRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level1_members', models.PositiveIntegerField(verbose_name='一级成员数')),
                ('level2_members', models.PositiveIntegerField(verbose_name='二级成员数')),
                ('level3_members', models.PositiveIntegerField(verbose_name='三级成员数')),
                ('level1_swmt', models.PositiveIntegerField(verbose_name='一级SWMT')),
                ('level2_swmt', models.PositiveIntegerField(verbose_name='二级SWMT')),
                ('level3_swmt', models.PositiveIntegerField(verbose_name='三级SWMT')),
                ('level1_commission', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='一级佣金')),
                ('level2_commission', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='二级佣金')),
                ('level3_commission', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='三级佣金')),
                ('total_commission', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='总佣金')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '佣金记录',
                'verbose_name_plural': '佣金记录',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SettlementPeriod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateTimeField(verbose_name='开始时间')),
                ('end_date', models.DateTimeField(verbose_name='结束时间')),
                ('status', models.CharField(choices=[('pending', '待结算'), ('processing', '结算中'), ('completed', '已完成'), ('failed', '失败')], default='pending', max_length=20, verbose_name='状态')),
                ('total_commission', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='总佣金')),
                ('agent_count', models.PositiveIntegerField(default=0, verbose_name='代理数量')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '结算周期',
                'verbose_name_plural': '结算周期',
                'ordering': ['-start_date'],
            },
        ),
    ]
