# Generated by Django 4.2.11 on 2025-03-31 02:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('agents', '0003_agentrelationship_settlement_restored_at_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SettlementWarning',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('warning_type', models.CharField(choices=[('commission_decrease', '佣金大幅减少'), ('zero_commission', '佣金为零'), ('swmt_decrease', 'SWMT产出大幅减少'), ('member_decrease', '团队成员大幅减少'), ('settlement_fail', '结算失败'), ('settlement_timeout', '结算超时'), ('high_error_rate', '高错误率'), ('other', '其他问题')], max_length=30, verbose_name='预警类型')),
                ('level', models.CharField(choices=[('info', '提示'), ('warning', '警告'), ('critical', '严重')], default='warning', max_length=20, verbose_name='预警级别')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('processing', '处理中'), ('resolved', '已解决'), ('ignored', '已忽略')], default='pending', max_length=20, verbose_name='处理状态')),
                ('title', models.CharField(max_length=100, verbose_name='预警标题')),
                ('description', models.TextField(verbose_name='预警详情')),
                ('data', models.JSONField(blank=True, null=True, verbose_name='相关数据')),
                ('handling_notes', models.TextField(blank=True, verbose_name='处理备注')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='解决时间')),
                ('is_system_level', models.BooleanField(default=False, help_text='若为True，则表示此预警影响整个系统而非单个代理', verbose_name='系统级别预警')),
                ('is_notified', models.BooleanField(default=False, verbose_name='是否已通知')),
                ('notification_sent_at', models.DateTimeField(blank=True, null=True, verbose_name='通知发送时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='settlement_warnings', to=settings.AUTH_USER_MODEL, verbose_name='代理')),
                ('handler', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='handled_warnings', to=settings.AUTH_USER_MODEL, verbose_name='处理人')),
                ('settlement_period', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warnings', to='agents.settlementperiod', verbose_name='结算周期')),
            ],
            options={
                'verbose_name': '结算预警',
                'verbose_name_plural': '结算预警',
                'ordering': ['-created_at'],
            },
        ),
    ]
