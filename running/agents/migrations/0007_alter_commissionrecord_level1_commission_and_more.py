# Generated by Django 4.2.11 on 2025-04-30 13:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('agents', '0006_add_processed_at_field'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='commissionrecord',
            name='level1_commission',
            field=models.DecimalField(decimal_places=8, max_digits=18, verbose_name='一级佣金'),
        ),
        migrations.AlterField(
            model_name='commissionrecord',
            name='level2_commission',
            field=models.DecimalField(decimal_places=8, max_digits=18, verbose_name='二级佣金'),
        ),
        migrations.AlterField(
            model_name='commissionrecord',
            name='level3_commission',
            field=models.DecimalField(decimal_places=8, max_digits=18, verbose_name='三级佣金'),
        ),
        migrations.AlterField(
            model_name='commissionrecord',
            name='total_commission',
            field=models.DecimalField(decimal_places=8, max_digits=18, verbose_name='总佣金'),
        ),
    ]
