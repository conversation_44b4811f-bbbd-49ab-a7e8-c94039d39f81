# Generated by Django 4.2.11 on 2025-02-23 04:33

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('agents', '0002_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='agentrelationship',
            name='settlement_restored_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='恢复结算时间'),
        ),
        migrations.AddField(
            model_name='agentrelationship',
            name='settlement_status',
            field=models.CharField(choices=[('active', '允许结算'), ('stopped', '停止结算')], default='active', help_text='代理的结算状态，停止结算后将不再参与佣金结算', max_length=20, verbose_name='结算状态'),
        ),
        migrations.AddField(
            model_name='agentrelationship',
            name='settlement_status_reason',
            field=models.TextField(blank=True, verbose_name='状态变更原因'),
        ),
        migrations.AddField(
            model_name='agentrelationship',
            name='settlement_stopped_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='停止结算时间'),
        ),
        migrations.AlterField(
            model_name='agentrelationship',
            name='agent_level',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='agents.agentlevel'),
        ),
        migrations.AlterField(
            model_name='agentrelationship',
            name='direct_member_count',
            field=models.PositiveIntegerField(default=0, verbose_name='直接下级数量'),
        ),
        migrations.AlterField(
            model_name='agentrelationship',
            name='referrer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='agent_referrals', to=settings.AUTH_USER_MODEL, verbose_name='推荐人'),
        ),
        migrations.AlterField(
            model_name='agentrelationship',
            name='total_commission',
            field=models.DecimalField(decimal_places=8, default=0, max_digits=18, verbose_name='累计佣金'),
        ),
        migrations.AlterField(
            model_name='agentrelationship',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='agent_relationship', to=settings.AUTH_USER_MODEL),
        ),
    ]
