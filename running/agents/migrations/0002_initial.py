# Generated by Django 4.2.19 on 2025-02-12 04:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('agents', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='commissionrecord',
            name='agent',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commission_records', to=settings.AUTH_USER_MODEL, verbose_name='代理'),
        ),
        migrations.AddField(
            model_name='commissionrecord',
            name='agent_level',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='agents.agentlevel', verbose_name='代理等级'),
        ),
        migrations.AddField(
            model_name='commissionrecord',
            name='settlement_period',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commission_records', to='agents.settlementperiod', verbose_name='结算周期'),
        ),
        migrations.AddField(
            model_name='agentrelationship',
            name='agent_level',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='agents.agentlevel', verbose_name='代理等级'),
        ),
        migrations.AddField(
            model_name='agentrelationship',
            name='referrer',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='direct_members', to=settings.AUTH_USER_MODEL, verbose_name='推荐人'),
        ),
        migrations.AddField(
            model_name='agentrelationship',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='agent_info', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
