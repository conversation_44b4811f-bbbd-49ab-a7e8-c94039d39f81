from rest_framework import serializers
from .models import AgentLevel, AgentRelationship, CommissionRecord, SettlementPeriod
from users.models import User
from .utils import format_decimal

class UserBasicSerializer(serializers.ModelSerializer):
    """用户基本信息序列化器"""
    
    class Meta:
        model = User
        fields = ['user_id', 'username', 'email', 'is_agent', 'referral_code', 'referral_count', 'created_at']


class AgentLevelSerializer(serializers.ModelSerializer):
    """代理等级序列化器"""
    
    commission_rates = serializers.SerializerMethodField()
    
    class Meta:
        model = AgentLevel
        fields = ['id', 'name', 'level', 'required_direct_members', 'min_swmt_output', 
                  'max_swmt_output', 'commission_rates', 'is_active']
    
    def get_commission_rates(self, obj):
        """获取佣金比例"""
        return {
            'level1': float(obj.level1_commission_rate),
            'level2': float(obj.level2_commission_rate),
            'level3': float(obj.level3_commission_rate)
        }


class AgentRelationshipSerializer(serializers.ModelSerializer):
    """代理关系序列化器"""
    
    agent_level = AgentLevelSerializer()
    user = UserBasicSerializer()
    
    class Meta:
        model = AgentRelationship
        fields = ['id', 'user', 'agent_level', 'settlement_status', 
                  'settlement_status_reason', 'settlement_stopped_at', 
                  'is_active', 'created_at', 'updated_at']


class SettlementPeriodSerializer(serializers.ModelSerializer):
    """结算周期序列化器"""
    
    class Meta:
        model = SettlementPeriod
        fields = ['id', 'start_date', 'end_date', 'status', 'created_at']


class CommissionRecordSerializer(serializers.ModelSerializer):
    """佣金记录序列化器"""
    
    agent_level = AgentLevelSerializer()
    settlement_period = SettlementPeriodSerializer()
    team_stats = serializers.SerializerMethodField()
    swmt_production = serializers.SerializerMethodField()
    commission = serializers.SerializerMethodField()
    
    class Meta:
        model = CommissionRecord
        fields = ['id', 'agent', 'agent_level', 'settlement_period', 
                  'team_stats', 'swmt_production', 'commission', 'created_at']
    
    def get_team_stats(self, obj):
        """获取团队统计信息"""
        return {
            'level1_members': obj.level1_members,
            'level2_members': obj.level2_members,
            'level3_members': obj.level3_members,
            'total_members': obj.level1_members + obj.level2_members + obj.level3_members
        }
    
    def get_swmt_production(self, obj):
        """获取SWMT产出信息"""
        return {
            'level1': obj.level1_swmt,
            'level2': obj.level2_swmt,
            'level3': obj.level3_swmt,
            'total': obj.level1_swmt + obj.level2_swmt + obj.level3_swmt
        }
    
    def get_commission(self, obj):
        """获取佣金信息"""
        return {
            'level1': format_decimal(obj.level1_commission, 2),
            'level2': format_decimal(obj.level2_commission, 2),
            'level3': format_decimal(obj.level3_commission, 2),
            'total': format_decimal(obj.total_commission, 2)
        }


class TeamTreeNodeSerializer(serializers.ModelSerializer):
    """团队树节点序列化器"""
    
    children = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['user_id', 'username', 'email', 'is_agent', 'referral_code', 
                  'referral_count', 'created_at', 'children']
    
    def get_children(self, obj):
        """递归获取下级成员"""
        max_depth = self.context.get('max_depth', 3)
        current_depth = self.context.get('current_depth', 1)
        
        if current_depth >= max_depth:
            return None
        
        direct_referrals = User.objects.filter(referrer=obj)
        
        if not direct_referrals.exists():
            return None
        
        serializer = TeamTreeNodeSerializer(
            direct_referrals, 
            many=True, 
            context={
                'max_depth': max_depth,
                'current_depth': current_depth + 1
            }
        )
        return serializer.data


class TeamMemberSerializer(serializers.ModelSerializer):
    """团队成员序列化器"""
    
    join_date = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['user_id', 'username', 'email', 'is_agent', 
                  'referral_count', 'join_date']
    
    def get_join_date(self, obj):
        """获取加入日期"""
        return obj.created_at.isoformat() 