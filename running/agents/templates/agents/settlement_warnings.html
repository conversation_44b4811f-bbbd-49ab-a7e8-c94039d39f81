{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
  {{ block.super }}
  <style type="text/css">
    .warning-level-high {
      color: #d9534f;
      font-weight: bold;
    }
    .warning-level-medium {
      color: #f0ad4e;
      font-weight: bold;
    }
    .warning-level-low {
      color: #5bc0de;
    }
    .warning-status-pending {
      background-color: #f2dede;
    }
    .warning-status-resolved {
      background-color: #dff0d8;
    }
    .warning-status-ignored {
      background-color: #f5f5f5;
    }
    .warning-data {
      max-width: 400px;
      word-break: break-all;
      white-space: pre-wrap;
    }
    .filter-form {
      margin-bottom: 20px;
      padding: 10px;
      background-color: #f8f8f8;
      border-radius: 4px;
    }
    .filter-form select, .filter-form input {
      margin-right: 10px;
      margin-bottom: 5px;
    }
  </style>
{% endblock %}

{% block content %}
<div id="content-main">
  <h1>结算预警管理</h1>
  
  <div class="filter-form">
    <form method="get">
      <label for="status">状态:</label>
      <select name="status" id="status">
        <option value="">全部</option>
        <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>待处理</option>
        <option value="resolved" {% if request.GET.status == 'resolved' %}selected{% endif %}>已解决</option>
        <option value="ignored" {% if request.GET.status == 'ignored' %}selected{% endif %}>已忽略</option>
      </select>
      
      <label for="level">级别:</label>
      <select name="level" id="level">
        <option value="">全部</option>
        <option value="high" {% if request.GET.level == 'high' %}selected{% endif %}>高</option>
        <option value="medium" {% if request.GET.level == 'medium' %}selected{% endif %}>中</option>
        <option value="low" {% if request.GET.level == 'low' %}selected{% endif %}>低</option>
      </select>
      
      <label for="type">类型:</label>
      <select name="type" id="type">
        <option value="">全部</option>
        <option value="commission_decrease" {% if request.GET.type == 'commission_decrease' %}selected{% endif %}>佣金下降</option>
        <option value="zero_commission" {% if request.GET.type == 'zero_commission' %}selected{% endif %}>零佣金</option>
        <option value="swmt_decrease" {% if request.GET.type == 'swmt_decrease' %}selected{% endif %}>SWMT下降</option>
        <option value="member_decrease" {% if request.GET.type == 'member_decrease' %}selected{% endif %}>团队成员减少</option>
        <option value="settlement_fail" {% if request.GET.type == 'settlement_fail' %}selected{% endif %}>结算失败</option>
        <option value="settlement_timeout" {% if request.GET.type == 'settlement_timeout' %}selected{% endif %}>结算超时</option>
      </select>
      
      <button type="submit" class="button">筛选</button>
    </form>
  </div>

  {% if warnings %}
    <table>
      <thead>
        <tr>
          <th>ID</th>
          <th>标题</th>
          <th>类型</th>
          <th>级别</th>
          <th>状态</th>
          <th>代理</th>
          <th>结算周期</th>
          <th>数据</th>
          <th>创建时间</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        {% for warning in warnings %}
          <tr class="warning-status-{{ warning.status }}">
            <td>{{ warning.id }}</td>
            <td>{{ warning.title }}</td>
            <td>{{ warning.get_type_display }}</td>
            <td class="warning-level-{{ warning.level }}">{{ warning.get_level_display }}</td>
            <td>{{ warning.get_status_display }}</td>
            <td>{% if warning.agent %}{{ warning.agent.email }}{% else %}系统{% endif %}</td>
            <td>{% if warning.period %}{{ warning.period.start_date|date:"Y-m-d" }} - {{ warning.period.end_date|date:"Y-m-d" }}{% else %}--{% endif %}</td>
            <td class="warning-data">{{ warning.data|linebreaksbr }}</td>
            <td>{{ warning.created_at|date:"Y-m-d H:i:s" }}</td>
            <td>
              {% if warning.status == 'pending' %}
                <a href="{% url 'agents:resolve_warning' warning.id %}" onclick="return confirm('确定标记为已解决吗？')">标记为已解决</a> |
                <a href="{% url 'agents:ignore_warning' warning.id %}" onclick="return confirm('确定忽略此预警吗？')">忽略</a>
              {% else %}
                <span>--</span>
              {% endif %}
            </td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
    
    {% if is_paginated %}
      <div class="pagination">
        <span class="step-links">
          {% if page_obj.has_previous %}
            <a href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">&laquo; 首页</a>
            <a href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">上一页</a>
          {% endif %}
  
          <span class="current">
            第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页
          </span>
  
          {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">下一页</a>
            <a href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">尾页 &raquo;</a>
          {% endif %}
        </span>
      </div>
    {% endif %}
  {% else %}
    <p>暂无预警记录</p>
  {% endif %}
</div>
{% endblock %} 