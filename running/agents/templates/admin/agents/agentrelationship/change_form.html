{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify custom_filters %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static "admin/css/forms.css" %}">
<style>
    /* 基础样式修复 */
    h1, h2, h3, legend, .section-title {
        color: #000 !important;
        font-weight: bold;
    }
    
    /* 自定义面板样式 */
    .info-panel {
        margin: 15px 0;
        padding: 15px;
        border-radius: 4px;
        background-color: #f8f9fa;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .panel-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 10px;
        padding-bottom: 5px;
        border-bottom: 1px solid #dee2e6;
    }
    
    .panel-body {
        display: flex;
        flex-wrap: wrap;
    }
    
    .stat-item {
        flex: 1 0 30%;
        margin: 5px 10px;
        min-width: 150px;
    }
    
    .stat-label {
        font-weight: bold;
        color: #495057;
    }
    
    .stat-value {
        font-size: 1.1em;
        padding: 3px 0;
    }
    
    /* 只读区域样式 */
    .readonly-section {
        background-color: #f8f9fa;
        border-left: 4px solid #6c757d;
        padding: 5px 0 5px 15px;
        margin: 5px 0;
    }
    
    /* 可编辑区域样式 */
    .editable-section {
        background-color: #fff;
        border-left: 4px solid #28a745;
        padding: 5px 0 5px 15px;
        margin: 5px 0;
    }
    
    /* 标记重要数据 */
    .highlight-value {
        font-weight: bold;
        color: #007bff;
    }
    
    /* 警告信息 */
    .warning-text {
        padding: 10px;
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
        color: #856404;
        margin: 10px 0;
    }
    
    /* 团队结构表格 */
    .team-table {
        width: 100%;
        border-collapse: collapse;
        margin: 15px 0;
    }
    
    .team-table th, .team-table td {
        padding: 8px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
    }
    
    .team-table th {
        background-color: #e9ecef;
    }
    
    .team-table tr:hover {
        background-color: #f5f5f5;
    }
    
    /* 提示文字 */
    .help-text {
        color: #6c757d;
        font-size: 0.9em;
        margin-top: 2px;
    }
    
    /* 表单样式 */
    .form-row {
        margin-bottom: 10px;
    }
    
    .field-label {
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .field-value {
        padding: 5px 0;
    }
    
    /* 可编辑字段样式 */
    .editable-field {
        padding: 10px;
        border: 1px solid #e3e3e3;
        background-color: #fafafa;
        margin-bottom: 10px;
        border-radius: 4px;
    }
    
    .editable-field select,
    .editable-field textarea {
        width: 100%;
        padding: 5px;
    }
    
    .submit-row {
        margin-top: 20px;
        padding: 10px;
        background-color: #f8f9fa;
    }
    
    /* 错误消息样式 */
    .field-error {
        color: #dc3545;
        font-size: 0.9em;
        margin-top: 5px;
    }
    
    /* 时间信息样式 */
    .time-info {
        margin-top: 5px;
        font-style: italic;
        color: #6c757d;
    }
    
    /* 日期显示统一样式 */
    .date-display {
        white-space: nowrap;
    }
    
    /* 预计投档等级 */
    .predicted-level {
        background-color: #e9f7fe;
        padding: 5px 8px;
        border-radius: 4px;
        border-left: 3px solid #17a2b8;
    }
</style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
&rsaquo; {% if has_view_permission %}<a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>{% else %}{{ opts.verbose_name_plural|capfirst }}{% endif %}
&rsaquo; {% if add %}{% blocktranslate with name=opts.verbose_name %}Add {{ name }}{% endblocktranslate %}{% else %}{{ original|truncatewords:"18" }}{% endif %}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    {% if errors %}
    <p class="errornote">
    {% blocktranslate count counter=errors|length %}Please correct the error below.{% plural %}Please correct the errors below.{% endblocktranslate %}
    </p>
    {{ adminform.form.non_field_errors }}
    {% endif %}
    
    <form {% if has_file_field %}enctype="multipart/form-data" {% endif %}{% if form_url %}action="{{ form_url }}" {% endif %}method="post" id="{{ opts.model_name }}_form" novalidate>{% csrf_token %}
    {% if is_popup %}<input type="hidden" name="{{ is_popup_var }}" value="1">{% endif %}
    {% if to_field %}<input type="hidden" name="{{ to_field_var }}" value="{{ to_field }}">{% endif %}
    
    {% if original %}
    <fieldset class="module aligned">
        <h2>代理关系详情</h2>
    </fieldset>
    
    <!-- 代理基本信息面板 -->
    <div class="info-panel">
        <h3 class="panel-title">代理基本信息</h3>
        <div class="panel-body">
            <div class="stat-item">
                <div class="stat-label">用户ID</div>
                <div class="stat-value">{{ original.user.user_id }}</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">邮箱</div>
                <div class="stat-value">{{ original.user.email }}</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">代理ID</div>
                <div class="stat-value">{{ original.user.agent_id }}</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">代理等级</div>
                <div class="stat-value highlight-value">{{ original.agent_level.name }}</div>
                <div class="help-text">由系统自动投档计算，不可手动修改</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">推荐人</div>
                <div class="stat-value">{{ original.referrer.email|default:"无" }}</div>
                <div class="help-text">由用户注册时确定，保持一致性</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">创建时间</div>
                <div class="stat-value date-display">{{ original.created_at|date:"Y-m-d H:i" }}</div>
            </div>
            
            <!-- 新增显示上次结算佣金 -->
            <div class="stat-item">
                <div class="stat-label">上次结算佣金</div>
                <div class="stat-value highlight-value">
                    {% if last_valid_commission %}
                    {{ last_commission_amount|swmt_format }}
                    {% else %}
                    无
                    {% endif %}
                </div>
            </div>
            
            <!-- 新增显示上期结算日期 -->
            <div class="stat-item">
                <div class="stat-label">上期结算日期</div>
                <div class="stat-value date-display">
                    {% if last_settlement_date %}
                    {{ last_settlement_date|date:"Y-m-d H:i" }}
                    {% else %}
                    无
                    {% endif %}
                </div>
            </div>
            
            <!-- 新增显示本期下级已产生SWMT -->
            <div class="stat-item">
                <div class="stat-label">本期下级已产生SWMT</div>
                <div class="stat-value highlight-value">{{ current_period_swmt|swmt_format }}</div>
            </div>
            
            <!-- 新增显示本期结算日期 -->
            <div class="stat-item">
                <div class="stat-label">本期结算日期</div>
                <div class="stat-value date-display">{{ next_settlement_date|date:"Y-m-d H:i" }}</div>
            </div>
            
            <!-- 新增显示预计投档等级 -->
            <div class="stat-item">
                <div class="stat-label">预计投档等级</div>
                <div class="stat-value predicted-level">
                    {% if predicted_level %}
                    {{ predicted_level.name }}
                    {% else %}
                    无法预测
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 可编辑区域面板 -->
    <div class="info-panel">
        <h3 class="panel-title">可编辑设置</h3>
        <div class="warning-text">
            <strong>注意：</strong>
            <ul>
                <li>修改"是否激活"为否会取消代理资格，用户变为普通用户</li>
                <li>修改"结算状态"为停止结算则不影响代理资格，仅不参与佣金结算</li>
                <li>修改任何状态必须填写变更原因，否则无法保存</li>
            </ul>
        </div>
        
        <div class="editable-field">
            <div class="field-label">是否激活</div>
            <div class="field-value">
                {{ adminform.form.is_active }}
                {% if adminform.form.is_active.errors %}
                <div class="field-error">
                    {{ adminform.form.is_active.errors }}
                </div>
                {% endif %}
                <div class="help-text">控制代理状态，禁用后将取消代理资格</div>
            </div>
        </div>
        
        <div class="editable-field">
            <div class="field-label">结算状态</div>
            <div class="field-value">
                {{ adminform.form.settlement_status }}
                {% if adminform.form.settlement_status.errors %}
                <div class="field-error">
                    {{ adminform.form.settlement_status.errors }}
                </div>
                {% endif %}
                <div class="help-text">控制该代理是否参与佣金结算</div>
            </div>
        </div>
        
        <div class="editable-field">
            <div class="field-label">状态变更原因 <span style="color:red">*</span></div>
            <div class="field-value">
                {{ adminform.form.settlement_status_reason }}
                {% if adminform.form.settlement_status_reason.errors %}
                <div class="field-error">
                    {{ adminform.form.settlement_status_reason.errors }}
                </div>
                {% endif %}
                <div class="help-text">必填项：请填写结算状态或激活状态变更的具体原因，将记录在日志中</div>
            </div>
        </div>
        
        {% if original.settlement_stopped_at or original.settlement_restored_at %}
        <div class="time-info">
            {% if original.settlement_stopped_at %}
            <div>停止结算时间: {{ original.settlement_stopped_at|date:"Y-m-d H:i" }}</div>
            {% endif %}
            {% if original.settlement_restored_at %}
            <div>恢复结算时间: {{ original.settlement_restored_at|date:"Y-m-d H:i" }}</div>
            {% endif %}
        </div>
        {% endif %}
    </div>
    
    <!-- 团队数据面板 -->
    <div class="info-panel">
        <h3 class="panel-title">团队数据</h3>
        <div class="panel-body">
            <div class="stat-item">
                <div class="stat-label">直接下级数量</div>
                <div class="stat-value highlight-value">{{ original.direct_member_count }}</div>
                <div class="help-text">系统自动统计，不可修改</div>
            </div>
            
            {% with level1=original.user.level1_members_count|default:"0" level2=original.user.level2_members_count|default:"0" level3=original.user.level3_members_count|default:"0" %}
            <div class="stat-item">
                <div class="stat-label">一级下线人数</div>
                <div class="stat-value">{{ level1 }}</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">二级下线人数</div>
                <div class="stat-value">{{ level2 }}</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">三级下线人数</div>
                <div class="stat-value">{{ level3 }}</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">团队总人数</div>
                <div class="stat-value highlight-value">{{ level1|add:level2|add:level3 }}</div>
            </div>
            {% endwith %}
            
            {% with level1_swmt=original.user.level1_swmt|default:"0"|stringformat:"s" level2_swmt=original.user.level2_swmt|default:"0"|stringformat:"s" level3_swmt=original.user.level3_swmt|default:"0"|stringformat:"s" %}
            <div class="stat-item">
                <div class="stat-label">一级SWMT产出</div>
                <div class="stat-value">{{ level1_swmt|swmt_format }}</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">二级SWMT产出</div>
                <div class="stat-value">{{ level2_swmt|swmt_format }}</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">三级SWMT产出</div>
                <div class="stat-value">{{ level3_swmt|swmt_format }}</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">团队总SWMT产出</div>
                <div class="stat-value highlight-value">
                    {% with lvl1=original.user.level1_swmt|default:"0"|stringformat:"f" %}
                    {% with lvl2=original.user.level2_swmt|default:"0"|stringformat:"f" %}
                    {% with lvl3=original.user.level3_swmt|default:"0"|stringformat:"f" %}
                    {% with total=lvl1|add:lvl2|add:lvl3 %}
                        {{ total|swmt_format }}
                    {% endwith %}
                    {% endwith %}
                    {% endwith %}
                    {% endwith %}
                </div>
            </div>
            {% endwith %}
        </div>
    </div>
    
    <!-- 佣金数据面板 -->
    <div class="info-panel">
        <h3 class="panel-title">佣金数据</h3>
        <div class="panel-body">
            <div class="stat-item">
                <div class="stat-label">累计佣金</div>
                <div class="stat-value highlight-value">{{ original.total_commission|swmt_format }}</div>
                <div class="help-text">系统自动统计，不可修改</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">等级佣金比例</div>
                <div class="stat-value">
                    一级: {{ original.agent_level.level1_commission_rate }}%<br>
                    二级: {{ original.agent_level.level2_commission_rate }}%<br>
                    三级: {{ original.agent_level.level3_commission_rate }}%
                </div>
            </div>
        </div>
    </div>
    
    <!-- 最近佣金记录 -->
    <div class="info-panel">
        <h3 class="panel-title">最近佣金结算记录</h3>
        <table class="team-table">
            <thead>
                <tr>
                    <th>结算周期</th>
                    <th>代理等级</th>
                    <th>一级佣金</th>
                    <th>二级佣金</th>
                    <th>三级佣金</th>
                    <th>总佣金</th>
                    <th>结算时间</th>
                </tr>
            </thead>
            <tbody>
                {% for record in recent_commission_records %}
                <tr>
                    <td>{{ record.settlement_period.start_date|date:"Y-m-d" }} 至 {{ record.settlement_period.end_date|date:"Y-m-d" }}</td>
                    <td>{{ record.agent_level.name }}</td>
                    <td>{{ record.level1_commission|swmt_format }}</td>
                    <td>{{ record.level2_commission|swmt_format }}</td>
                    <td>{{ record.level3_commission|swmt_format }}</td>
                    <td class="highlight-value">{{ record.total_commission|swmt_format }}</td>
                    <td class="date-display">{{ record.created_at|date:"Y-m-d H:i" }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" style="text-align: center;">暂无佣金记录</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- 团队直接下线 -->
    <div class="info-panel">
        <h3 class="panel-title">直接下线用户</h3>
        <table class="team-table">
            <thead>
                <tr>
                    <th>用户ID</th>
                    <th>邮箱</th>
                    <th>是否为代理</th>
                    <th>注册时间</th>
                    <th>最近活跃</th>
                    <th>SWMT产出</th>
                </tr>
            </thead>
            <tbody>
                {% for member in direct_members %}
                <tr>
                    <td>{{ member.user_id }}</td>
                    <td>{{ member.email }}</td>
                    <td>{{ member.is_agent|yesno:"是,否" }}</td>
                    <td>{{ member.created_at|date:"Y-m-d" }}</td>
                    <td>{{ member.last_active_time|date:"Y-m-d H:i"|default:"未知" }}</td>
                    <td>{{ member.total_swmt|swmt_format }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" style="text-align: center;">暂无直接下线用户</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% if direct_members %}
        <div style="text-align: right; margin-top: 10px;">
            <a href="{% url 'admin:users_user_changelist' %}?referrer__id__exact={{ original.user.id }}" class="button">查看全部下线</a>
        </div>
        {% endif %}
    </div>
    
    <!-- 提示信息 -->
    <div class="warning-text">
        <strong>注意：</strong>
        <ul>
            <li>代理等级由系统根据直接下级人数和团队SWMT产出自动调整，不可手动修改</li>
            <li>佣金数据由系统自动统计，不可手动修改</li>
            <li>只有"结算状态"和"是否激活"可以由管理员手动修改</li>
        </ul>
    </div>
    
    <!-- 原始表单字段（隐藏） -->
    <div style="display:none;">
        {% for fieldset in adminform %}
            {% for line in fieldset %}
                {% for field in line %}
                    {% if field.field.name not in 'is_active,settlement_status,settlement_status_reason' %}
                        {{ field.field }}
                    {% endif %}
                {% endfor %}
            {% endfor %}
        {% endfor %}
    </div>
    
    <!-- 提交按钮 -->
    <div class="submit-row">
        <input type="submit" value="保存" class="default" name="_save">
        {% if show_delete_link %}
            {% url opts|admin_urlname:'delete' original.pk|admin_urlquote as delete_url %}
            <p class="deletelink-box"><a href="{% add_preserved_filters delete_url %}" class="deletelink">{% translate "Delete" %}</a></p>
        {% endif %}
    </div>
    {% endif %}
    
    </form>
</div>
{% endblock %}

{% block admin_change_form_document_ready %}
{{ block.super }}
<script type="text/javascript">
(function($) {
    // 确保DOM已加载
    $(document).ready(function() {
        // 获取表单元素
        var isActiveField = $('#id_is_active');
        var settlementStatusField = $('#id_settlement_status');
        var reasonField = $('#id_settlement_status_reason');
        
        // 如果找不到元素，直接返回，避免JavaScript错误
        if (!isActiveField.length || !settlementStatusField.length || !reasonField.length) {
            console.log('表单字段未找到，跳过验证');
            return;
        }
        
        // 记录初始值，用于比较是否有变化
        var initialIsActive = isActiveField.prop('checked');
        var initialSettlementStatus = settlementStatusField.val();
        
        // 监听表单提交
        var formId = '{{ opts.model_name }}_form';
        var form = $('#' + formId);
        
        if (!form.length) {
            console.log('表单未找到: ' + formId);
            // 尝试使用通用选择器
            form = $('form');
        }
        
        form.on('submit', function(e) {
            // 检查是否有变更
            var isActiveChanged = isActiveField.prop('checked') !== initialIsActive;
            var settlementStatusChanged = settlementStatusField.val() !== initialSettlementStatus;
            
            // 如果有变更但没有填写原因，阻止提交
            if ((isActiveChanged || settlementStatusChanged) && !reasonField.val().trim()) {
                e.preventDefault();
                alert('修改状态时必须填写变更原因！');
                reasonField.focus();
                
                // 添加错误提示样式
                var fieldValue = reasonField.closest('.field-value');
                if (fieldValue.length && !fieldValue.find('.field-error').length) {
                    fieldValue.append('<div class="field-error">必须填写变更原因</div>');
                }
            }
        });
    });
})(django.jQuery);
</script>
{% endblock %} 