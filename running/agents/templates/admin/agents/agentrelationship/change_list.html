{% extends "admin/change_list.html" %}
{% load i18n admin_urls static %}

{% block object-tools %}
    <ul class="object-tools">
        {% block object-tools-items %}
            {% if has_add_permission %}
                <li>
                    {% url cl.opts|admin_urlname:'add' as add_url %}
                    <a href="{% add_preserved_filters add_url is_popup to_field %}" class="addlink">
                        {% blocktranslate with cl.opts.verbose_name as name %}Add {{ name }}{% endblocktranslate %}
                    </a>
                </li>
            {% endif %}
            
            {% if custom_button %}
            <li>
                <a href="add-agent-relationship/" class="addlink">
                    {{ custom_button.label }}
                </a>
            </li>
            {% endif %}
        {% endblock %}
    </ul>
{% endblock %} 