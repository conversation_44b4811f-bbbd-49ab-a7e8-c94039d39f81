{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block extrastyle %}
<style>
    h1 {
        color: #000 !important; /* 强制黑色标题 */
        font-weight: bold;
    }
    .fieldset-title {
        color: #000 !important;
        font-weight: bold;
    }
    .help-text {
        margin: 10px 0;
        padding: 10px;
        background-color: #f8f9fa;
        border-left: 4px solid #0088cc;
        color: #333;
    }
    .warning-text {
        margin: 10px 0;
        padding: 10px;
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
        color: #856404;
    }
    .info-text {
        margin: 10px 0;
        padding: 10px;
        background-color: #d1ecf1;
        border-left: 4px solid #17a2b8;
        color: #0c5460;
    }
</style>
{% endblock %}

{% block content %}
<div id="content-main">
    <h1>增加代理关系</h1>
    
    <div class="help-text">
        代理等级由系统按照下线人数和团队SWMT产出自动投档确定，不可手动修改
    </div>
    
    <div class="warning-text">
        注意：只能为普通用户创建代理关系，已经具备代理资格的用户不能再次创建代理关系
    </div>
    
    <div class="info-text">
        重要说明：创建代理关系将使用用户注册时填写的推荐码建立的上级关系，这样确保系统中推荐关系的一致性。创建代理后不会改变用户的上级关系。
    </div>

    <form method="post" id="create_agent_form">
        {% csrf_token %}
        <fieldset class="module aligned">
            <div class="form-row field-user">
                <div>
                    <label for="id_user" class="fieldset-title">User:</label>
                    <input type="text" name="user_id" id="id_user" required>
                    <input type="button" value="查找" onclick="searchUser()">
                    <p class="help">请选择一个普通用户（非代理用户）</p>
                </div>
            </div>
            
            <div class="form-row field-is_active">
                <div>
                    <label for="id_is_active" class="fieldset-title">是否激活:</label>
                    <input type="checkbox" name="is_active" id="id_is_active" checked>
                </div>
            </div>
        </fieldset>
        
        <div class="submit-row">
            <input type="submit" value="保存" class="default" name="_save">
        </div>
    </form>
</div>

<script>
    function searchUser() {
        // 打开用户选择窗口，添加查询参数确保只显示非代理用户
        var userWindow = window.open('/admin/users/user/?is_agent__exact=0&_popup=1', 'userWindow', 'width=800,height=600');
        
        // 对打开的窗口添加用户选择事件监听
        if (userWindow) {
            // 需要覆盖原窗口的dismissRelatedLookupPopup函数
            userWindow.onload = function() {
                var origDismiss = userWindow.dismissRelatedLookupPopup;
                userWindow.dismissRelatedLookupPopup = function(win, chosenId) {
                    // 获取选中的行内容
                    var selectedRow = userWindow.document.getElementById('result_list').querySelector('tr[data-object-id="' + chosenId + '"]');
                    if (selectedRow) {
                        var userId = selectedRow.querySelector('th').innerText.trim();
                        document.getElementById('id_user').value = userId;
                        userWindow.close();
                    } else {
                        origDismiss(win, chosenId);
                    }
                };
                
                // 给所有结果行添加点击事件
                var resultRows = userWindow.document.querySelectorAll('#result_list tbody tr');
                resultRows.forEach(function(row) {
                    row.style.cursor = 'pointer';
                    row.addEventListener('click', function() {
                        var objectId = row.getAttribute('data-object-id');
                        var userId = row.querySelector('th').innerText.trim();
                        document.getElementById('id_user').value = userId;
                        userWindow.close();
                    });
                });
            };
        }
    }
</script>
{% endblock %} 