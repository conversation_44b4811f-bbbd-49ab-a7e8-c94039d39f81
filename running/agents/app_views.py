from django.shortcuts import get_object_or_404
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.db import transaction
from django.db.models import Sum, Count, Q, F
from django.core.cache import cache
import logging
import datetime
# Add drf-spectacular imports
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiTypes, inline_serializer
from rest_framework import serializers # Needed for inline_serializer

from users.models import User
from .models import AgentLevel, AgentRelationship, CommissionRecord, SettlementPeriod
from core.utils.api_utils import ApiResponse
from core.utils.messages import get_message, AGENT_MESSAGES
from core.utils.cache_utils import get_cache, set_cache

logger = logging.getLogger(__name__)

# Define a generic success response schema for views returning simple messages or basic data
GenericSuccessResponseSerializer = inline_serializer(
    name='GenericSuccessResponse',
    fields={
        'code': serializers.IntegerField(default=200),
        'message': serializers.CharField(),
        'data': serializers.DictField(required=False, allow_null=True) # Use DictField for generic data
    }
)

# Define a generic error response schema
GenericErrorResponseSerializer = inline_serializer(
    name='GenericErrorResponse',
    fields={
        'code': serializers.IntegerField(),
        'message': serializers.CharField(),
        'data': serializers.DictField(required=False, allow_null=True)
    }
)


@extend_schema(
    tags=["App - Agents"],
    summary="获取团队统计信息",
    description="获取当前认证用户的团队统计数据，包括团队规模、当前周期产出、潜在佣金和最近佣金记录。如果用户不是代理，则返回提示信息。",
    responses={
        200: inline_serializer( # Define specific response for this view
            name='TeamStatsResponse',
            fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': serializers.DictField() # Keep it generic for now, can be refined later
            }
        ),
        403: inline_serializer(
            name='TeamStatsNotAgentResponse',
            fields={
                'code': serializers.IntegerField(default=403),
                'message': serializers.CharField(),
                'data': serializers.DictField()
            }
        ),
        500: GenericErrorResponseSerializer,
    }
)
class TeamStatsView(APIView):
    """团队统计视图"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取团队统计"""
        cache_key = f"agent_team_stats_{request.user.id}"
        cached_data = get_cache(cache_key)
        
        if cached_data:
            return ApiResponse(
                code=200,
                message=get_message('team_stats_success'),
                data=cached_data
            )
        
        try:
            # 检查用户是否是代理
            if not request.user.is_agent:
                return ApiResponse(
                    code=403,
                    message=get_message('agent_not_eligible'),
                    data={
                        "is_agent": False,
                        "requirement": {
                            "direct_referrals_needed": 3,
                            "current_referrals": request.user.referral_count
                        }
                    }
                )
            
            # 获取代理关系
            agent_relationship = get_object_or_404(
                AgentRelationship, 
                user=request.user,
                is_active=True
            )
            
            # 获取团队成员
            level1_members = User.objects.filter(referrer=request.user)
            level1_count = level1_members.count()
            
            level2_members = User.objects.filter(referrer__in=level1_members)
            level2_count = level2_members.count()
            
            level3_members = User.objects.filter(referrer__in=level2_members)
            level3_count = level3_members.count()
            
            # 获取代理等级
            agent_level = agent_relationship.agent_level
            
            # 获取当前结算周期
            current_period = SettlementPeriod.objects.filter(
                status='pending'
            ).order_by('-start_date').first()
            
            # 计算当前周期统计
            if current_period:
                period_start = current_period.start_date
                period_end = current_period.end_date
                
                # 计算SWMT产出
                level1_swmt = self._get_period_swmt(level1_members, period_start, period_end)
                level2_swmt = self._get_period_swmt(level2_members, period_start, period_end)
                level3_swmt = self._get_period_swmt(level3_members, period_start, period_end)
                
                # 计算潜在佣金
                potential_commission = self._calculate_potential_commission(
                    agent_level, level1_swmt, level2_swmt, level3_swmt
                )
                
                # 获取当前周期信息
                current_period_info = {
                    "period_id": current_period.id,
                    "start_date": current_period.start_date.isoformat(),
                    "end_date": current_period.end_date.isoformat(),
                    "days_left": (current_period.end_date - timezone.now()).days + 1
                }
            else:
                level1_swmt = 0
                level2_swmt = 0
                level3_swmt = 0
                potential_commission = 0
                current_period_info = None
            
            # 获取最近佣金记录
            recent_commissions = CommissionRecord.objects.filter(
                agent=request.user
            ).order_by('-created_at')[:5]
            
            recent_commission_data = [{
                "id": record.id,
                "period": f"{record.settlement_period.start_date.strftime('%Y-%m-%d')} to {record.settlement_period.end_date.strftime('%Y-%m-%d')}",
                "amount": str(record.total_commission),
                "date": record.created_at.isoformat()
            } for record in recent_commissions]
            
            # 准备响应数据
            data = {
                "is_agent": True,
                "agent_info": {
                    "agent_id": request.user.agent_id,
                    "agent_since": agent_relationship.created_at.isoformat(),
                    "agent_level": {
                        "id": agent_level.id if agent_level else None,
                        "name": agent_level.name if agent_level else "None",
                        "level": agent_level.level if agent_level else 0,
                        "commission_rates": {
                            "level1": float(agent_level.level1_commission_rate) if agent_level else 0,
                            "level2": float(agent_level.level2_commission_rate) if agent_level else 0,
                            "level3": float(agent_level.level3_commission_rate) if agent_level else 0
                        }
                    } if agent_level else None,
                    "total_commission": str(request.user.total_commission)
                },
                "team_stats": {
                    "team_size": {
                        "level1": level1_count,
                        "level2": level2_count,
                        "level3": level3_count,
                        "total": level1_count + level2_count + level3_count
                    },
                    "current_period": current_period_info,
                    "current_production": {
                        "level1_swmt": level1_swmt,
                        "level2_swmt": level2_swmt,
                        "level3_swmt": level3_swmt,
                        "total_swmt": level1_swmt + level2_swmt + level3_swmt
                    },
                    "potential_commission": str(potential_commission)
                },
                "recent_commissions": recent_commission_data
            }
            
            # 缓存数据5分钟
            set_cache(cache_key, data, timeout=300)
            
            return ApiResponse(
                code=200,
                message=get_message('team_stats_success'),
                data=data
            )
        except Exception as e:
            logger.error(f"获取团队统计错误: {str(e)}", exc_info=True)
            return ApiResponse(
                code=500,
                message=get_message('error'),
                data=None
            )
    
    def _get_period_swmt(self, users, period_start, period_end):
        """Calculate total SWMT earned by users in a given period"""
        from tasks.models import TaskCompletionLog
        
        if not users.exists():
            return 0
        
        # Get all task completion logs for these users in the given period
        logs = TaskCompletionLog.objects.filter(
            user_task__user__in=users,
            created_at__gte=period_start,
            created_at__lte=period_end
        )
        
        # Sum up the SWMT rewards
        total_swmt = 0
        for log in logs:
            user_task = log.user_task
            if user_task:
                # Include base reward plus all bonuses
                total_swmt += (
                    user_task.base_swmt_reward +
                    user_task.vip_bonus_swmt +
                    user_task.additional_bonus_swmt
                )
        
        return total_swmt
    
    def _calculate_potential_commission(self, agent_level, level1_swmt, level2_swmt, level3_swmt):
        """Calculate potential commission based on current production"""
        if not agent_level:
            return 0
        
        # Calculate commission for each level
        level1_commission = level1_swmt * (agent_level.level1_commission_rate / 100)
        level2_commission = level2_swmt * (agent_level.level2_commission_rate / 100)
        level3_commission = level3_swmt * (agent_level.level3_commission_rate / 100)
        
        # Return total potential commission
        return level1_commission + level2_commission + level3_commission


@extend_schema(
    tags=["App - Agents"],
    summary="获取团队层级树状图",
    description="获取当前认证用户的团队层级结构，最多展示指定层级（默认为3层）。",
    parameters=[
        OpenApiParameter(name='max_depth', description='最大展示层级 (默认 3)', required=False, type=OpenApiTypes.INT)
    ],
    responses={
        200: inline_serializer( # Define specific response for this view
            name='TeamTreeResponse',
            fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': serializers.DictField() # Represents the tree structure
            }
        ),
         403: inline_serializer(
            name='TeamTreeNotAgentResponse',
            fields={
                'code': serializers.IntegerField(default=403),
                'message': serializers.CharField(),
                'data': serializers.DictField()
            }
        ),
        500: GenericErrorResponseSerializer,
    }
)
class TeamTreeView(APIView):
    """
    Team tree view
    Provides hierarchical structure of user's team
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get team hierarchy tree"""
        try:
            # Check if user is an agent
            if not request.user.is_agent:
                return ApiResponse(
                    code=403,
                    message="You are not an agent yet",
                    data={
                        "is_agent": False,
                        "requirement": {
                            "direct_referrals_needed": 3,
                            "current_referrals": request.user.referral_count
                        }
                    }
                )
            
            # Get query parameters
            max_depth = int(request.query_params.get('max_depth', 3))
            max_depth = min(max_depth, 3)  # Limit to maximum 3 levels
            
            # Get team tree
            tree = self._get_team_tree(request.user, max_depth)
            
            return ApiResponse(
                code=200,
                message="Team tree retrieved successfully",
                data=tree
            )
        except Exception as e:
            logger.error(f"Error retrieving team tree: {str(e)}", exc_info=True)
            return ApiResponse(
                code=500,
                message=f"An error occurred: {str(e)}",
                data=None
            )
    
    def _get_team_tree(self, user, max_depth=3, current_depth=1):
        """
        Recursively build team tree
        
        Args:
            user: The user to get team for
            max_depth: Maximum depth of tree to return
            current_depth: Current depth in the recursion
        
        Returns:
            dict: Tree structure of team
        """
        # Base user info
        user_info = {
            "user_id": user.user_id,
            "username": user.username,
            "email": user.email,
            "is_agent": user.is_agent,
            "referral_code": user.referral_code,
            "referral_count": user.referral_count,
            "join_date": user.created_at.isoformat()
        }
        
        # If at max depth, don't include children
        if current_depth >= max_depth:
            return user_info
        
        # Get direct referrals (level 1)
        referrals = User.objects.filter(referrer=user)
        
        # If no referrals, return without children
        if not referrals.exists():
            return user_info
        
        # Add children recursively
        user_info["children"] = []
        for referral in referrals:
            child_info = self._get_team_tree(referral, max_depth, current_depth + 1)
            user_info["children"].append(child_info)
        
        return user_info


@extend_schema(
    tags=["App - Agents"],
    summary="获取团队列表和结算信息",
    description="获取用户的直接下线列表、团队统计摘要以及结算预测信息。",
    responses={
        200: inline_serializer(
            name='TeamListResponse',
            fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': serializers.DictField() # Keep it generic for now
            }
        ),
        403: inline_serializer(
            name='TeamListNotAgentResponse',
            fields={
                'code': serializers.IntegerField(default=403),
                'message': serializers.CharField(),
                'data': serializers.DictField()
            }
        ),
        500: GenericErrorResponseSerializer,
    }
)
class TeamListView(APIView):
    """
    Team list view
    提供用户的直接下线列表及团队结算相关数据
    
    本API优化了数据结构，专注于展示：
    1. 用户当前代理等级和上次结算等级
    2. 直接下线成员的详细数据
    3. 二级和三级团队的摘要统计信息
    4. 结算预测和佣金估算
    
    该接口设计重点突出结算投档相关信息，帮助用户了解当前结算状态和升级路径。
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get paginated list of team members"""
        try:
            # Check if user is an agent
            if not request.user.is_agent:
                return ApiResponse(
                    code=403,
                    message="You are not an agent yet",
                    data={
                        "is_agent": False,
                        "requirement": {
                            "direct_referrals_needed": 3,
                            "current_referrals": request.user.referral_count
                        }
                    }
                )
            
            # Get query parameters
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 20))
            search = request.query_params.get('search', '')
            sort_by = request.query_params.get('sort_by', 'joined_at')
            order = request.query_params.get('order', 'desc')
            
            # Get user agent relationship and level information
            try:
                agent_relationship = AgentRelationship.objects.get(
                    user=request.user,
                    is_active=True
                )
                current_agent_level = agent_relationship.agent_level
            except AgentRelationship.DoesNotExist:
                current_agent_level = None
            
            # Get last settlement agent level
            try:
                latest_commission = CommissionRecord.objects.filter(
                    agent=request.user,
                    settlement_period__status='closed'
                ).order_by('-settlement_period__end_date').first()
                
                last_settlement_level = latest_commission.agent_level if latest_commission else None
            except Exception as e:
                logger.error(f"Error retrieving last settlement level: {str(e)}", exc_info=True)
                last_settlement_level = None
            
            # Get possible next level
            try:
                if current_agent_level:
                    next_level = AgentLevel.objects.filter(
                        level__gt=current_agent_level.level,
                        is_active=True
                    ).order_by('level').first()
                else:
                    next_level = AgentLevel.objects.filter(is_active=True).order_by('level').first()
                
                # Calculate additional SWMT needed for upgrade
                current_period = SettlementPeriod.objects.filter(status='active').first()
                if current_period and next_level:
                    current_team_swmt = self._get_period_swmt(request.user, current_period)
                    additional_swmt_needed = float(next_level.min_swmt_output) - current_team_swmt
                    additional_swmt_needed = max(0, additional_swmt_needed)
                else:
                    additional_swmt_needed = 0
            except Exception as e:
                logger.error(f"Error calculating next level requirements: {str(e)}", exc_info=True)
                next_level = None
                additional_swmt_needed = 0
            
            # Get direct members (level 1)
            direct_members_query = User.objects.filter(referrer=request.user)
            
            # Apply search filter if provided
            if search:
                direct_members_query = direct_members_query.filter(
                    Q(username__icontains=search) | Q(user_id__icontains=search)
                )
            
            # Apply sorting
            if sort_by == 'username':
                order_field = 'username' if order == 'asc' else '-username'
            elif sort_by == 'current_period_swmt':
                # This requires custom sorting after query
                order_field = 'created_at'  # Temporary, will be sorted after query
            else:  # Default to joined_at
                order_field = 'created_at' if order == 'asc' else '-created_at'
            
            direct_members = list(direct_members_query.order_by(order_field))
            
            # If sorting by SWMT, we need custom sorting after retrieval
            if sort_by == 'current_period_swmt':
                current_period = SettlementPeriod.objects.filter(status='active').first()
                if current_period:
                    # This would be optimized in a real implementation
                    for member in direct_members:
                        member.current_period_swmt = self._get_user_period_swmt(member, current_period)
                    
                    direct_members.sort(
                        key=lambda m: m.current_period_swmt,
                        reverse=(order == 'desc')
                    )
            
            # Calculate total and pagination
            total_direct_members = len(direct_members)
            total_pages = (total_direct_members + page_size - 1) // page_size
            
            # Apply pagination
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            paginated_members = direct_members[start_idx:end_idx]
            
            # Format direct members data
            current_period = SettlementPeriod.objects.filter(status='active').first()
            previous_period = SettlementPeriod.objects.filter(
                status='closed'
            ).order_by('-end_date').first()
            
            current_period_swmt = self._get_period_swmt(request.user, current_period) if current_period else 0
            previous_period_swmt = self._get_period_swmt(request.user, previous_period) if previous_period else 0
            
            # Calculate level 2 and 3 stats
            level_2_members = self._get_team_members(request.user, 2)
            level_3_members = self._get_team_members(request.user, 3)
            
            level_2_current_swmt = sum(self._get_user_period_swmt(m, current_period) for m in level_2_members) if current_period else 0
            level_2_previous_swmt = sum(self._get_user_period_swmt(m, previous_period) for m in level_2_members) if previous_period else 0
            
            level_3_current_swmt = sum(self._get_user_period_swmt(m, current_period) for m in level_3_members) if current_period else 0
            level_3_previous_swmt = sum(self._get_user_period_swmt(m, previous_period) for m in level_3_members) if previous_period else 0
            
            # Calculate settlement forecast
            days_remaining = 0
            progress_percentage = 0
            if current_period:
                now = timezone.now()
                total_days = (current_period.end_date - current_period.start_date).days
                days_elapsed = (now - current_period.start_date).days
                days_remaining = (current_period.end_date - now).days
                progress_percentage = round((days_elapsed / total_days) * 100, 1) if total_days > 0 else 0
            
            # Calculate estimated commission
            potential_commission = 0
            level_1_contribution = 0
            level_2_contribution = 0
            level_3_contribution = 0
            
            if current_agent_level:
                # Calculate level 1 commission (direct referrals)
                direct_members_swmt = sum(self._get_user_period_swmt(m, current_period) for m in paginated_members) if current_period else 0
                level_1_contribution = direct_members_swmt * float(current_agent_level.level1_commission_rate)
                
                # Calculate level 2 commission
                level_2_contribution = level_2_current_swmt * float(current_agent_level.level2_commission_rate)
                
                # Calculate level 3 commission
                level_3_contribution = level_3_current_swmt * float(current_agent_level.level3_commission_rate)
                
                potential_commission = level_1_contribution + level_2_contribution + level_3_contribution
            
            # Get previous commission for comparison
            previous_commission_total = 0
            if previous_period:
                try:
                    previous_commission = CommissionRecord.objects.filter(
                        agent=request.user,
                        settlement_period=previous_period
                    ).first()
                    
                    if previous_commission:
                        previous_commission_total = float(previous_commission.total_commission)
                except Exception as e:
                    logger.error(f"Error retrieving previous commission: {str(e)}", exc_info=True)
            
            # Calculate comparison percentage
            change_percentage = 0
            if previous_commission_total > 0:
                change_percentage = round(((potential_commission - previous_commission_total) / previous_commission_total) * 100, 1)
            
            # Format direct members for response
            member_data = []
            for member in paginated_members:
                member_swmt = self._get_user_period_swmt(member, current_period) if current_period else 0
                member_info = {
                    "user_id": member.user_id,
                    "username": member.username,
                    "is_agent": member.is_agent,
                    "joined_at": member.created_at.isoformat(),
                    "current_period_swmt": str(member_swmt)
                }
                member_data.append(member_info)
            
            # Format response
            response_data = {
                "user_agent_info": {
                    "is_agent": request.user.is_agent
                },
                "direct_members": {
                    "total_count": total_direct_members,
                    "current_period_swmt": str(current_period_swmt),
                    "previous_period_swmt": str(previous_period_swmt),
                    "members": member_data
                },
                "team_stats": {
                    "level_2": {
                        "total_members": len(level_2_members),
                        "current_period_swmt": str(level_2_current_swmt),
                        "previous_period_swmt": str(level_2_previous_swmt),
                        "trend_percentage": round(((level_2_current_swmt - level_2_previous_swmt) / level_2_previous_swmt) * 100, 1) if level_2_previous_swmt > 0 else 0
                    },
                    "level_3": {
                        "total_members": len(level_3_members),
                        "current_period_swmt": str(level_3_current_swmt),
                        "previous_period_swmt": str(level_3_previous_swmt),
                        "trend_percentage": round(((level_3_current_swmt - level_3_previous_swmt) / level_3_previous_swmt) * 100, 1) if level_3_previous_swmt > 0 else 0
                    }
                },
                "settlement_forecast": {
                    "current_period": {
                        "id": current_period.id if current_period else None,
                        "days_remaining": days_remaining,
                        "progress_percentage": progress_percentage
                    },
                    "estimated_commission": {
                        "total": str(round(potential_commission, 2)),
                        "level_1_contribution": str(round(level_1_contribution, 2)),
                        "level_2_contribution": str(round(level_2_contribution, 2)),
                        "level_3_contribution": str(round(level_3_contribution, 2))
                    },
                    "comparison": {
                        "previous_total": str(round(previous_commission_total, 2)),
                        "change_percentage": change_percentage
                    }
                },
                "pagination": {
                    "total_direct_members": total_direct_members,
                    "total_pages": total_pages,
                    "current_page": page,
                    "page_size": page_size
                }
            }
            
            # Add agent level details if available
            if current_agent_level:
                response_data["user_agent_info"]["current_level"] = {
                    "id": current_agent_level.id,
                    "name": current_agent_level.name,
                    "level": current_agent_level.level,
                    "commission_rates": {
                        "level_1": float(current_agent_level.level1_commission_rate),
                        "level_2": float(current_agent_level.level2_commission_rate),
                        "level_3": float(current_agent_level.level3_commission_rate)
                    }
                }
            
            # Add last settlement level if available
            if last_settlement_level:
                response_data["user_agent_info"]["last_settlement_level"] = {
                    "id": last_settlement_level.id,
                    "name": last_settlement_level.name,
                    "level": last_settlement_level.level
                }
            
            # Add next possible level if available
            if next_level:
                response_data["user_agent_info"]["next_possible_level"] = {
                    "id": next_level.id,
                    "name": next_level.name,
                    "level": next_level.level,
                    "additional_swmt_needed": round(additional_swmt_needed, 1)
                }
            
            return ApiResponse(
                code=200,
                message="Team members retrieved successfully",
                data=response_data
            )
        except Exception as e:
            logger.error(f"Error retrieving team list: {str(e)}", exc_info=True)
            return ApiResponse(
                code=500,
                message=f"An error occurred: {str(e)}",
                data=None
            )
    
    def _get_team_members(self, user, level):
        """Get team members at specified level"""
        if level == 1:
            # Direct referrals
            return list(User.objects.filter(referrer=user).order_by('-created_at'))
        elif level == 2:
            # Level 2 referrals
            direct_referrals = User.objects.filter(referrer=user)
            return list(User.objects.filter(referrer__in=direct_referrals).order_by('-created_at'))
        elif level == 3:
            # Level 3 referrals
            direct_referrals = User.objects.filter(referrer=user)
            level2_referrals = User.objects.filter(referrer__in=direct_referrals)
            return list(User.objects.filter(referrer__in=level2_referrals).order_by('-created_at'))
        else:
            return []
    
    def _get_period_swmt(self, user, period):
        """Get total SWMT earned by user's team in a given period"""
        if not period:
            return 0
            
        try:
            # Get direct referrals (level 1)
            level1_users = self._get_team_members(user, 1)
            level1_swmt = sum(self._get_user_period_swmt(u, period) for u in level1_users)
            
            # Get level 2 referrals
            level2_users = self._get_team_members(user, 2)
            level2_swmt = sum(self._get_user_period_swmt(u, period) for u in level2_users)
            
            # Get level 3 referrals
            level3_users = self._get_team_members(user, 3)
            level3_swmt = sum(self._get_user_period_swmt(u, period) for u in level3_users)
            
            return level1_swmt + level2_swmt + level3_swmt
        except Exception as e:
            logger.error(f"Error calculating period SWMT: {str(e)}", exc_info=True)
            return 0
    
    def _get_user_period_swmt(self, user, period):
        """Get SWMT earned by a user in a given period"""
        if not period:
            return 0
            
        try:
            # This would need to be replaced with actual logic to fetch user's SWMT production
            # For now, using a random value for illustration
            from random import uniform
            return round(uniform(10, 250), 2)
        except Exception as e:
            logger.error(f"Error getting user period SWMT: {str(e)}", exc_info=True)
            return 0


@extend_schema(
    tags=["App - Agents"],
    summary="获取佣金历史记录",
    description="获取当前用户的佣金结算历史记录，支持分页和按结算周期过滤。",
    parameters=[
        OpenApiParameter(name='period_id', description='按结算周期ID过滤', required=False, type=OpenApiTypes.INT),
        OpenApiParameter(name='page', description='页码', required=False, type=OpenApiTypes.INT),
        OpenApiParameter(name='page_size', description='每页条数', required=False, type=OpenApiTypes.INT),
    ],
    responses={
        200: inline_serializer(
            name='CommissionHistoryResponse',
             fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': serializers.DictField() # Contains pagination and results
            }
        ),
        500: GenericErrorResponseSerializer,
    }
)
class CommissionHistoryView(APIView):
    """佣金历史视图"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取佣金历史记录"""
        try:
            # 检查用户是否是代理
            if not request.user.is_agent:
                return ApiResponse(
                    code=403,
                    message=get_message('agent_not_eligible'),
                    data={
                        "is_agent": False,
                        "requirement": {
                            "direct_referrals_needed": 3,
                            "current_referrals": request.user.referral_count
                        }
                    }
                )
            
            # 获取查询参数
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 10))
            
            # 获取佣金记录
            records = CommissionRecord.objects.filter(
                agent=request.user
            ).select_related('settlement_period', 'agent_level').order_by('-created_at')
            
            # 计算分页
            total_records = records.count()
            total_pages = (total_records + page_size - 1) // page_size
            
            # 应用分页
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            paginated_records = records[start_idx:end_idx]
            
            # 格式化响应
            record_data = []
            for record in paginated_records:
                record_info = {
                    "id": record.id,
                    "period": {
                        "id": record.settlement_period.id,
                        "start_date": record.settlement_period.start_date.isoformat(),
                        "end_date": record.settlement_period.end_date.isoformat()
                    },
                    "agent_level": {
                        "id": record.agent_level.id,
                        "name": record.agent_level.name,
                        "level": record.agent_level.level
                    },
                    "team_stats": {
                        "level1_members": record.level1_members,
                        "level2_members": record.level2_members,
                        "level3_members": record.level3_members,
                        "total_members": record.level1_members + record.level2_members + record.level3_members
                    },
                    "swmt_production": {
                        "level1": record.level1_swmt,
                        "level2": record.level2_swmt,
                        "level3": record.level3_swmt,
                        "total": record.level1_swmt + record.level2_swmt + record.level3_swmt
                    },
                    "commission": {
                        "level1": str(record.level1_commission),
                        "level2": str(record.level2_commission),
                        "level3": str(record.level3_commission),
                        "total": str(record.total_commission)
                    },
                    "date": record.created_at.isoformat()
                }
                record_data.append(record_info)
            
            # 获取统计数据
            stats = self._get_commission_stats(request.user)
            
            # 构造响应数据
            response_data = {
                "stats": stats,
                "records": record_data,
                "pagination": {
                    "total": total_records,
                    "total_pages": total_pages,
                    "current_page": page,
                    "page_size": len(record_data)
                }
            }
            
            return ApiResponse(
                code=200,
                message=get_message('commission_received'),
                data=response_data
            )
        except Exception as e:
            logger.error(f"获取佣金历史错误: {str(e)}", exc_info=True)
            return ApiResponse(
                code=500,
                message=get_message('error'),
                data=None
            )
    
    def _get_commission_stats(self, user):
        """Get commission statistics"""
        # Total commission
        total_commission = user.total_commission
        
        # Settlement count
        settlement_count = CommissionRecord.objects.filter(agent=user).count()
        
        # Get highest single commission
        highest_commission = CommissionRecord.objects.filter(agent=user).order_by('-total_commission').first()
        highest_amount = highest_commission.total_commission if highest_commission else 0
        
        # Get recent trend (last 3 months)
        three_months_ago = timezone.now() - datetime.timedelta(days=90)
        recent_commissions = CommissionRecord.objects.filter(
            agent=user,
            created_at__gte=three_months_ago
        ).order_by('created_at')
        
        trend_data = [{
            "date": record.created_at.strftime('%Y-%m-%d'),
            "amount": str(record.total_commission)
        } for record in recent_commissions]
        
        return {
            "total_commission": str(total_commission),
            "settlement_count": settlement_count,
            "highest_single_commission": str(highest_amount),
            "recent_trend": trend_data
        }


@extend_schema(
    tags=["App - Agents"],
    summary="获取当前结算周期信息和潜在佣金",
    description="获取当前待结算周期的详细信息，包括团队产出、预计佣金和下一个结算日期。",
     responses={
        200: inline_serializer(
            name='CurrentCommissionResponse',
             fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': serializers.DictField()
            }
        ),
         403: inline_serializer(
            name='CurrentCommissionNotAgentResponse',
            fields={
                'code': serializers.IntegerField(default=403),
                'message': serializers.CharField(),
                'data': serializers.DictField()
            }
        ),
        500: GenericErrorResponseSerializer,
    }
)
class CurrentCommissionView(APIView):
    """
    Current commission view
    Provides details about the current settlement period and potential commission
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get current settlement period details"""
        try:
            # Check if user is an agent
            if not request.user.is_agent:
                return ApiResponse(
                    code=403,
                    message="You are not an agent yet",
                    data={
                        "is_agent": False,
                        "requirement": {
                            "direct_referrals_needed": 3,
                            "current_referrals": request.user.referral_count
                        }
                    }
                )
            
            # Get agent relationship
            agent_relationship = get_object_or_404(
                AgentRelationship, 
                user=request.user,
                is_active=True
            )
            
            # Check if settlement is allowed
            if agent_relationship.settlement_status != 'active':
                return ApiResponse(
                    code=403,
                    message="Your settlement is currently stopped",
                    data={
                        "settlement_status": {
                            "status": agent_relationship.settlement_status,
                            "reason": agent_relationship.settlement_status_reason,
                            "stopped_at": agent_relationship.settlement_stopped_at.isoformat() if agent_relationship.settlement_stopped_at else None
                        }
                    }
                )
            
            # Get current settlement period
            current_period = SettlementPeriod.objects.filter(
                status='pending'
            ).order_by('-start_date').first()
            
            if not current_period:
                return ApiResponse(
                    code=404,
                    message="No active settlement period found",
                    data=None
                )
            
            # Get team members
            team_stats_view = TeamStatsView()
            
            # Get direct (level 1) team members
            level1_members = User.objects.filter(referrer=request.user)
            level1_count = level1_members.count()
            
            # Get level 2 team members
            level2_members = User.objects.filter(referrer__in=level1_members)
            level2_count = level2_members.count()
            
            # Get level 3 team members
            level3_members = User.objects.filter(referrer__in=level2_members)
            level3_count = level3_members.count()
            
            # Get agent level
            agent_level = agent_relationship.agent_level
            
            # Calculate current period stats
            period_start = current_period.start_date
            period_end = current_period.end_date
            
            # Calculate SWMT production in current period for each level
            level1_swmt = team_stats_view._get_period_swmt(level1_members, period_start, period_end)
            level2_swmt = team_stats_view._get_period_swmt(level2_members, period_start, period_end)
            level3_swmt = team_stats_view._get_period_swmt(level3_members, period_start, period_end)
            
            # Get eligible agent levels
            eligible_levels = self._get_eligible_agent_levels(level1_count, level1_swmt)
            
            # Calculate potential commission for each eligible level
            potential_commissions = []
            for level in eligible_levels:
                level1_commission = level1_swmt * (level.level1_commission_rate / 100)
                level2_commission = level2_swmt * (level.level2_commission_rate / 100)
                level3_commission = level3_swmt * (level.level3_commission_rate / 100)
                total_commission = level1_commission + level2_commission + level3_commission
                
                potential_commissions.append({
                    "level": {
                        "id": level.id,
                        "name": level.name,
                        "level": level.level,
                        "commission_rates": {
                            "level1": float(level.level1_commission_rate),
                            "level2": float(level.level2_commission_rate),
                            "level3": float(level.level3_commission_rate)
                        }
                    },
                    "commission": {
                        "level1": str(level1_commission),
                        "level2": str(level2_commission),
                        "level3": str(level3_commission),
                        "total": str(total_commission)
                    }
                })
            
            # Sort potential commissions by total amount (highest first)
            potential_commissions.sort(key=lambda x: float(x["commission"]["total"]), reverse=True)
            
            # Format response
            response_data = {
                "period": {
                    "id": current_period.id,
                    "start_date": current_period.start_date.isoformat(),
                    "end_date": current_period.end_date.isoformat(),
                    "status": current_period.status,
                    "days_remaining": (current_period.end_date - timezone.now()).days + 1
                },
                "team_stats": {
                    "members": {
                        "level1": level1_count,
                        "level2": level2_count,
                        "level3": level3_count,
                        "total": level1_count + level2_count + level3_count
                    },
                    "swmt_production": {
                        "level1": level1_swmt,
                        "level2": level2_swmt,
                        "level3": level3_swmt,
                        "total": level1_swmt + level2_swmt + level3_swmt
                    }
                },
                "current_agent_level": {
                    "id": agent_level.id if agent_level else None,
                    "name": agent_level.name if agent_level else "None",
                    "level": agent_level.level if agent_level else 0,
                    "commission_rates": {
                        "level1": float(agent_level.level1_commission_rate) if agent_level else 0,
                        "level2": float(agent_level.level2_commission_rate) if agent_level else 0,
                        "level3": float(agent_level.level3_commission_rate) if agent_level else 0
                    }
                } if agent_level else None,
                "potential_commissions": potential_commissions,
                "highest_potential_commission": potential_commissions[0] if potential_commissions else None,
                "next_settlement_date": self._get_next_settlement_date().isoformat()
            }
            
            return ApiResponse(
                code=200,
                message="Current commission details retrieved successfully",
                data=response_data
            )
        except Exception as e:
            logger.error(f"Error retrieving current commission: {str(e)}", exc_info=True)
            return ApiResponse(
                code=500,
                message=f"An error occurred: {str(e)}",
                data=None
            )
    
    def _get_eligible_agent_levels(self, direct_members_count, level1_swmt):
        """Get eligible agent levels based on direct members count and SWMT production"""
        # Get all active agent levels
        all_levels = AgentLevel.objects.filter(is_active=True).order_by('level')
        
        # Filter levels by required direct members
        eligible_levels = [
            level for level in all_levels 
            if level.required_direct_members <= direct_members_count
        ]
        
        # Further filter by SWMT output range
        eligible_levels = [
            level for level in eligible_levels
            if level.min_swmt_output <= level1_swmt and (level.max_swmt_output >= level1_swmt or level1_swmt == 0)
        ]
        
        return eligible_levels
    
    def _get_next_settlement_date(self):
        """Calculate the next settlement date (next Monday)"""
        today = timezone.now().date()
        days_until_monday = (7 - today.weekday()) % 7
        if days_until_monday == 0:
            days_until_monday = 7  # If today is Monday, get next Monday
        
        next_monday = today + datetime.timedelta(days=days_until_monday)
        return datetime.datetime.combine(next_monday, datetime.time.min)


@extend_schema(
    tags=["App - Agents"],
    summary="获取代理等级列表",
    description="获取系统中所有定义的代理等级及其详细信息（名称、等级、佣金率、升级条件）。",
    responses={
        200: inline_serializer(
            name='AgentLevelListResponse',
             fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': serializers.ListField(child=serializers.DictField())
            }
        ),
        500: GenericErrorResponseSerializer,
    }
)
class AgentLevelListView(APIView):
    """代理等级列表视图"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取代理等级列表"""
        cache_key = "agent_levels_list"
        cached_data = get_cache(cache_key)
        
        if cached_data:
            return ApiResponse(
                code=200,
                message=get_message('success'),
                data=cached_data
            )
        
        try:
            # 获取所有活跃的代理等级
            levels = AgentLevel.objects.filter(is_active=True).order_by('level')
            
            # 获取当前用户的直接成员数量
            direct_members_count = User.objects.filter(referrer=request.user).count()
            
            # 格式化响应
            level_data = []
            for level in levels:
                level_info = {
                    "id": level.id,
                    "name": level.name,
                    "level": level.level,
                    "requirements": {
                        "direct_members": level.required_direct_members,
                        "swmt_output": {
                            "min": level.min_swmt_output,
                            "max": level.max_swmt_output
                        }
                    },
                    "commission_rates": {
                        "level1": float(level.level1_commission_rate),
                        "level2": float(level.level2_commission_rate),
                        "level3": float(level.level3_commission_rate)
                    },
                    "status": {
                        "direct_members_met": direct_members_count >= level.required_direct_members,
                        "direct_members_progress": min(direct_members_count, level.required_direct_members),
                        "direct_members_remaining": max(0, level.required_direct_members - direct_members_count)
                    }
                }
                level_data.append(level_info)
            
            response_data = {
                "levels": level_data,
                "user_status": {
                    "is_agent": request.user.is_agent,
                    "direct_members": direct_members_count,
                    "referral_code": request.user.referral_code
                }
            }
            
            # 缓存6小时（等级不经常变化）
            set_cache(cache_key, response_data, timeout=21600)
            
            return ApiResponse(
                code=200,
                message=get_message('success'),
                data=response_data
            )
        except Exception as e:
            logger.error(f"获取代理等级列表错误: {str(e)}", exc_info=True)
            return ApiResponse(
                code=500,
                message=get_message('error'),
                data=None
            )


@extend_schema(
    tags=["App - Agents"],
    summary="获取用户邀请码信息",
    description="获取当前认证用户的邀请码、邀请链接以及相关的分享信息。",
    responses={
        200: inline_serializer(
            name='InviteCodeResponse',
             fields={
                'code': serializers.IntegerField(default=200),
                'message': serializers.CharField(),
                'data': serializers.DictField()
            }
        ),
        500: GenericErrorResponseSerializer,
    }
)
class InviteCodeView(APIView):
    """
    Invite code view
    Provides user's referral code and sharing information
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get user's referral code and sharing information"""
        try:
            # Ensure user has a referral code
            if not request.user.referral_code:
                request.user.referral_code = request.user.generate_referral_code()
                request.user.save(update_fields=['referral_code'])
            
            # Get referral statistics
            direct_referrals = User.objects.filter(referrer=request.user).count()
            
            # Get app download URLs
            # These should be configured in settings or environment variables
            app_download = {
                "android": "https://play.google.com/store/apps/details?id=com.sweatmint.app",
                "ios": "https://apps.apple.com/app/sweatmint/id123456789",
                "website": "https://sweatmint.com/download"
            }
            
            # Get sharing text templates
            sharing_templates = {
                "sms": f"Join SweatMint and earn rewards for your fitness activities! Use my invite code {request.user.referral_code} when you sign up: {app_download['website']}",
                "email": f"Hi there,\n\nI've been using SweatMint to earn rewards for my fitness activities and thought you might like it too.\n\nUse my invite code {request.user.referral_code} when you sign up to get a bonus reward!\n\nDownload the app here: {app_download['website']}\n\nEnjoy!",
                "social": f"I'm earning rewards for staying fit with SweatMint! Join me using my invite code {request.user.referral_code} and get a bonus reward. Download now: {app_download['website']} #SweatMint #FitnessRewards"
            }
            
            # Format response
            response_data = {
                "referral_code": request.user.referral_code,
                "statistics": {
                    "direct_referrals": direct_referrals,
                    "is_agent": request.user.is_agent
                },
                "requirements": {
                    "agent_qualification": 3,
                    "remaining_for_agent": max(0, 3 - direct_referrals)
                },
                "app_download": app_download,
                "sharing_templates": sharing_templates,
                "sharing_url": f"https://sweatmint.com/register?ref={request.user.referral_code}"
            }
            
            return ApiResponse(
                code=200,
                message="Invite code retrieved successfully",
                data=response_data
            )
        except Exception as e:
            logger.error(f"Error retrieving invite code: {str(e)}", exc_info=True)
            return ApiResponse(
                code=500,
                message=f"An error occurred: {str(e)}",
                data=None
            ) 