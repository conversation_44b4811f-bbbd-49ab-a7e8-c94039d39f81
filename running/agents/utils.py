"""代理系统工具函数"""

import logging
from decimal import Decimal
from django.db.models import Sum, Q, Count, F
from django.core.cache import cache
from users.models import User
import hashlib
import json
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

def generate_cache_key(prefix, user_id, **kwargs):
    """生成统一的缓存键
    
    Args:
        prefix: 缓存前缀
        user_id: 用户ID
        **kwargs: 其他键值参数
        
    Returns:
        str: 生成的缓存键
    """
    # 将参数排序并序列化，确保相同参数生成相同的键
    kwargs_str = json.dumps(kwargs, sort_keys=True)
    key_data = f"{prefix}_{user_id}_{kwargs_str}"
    return f"agent_{hashlib.md5(key_data.encode()).hexdigest()}"

def count_level_members(user, level, use_cache=True):
    """计算指定级别的下线数量
    
    Args:
        user: 用户对象
        level: 下线级别(1-3)
        use_cache: 是否使用缓存
        
    Returns:
        int: 下线数量
    """
    if not user:
        return 0
        
    # 生成缓存键
    cache_key = generate_cache_key("count_members", user.id, level=level)
    
    # 如果启用缓存且缓存中存在数据，直接返回
    if use_cache:
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            logger.debug(f"使用缓存数据: {cache_key} = {cached_result}")
            return cached_result
    
    count = 0
    # 优化查询：使用更高效的查询方法避免多次JOIN
    if level == 1:
        # 直接下线查询，使用count()直接在数据库层面计算
        count = User.objects.filter(referrer=user).count()
    elif level == 2:
        # 二级下线查询: 先获取一级下线ID，再查询他们的下线
        level1_ids = User.objects.filter(referrer=user).values_list('id', flat=True)
        if level1_ids:
            count = User.objects.filter(referrer_id__in=level1_ids).count()
    elif level == 3:
        # 三级下线查询: 先获取二级下线ID，再查询他们的下线
        level1_ids = User.objects.filter(referrer=user).values_list('id', flat=True)
        if level1_ids:
            level2_ids = User.objects.filter(referrer_id__in=level1_ids).values_list('id', flat=True)
            if level2_ids:
                count = User.objects.filter(referrer_id__in=level2_ids).count()
    
    # 缓存结果，有效期1小时
    if use_cache:
        cache.set(cache_key, count, 3600)  # 3600秒 = 1小时
        
    return count

def calculate_level_swmt(user, level, start_date, end_date, use_cache=True):
    """计算指定级别下线的SWMT产出
    
    包括：
    1. 基础任务奖励
    2. VIP加成奖励
    3. 附加任务加成奖励
    
    Args:
        user: 用户对象
        level: 下线级别(1-3)
        start_date: 开始日期
        end_date: 结束日期
        use_cache: 是否使用缓存
        
    Returns:
        Decimal: SWMT产出
    """
    from tasks.models import UserTask
    
    if not user:
        return Decimal('0')
    
    # 生成缓存键
    cache_key = generate_cache_key(
        "calculate_swmt", 
        user.id, 
        level=level, 
        start=start_date.isoformat(), 
        end=end_date.isoformat()
    )
    
    # 如果启用缓存且缓存中存在数据，直接返回
    if use_cache:
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            logger.debug(f"使用缓存数据: {cache_key} = {cached_result}")
            return Decimal(str(cached_result))
    
    # 优化查询策略：使用批量ID查询代替复杂JOIN操作
    downline_ids = []
    
    if level == 1:
        # 获取一级下线用户ID
        downline_ids = User.objects.filter(referrer=user).values_list('id', flat=True)
    elif level == 2:
        # 获取二级下线用户ID：先查一级，再查二级
        level1_ids = User.objects.filter(referrer=user).values_list('id', flat=True)
        if level1_ids:
            downline_ids = User.objects.filter(referrer_id__in=level1_ids).values_list('id', flat=True)
    elif level == 3:
        # 获取三级下线用户ID：先查一级，再查二级，再查三级
        level1_ids = User.objects.filter(referrer=user).values_list('id', flat=True)
        if level1_ids:
            level2_ids = User.objects.filter(referrer_id__in=level1_ids).values_list('id', flat=True)
            if level2_ids:
                downline_ids = User.objects.filter(referrer_id__in=level2_ids).values_list('id', flat=True)
    
    # 将QuerySet转为列表
    downline_ids = list(downline_ids)
    logger.info(f"计算{level}级下线SWMT产出: 下线用户数={len(downline_ids)}")
    
    # 如果没有下线，直接返回0
    if not downline_ids:
        logger.info(f"没有{level}级下线，返回0")
        # 缓存结果
        if use_cache:
            cache.set(cache_key, '0', 3600)  # 3600秒 = 1小时
        return Decimal('0')
    
    # 使用聚合查询一次性获取总奖励，避免逐条计算
    total_rewards = UserTask.objects.filter(
        user_id__in=downline_ids,
        status='completed',
        assignment_date__gte=start_date.date(),
        assignment_date__lte=end_date.date()
    ).aggregate(
        base_sum=Sum('base_swmt_reward') or Decimal('0'),
        vip_sum=Sum('vip_bonus_swmt') or Decimal('0'),
        addon_sum=Sum('additional_bonus_swmt') or Decimal('0')
    )
    
    # 计算总SWMT
    base_sum = total_rewards.get('base_sum') or Decimal('0')
    vip_sum = total_rewards.get('vip_sum') or Decimal('0')
    addon_sum = total_rewards.get('addon_sum') or Decimal('0')
    
    total_swmt = base_sum + vip_sum + addon_sum
    
    logger.debug(f"计算{level}级下线SWMT产出: 基础={base_sum}, VIP加成={vip_sum}, 附加任务加成={addon_sum}, 总计={total_swmt}")
    
    # 缓存结果，有效期1小时
    if use_cache:
        cache.set(cache_key, str(total_swmt), 3600)  # 3600秒 = 1小时
    
    return total_swmt 

def format_decimal(value, decimal_places=2):
    """格式化小数到指定位数
    
    Args:
        value: 要格式化的值，可以是Decimal, float, int或None
        decimal_places: 小数位数，默认为2
        
    Returns:
        str: 格式化后的字符串，例如"123.45"
    """
    if value is None:
        return f"0.{decimal_places * '0'}"
    
    try:
        # 转换为Decimal以确保精确计算
        decimal_value = Decimal(str(value))
        
        # 格式化为字符串
        format_str = f"{{:.{decimal_places}f}}"
        return format_str.format(float(decimal_value))
    except (ValueError, TypeError, AttributeError):
        # 如果无法转换，返回默认值
        return f"0.{decimal_places * '0'}" 

def invalidate_agent_cache(user):
    """使指定用户的缓存失效
    
    当用户数据变化时调用此函数清除相关缓存
    
    Args:
        user: 用户对象
    """
    # 清除下线人数缓存
    for level in range(1, 4):
        cache_key = generate_cache_key("count_members", user.id, level=level)
        cache.delete(cache_key)
    
    # 清除团队统计相关缓存
    team_stats_key = f"agent_team_stats_{user.id}"
    cache.delete(team_stats_key)
    
    # 清除投档等级相关缓存
    # 这可能有多个基于不同日期范围的缓存，但很难全部清除
    # 针对最近一周和一个月的数据进行清除
    now = datetime.now()
    
    # 尝试清除最近一周的投档缓存
    week_start = (now - timedelta(days=7)).isoformat()
    week_end = now.isoformat()
    week_cache_key = generate_cache_key(
        "determine_level", 
        user.id, 
        start=week_start, 
        end=week_end
    )
    cache.delete(week_cache_key)
    
    # 尝试清除最近一个月的投档缓存
    month_start = (now - timedelta(days=30)).isoformat()
    month_cache_key = generate_cache_key(
        "determine_level", 
        user.id, 
        start=month_start, 
        end=week_end
    )
    cache.delete(month_cache_key)
    
    # 注意：SWMT产出缓存因有日期参数较为复杂，不方便全部清除
    # 可以考虑在关键时间点（如每日零点或结算周期开始）调用专门清理缓存的方法
    logger.debug(f"已清除用户 {user.email} 的代理缓存")
    
    # 如果用户有推荐人且推荐人是代理，也清除推荐人的缓存
    # 这确保当用户关系变化时，上层代理的统计数据也会更新
    if user.referrer and user.referrer.is_agent:
        logger.debug(f"级联清除推荐人 {user.referrer.email} 的代理缓存")
        invalidate_agent_cache(user.referrer) 