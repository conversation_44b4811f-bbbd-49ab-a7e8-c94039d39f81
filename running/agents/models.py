from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.utils import timezone
from datetime import datetime
from django.contrib import admin
from django.utils.html import format_html
import logging
from users.models import User

logger = logging.getLogger(__name__)


class AgentLevel(models.Model):
    """代理等级配置"""
    name = models.CharField(_('等级名称'), max_length=50)
    level = models.PositiveIntegerField(_('代理等级'), unique=True)
    required_direct_members = models.PositiveIntegerField(_('所需直接下级数'))
    min_swmt_output = models.PositiveIntegerField(_('最小SWMT产出'))
    max_swmt_output = models.PositiveIntegerField(_('最大SWMT产出'))
    level1_commission_rate = models.DecimalField(_('一级佣金比例%'), max_digits=5, decimal_places=2)
    level2_commission_rate = models.DecimalField(_('二级佣金比例%'), max_digits=5, decimal_places=2)
    level3_commission_rate = models.DecimalField(_('三级佣金比例%'), max_digits=5, decimal_places=2)
    is_active = models.BooleanField(_('是否启用'), default=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)

    class Meta:
        verbose_name = _('代理等级')
        verbose_name_plural = _('代理等级')
        ordering = ['level']

    def __str__(self):
        return self.name


class AgentRelationship(models.Model):
    """代理关系模型"""
    SETTLEMENT_STATUS_CHOICES = [
        ('active', '允许结算'),
        ('stopped', '停止结算')
    ]

    user = models.ForeignKey('users.User', on_delete=models.CASCADE, related_name='agent_relationship')
    referrer = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='agent_referrals', verbose_name='推荐人')
    agent_level = models.ForeignKey('AgentLevel', on_delete=models.PROTECT, null=True, blank=True)
    is_active = models.BooleanField('是否激活', default=True)
    settlement_status = models.CharField(
        '结算状态',
        max_length=20,
        choices=SETTLEMENT_STATUS_CHOICES,
        default='active',
        help_text='代理的结算状态，停止结算后将不再参与佣金结算'
    )
    settlement_stopped_at = models.DateTimeField('停止结算时间', null=True, blank=True)
    settlement_restored_at = models.DateTimeField('恢复结算时间', null=True, blank=True)
    settlement_status_reason = models.TextField('状态变更原因', blank=True)
    direct_member_count = models.PositiveIntegerField('直接下级数量', default=0)
    total_commission = models.DecimalField('累计佣金', max_digits=18, decimal_places=8, default=0)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '代理关系'
        verbose_name_plural = '代理关系'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.email} - {self.agent_level.name}"

    def stop_settlement(self, reason: str):
        """停止结算"""
        self.settlement_status = 'stopped'
        self.settlement_stopped_at = timezone.now()
        self.settlement_status_reason = reason
        self.save()

    def restore_settlement(self, reason: str):
        """恢复结算"""
        self.settlement_status = 'active'
        self.settlement_restored_at = timezone.now()
        self.settlement_status_reason = reason
        self.save()

    def can_settle(self, period_start: datetime, period_end: datetime) -> bool:
        """检查在指定时间段内是否可以结算
        
        Args:
            period_start: 结算周期开始时间
            period_end: 结算周期结束时间
            
        Returns:
            bool: 是否可以结算
        """
        if self.settlement_status == 'stopped':
            return False
            
        if self.settlement_restored_at and self.settlement_restored_at > period_start:
            # 如果在结算周期内恢复，只计算恢复后的部分
            return True
            
        return self.settlement_status == 'active'

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)  # 调用父类的 save 方法
        # 检查下线人数并自动升级代理等级
        if self.user:
            downline_count = User.objects.filter(referrer=self.user).count()
            if downline_count >= self.agent_level.required_direct_members:
                self.user.is_agent = True
                self.user.agent_level = self.agent_level
                self.user.save()
                logger.info(f"用户 {self.user.email} 自动升级为代理，当前下线人数: {downline_count}")


class SettlementPeriod(models.Model):
    """结算周期"""
    STATUS_CHOICES = [
        ('pending', _('待结算')),
        ('processing', _('结算中')),
        ('completed', _('已完成')),
        ('failed', _('失败')),
    ]

    start_date = models.DateTimeField(_('开始时间'))
    end_date = models.DateTimeField(_('结束时间'))
    status = models.CharField(_('状态'), max_length=20, choices=STATUS_CHOICES, default='pending')
    processed_at = models.DateTimeField(_('开始处理时间'), null=True, blank=True)
    total_commission = models.DecimalField(_('总佣金'), max_digits=10, decimal_places=2, default=0)
    agent_count = models.PositiveIntegerField(_('代理数量'), default=0)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)

    class Meta:
        verbose_name = _('结算周期')
        verbose_name_plural = _('结算周期')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.start_date.strftime('%Y-%m-%d')} - {self.end_date.strftime('%Y-%m-%d')}"
        
    def set_status(self, status):
        """设置结算周期状态，并自动处理相关字段
        
        Args:
            status: 新状态，如'processing', 'completed', 'failed'
            
        Returns:
            bool: 是否成功设置状态
        """
        if status not in [s[0] for s in self.STATUS_CHOICES]:
            return False
            
        old_status = self.status
        self.status = status
        
        # 如果状态从非processing变为processing，则设置处理开始时间
        if status == 'processing' and old_status != 'processing':
            self.processed_at = timezone.now()
            
        self.save(update_fields=['status', 'processed_at', 'updated_at'])
        return True


class CommissionRecord(models.Model):
    """佣金记录"""
    agent = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE,
                            related_name='commission_records', verbose_name=_('代理'))
    settlement_period = models.ForeignKey(SettlementPeriod, on_delete=models.CASCADE,
                                        related_name='commission_records', verbose_name=_('结算周期'))
    agent_level = models.ForeignKey('AgentLevel', on_delete=models.PROTECT, verbose_name=_('代理等级'))
    level1_members = models.PositiveIntegerField(_('一级成员数'))
    level2_members = models.PositiveIntegerField(_('二级成员数'))
    level3_members = models.PositiveIntegerField(_('三级成员数'))
    level1_swmt = models.PositiveIntegerField(_('一级SWMT'))
    level2_swmt = models.PositiveIntegerField(_('二级SWMT'))
    level3_swmt = models.PositiveIntegerField(_('三级SWMT'))
    level1_commission = models.DecimalField(_('一级佣金'), max_digits=18, decimal_places=8)
    level2_commission = models.DecimalField(_('二级佣金'), max_digits=18, decimal_places=8)
    level3_commission = models.DecimalField(_('三级佣金'), max_digits=18, decimal_places=8)
    total_commission = models.DecimalField(_('总佣金'), max_digits=18, decimal_places=8)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)

    class Meta:
        verbose_name = _('佣金记录')
        verbose_name_plural = _('佣金记录')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.agent.email} - {self.settlement_period}"


class SettlementWarning(models.Model):
    """结算预警记录"""
    WARNING_TYPES = [
        ('commission_decrease', _('佣金大幅减少')),
        ('zero_commission', _('佣金为零')),
        ('swmt_decrease', _('SWMT产出大幅减少')),
        ('member_decrease', _('团队成员大幅减少')),
        ('settlement_fail', _('结算失败')),
        ('settlement_timeout', _('结算超时')),
        ('high_error_rate', _('高错误率')),
        ('other', _('其他问题'))
    ]
    
    WARNING_LEVELS = [
        ('high', _('高')),
        ('medium', _('中')),
        ('low', _('低')),
    ]
    
    WARNING_STATUS = [
        ('pending', _('待处理')),
        ('processing', _('处理中')),
        ('resolved', _('已解决')),
        ('ignored', _('已忽略')),
    ]
    
    agent = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE,
                            related_name='settlement_warnings', verbose_name=_('代理'),
                            null=True, blank=True)
    settlement_period = models.ForeignKey(SettlementPeriod, on_delete=models.CASCADE,
                                        related_name='warnings', verbose_name=_('结算周期'))
    warning_type = models.CharField(_('预警类型'), max_length=30, choices=WARNING_TYPES)
    level = models.CharField(_('预警级别'), max_length=20, choices=WARNING_LEVELS, default='medium')
    status = models.CharField(_('处理状态'), max_length=20, choices=WARNING_STATUS, default='pending')
    title = models.CharField(_('预警标题'), max_length=100)
    description = models.TextField(_('预警详情'), blank=True)
    data = models.JSONField(_('相关数据'), null=True, blank=True)
    
    handler = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL,
                               related_name='handled_warnings', verbose_name=_('处理人'),
                               null=True, blank=True)
    handling_notes = models.TextField(_('处理备注'), blank=True)
    resolved_at = models.DateTimeField(_('解决时间'), null=True, blank=True)
    
    is_notified = models.BooleanField(_('是否已通知'), default=False)
    notification_sent_at = models.DateTimeField(_('通知发送时间'), null=True, blank=True)
    
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    
    class Meta:
        verbose_name = _('结算预警')
        verbose_name_plural = _('结算预警')
        ordering = ['-created_at']
        
    def __str__(self):
        if self.agent:
            return f"{self.get_warning_type_display()} - {self.agent.email} - {self.settlement_period}"
        return f"{self.get_warning_type_display()} - {self.settlement_period}"
        
    def resolve(self, handler, notes=''):
        """解决预警"""
        self.status = 'resolved'
        self.handler = handler
        self.handling_notes = notes
        self.resolved_at = timezone.now()
        self.save()
        
        # 记录系统日志
        from stats.models import SystemLog
        SystemLog.objects.create(
            type='agent',
            level='info',
            user_id=str(handler.id) if handler else None,
            action='resolve_settlement_warning',
            description=f"结算预警 {self.id} 已解决: {notes}",
            data={
                'warning_id': self.id,
                'warning_type': self.warning_type,
                'agent_email': self.agent.email if self.agent else None,
                'settlement_period': str(self.settlement_period),
                'notes': notes
            }
        )
        return True
        
    def ignore(self, handler, notes=''):
        """忽略预警"""
        self.status = 'ignored'
        self.handler = handler
        self.handling_notes = notes
        self.resolved_at = timezone.now()
        self.save()
        
        # 记录系统日志
        from stats.models import SystemLog
        SystemLog.objects.create(
            type='agent',
            level='info',
            user_id=str(handler.id) if handler else None,
            action='ignore_settlement_warning',
            description=f"结算预警 {self.id} 已忽略: {notes}",
            data={
                'warning_id': self.id,
                'warning_type': self.warning_type,
                'agent_email': self.agent.email if self.agent else None,
                'settlement_period': str(self.settlement_period),
                'notes': notes
            }
        )
        return True
