"""代理佣金服务模块，负责佣金计算和投档逻辑"""

from datetime import datetime, timedelta
from decimal import Decimal
from django.utils import timezone
from django.db import transaction
import logging
from typing import Optional, Dict, List, Tuple, Any
from django.core.cache import cache

from agents.models import AgentLevel, AgentRelationship, CommissionRecord, SettlementPeriod
from users.models import User
from agents.utils import count_level_members, calculate_level_swmt, generate_cache_key

logger = logging.getLogger(__name__)

class AgentCommissionService:
    """代理佣金服务类，提供更优化的佣金计算逻辑"""
    
    def __init__(self, agent_user: User, use_cache: bool = True):
        """初始化
        
        Args:
            agent_user: 代理用户
            use_cache: 是否使用缓存
        """
        self.agent_user = agent_user
        self.use_cache = use_cache
    
    def determine_agent_level(self, 
                             start_date: datetime, 
                             end_date: datetime) -> <PERSON><PERSON>[AgentLevel, Dict[str, int], Dict[str, Decimal]]:
        """确定代理投档等级
        
        该方法综合考虑下线人数和SWMT产出，确定最终的投档等级
        
        Args:
            start_date: 结算开始时间
            end_date: 结算结束时间
            
        Returns:
            Tuple[AgentLevel, Dict, Dict]: 返回代理等级、下线人数统计和SWMT产出统计
        """
        # 使用缓存机制
        if self.use_cache:
            cache_key = generate_cache_key(
                "determine_level", 
                self.agent_user.id, 
                start=start_date.isoformat(), 
                end=end_date.isoformat()
            )
            cached_result = cache.get(cache_key)
            if cached_result:
                logger.debug(f"使用缓存的投档结果: {cache_key}")
                level_id, downline_stats, swmt_stats_str = cached_result
                
                # 从缓存恢复数据
                agent_level = AgentLevel.objects.get(id=level_id)
                swmt_stats = {
                    'level1_swmt': Decimal(swmt_stats_str['level1_swmt']),
                    'level2_swmt': Decimal(swmt_stats_str['level2_swmt']),
                    'level3_swmt': Decimal(swmt_stats_str['level3_swmt'])
                }
                
                return agent_level, downline_stats, swmt_stats
        
        # 1. 获取下线人数统计 - 使用优化过的工具函数(已缓存)
        downline_stats = {
            'level1_members': count_level_members(self.agent_user, 1, self.use_cache),
            'level2_members': count_level_members(self.agent_user, 2, self.use_cache),
            'level3_members': count_level_members(self.agent_user, 3, self.use_cache)
        }
        
        # 2. 获取SWMT产出统计 - 使用优化过的工具函数(已缓存)
        swmt_stats = {
            'level1_swmt': calculate_level_swmt(self.agent_user, 1, start_date, end_date, self.use_cache),
            'level2_swmt': calculate_level_swmt(self.agent_user, 2, start_date, end_date, self.use_cache),
            'level3_swmt': calculate_level_swmt(self.agent_user, 3, start_date, end_date, self.use_cache)
        }
        
        # 3. 根据下线人数获取可用等级范围（先判断资格）
        available_levels = AgentLevel.objects.filter(
            is_active=True,
            required_direct_members__lte=downline_stats['level1_members']
        ).order_by('-level')
        
        if not available_levels.exists():
            logger.warning(f"代理 {self.agent_user.email} 没有可用的代理等级")
            # 使用一个默认的最低等级
            default_level = AgentLevel.objects.filter(is_active=True).order_by('level').first()
            return default_level or None, downline_stats, swmt_stats
        
        # 4. 根据一级下线SWMT产出确定最终等级（在可用范围内投档）
        level1_swmt = swmt_stats['level1_swmt']
        
        # 在可用等级范围内，根据SWMT产出找到合适的等级
        suitable_level = None
        
        # 找到最高符合条件的等级
        for level in available_levels:
            # 检查SWMT产出是否符合要求
            # max_swmt_output为0表示无上限
            if level.min_swmt_output <= level1_swmt and (level.max_swmt_output >= level1_swmt or level.max_swmt_output == 0):
                suitable_level = level
                break
        
        # 如果没有找到合适的等级，使用可用范围内最低的等级
        if not suitable_level:
            suitable_level = available_levels.last()
            logger.info(f"代理 {self.agent_user.email} 的SWMT产出 {level1_swmt} 不在任何等级范围内，使用可用范围内最低等级 {suitable_level.name}")
        
        logger.info(f"代理 {self.agent_user.email} 投档结果: 等级={suitable_level.name}, 下线人数={downline_stats['level1_members']}, SWMT产出={level1_swmt}")
        
        # 缓存结果供后续使用，有效期1小时
        if self.use_cache and suitable_level:
            # 需要将Decimal转为字符串以便JSON序列化
            swmt_stats_str = {
                'level1_swmt': str(swmt_stats['level1_swmt']),
                'level2_swmt': str(swmt_stats['level2_swmt']),
                'level3_swmt': str(swmt_stats['level3_swmt'])
            }
            cache.set(
                cache_key, 
                (suitable_level.id, downline_stats, swmt_stats_str), 
                3600
            )
        
        return suitable_level, downline_stats, swmt_stats
    
    def calculate_commission(self, 
                            start_date: datetime, 
                            end_date: datetime,
                            settlement_period: SettlementPeriod,
                            force_recalculate: bool = False) -> Optional[CommissionRecord]:
        """计算佣金
        
        Args:
            start_date: 结算开始时间
            end_date: 结算结束时间
            settlement_period: 结算周期
            force_recalculate: 是否强制重新计算(忽略已有记录)
            
        Returns:
            Optional[CommissionRecord]: 返回创建的佣金记录，若无法计算则返回None
        """
        # 检查用户是否为代理
        if not self.agent_user.is_agent:
            logger.warning(f"用户 {self.agent_user.email} 不是代理，无法计算佣金")
            return None
        
        # 检查是否有代理关系记录
        try:
            agent_relationship = AgentRelationship.objects.get(
                user=self.agent_user,
                is_active=True
            )
        except AgentRelationship.DoesNotExist:
            logger.warning(f"用户 {self.agent_user.email} 没有激活的代理关系记录")
            return None
        
        # 检查是否允许结算
        if not agent_relationship.can_settle(start_date, end_date):
            logger.info(f"代理 {self.agent_user.email} 在此期间不可结算，跳过")
            return None
        
        # 如果结算周期内有恢复结算的操作，从恢复时间开始计算
        actual_start = start_date
        if agent_relationship.settlement_restored_at and agent_relationship.settlement_restored_at > start_date:
            actual_start = agent_relationship.settlement_restored_at
            logger.info(f"代理 {self.agent_user.email} 在结算周期内恢复({actual_start})，从恢复时间开始计算")
        
        # 检查是否已经存在该结算周期的佣金记录
        if not force_recalculate:
            existing_commission = CommissionRecord.objects.filter(
                agent=self.agent_user,
                settlement_period=settlement_period
            ).first()
            
            if existing_commission:
                logger.info(f"代理 {self.agent_user.email} 在该结算周期已有佣金记录，跳过处理")
                return existing_commission
        
        # 确定投档等级
        agent_level, downline_stats, swmt_stats = self.determine_agent_level(
            actual_start, end_date
        )
        
        if not agent_level:
            logger.warning(f"无法确定代理 {self.agent_user.email} 的投档等级")
            return None
        
        # 获取SWMT产出
        level1_swmt = swmt_stats['level1_swmt']
        level2_swmt = swmt_stats['level2_swmt']
        level3_swmt = swmt_stats['level3_swmt']
        
        # 如果没有任何SWMT产出，跳过创建佣金记录
        if level1_swmt == 0 and level2_swmt == 0 and level3_swmt == 0:
            logger.info(f"代理 {self.agent_user.email} 没有任何SWMT产出，跳过创建佣金记录")
            return None
        
        # 计算佣金 - 使用小数计算而非整数计算，以匹配测试预期
        level1_commission = level1_swmt * agent_level.level1_commission_rate * Decimal('0.01')
        level2_commission = level2_swmt * agent_level.level2_commission_rate * Decimal('0.01')
        level3_commission = level3_swmt * agent_level.level3_commission_rate * Decimal('0.01')
        total_commission = level1_commission + level2_commission + level3_commission
        
        # 创建佣金记录
        with transaction.atomic():
            # 如果强制重新计算，先删除已有记录
            if force_recalculate:
                CommissionRecord.objects.filter(
                    agent=self.agent_user,
                    settlement_period=settlement_period
                ).delete()
            
            commission_record = CommissionRecord.objects.create(
                agent=self.agent_user,
                settlement_period=settlement_period,
                agent_level=agent_level,
                level1_members=downline_stats['level1_members'],
                level2_members=downline_stats['level2_members'],
                level3_members=downline_stats['level3_members'],
                level1_swmt=level1_swmt,
                level2_swmt=level2_swmt,
                level3_swmt=level3_swmt,
                level1_commission=level1_commission,
                level2_commission=level2_commission,
                level3_commission=level3_commission,
                total_commission=total_commission
            )
            
            # 更新用户总佣金
            self.agent_user.total_commission += total_commission
            self.agent_user.save(update_fields=['total_commission'])
            
            # 更新代理关系中的累计佣金
            agent_relationship.total_commission += total_commission
            agent_relationship.save(update_fields=['total_commission'])
            
            logger.info(f"为代理 {self.agent_user.email} 创建佣金记录成功，记录ID：{commission_record.id}")
            logger.info(f"代理 {self.agent_user.email} 投档等级：{agent_level.name}")
            logger.info(f"一级佣金：{level1_commission}，二级佣金：{level2_commission}，三级佣金：{level3_commission}")
            logger.info(f"总佣金：{total_commission}")
            
            return commission_record

    @classmethod
    def batch_calculate_commissions(cls, 
                                  users: List[User], 
                                  start_date: datetime, 
                                  end_date: datetime,
                                  settlement_period: SettlementPeriod,
                                  use_cache: bool = True) -> Tuple[List[CommissionRecord], Dict[str, int]]:
        """批量计算多个代理的佣金
        
        Args:
            users: 代理用户列表
            start_date: 结算开始时间
            end_date: 结算结束时间
            settlement_period: 结算周期
            use_cache: 是否使用缓存
            
        Returns:
            Tuple[List[CommissionRecord], Dict[str, int]]: 返回创建的佣金记录列表和处理统计
        """
        results = []
        stats = {
            "success": 0,
            "failed": 0, 
            "skipped": 0
        }
        
        for user in users:
            try:
                service = cls(user, use_cache=use_cache)
                commission = service.calculate_commission(
                    start_date=start_date,
                    end_date=end_date,
                    settlement_period=settlement_period
                )
                
                if commission:
                    results.append(commission)
                    stats["success"] += 1
                else:
                    stats["skipped"] += 1
            except Exception as e:
                logger.error(f"批量计算代理 {user.email} 的佣金时出错: {str(e)}", exc_info=True)
                stats["failed"] += 1
        
        return results, stats 