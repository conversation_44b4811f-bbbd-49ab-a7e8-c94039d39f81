"""结算预警服务模块，负责检测和生成预警"""

from datetime import datetime, timedelta
from decimal import Decimal
import logging
from typing import Dict, List, Optional, Tuple, Any
from django.db.models import Q, Count
from django.utils import timezone
from django.conf import settings
import json

from agents.models import SettlementPeriod, CommissionRecord, AgentRelationship, SettlementWarning
from users.models import User
from stats.models import SystemLog
from .notification_service import send_warning_notification, send_warnings_batch

logger = logging.getLogger(__name__)

class SettlementWarningService:
    """结算预警服务，提供预警检测和生成功能"""
    
    # 预警阈值配置
    WARNING_THRESHOLDS = {
        # 如果一个代理的佣金比上一周期减少超过30%则触发预警
        'commission_decrease_rate': Decimal('30.0'),
        
        # 如果一个代理的SWMT总产出比上一周期减少超过50%则触发预警
        'swmt_decrease_rate': Decimal('50.0'),
        
        # 如果一个代理的团队成员数量比上一周期减少超过20%则触发预警
        'member_decrease_rate': Decimal('20.0'),
        
        # 如果结算周期内总结算失败比例超过10%则触发系统级预警
        'settlement_fail_rate': Decimal('10.0'),
        
        # 如果结算持续时间超过1小时则触发系统级预警
        'settlement_timeout_minutes': 60,
        
        # 如果结算错误率超过5%则触发系统级预警
        'high_error_rate': Decimal('5.0'),
    }
    
    def __init__(self):
        """初始化预警服务"""
        pass
    
    def check_commission_decrease(self, agent, current_record, previous_record):
        """
        检查佣金是否显著下降
        
        Args:
            agent: User对象
            current_record: 当前佣金记录
            previous_record: 上一期佣金记录
            
        Returns:
            SettlementWarning或None
        """
        if not previous_record or not current_record:
            return None
            
        # 如果前一期佣金为0，无法计算下降率
        if previous_record.total_commission == 0:
            return None
            
        # 计算佣金下降率
        decrease_rate = (previous_record.total_commission - current_record.total_commission) / previous_record.total_commission
        
        # 检查是否超过阈值
        if decrease_rate > self.WARNING_THRESHOLDS['commission_decrease_rate']:
            data = {
                'current_amount': str(current_record.total_commission),
                'previous_amount': str(previous_record.total_commission),
                'decrease_rate': f"{decrease_rate:.2%}",
                'threshold': f"{self.WARNING_THRESHOLDS['commission_decrease_rate']:.2%}"
            }
            
            warning = SettlementWarning.objects.create(
                title=f"代理 {agent.email} 佣金显著下降",
                warning_type='commission_decrease',
                level='high' if decrease_rate > 0.5 else 'medium',
                data=json.dumps(data, ensure_ascii=False, indent=2),
                agent=agent,
                settlement_period=current_record.settlement_period
            )
            
            # 发送通知
            send_warning_notification(warning)
            return warning
            
        return None
    
    def check_zero_commission(self, agent, current_record):
        """
        检查佣金是否为零但是有团队成员
        
        Args:
            agent: User对象
            current_record: 当前佣金记录
            
        Returns:
            SettlementWarning或None
        """
        if not current_record or current_record.total_commission > 0:
            return None
            
        # 检查是否有团队成员
        team_count = AgentRelationship.objects.filter(referrer=agent).count()
        
        if team_count > 0:
            data = {
                'team_size': team_count,
                'period_start': current_record.settlement_period.start_date.strftime('%Y-%m-%d'),
                'period_end': current_record.settlement_period.end_date.strftime('%Y-%m-%d')
            }
            
            warning = SettlementWarning.objects.create(
                title=f"代理 {agent.email} 有 {team_count} 名团队成员但佣金为零",
                warning_type='zero_commission',
                level='medium',
                data=json.dumps(data, ensure_ascii=False, indent=2),
                agent=agent,
                settlement_period=current_record.settlement_period
            )
            
            # 发送通知
            send_warning_notification(warning)
            return warning
            
        return None
    
    def check_swmt_decrease(self, agent, current_record, previous_record):
        """
        检查SWMT产出是否显著下降
        
        Args:
            agent: User对象
            current_record: 当前佣金记录
            previous_record: 上一期佣金记录
            
        Returns:
            SettlementWarning或None
        """
        if not previous_record or not current_record:
            return None
        
        # 获取SWMT数据
        current_swmt = current_record.metadata.get('swmt_output', 0) if current_record.metadata else 0
        previous_swmt = previous_record.metadata.get('swmt_output', 0) if previous_record.metadata else 0
        
        # 如果前一期SWMT为0，无法计算下降率
        if previous_swmt == 0:
            return None
            
        # 计算SWMT下降率
        decrease_rate = (previous_swmt - current_swmt) / previous_swmt
        
        # 检查是否超过阈值
        if decrease_rate > self.WARNING_THRESHOLDS['swmt_decrease_rate']:
            data = {
                'current_swmt': current_swmt,
                'previous_swmt': previous_swmt,
                'decrease_rate': f"{decrease_rate:.2%}",
                'threshold': f"{self.WARNING_THRESHOLDS['swmt_decrease_rate']:.2%}"
            }
            
            warning = SettlementWarning.objects.create(
                title=f"代理 {agent.email} SWMT产出显著下降",
                warning_type='swmt_decrease',
                level='medium',
                data=json.dumps(data, ensure_ascii=False, indent=2),
                agent=agent,
                settlement_period=current_record.settlement_period
            )
            
            # 发送通知
            send_warning_notification(warning)
            return warning
            
        return None
    
    def check_member_decrease(self, agent, current_period, previous_period):
        """
        检查团队成员是否显著减少
        
        Args:
            agent: User对象
            current_period: 当前结算周期
            previous_period: 上一结算周期
            
        Returns:
            SettlementWarning或None
        """
        if not previous_period or not current_period:
            return None
            
        # 获取当前和上一期的团队成员数
        current_stats = AgentRelationship.objects.filter(
            referrer=agent, 
            created_at__lte=current_period.end_date
        ).aggregate(count=Count('id'))
        
        previous_stats = AgentRelationship.objects.filter(
            referrer=agent, 
            created_at__lte=previous_period.end_date
        ).aggregate(count=Count('id'))
        
        current_count = current_stats['count'] or 0
        previous_count = previous_stats['count'] or 0
        
        # 如果前一期没有团队成员，无法计算减少率
        if previous_count == 0:
            return None
            
        # 如果团队成员增加，不触发警告
        if current_count >= previous_count:
            return None
            
        # 计算团队成员减少率
        decrease_rate = (previous_count - current_count) / previous_count
        
        # 检查是否超过阈值
        if decrease_rate > self.WARNING_THRESHOLDS['member_decrease_rate']:
            data = {
                'current_count': current_count,
                'previous_count': previous_count,
                'decrease_rate': f"{decrease_rate:.2%}",
                'threshold': f"{self.WARNING_THRESHOLDS['member_decrease_rate']:.2%}"
            }
            
            warning = SettlementWarning.objects.create(
                title=f"代理 {agent.email} 团队成员显著减少",
                warning_type='member_decrease',
                level='medium',
                data=json.dumps(data, ensure_ascii=False, indent=2),
                agent=agent,
                settlement_period=current_period
            )
            
            # 发送通知
            send_warning_notification(warning)
            return warning
            
        return None
    
    def check_settlement_fail(self, period, stats):
        """
        检查结算失败率
        
        Args:
            period: 结算周期
            stats: 统计数据，包含success_count, fail_count
            
        Returns:
            SettlementWarning或None
        """
        total = stats.get('success_count', 0) + stats.get('fail_count', 0)
        if total == 0:
            return None
            
        fail_rate = stats.get('fail_count', 0) / total
        
        # 检查是否超过阈值
        if fail_rate > self.WARNING_THRESHOLDS['settlement_fail_rate']:
            data = {
                'success_count': stats.get('success_count', 0),
                'fail_count': stats.get('fail_count', 0),
                'total_count': total,
                'fail_rate': f"{fail_rate:.2%}",
                'threshold': f"{self.WARNING_THRESHOLDS['settlement_fail_rate']:.2%}"
            }
            
            warning = SettlementWarning.objects.create(
                title=f"结算周期 {period.start_date.strftime('%Y-%m-%d')} - {period.end_date.strftime('%Y-%m-%d')} 失败率过高",
                warning_type='settlement_fail',
                level='high',
                data=json.dumps(data, ensure_ascii=False, indent=2),
                agent=None,  # 系统级别警告
                settlement_period=period
            )
            
            # 发送通知
            send_warning_notification(warning)
            return warning
            
        return None
    
    def check_settlement_timeout(self, period):
        """
        检查结算是否超时
        
        Args:
            period: 结算周期
            
        Returns:
            SettlementWarning或None
        """
        if period.status != 'processing':
            return None
            
        # 如果没有开始时间，则无法判断
        if not period.processed_at:
            return None
            
        # 计算当前处理时长（分钟）
        processing_minutes = (timezone.now() - period.processed_at).total_seconds() / 60
        
        # 检查是否超过阈值
        if processing_minutes > self.WARNING_THRESHOLDS['settlement_timeout_minutes']:
            data = {
                'period_start': period.start_date.strftime('%Y-%m-%d'),
                'period_end': period.end_date.strftime('%Y-%m-%d'),
                'processing_start': period.processed_at.strftime('%Y-%m-%d %H:%M:%S'),
                'processing_minutes': round(processing_minutes),
                'threshold_minutes': self.WARNING_THRESHOLDS['settlement_timeout_minutes']
            }
            
            # 根据超时时长设置警告级别
            if processing_minutes > self.WARNING_THRESHOLDS['settlement_timeout_minutes'] * 3:
                level = 'high'
            elif processing_minutes > self.WARNING_THRESHOLDS['settlement_timeout_minutes'] * 2:
                level = 'medium'
            else:
                level = 'low'
                
            warning = SettlementWarning.objects.create(
                title=f"结算周期 {period.start_date.strftime('%Y-%m-%d')} - {period.end_date.strftime('%Y-%m-%d')} 处理超时",
                warning_type='settlement_timeout',
                level=level,
                data=json.dumps(data, ensure_ascii=False, indent=2),
                agent=None,  # 系统级别警告
                settlement_period=period
            )
            
            # 发送通知
            send_warning_notification(warning)
            return warning
            
        return None
    
    def process_commission_record(self, record, previous_record=None):
        """
        处理单个佣金记录，检查所有可能的警告
        
        Args:
            record: 当前佣金记录
            previous_record: 上一期佣金记录（可选）
            
        Returns:
            list: 触发的警告列表
        """
        warnings = []
        agent = record.agent
        
        # 检查佣金下降
        if previous_record:
            warning = self.check_commission_decrease(agent, record, previous_record)
            if warning:
                warnings.append(warning)
            
        # 检查零佣金
        warning = self.check_zero_commission(agent, record)
        if warning:
            warnings.append(warning)
        
        # 检查SWMT下降
        if previous_record:
            warning = self.check_swmt_decrease(agent, record, previous_record)
            if warning:
                warnings.append(warning)
        
        # 检查团队成员减少
        if previous_record:
            warning = self.check_member_decrease(agent, record.settlement_period, previous_record.settlement_period)
            if warning:
                warnings.append(warning)
                
        return warnings
    
    def check_period_warnings(self, period: SettlementPeriod, stats: Dict) -> List[SettlementWarning]:
        """检查结算周期级别的预警
        
        Args:
            period: 结算周期
            stats: 结算统计数据
            
        Returns:
            List[SettlementWarning]: 触发的预警列表
        """
        warnings = []
        
        # 检查结算失败率
        failed_count = stats.get('failed', 0)
        total_count = sum([
            stats.get('success', 0),
            stats.get('failed', 0),
            stats.get('skipped', 0)
        ])
        
        warning = self.check_settlement_fail(period, stats)
        if warning:
            warnings.append(warning)
        
        # 检查结算是否超时
        warning = self.check_settlement_timeout(period)
        if warning:
            warnings.append(warning)
        
        return warnings
    
    def send_warning_notification(self, warning: SettlementWarning) -> bool:
        """发送预警通知
        
        Args:
            warning: 预警对象
            
        Returns:
            bool: 是否成功发送通知
        """
        # 检查是否已经发送过通知
        if warning.is_notified:
            return True
        
        try:
            # 根据预警级别确定通知方式
            if warning.level == 'critical':
                # 严重级别: 系统日志 + 邮件通知
                from django.core.mail import send_mail
                
                subject = f"[SweatMint] 严重结算预警: {warning.title}"
                message = (
                    f"结算预警详情:\n\n"
                    f"ID: {warning.id}\n"
                    f"级别: {warning.get_level_display()}\n"
                    f"类型: {warning.get_warning_type_display()}\n"
                    f"标题: {warning.title}\n"
                    f"时间: {warning.created_at}\n\n"
                    f"详情:\n{warning.description}\n\n"
                    f"请及时处理此预警。"
                )
                
                recipient_list = getattr(settings, 'ALERT_EMAILS', [])
                if recipient_list:
                    send_mail(
                        subject=subject,
                        message=message,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=recipient_list,
                        fail_silently=False
                    )
                    
                    # 记录系统日志
                    SystemLog.objects.create(
                        type='agent',
                        level='critical',
                        action='settlement_critical_warning',
                        description=f"严重结算预警: {warning.title}",
                        data={
                            'warning_id': warning.id,
                            'warning_type': warning.warning_type,
                            'warning_level': warning.level,
                            'agent_email': warning.agent.email if warning.agent else None,
                            'description': warning.description,
                        }
                    )
                    
                    # 标记为已通知
                    warning.is_notified = True
                    warning.notification_sent_at = timezone.now()
                    warning.save(update_fields=['is_notified', 'notification_sent_at'])
                    
                    logger.info(f"已发送严重预警通知: {warning.id} - {warning.title}")
                    return True
                else:
                    logger.warning("未配置通知邮箱，无法发送严重预警通知")
                    return False
                    
            elif warning.level == 'warning':
                # 警告级别: 系统日志
                SystemLog.objects.create(
                    type='agent',
                    level='warning',
                    action='settlement_warning',
                    description=f"结算预警: {warning.title}",
                    data={
                        'warning_id': warning.id,
                        'warning_type': warning.warning_type,
                        'warning_level': warning.level,
                        'agent_email': warning.agent.email if warning.agent else None,
                        'description': warning.description,
                    }
                )
                
                # 标记为已通知
                warning.is_notified = True
                warning.notification_sent_at = timezone.now()
                warning.save(update_fields=['is_notified', 'notification_sent_at'])
                
                logger.info(f"已创建警告级别预警系统日志: {warning.id} - {warning.title}")
                return True
                
            else:
                # 信息级别: 仅写日志
                logger.info(f"结算信息提示: {warning.id} - {warning.title}")
                
                # 标记为已通知
                warning.is_notified = True
                warning.notification_sent_at = timezone.now()
                warning.save(update_fields=['is_notified', 'notification_sent_at'])
                
                return True
                
        except Exception as e:
            logger.error(f"发送预警通知失败: {str(e)}", exc_info=True)
            return False
            
    def send_pending_notifications(self) -> int:
        """发送所有待发送的预警通知
        
        Returns:
            int: 成功发送的通知数量
        """
        # 查找所有未通知的预警
        pending_warnings = SettlementWarning.objects.filter(
            is_notified=False
        ).order_by('-level', '-created_at')
        
        count = 0
        for warning in pending_warnings:
            if self.send_warning_notification(warning):
                count += 1
                
        return count 