import logging
from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.urls import reverse
from django.contrib.sites.models import Site

logger = logging.getLogger(__name__)

def send_warning_notification(warning):
    """
    发送结算预警通知
    
    Args:
        warning: SettlementWarning实例
    
    Returns:
        bool: 发送成功返回True，否则返回False
    """
    try:
        # 获取收件人列表
        recipients = _get_admin_recipients()
        if not recipients:
            logger.warning("没有找到管理员收件人，无法发送预警通知")
            return False
            
        # 获取当前站点域名
        current_site = Site.objects.get_current()
        domain = current_site.domain
        
        # 构建EmailContext
        email_context = {
            'warning': warning,
            'agent_email': warning.agent.email,
            'settlement_period': warning.settlement_period,
            'warning_level': warning.get_level_display(),
            'warning_data': warning.data,
            'warning_list_url': f"https://{domain}/admin/agents/settlementwarning/",
            'site_name': current_site.name,
        }
        
        # 根据预警级别设置主题前缀
        level_prefix = {
            'high': '【高级】',
            'medium': '【中级】',
            'low': '【低级】'
        }.get(warning.level, '')
        
        subject = f"{level_prefix}SweatMint结算预警: {warning.title}"
        html_message = render_to_string('agents/email/settlement_warning.html', email_context)
        plain_message = f"预警通知: {warning.title}\n类型: {warning.get_type_display()}\n级别: {warning.get_level_display()}\n详情请登录管理后台查看。"
        
        # 发送邮件
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=recipients,
            html_message=html_message,
            fail_silently=False
        )
        
        logger.info(f"成功发送预警通知 #{warning.id}: {warning.title} 给 {len(recipients)} 位管理员")
        return True
    except Exception as e:
        logger.error(f"发送预警通知失败: {str(e)}", exc_info=True)
        return False

def _get_admin_recipients():
    """获取管理员邮箱列表"""
    from django.contrib.auth import get_user_model
    User = get_user_model()
    
    # 获取所有超级管理员和有权限的职员用户的邮箱
    admin_emails = list(User.objects.filter(
        is_active=True, 
        is_staff=True,
        email__isnull=False
    ).exclude(email='').values_list('email', flat=True))
    
    # 添加设置中配置的额外接收邮箱（如果有）
    extra_emails = getattr(settings, 'SETTLEMENT_WARNING_RECIPIENTS', [])
    if extra_emails:
        admin_emails.extend(extra_emails)
    
    return admin_emails

def send_warnings_batch(warnings):
    """
    批量发送多个预警通知
    
    Args:
        warnings: SettlementWarning查询集
        
    Returns:
        int: 成功发送的数量
    """
    success_count = 0
    for warning in warnings:
        if send_warning_notification(warning):
            success_count += 1
    
    return success_count 