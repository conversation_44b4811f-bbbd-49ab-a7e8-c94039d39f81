"""
代理管理服务类
负责统一管理代理关系的创建、检查、升级等操作
"""

import logging
from django.db import transaction
from django.utils import timezone
from typing import Optional, Tuple, Dict, Any

from users.models import User
from agents.models import AgentLevel, AgentRelationship
from stats.models import SystemLog

logger = logging.getLogger(__name__)

class AgentService:
    """代理服务类，负责统一管理代理关系"""
    
    @classmethod
    def create_agent_relationship(cls, 
                                 user: User, 
                                 is_active: bool = True, 
                                 admin_user=None,
                                 reason: str = None) -> Tuple[bool, str, Optional[AgentRelationship]]:
        """
        创建代理关系
        
        Args:
            user: 要升级为代理的用户
            is_active: 是否立即激活
            admin_user: 执行操作的管理员（如果是管理操作）
            reason: 创建原因
            
        Returns:
            Tuple[bool, str, Optional[AgentRelationship]]: 成功标志、消息、创建的代理关系对象
        """
        # 检查用户是否已经是代理
        if user.is_agent:
            return False, f"用户 {user.email} 已经是代理", None
            
        # 检查是否已有代理关系
        existing_relationship = AgentRelationship.objects.filter(user=user).first()
        if existing_relationship:
            return False, f"用户 {user.email} 已有代理关系记录", existing_relationship
            
        try:
            with transaction.atomic():
                # 1. 查找最低代理等级
                default_agent_level = AgentLevel.objects.filter(
                    level=1,
                    is_active=True
                ).first()
                
                if not default_agent_level:
                    return False, "系统中无可用的代理等级配置", None
                
                # 2. 计算直接下线数量
                direct_member_count = User.objects.filter(referrer=user).count()
                
                # 3. 更新用户为代理状态
                user.is_agent = True
                if not user.agent_id:
                    user.agent_id = f"A{user.user_id[1:]}"
                user.save(update_fields=['is_agent', 'agent_id'])
                
                # 4. 创建代理关系记录 - 使用用户原有的推荐人，保持关系一致性
                agent_relationship = AgentRelationship.objects.create(
                    user=user,
                    referrer=user.referrer,  # 确保与User表中的推荐人一致
                    agent_level=default_agent_level,
                    is_active=is_active,
                    settlement_status='active',
                    direct_member_count=direct_member_count
                )
                
                # 5. 记录系统日志
                action_type = "admin_create_agent" if admin_user else "auto_create_agent"
                action_reason = reason or ("管理员手动创建" if admin_user else "自动创建(满足条件)")
                
                SystemLog.objects.create(
                    type='agent',
                    level='info',
                    user_id=str(admin_user.id) if admin_user else None,
                    action=action_type,
                    description=f"用户 {user.email} 的代理关系已创建，初始等级设为 {default_agent_level.name}，原因: {action_reason}",
                    data={
                        'user_email': user.email,
                        'user_id': user.user_id,
                        'agent_id': user.agent_id,
                        'agent_level': default_agent_level.name,
                        'referrer': user.referrer.email if user.referrer else None,
                        'is_active': is_active,
                        'reason': action_reason
                    }
                )
                
                logger.info(f"用户 {user.email} 的代理关系已创建成功，代理ID: {user.agent_id}")
                return True, "代理关系创建成功", agent_relationship
                
        except Exception as e:
            logger.error(f"创建代理关系时出错: {str(e)}", exc_info=True)
            return False, f"创建代理关系失败: {str(e)}", None
    
    @classmethod
    def check_and_upgrade_to_agent(cls, user: User) -> bool:
        """
        检查用户是否满足自动升级为代理的条件并执行升级
        
        Args:
            user: 要检查的用户
        
        Returns:
            bool: 升级成功返回True，否则返回False
        """
        # 如果已经是代理，直接返回
        if user.is_agent:
            return False
            
        # 检查是否有3个或以上直接下线
        direct_downlines_count = User.objects.filter(referrer=user).count()
        if direct_downlines_count >= 3:
            try:
                # 将整个升级流程放在一个事务中
                with transaction.atomic():
                    # 1. 设置用户为代理
                    if not user.is_agent:
                        user.is_agent = True
                        if not user.agent_id:
                            user.agent_id = f"A{user.user_id[1:]}"
                        user.save(update_fields=['is_agent', 'agent_id'])
                    
                    # 2. 创建代理关系 - 在同一事务中
                    success, message, relationship = cls.create_agent_relationship(
                        user=user,
                        is_active=True,
                        reason=f"自动升级(直接下线数量达到{direct_downlines_count}个)"
                    )
                    
                    if not success:
                        # 如果创建关系失败，回滚整个事务
                        logger.error(f"创建代理关系失败，回滚事务: {message}")
                        raise ValueError(f"创建代理关系失败: {message}")
                    
                    # 记录系统日志
                    SystemLog.objects.create(
                        type='agent',
                        level='info',
                        action='auto_upgrade_agent',
                        description=f"用户 {user.email} 自动升级为代理，满足条件：直接下线数量{direct_downlines_count}个",
                        data={
                            'user_email': user.email,
                            'user_id': user.user_id,
                            'agent_id': user.agent_id,
                            'direct_downlines': direct_downlines_count
                        }
                    )
                    
                    return True
                    
            except Exception as e:
                logger.error(f"代理升级过程发生错误: {str(e)}", exc_info=True)
                return False
        
        return False
    
    @classmethod
    def check_agent_relationship_consistency(cls, user: User) -> Tuple[bool, str]:
        """
        检查用户的代理关系一致性
        
        Args:
            user: 要检查的用户
            
        Returns:
            Tuple[bool, str]: 一致性状态和消息
        """
        if not user.is_agent:
            return True, "用户不是代理，无需检查一致性"
            
        try:
            # 查找代理关系
            relationship = AgentRelationship.objects.get(user=user)
            
            # 检查推荐人一致性
            if relationship.referrer != user.referrer:
                return False, f"代理关系不一致: User表推荐人={user.referrer}, 代理关系表推荐人={relationship.referrer}"
                
            # 检查其他可能的不一致...
            return True, "代理关系一致"
            
        except AgentRelationship.DoesNotExist:
            return False, "用户标记为代理但没有代理关系记录"
        except Exception as e:
            logger.error(f"检查代理关系一致性时出错: {str(e)}", exc_info=True)
            return False, f"检查失败: {str(e)}"
    
    @classmethod
    def fix_agent_relationship_consistency(cls, user: User) -> Tuple[bool, str]:
        """
        修复用户的代理关系一致性
        
        Args:
            user: 要修复的用户
            
        Returns:
            Tuple[bool, str]: 修复状态和消息
        """
        if not user.is_agent:
            return False, "用户不是代理，无需修复"
            
        try:
            with transaction.atomic():
                # 查找代理关系
                try:
                    relationship = AgentRelationship.objects.get(user=user)
                    
                    # 如果推荐人不一致，以User表为准
                    if relationship.referrer != user.referrer:
                        old_referrer = relationship.referrer
                        relationship.referrer = user.referrer
                        relationship.save(update_fields=['referrer'])
                        
                        # 记录修复日志
                        SystemLog.objects.create(
                            type='agent',
                            level='warning',
                            action='fix_agent_relationship',
                            description=f"修复了用户 {user.email} 的代理关系不一致问题，"
                                        f"原代理关系推荐人: {old_referrer.email if old_referrer else 'None'}, "
                                        f"修正为: {user.referrer.email if user.referrer else 'None'}",
                            data={
                                'user_email': user.email,
                                'user_id': user.user_id,
                                'old_referrer': old_referrer.email if old_referrer else None,
                                'new_referrer': user.referrer.email if user.referrer else None
                            }
                        )
                        
                        return True, "代理关系推荐人已修复"
                    
                    return True, "代理关系已一致，无需修复"
                    
                except AgentRelationship.DoesNotExist:
                    # 如果用户是代理但没有代理关系记录，创建一个
                    success, message, relationship = cls.create_agent_relationship(
                        user=user,
                        reason="修复缺失的代理关系"
                    )
                    
                    if success:
                        return True, "已创建缺失的代理关系记录"
                    else:
                        return False, f"创建代理关系失败: {message}"
                        
        except Exception as e:
            logger.error(f"修复代理关系一致性时出错: {str(e)}", exc_info=True)
            return False, f"修复失败: {str(e)}" 