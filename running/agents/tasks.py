from celery import shared_task
from celery.canvas import group
from django.utils import timezone
from datetime import timedelta
from django.db import transaction
from .models import SettlementPeriod, CommissionRecord, AgentRelationship, AgentLevel
import logging
from tasks.models import Task, UserTask, TaskCompletionLog, SuspiciousHealthData
from users.models import User
from django.db import models
from decimal import Decimal
from django.db.models import Sum, Count
from .services.commission_service import AgentCommissionService
from .utils import count_level_members, calculate_level_swmt, invalidate_agent_cache

logger = logging.getLogger(__name__)

@shared_task
def weekly_commission_settlement():
    """每周一00:00执行的代理佣金结算任务
    
    该任务负责创建结算周期并分发子任务处理每个代理的佣金计算
    """
    try:
        # 1. 创建新的结算周期
        now = timezone.now()
        start_date = now - timedelta(days=7)
        
        # 检查是否已经存在该时间段的结算周期
        existing_period = SettlementPeriod.objects.filter(
            models.Q(start_date__lte=now, end_date__gte=start_date) |
            models.Q(start_date__gte=start_date, start_date__lte=now) |
            models.Q(end_date__gte=start_date, end_date__lte=now)
        ).first()
        
        if existing_period:
            logger.info(f"该时间段已存在结算周期: {existing_period}")
            return
        
        with transaction.atomic():
            period = SettlementPeriod.objects.create(
                start_date=start_date,
                end_date=now,
                status='processing'
            )
            
            # 2. 获取所有活跃代理关系
            agent_relationships = AgentRelationship.objects.select_related(
                'user', 'agent_level'
            ).filter(
                is_active=True,
                settlement_status='active',  # 只处理允许结算的代理
                user__is_agent=True  # 确保用户是代理
            )
            
            # 统计总代理数量
            total_agent_count = agent_relationships.count()
            logger.info(f"找到 {total_agent_count} 个活跃代理需要结算")
            
            if total_agent_count == 0:
                # 如果没有代理需要结算，直接完成
                period.status = 'completed'
                period.agent_count = 0
                period.save()
                logger.info(f"没有代理需要结算，结算周期 {period.id} 已完成")
                return
            
            # 3. 根据代理数量决定处理策略
            if total_agent_count <= 100:
                # 少量代理，直接在当前任务中处理
                logger.info(f"代理数量较少，将在当前任务中处理所有 {total_agent_count} 个代理")
                process_agent_commissions_in_batch(
                    period_id=period.id,
                    start_date=start_date,
                    end_date=now,
                    agent_ids=list(agent_relationships.values_list('user_id', flat=True))
                )
            else:
                # 大量代理，分批异步处理
                logger.info(f"代理数量较多，将分批异步处理 {total_agent_count} 个代理")
                # 每批处理的代理数量
                batch_size = 100
                
                # 获取所有需要处理的代理ID
                agent_ids = list(agent_relationships.values_list('user_id', flat=True))
                
                # 创建处理任务组
                tasks = []
                for i in range(0, total_agent_count, batch_size):
                    batch_ids = agent_ids[i:i+batch_size]
                    tasks.append(process_agent_commissions_in_batch.s(
                        period_id=period.id,
                        start_date=start_date,
                        end_date=now,
                        agent_ids=batch_ids
                    ))
                
                # 创建分组任务并立即执行
                job = group(tasks)
                result = job.apply_async()
                
                # 添加回调任务，在所有批次处理完成后更新结算周期状态
                result.then(finalize_commission_settlement.s(period_id=period.id))
                
                logger.info(f"已分发 {len(tasks)} 个批次任务处理代理佣金计算")
                return
            
            # 更新结算周期状态 (仅在直接处理时执行)
            update_settlement_period_status(period.id)
        
    except Exception as e:
        logger.error(f"佣金结算任务执行失败: {str(e)}", exc_info=True)
        
        # 确保在出错时也能更新结算周期状态
        try:
            if 'period' in locals() and period:
                period.status = 'failed'
                period.save()
        except Exception as inner_e:
            logger.error(f"更新结算周期状态时出错: {str(inner_e)}")
            
        raise


@shared_task
def process_agent_commissions_in_batch(period_id, start_date, end_date, agent_ids):
    """批量处理代理佣金计算
    
    Args:
        period_id: 结算周期ID
        start_date: 开始日期
        end_date: 结束日期
        agent_ids: 要处理的代理用户ID列表
    
    Returns:
        dict: 处理结果统计
    """
    if not agent_ids:
        logger.warning("没有指定要处理的代理ID")
        return {"success": 0, "failed": 0, "skipped": 0}
    
    # 转换日期为datetime对象(如果是字符串)
    if isinstance(start_date, str):
        start_date = timezone.datetime.fromisoformat(start_date)
    if isinstance(end_date, str):
        end_date = timezone.datetime.fromisoformat(end_date)
    
    logger.info(f"开始批量处理 {len(agent_ids)} 个代理的佣金计算，周期ID: {period_id}")
    
    try:
        # 获取结算周期对象
        period = SettlementPeriod.objects.get(id=period_id)
        
        # 统计处理结果
        results = {
            "success": 0,
            "failed": 0,
            "skipped": 0
        }
        
        # 批量获取代理用户，减少数据库查询
        users = User.objects.filter(id__in=agent_ids)
        
        # 批量处理每个代理的佣金
        for user in users:
            try:
                # 检查代理关系
                try:
                    relationship = AgentRelationship.objects.get(
                        user=user,
                        is_active=True
                    )
                except AgentRelationship.DoesNotExist:
                    logger.warning(f"代理 {user.email} 没有激活的代理关系，跳过处理")
                    results["skipped"] += 1
                    continue
                
                # 检查是否可以结算
                if not relationship.can_settle(start_date, end_date):
                    logger.info(f"代理 {user.email} 在此期间不可结算，跳过")
                    results["skipped"] += 1
                    continue
                
                # 检查是否已有佣金记录
                existing_commission = CommissionRecord.objects.filter(
                    agent=user,
                    settlement_period=period
                ).exists()
                
                if existing_commission:
                    logger.info(f"代理 {user.email} 在该时间段已有佣金记录，跳过处理")
                    results["skipped"] += 1
                    continue
                
                # 使用AgentCommissionService计算佣金
                commission_service = AgentCommissionService(user)
                commission_record = commission_service.calculate_commission(
                    start_date=start_date,
                    end_date=end_date,
                    settlement_period=period
                )
                
                if commission_record:
                    results["success"] += 1
                    logger.info(f"代理 {user.email} 佣金结算成功，记录ID: {commission_record.id}")
                    
                    # --- 新增：触发结算通知 ---
                    try:
                        from message_center.services import MessageService
                        from .utils import format_decimal # 导入格式化工具
                        
                        context = {
                            # username 会由 MessageService 自动添加
                            'settlement_period': f"{period.start_date.strftime('%Y-%m-%d')} - {period.end_date.strftime('%Y-%m-%d')}",
                            'settlement_amount': format_decimal(commission_record.total_commission, 2), # 格式化为2位小数
                            'settlement_time': commission_record.created_at.strftime('%Y-%m-%d %H:%M:%S')
                        }
                        
                        # 同时发送站内信和邮件
                        send_result = MessageService.send_message(
                            user=user,
                            trigger='agent_settlement',
                            context=context,
                            send_email=True
                        )
                        
                        if send_result.get('message_sent') or send_result.get('email_sent'):
                            logger.info(f"已为代理 {user.email} 触发结算通知 (站内信: {send_result.get('message_sent')}, 邮件: {send_result.get('email_sent')})")
                        else:
                            logger.warning(f"为代理 {user.email} 触发结算通知失败: {send_result.get('errors')}")
                            
                    except Exception as notify_error:
                        logger.error(f"触发代理 {user.email} 结算通知时发生意外错误: {notify_error}", exc_info=True)
                    # --- 结束新增 ---
                        
                else:
                    results["failed"] += 1
                
            except Exception as e:
                results["failed"] += 1
                logger.error(f"处理代理 {user.email} 的佣金结算时出错: {str(e)}", exc_info=True)
        
        logger.info(f"批次处理完成，成功: {results['success']}, 失败: {results['failed']}, 跳过: {results['skipped']}")
        return results
        
    except Exception as e:
        logger.error(f"批量处理代理佣金时出错: {str(e)}", exc_info=True)
        raise


@shared_task
def finalize_commission_settlement(batch_results, period_id):
    """完成佣金结算过程，处理所有批次的结果并更新结算周期状态
    
    Args:
        batch_results: 批量处理结果列表
        period_id: 结算周期ID
    """
    logger.info(f"准备完成结算周期 {period_id} 的结算")
    
    try:
        # 合并批次结果
        combined_results = {
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        for result in batch_results:
            combined_results['success'] += result.get('success', 0)
            combined_results['failed'] += result.get('failed', 0)
            combined_results['skipped'] += result.get('skipped', 0)
        
        logger.info(f"结算周期 {period_id} 的汇总结果: {combined_results}")
        
        # 更新结算周期状态
        update_settlement_period_status(period_id)
        
        # 检查结算预警
        check_settlement_warnings(period_id, combined_results)
        
        logger.info(f"结算周期 {period_id} 处理完成")
        
    except Exception as e:
        logger.error(f"完成结算过程时出错: {str(e)}", exc_info=True)
        raise


def update_settlement_period_status(period_id):
    """更新结算周期状态和统计数据
    
    Args:
        period_id: 结算周期ID
    """
    try:
        with transaction.atomic():
            # 获取结算周期
            period = SettlementPeriod.objects.get(id=period_id)
            
            # 获取该周期的佣金记录统计
            commission_stats = CommissionRecord.objects.filter(
                settlement_period_id=period_id
            ).aggregate(
                total_commission=Sum('total_commission'),
                count=Count('id')
            )
            
            # 更新结算周期
            period.status = 'completed'
            period.total_commission = commission_stats.get('total_commission') or 0
            period.agent_count = commission_stats.get('count') or 0
            period.save()
            
            logger.info(f"结算周期 {period_id} 状态已更新为'completed'，成功结算 {period.agent_count} 个代理")
            
    except Exception as e:
        logger.error(f"更新结算周期状态时出错: {str(e)}", exc_info=True)
        try:
            period = SettlementPeriod.objects.get(id=period_id)
            period.status = 'failed'
            period.save()
        except:
            pass
        raise


def check_settlement_warnings(period_id, stats=None):
    """检查结算周期的预警情况
    
    Args:
        period_id: 结算周期ID
        stats: 结算统计数据（可选）
    """
    try:
        from agents.services.warning_service import SettlementWarningService
        warning_service = SettlementWarningService()
        
        # 获取结算周期
        period = SettlementPeriod.objects.get(id=period_id)
        
        # 获取该周期的所有佣金记录
        commission_records = CommissionRecord.objects.filter(
            settlement_period_id=period_id
        ).select_related('agent', 'agent_level')
        
        # 记录预警数量
        agent_warning_count = 0
        system_warning_count = 0
        
        # 检查每个佣金记录的预警
        for record in commission_records:
            warnings = warning_service.process_commission_record(record)
            agent_warning_count += len(warnings)
        
        # 检查周期级别的预警
        if stats:
            system_warnings = warning_service.check_period_warnings(period, stats)
            system_warning_count = len(system_warnings)
        
        # 发送所有预警通知
        notification_count = warning_service.send_pending_notifications()
        
        logger.info(
            f"结算周期 {period_id} 预警检查完成: "
            f"发现 {agent_warning_count} 个代理级预警, "
            f"{system_warning_count} 个系统级预警, "
            f"发送了 {notification_count} 个通知"
        )
        
    except Exception as e:
        logger.error(f"检查结算预警时出错: {str(e)}", exc_info=True)
        # 继续执行，不中断结算流程
        pass


@shared_task
def clear_agent_cache_daily():
    """每日清理代理缓存的任务
    
    在每天零点执行，确保缓存数据不会过期太久
    """
    from django.core.cache import cache
    
    # 使用cache.clear()可能会清除所有缓存，包括非代理相关的
    # 这里选择更精确的清理方式：查找所有代理并清理他们的缓存
    agent_users = User.objects.filter(is_agent=True)
    
    for user in agent_users:
        invalidate_agent_cache(user)
    
    logger.info(f"已清理 {agent_users.count()} 个代理的缓存数据")
    return True


@shared_task
def check_pending_settlements():
    """定期检查处理中的结算周期是否超时"""
    try:
        from agents.services.warning_service import SettlementWarningService
        warning_service = SettlementWarningService()
        
        # 查找所有处理中的结算周期
        processing_periods = SettlementPeriod.objects.filter(
            status='processing'
        )
        
        if not processing_periods.exists():
            logger.info("没有找到处理中的结算周期，跳过检查")
            return
        
        timeout_warnings = []
        for period in processing_periods:
            warning = warning_service.check_settlement_timeout(period)
            if warning:
                timeout_warnings.append(warning)
        
        # 发送预警通知
        notification_count = warning_service.send_pending_notifications()
        
        logger.info(
            f"处理中结算周期检查完成: 找到 {processing_periods.count()} 个处理中的周期, "
            f"发现 {len(timeout_warnings)} 个超时预警, "
            f"发送了 {notification_count} 个通知"
        )
        
    except Exception as e:
        logger.error(f"检查处理中的结算周期时出错: {str(e)}", exc_info=True) 