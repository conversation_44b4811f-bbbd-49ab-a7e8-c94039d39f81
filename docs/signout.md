2025-06-17 00:40:18.921842+0800 Runner[1340:310921] flutter: Menu icon tapped - opening drawer
2025-06-17 00:40:20.135145+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.135441+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:272:13)<…>
2025-06-17 00:40:20.135547+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   CommonDrawer.build.<anonymous closure> (package:sweatmint/presentation/widgets/common/common_drawer.dart:174:70)<…>
2025-06-17 00:40:20.135644+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.135736+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🔐 AuthProvider: 开始执行统一设备会话管理模型的登出流程...<…>
2025-06-17 00:40:20.135824+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.137163+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌──────────────────────────────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.135736+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🔐 AuthProvider: 开始执行统一设备会话管理模型的登出流程...<…>
2025-06-17 00:40:20.135824+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.137163+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌──────────────────────────────\342\224─────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.137312+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   HealthServiceImpl.getHealthDataForDate (package:sweatmint/core/services/health_service_impl.dart:277:15)<…>
2025-06-17 00:40:20.137409+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   HealthServiceImpl.getTodayHealthData (package:sweatmint/core/services/health_service_impl.dart:130:18)<…>
2025-06-17 00:40:20.137501+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.135736+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🔐 AuthProvider: 开始执行统一设备会话管理模型的登出流程...<…>
2025-06-17 00:40:20.135824+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.137163+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌──────────────────────────────\342\224─────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.137312+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   HealthServiceImpl.getHealthDataForDate (package:sweatmint/core/services/health_service_impl.dart:277:15)<…>
2025-06-17 00:40:20.137409+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   HealthServiceImpl.getTodayHealthData (package:sweatmint/core/services/health_service_impl.dart:130:18)<…>
2025-06-17 00:40:20.137501+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.137624+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🔍 开始获取健康数据: 2025-06-17 00:00:00.000<…>
2025-06-17 00:40:20.137715+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.137929+0800 Runner[1340:310921] flutter: Logout initiated
2025-06-17 00:40:20.145900+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.146027+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   HealthServiceImpl.getHealthDataForDate (package:sweatmint/core/services/health_service_impl.dart:284:15)<…>
2025-06-17 00:40:20.146097+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.146168+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.146239+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 📱 设备环境: 真实设备<…>
2025-06-17 00:40:20.146305+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.146642+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.146748+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   HealthServiceImpl.getHealthDataForDate (package:sweatmint/core/services/health_service_impl.dart:288:15)<…>
2025-06-17 00:40:20.146808+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.146869+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.146927+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 📅 查询时间范围: 2025-06-17 00:00:00.000 到 2025-06-18 00:00:00.000<…>
2025-06-17 00:40:20.146991+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.147206+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.147282+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   HealthServiceImpl.getHealthDataForDate (package:sweatmint/core/services/health_service_impl.dart:301:19)<…>
2025-06-17 00:40:20.147341+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.147402+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.147459+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🔍 真机环境：直接尝试获取真实健康数据（通过数据访问验证权限）...<…>
2025-06-17 00:40:20.147522+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.182044+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.182162+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   HealthServiceImpl.getHealthDataForDate (package:sweatmint/core/services/health_service_impl.dart:308:19)<…>
2025-06-17 00:40:20.182199+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.182239+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.182275+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 📊 Health插件返回数据点数量: 0<…>
2025-06-17 00:40:20.182306+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.182416+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.182449+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   HealthServiceImpl.getHealthDataForDate (package:sweatmint/core/services/health_service_impl.dart:311:21)<…>
2025-06-17 00:40:20.182586+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.182717+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.182827+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 ⚠️ 真机环境未获取到健康数据，可能是没有数据或权限问题<…>
2025-06-17 00:40:20.182983+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.184830+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.184917+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:297:21)<…>
2025-06-17 00:40:20.184955+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.184993+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.185025+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 📊 AuthProvider: 登出时获取健康数据成功 - 步数: 0, 距离: 0.0, 卡路里: 0<…>
2025-06-17 00:40:20.185055+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.194357+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.194519+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:325:21)<…>
2025-06-17 00:40:20.194556+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.194584+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.194610+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🌐 AuthProvider: 调用统一设备会话管理模型后端登出API...<…>
2025-06-17 00:40:20.194636+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.195395+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.195449+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthServiceImpl.logout (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:128:15)<…>
2025-06-17 00:40:20.195586+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:326:31)<…>
2025-06-17 00:40:20.195664+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.195690+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🔐 AuthService: 开始执行统一设备会话管理模型的登出流程<…>
2025-06-17 00:40:20.195713+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.195811+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.195837+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthServiceImpl.logout (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:131:17)<…>
2025-06-17 00:40:20.195858+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:326:31)<…>
2025-06-17 00:40:20.195881+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.195903+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 📊 AuthService: 包含健康数据进行登出 - 步数: 0, 距离: 0.0, 卡路里: 0<…>
2025-06-17 00:40:20.195925+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.196324+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌─────────────────────────────────────────────────────────────────────────────────────────────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.195903+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 📊 AuthService: 包含健康数据进行登出 - 步数: 0, 距离: 0.0, 卡路里: 0<…>
2025-06-17 00:40:20.195925+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.196324+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌─────────────────────────────────────────────────────────────────────────────────────────────\342\224──────────────────────────<…>
2025-06-17 00:40:20.196359+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthRepositoryImpl.logout (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:161:15)<…>
2025-06-17 00:40:20.196388+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   AuthServiceImpl.logout (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:136:29)<…>
2025-06-17 00:40:20.196413+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.196434+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🔐 AuthRepository: 开始执行统一设备会话管理模型的登出流程<…>
2025-06-17 00:40:20.196457+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.197003+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.197039+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthRemoteDataSourceImpl.logout (package:sweatmint/features/auth/data/datasources/auth_remote_datasource_impl.dart:277:15)<…>
2025-06-17 00:40:20.197065+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   AuthRepositoryImpl.logout (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:163:30)<…>
2025-06-17 00:40:20.197089+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.197150+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🔐 AuthDataSource: 开始执行统一设备会话管理模型的安全登出流程<…>
2025-06-17 00:40:20.197174+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.197289+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.197289+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────\342\224──────<…>
2025-06-17 00:40:20.197316+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthRemoteDataSourceImpl.logout (package:sweatmint/features/auth/data/datasources/auth_remote_datasource_impl.dart:287:17)<…>
2025-06-17 00:40:20.197340+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   AuthRepositoryImpl.logout (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:163:30)<…>
2025-06-17 00:40:20.197366+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.197391+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 📊 AuthDataSource: 包含健康数据进行登出 - 步数: 0, 距离: 0.0, 卡路里: 0<…>
2025-06-17 00:40:20.197456+0800 Runner[1340:310921] flutter: \^[[38;5;12m└──────────────────────────────────────────────────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.197391+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 📊 AuthDataSource: 包含健康数据进行登出 - 步数: 0, 距离: 0.0, 卡路里: 0<…>
2025-06-17 00:40:20.197456+0800 Runner[1340:310921] flutter: \^[[38;5;12m└──────────────────────────────────────────────────\342\224─────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.204775+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.204857+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.204880+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor.onRequest (package:dio/src/interceptors/log.dart:68:13)
2025-06-17 00:40:20.204907+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.204940+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: *** Request ***
2025-06-17 00:40:20.204964+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.205455+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────
2025-06-17 00:40:20.205455+0800 Runner[1340:310921] flutter: ┌──────────────\342\224─────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.205488+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.205510+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.205532+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.205729+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: uri: http://192.168.100.232:8000/api/app/v1/authentication/logout/
2025-06-17 00:40:20.205752+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.205853+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────
2025-06-17 00:40:20.205853+0800 Runner[1340:310921] flutter: ┌─────────────────────────────────\342\224──────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.205878+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.205898+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.205919+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.205940+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: method: POST
2025-06-17 00:40:20.205963+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.206045+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.206070+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.206090+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.206111+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.206131+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: responseType: ResponseType.json
2025-06-17 00:40:20.206153+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.206232+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.206259+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.206279+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.206300+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄──────────────────
2025-06-17 00:40:20.206259+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.206279+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.206300+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.206394+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: followRedirects: true
2025-06-17 00:40:20.206417+0800 Runner[1340:310921] flutter: └──────────────────────────────────────
2025-06-17 00:40:20.206259+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.206279+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.206300+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.206394+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: followRedirects: true
2025-06-17 00:40:20.206417+0800 Runner[1340:310921] flutter: └────────────────────\342───────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.206259+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.206279+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.206300+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.206394+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: followRedirects: true
2025-06-17 00:40:20.206417+0800 Runner[1340:310921] flutter: └────────────────────\342─────────────────────────────────────────────────────────────────────────────────────\342\224──────────────
2025-06-17 00:40:20.206496+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.206522+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.206542+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.206564+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.206585+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: persistentConnection: true
2025-06-17 00:40:20.206606+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.206690+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.206713+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.206732+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.206928+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.207014+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: connectTimeout: 0:00:10.000000
2025-06-17 00:40:20.207167+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.207415+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.207442+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.207462+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.207483+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄──────
2025-06-17 00:40:20.207442+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.207462+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.207483+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.207542+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: sendTimeout: null
2025-06-17 00:40:20.207564+0800 Runner[1340:310921] flutter: └───────────────────────────────────────
2025-06-17 00:40:20.207442+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.207462+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.207483+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.207542+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: sendTimeout: null
2025-06-17 00:40:20.207564+0800 Runner[1340:310921] flutter: └─────────────────────────────────\342\224──────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.207649+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.207673+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.207694+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.207715+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.207737+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: receiveTimeout: 0:00:10.000000
2025-06-17 00:40:20.207759+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.207846+0800 Runner[1340:310921] flutter: ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.207846+0800 Runner[1340:310921] flutter: ┌──────────────────────────────────────────────────────────────────────────────────────────────\342─────────────────────────
2025-06-17 00:40:20.207870+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.207889+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.207912+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄───────────────────────────────────────────────────────
2025-06-17 00:40:20.207846+0800 Runner[1340:310921] flutter: ┌──────────────────────────────────────────────────────────────────────────────────────────────\342─────────────────────────
2025-06-17 00:40:20.207870+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.207889+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.207912+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄───────────────────────────────────────────────────────
2025-06-17 00:40:20.207846+0800 Runner[1340:310921] flutter: ┌──────────────────────────────────────────────────────────────────────────────────────────────\342─────────────────────────
2025-06-17 00:40:20.207870+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.207889+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.207912+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.207934+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: receiveDataWhenStatusError: true
2025-06-17 00:40:20.207957+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.208063+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.208087+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.208106+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.208128+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.208184+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: extra: {requiresAuth: true}
2025-06-17 00:40:20.208208+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.208290+0800 Runner[1340:310921] flutter: ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.208290+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────\342────────────────────────────────────────────────
2025-06-17 00:40:20.208322+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.208343+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor.onRequest (package:dio/src/interceptors/log.dart:87:15)
2025-06-17 00:40:20.208365+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.208386+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: headers:
2025-06-17 00:40:20.208407+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.208515+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.208544+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.208564+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.208586+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄──────────
2025-06-17 00:40:20.208544+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.208564+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.208586+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.208608+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:  Content-Type: application/json
2025-06-17 00:40:20.208630+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\342\224─────────
2025-06-17 00:40:20.208708+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.208735+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.208757+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor.onRequest (package:dio/src/interceptors/log.dart:91:15)
2025-06-17 00:40:20.208783+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.208806+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: data:
2025-06-17 00:40:20.208827+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.208937+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.208963+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.208985+0800 Runner[1340:310921] flutter: │ #1   List.forEach (dart:core-patch/growable_array.dart:425:8)
2025-06-17 00:40:20.209007+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.209104+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: {refresh_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************.3yxQj698F50FOfhUzRazzyV__IZc97grn7fyONtIKxI, health_data: {steps: 0, distance: 0.0, calories: 0}}
2025-06-17 00:40:20.209134+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.209245+0800 Runner[1340:310921] flutter: ┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\342\224──────────────
2025-06-17 00:40:20.209374+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.209400+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor.onRequest (package:dio/src/interceptors/log.dart:94:13)
2025-06-17 00:40:20.209427+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.209449+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:
2025-06-17 00:40:20.209481+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.216597+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────
2025-06-17 00:40:20.216597+0800 Runner[1340:310921] flutter: ┌─────────────────────────────────────────────────────────────\342\224──────────────────────────────────────────────────────────
2025-06-17 00:40:20.216653+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:65:25)
2025-06-17 00:40:20.216677+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.216700+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.216721+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: Using stored token for logout request to avoid conflict check.
2025-06-17 00:40:20.216744+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.216829+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────
2025-06-17 00:40:20.216829+0800 Runner[1340:310921] flutter: ┌───────────\342\224────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.216856+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:77:23)
2025-06-17 00:40:20.216874+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.216899+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.217633+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: Added authorization header for /api/app/v1/authentication/logout/
2025-06-17 00:40:20.217656+0800 Runner[1340:310921] flutter: └──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\342\224──────────────────
2025-06-17 00:40:20.324465+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.324567+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.324593+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor.onResponse (package:dio/src/interceptors/log.dart:101:13)
2025-06-17 00:40:20.324622+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.324646+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: *** Response ***
2025-06-17 00:40:20.324670+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.324825+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.324852+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.325058+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.325223+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.325405+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: uri: http://192.168.100.232:8000/api/app/v1/authentication/logout/
2025-06-17 00:40:20.325576+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.325807+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────
2025-06-17 00:40:20.325807+0800 Runner[1340:310921] flutter: ┌────────────────────────────────\342\224───────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.325835+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.326019+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.326152+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.326624+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: statusCode: 200
2025-06-17 00:40:20.326767+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.327211+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.327250+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.327273+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printResponse (package:dio/src/interceptors/log.dart:129:15)
2025-06-17 00:40:20.327401+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.327584+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: headers:
2025-06-17 00:40:20.327704+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.327953+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────
2025-06-17 00:40:20.327953+0800 Runner[1340:310921] flutter: ┌───────────────────\342\224────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.328034+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.328183+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.328414+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.328556+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:  vary: origin, Cookie
2025-06-17 00:40:20.328716+0800 Runner[1340:310921] flutter: └─────────────────────────────────────────────────────────────────────────────────────────────────────────────┄┄┄┄┄
2025-06-17 00:40:20.328556+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:  vary: origin, Cookie
2025-06-17 00:40:20.328716+0800 Runner[1340:310921] flutter: └─────────────────────────────────────────────────────────────────────────────────────────────────────────────\342──────────
2025-06-17 00:40:20.329038+0800 Runner[1340:310921] flutter: ┌─────────────────────────────────────────────────────┄┄┄┄┄
2025-06-17 00:40:20.328556+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:  vary: origin, Cookie
2025-06-17 00:40:20.328716+0800 Runner[1340:310921] flutter: └─────────────────────────────────────────────────────────────────────────────────────────────────────────────\342──────────
2025-06-17 00:40:20.329038+0800 Runner[1340:310921] flutter: ┌─────────────────────────────────────────────────────\342\224──────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.329135+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.329326+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.329408+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.329610+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:  content-length: 54
2025-06-17 00:40:20.329692+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.329984+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.330011+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.330107+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.330235+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄───────────────────────────────
2025-06-17 00:40:20.330011+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.330107+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.330235+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄───────────────────────────────
2025-06-17 00:40:20.330011+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.330107+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.330235+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.330330+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:  referrer-policy: same-origin
2025-06-17 00:40:20.330482+0800 Runner[1340:310921] flutter: └───────────────────────────────────
2025-06-17 00:40:20.330011+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.330107+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.330235+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.330330+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:  referrer-policy: same-origin
2025-06-17 00:40:20.330482+0800 Runner[1340:310921] flutter: └────\342\224───────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.330703+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.330788+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.330855+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.331011+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.331075+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:  content-type: application/json
2025-06-17 00:40:20.331238+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.331584+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.331611+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.331748+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.331830+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.331969+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:  cross-origin-opener-policy: same-origin
2025-06-17 00:40:20.332078+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.332305+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.332335+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.332464+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.332608+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.332691+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:  x-content-type-options: nosniff
2025-06-17 00:40:20.332837+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.333182+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.333216+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.333237+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.333260+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.333281+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:  server: daphne
2025-06-17 00:40:20.333303+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.333405+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.333430+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.333515+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-06-17 00:40:20.333656+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.333732+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:  allow: POST, OPTIONS
2025-06-17 00:40:20.333895+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.334131+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.334267+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.334348+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printResponse (package:dio/src/interceptors/log.dart:133:15)
2025-06-17 00:40:20.334514+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.334614+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: Response Text:
2025-06-17 00:40:20.334718+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.334976+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────
2025-06-17 00:40:20.334976+0800 Runner[1340:310921] flutter: ┌────────────────────────────────────────────────────────\342\224───────────────────────────────────────────────────────────────
2025-06-17 00:40:20.335003+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.335123+0800 Runner[1340:310921] flutter: │ #1   List.forEach (dart:core-patch/growable_array.dart:425:8)
2025-06-17 00:40:20.335228+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.335793+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient: {"code":200,"message":"Logout successful","data":null}
2025-06-17 00:40:20.335817+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.335969+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.336441+0800 Runner[1340:310921] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:43:37)
2025-06-17 00:40:20.336463+0800 Runner[1340:310921] flutter: │ #1   LogInterceptor._printResponse (package:dio/src/interceptors/log.dart:136:13)
2025-06-17 00:40:20.336485+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.336504+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:
2025-06-17 00:40:20.336525+0800 Runner[1340:310921] flutter: └────────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.336504+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:
2025-06-17 00:40:20.336525+0800 Runner[1340:310921] flutter: └────────\342\224───────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.340461+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌──────────────────────────────────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.336504+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:
2025-06-17 00:40:20.336525+0800 Runner[1340:310921] flutter: └────────\342\224───────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.340461+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌──────────────────────────────────\342─────────────────────────────────────────────────────────────────────────────────────<┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.336504+0800 Runner[1340:310921] flutter: │ 🐛 ApiClient:
2025-06-17 00:40:20.336525+0800 Runner[1340:310921] flutter: └────────\342\224───────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.340461+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌──────────────────────────────────\342─────────────────────────────────────────────────────────────────────────────────────<\342…>
2025-06-17 00:40:20.340549+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthRemoteDataSourceImpl.logout (package:sweatmint/features/auth/data/datasources/auth_remote_datasource_impl.dart:299:15)<…>
2025-06-17 00:40:20.340581+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.340606+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.340633+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 ✅ AuthDataSource: 统一设备会话管理模型登出API调用成功<…>
2025-06-17 00:40:20.340655+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.341055+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.341086+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthRemoteDataSourceImpl.logout (package:sweatmint/features/auth/data/datasources/auth_remote_datasource_impl.dart:300:15)<…>
2025-06-17 00:40:20.341192+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.341358+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.341468+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🔒 后端已执行: token黑名单、健康数据快照、会话状态更新、审计日志记录<…>
2025-06-17 00:40:20.341580+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.342353+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.342399+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthRepositoryImpl.logout (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:168:15)<…>
2025-06-17 00:40:20.342426+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.342451+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄──────────────────────────────────────────<…>
2025-06-17 00:40:20.342353+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.342399+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthRepositoryImpl.logout (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:168:15)<…>
2025-06-17 00:40:20.342426+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.342451+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄──────────────────────────────────────────<…>
2025-06-17 00:40:20.342353+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.342399+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthRepositoryImpl.logout (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:168:15)<…>
2025-06-17 00:40:20.342426+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.342451+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.342475+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 ✅ AuthRepository: 统一设备会话管理模型登出流程完成<…>
2025-06-17 00:40:20.342498+0800 Runner[1340:310921] flutter: \^[[38;5;12m└────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.342353+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.342399+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthRepositoryImpl.logout (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:168:15)<…>
2025-06-17 00:40:20.342426+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.342451+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.342475+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 ✅ AuthRepository: 统一设备会话管理模型登出流程完成<…>
2025-06-17 00:40:20.342498+0800 Runner[1340:310921] flutter: \^[[38;5;12m└──────────────────────────────────────────────────────────────────────────────\342─────────────────────────────────────────<…>
2025-06-17 00:40:20.343168+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.343196+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthRepositoryImpl.logout (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:169:15)<…>
2025-06-17 00:40:20.343216+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.343238+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.343449+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🔒 已完成: 后端API调用、token黑名单、健康数据快照、会话终止<…>
2025-06-17 00:40:20.343634+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.343969+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌──────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.343969+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌────────────\342───────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.343999+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthServiceImpl.logout (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:141:15)<…>
2025-06-17 00:40:20.344021+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.344152+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.344355+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 ✅ AuthService: 统一设备会话管理模型登出流程完成<…>
2025-06-17 00:40:20.344520+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.344958+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.344990+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthServiceImpl.logout (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:142:15)<…>
2025-06-17 00:40:20.345180+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.345209+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.345324+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🔒 已完成完整的会话终止流程: 后端API调用、token黑名单、健康数据快照、会话状态更新、审计日志<…>
2025-06-17 00:40:20.345487+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.345724+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.345750+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:330:21)<…>
2025-06-17 00:40:20.345769+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.345842+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.345971+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 统一设备会话管理模型后端登出API调用成功<…>
2025-06-17 00:40:20.346049+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.346296+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.346377+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:331:21)<…>
2025-06-17 00:40:20.346446+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.346549+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.346699+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🔒 后端已完成: token黑名单、健康数据快照、会话状态更新、审计┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.346699+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🔒 后端已完成: token黑名单、健康数据快照、会话状态更新、审计\346日志记录<…>
2025-06-17 00:40:20.346822+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.347094+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.347137+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:347:15)<…>
2025-06-17 00:40:20.347260+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.347366+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.347523+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🔒 AuthProvider: 用户状态已重置为未认证状态<…>
2025-06-17 00:40:20.347596+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.348921+0800 Runner[1340:310921] flutter: ┌──────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.348921+0800 Runner[1340:310921] flutter: ┌───\342────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.348921+0800 Runner[1340:310921] flutter: ┌───\342─────────────────────────────────────────────────────────────────────────────────────\342\224───────────────────────────────
2025-06-17 00:40:20.349017+0800 Runner[1340:310921] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:183:17)

2025-06-17 00:40:20.349044+0800 Runner[1340:310921] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-06-17 00:40:20.349247+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.349273+0800 Runner[1340:310921] flutter: │ 🐛 Redirect check: Current location: /home, Auth status: AuthStatus.unauthenticated
2025-06-17 00:40:20.349297+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.349743+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\342\224─────────────────
2025-06-17 00:40:20.349773+0800 Runner[1340:310921] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:229:19)
2025-06-17 00:40:20.349795+0800 Runner[1340:310921] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-06-17 00:40:20.349825+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.349847+0800 Runner[1340:310921] flutter: │ 🐛 Redirect: Unauthenticated user accessing protected route (/home). Redirecting to login.
2025-06-17 00:40:20.349868+0800 Runner[1340:310921] flutter: └────────────────────────────────────────────────────────────────────────────────────└────────────────────────────────────────────────────────────────────────────────────\342\224───────────────────────────────────
2025-06-17 00:40:20.350685+0800 Runner[1340:310921] flutter: ┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────────└────────────────────────────────────────────────────────────────────────────────────\342\224───────────────────────────────────
2025-06-17 00:40:20.350685+0800 Runner[1340:310921] flutter: ┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────────\342─────
2025-06-17 00:40:20.350721+0800 Runner[1340:310921] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:183:17)
2025-06-17 00:40:20.350744+0800 Runner[1340:310921] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-06-17 00:40:20.350769+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.350834+0800 Runner[1340:310921] flutter: │ 🐛 Redirect check: Current location: /login, Auth status: AuthStatus.unauthenticated
2025-06-17 00:40:20.350858+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.350975+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────
2025-06-17 00:40:20.350975+0800 Runner[1340:310921] flutter: ┌──────────────────────────────────────────\342\224─────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.351001+0800 Runner[1340:310921] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:225:21)
2025-06-17 00:40:20.351023+0800 Runner[1340:310921] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-06-17 00:40:20.351044+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.351114+0800 Runner[1340:310921] flutter: │ 🐛 Redirect: Unauthenticated user accessing public route (/login). Allowed.
2025-06-17 00:40:20.351152+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.359079+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.359139+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   TokenManager.clearAllTokens (package:sweatmint/core/services/token_manager.dart:713:15)<…>
2025-06-17 00:40:20.359164+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.359190+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.359303+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 TokenManager: All tokens cleared<…>
2025-06-17 00:40:20.359339+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.359457+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.359483+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:353:17)<…>
2025-06-17 00:40:20.359710+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.359735+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.359757+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🔑 AuthProvider: TokenManager中的Token已清理<…>
2025-06-17 00:40:20.359781+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.360883+0800 Runner[1340:310921] flutter: \^[[38;5;12m───────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.360883+0800 Runner[1340:310921] flutter: \^[[38;5;12m\342┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.360883+0800 Runner[1340:310921] flutter: \^[[38;5;12m\342┌────────────────────────────────────────────────────────────────────────────────────\342\224───────────────────────────────────<…>
2025-06-17 00:40:20.360932+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   CacheService.clearUserCaches (package:sweatmint/core/services/cache_service.dart:142:14)<…>
2025-06-17 00:40:20.360954+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:360:37)<…>
2025-06-17 00:40:20.360976+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.361036+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 00:40:20.360 (+0:29:04.967065)<…>
2025-06-17 00:40:20.361058+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.361078+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 CacheService: 开始清除用户相关缓存<…>
2025-06-17 00:40:20.361101+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.361722+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.361758+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.361779+0800 Runner[1340:310921] flutter: │ #1   CacheService.clearUserCaches (package:sweatmint/core/services/cache_service.dart:146:22)
2025-06-17 00:40:20.361801+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.361821+0800 Runner[1340:310921] flutter: │ 00:40:20.361 (+0:29:04.967975)
2025-06-17 00:40:20.361843+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.361873+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 dashboard 的缓存
2025-06-17 00:40:20.361897+0800 Runner[1340:310921] flutter: └─────────────────────────────────────────────────────────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.361873+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 dashboard 的缓存
2025-06-17 00:40:20.361897+0800 Runner[1340:310921] flutter: └─────────────────────────────────────────────────────────\342──────────────────────────────────────────────────────────────
2025-06-17 00:40:20.362484+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.362516+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.362538+0800 Runner[1340:310921] flutter: │ #1   CacheService.clearUserCaches (package:sweatmint/core/services/cache_service.dart:147:22)
2025-06-17 00:40:20.362561+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.362582+0800 Runner[1340:310921] flutter: │ 00:40:20.362 (+0:29:04.968767)
2025-06-17 00:40:20.362606+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.362582+0800 Runner[1340:310921] flutter: │ 00:40:20.362 (+0:29:04.968767)
2025-06-17 00:40:20.362606+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.362582+0800 Runner[1340:310921] flutter: │ 00:40:20.362 (+0:29:04.968767)
2025-06-17 00:40:20.362606+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.362625+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 user 的缓存
2025-06-17 00:40:20.362648+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.362885+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.362920+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.362943+0800 Runner[1340:310921] flutter: │ #1   CacheService.clearUserCaches (package:sweatmint/core/services/cache_service.dart:148:22)
2025-06-17 00:40:20.362966+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.362994+0800 Runner[1340:310921] flutter: │ 00:40:20.362 (+0:29:04.969189)
2025-06-17 00:40:20.363016+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.363036+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 vip 的缓存
2025-06-17 00:40:20.363136+0800 Runner[1340:310921] flutter: └───────────────────┄
2025-06-17 00:40:20.363036+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 vip 的缓存
2025-06-17 00:40:20.363136+0800 Runner[1340:310921] flutter: └───────────────────\342\224────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.363248+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.363274+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.363296+0800 Runner[1340:310921] flutter: │ #1   CacheService.clearUserCaches (package:sweatmint/core/services/cache_service.dart:149:22)
2025-06-17 00:40:20.363318+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.363338+0800 Runner[1340:310921] flutter: │ 00:40:20.363 (+0:29:04.969556)
2025-06-17 00:40:20.363360+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.363380+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 daily_tasks 的缓存
2025-06-17 00:40:20.363404+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.363518+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.363550+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.363572+0800 Runner[1340:310921] flutter: │ #1   CacheService.clearUserCaches (package:sweatmint/core/services/cache_service.dart:150:22)
2025-06-17 00:40:20.363593+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.363998+0800 Runner[1340:310921] flutter: │ 00:40:20.363 (+0:29:04.969828)
2025-06-17 00:40:20.364022+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.364042+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 addon_tasks 的缓存
2025-06-17 00:40:20.364064+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.364176+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.364237+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.364341+0800 Runner[1340:310921] flutter: │ #1   CacheService.clearUserCaches (package:sweatmint/core/services/cache_service.dart:151:22)
2025-06-17 00:40:20.364426+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.364636+0800 Runner[1340:310921] flutter: │ 00:40:20.364 (+0:29:04.970483)
2025-06-17 00:40:20.364742+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.364854+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 today_summary 的缓存
2025-06-17 00:40:20.364958+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.365568+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.365607+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.365630+0800 Runner[1340:310921] flutter: │ #1   CacheService.clearUserCaches (package:sweatmint/core/services/cache_service.dart:152:22)
2025-06-17 00:40:20.365652+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.365915+0800 Runner[1340:310921] flutter: │ 00:40:20.365 (+0:29:04.971621)
2025-06-17 00:40:20.365995+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.366163+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 health_data 的缓存
2025-06-17 00:40:20.366277+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.366575+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.366605+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.366690+0800 Runner[1340:310921] flutter: │ #1   CacheService.clearUserCaches (package:sweatmint/core/services/cache_service.dart:153:22)
2025-06-17 00:40:20.366792+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.367187+0800 Runner[1340:310921] flutter: │ 00:40:20.366 (+0:29:04.972849)
2025-06-17 00:40:20.367225+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.367383+0800 Runner[1340:310921] flutter: │ ┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.367383+0800 Runner[1340:310921] flutter: │ \360🐛 CacheManager.clearByPrefix: 清除前缀为 rewards 的缓存
2025-06-17 00:40:20.367490+0800 Runner[1340:310921] flutter: └─────────────────────────────────────────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.367383+0800 Runner[1340:310921] flutter: │ \360🐛 CacheManager.clearByPrefix: 清除前缀为 rewards 的缓存
2025-06-17 00:40:20.367490+0800 Runner[1340:310921] flutter: └─────────────────────────────────────────\342\224──────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.367862+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.367889+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.367911+0800 Runner[1340:310921] flutter: │ #1   CacheService.clearUserCaches (package:sweatmint/core/services/cache_service.dart:154:22)
2025-06-17 00:40:20.367979+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.368127+0800 Runner[1340:310921] flutter: │ 00:40:20.367 (+0:29:04.974090)
2025-06-17 00:40:20.368223+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.368329+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 exchange 的缓存
2025-06-17 00:40:20.368415+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.369965+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.370015+0800 Runner[1340:310921] flutter: │ #0   main.<anonymous closure> (package:sweatmint/main.dart:304:21)
2025-06-17 00:40:20.370037+0800 Runner[1340:310921] flutter: │ #1   new ListenableProxyProvider.<anonymous closure> (package:provider/src/listenable_provider.dart:122:48)
2025-06-17 00:40:20.370064+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.370148+0800 Runner[1340:310921] flutter: │ 🐛 🔄 ProxyProvider updating HealthProvider. Auth status: AuthStatus.unauthenticated
2025-06-17 00:40:20.370170+0800 Runner[1340:310921] flutter: └────────────────────────────────────────────────────────────────────────────────────┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.370148+0800 Runner[1340:310921] flutter: │ 🐛 🔄 ProxyProvider updating HealthProvider. Auth status: AuthStatus.unauthenticated
2025-06-17 00:40:20.370170+0800 Runner[1340:310921] flutter: └────────────────────────────────────────────────────────────────────────────────────\342───────────────────────────────────
2025-06-17 00:40:20.370299+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌────────────────────────┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.370148+0800 Runner[1340:310921] flutter: │ 🐛 🔄 ProxyProvider updating HealthProvider. Auth status: AuthStatus.unauthenticated
2025-06-17 00:40:20.370170+0800 Runner[1340:310921] flutter: └────────────────────────────────────────────────────────────────────────────────────\342───────────────────────────────────
2025-06-17 00:40:20.370299+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌────────────────────────\342\224───────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.370440+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   main.<anonymous closure> (package:sweatmint/main.dart:308:23)<…>
2025-06-17 00:40:20.370536+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   new ListenableProxyProvider.<anonymous closure> (package:provider/src/listenable_provider.dart:122:48)<…>
2025-06-17 00:40:20.370694+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.370819+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🚨 认证状态变为未认证，清理HealthProvider健康数据<…>
2025-06-17 00:40:20.371139+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.371813+0800 Runner[1340:310921] flutter: ┌──────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.371813+0800 Runner[1340:310921] flutter: ┌────────────────────\342───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.371813+0800 Runner[1340:310921] flutter: ┌────────────────────\342─────────────────────────────────────────────────────────────────────────────────────\342\224──────────────
2025-06-17 00:40:20.371851+0800 Runner[1340:310921] flutter: │ #0   main.<anonymous closure> (package:sweatmint/main.dart:340:21)
2025-06-17 00:40:20.371873+0800 Runner[1340:310921] flutter: │ #1   new ListenableProxyProvider2.<anonymous closure> (package:provider/src/listenable_provider.dart:149:48)
2025-06-17 00:40:20.371896+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.371961+0800 Runner[1340:310921] flutter: │ 🐛 🔄 ProxyProvider updating HomeProvider. Auth status: AuthStatus.unauthenticated
2025-06-17 00:40:20.372053+0800 Runner[1340:310921] flutter: └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\342\224───────────────────────
2025-06-17 00:40:20.372323+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.372350+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   main.<anonymous closure> (package:sweatmint/main.dart:344:23)<…>
2025-06-17 00:40:20.372372+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   new ListenableProxyProvider2.<anonymous closure> (package:provider/src/listenable_provider.dart:149:48)<…>
2025-06-17 00:40:20.372396+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄<…>
2025-06-17 00:40:20.372551+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🚨 认证状态变为未认证，清理HomeProvider缓存数据<…>
2025-06-17 00:40:20.372577+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.379456+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.379538+0800 Runner[1340:310921] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:183:17)
2025-06-17 00:40:20.379573+0800 Runner[1340:310921] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-06-17 00:40:20.379596+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.380010+0800 Runner[1340:310921] flutter: │ 🐛 Redirect check: Current location: /, Auth status: AuthStatus.unauthenticated
2025-06-17 00:40:20.380036+0800 Runner[1340:310921] flutter: └──────────────────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.380010+0800 Runner[1340:310921] flutter: │ 🐛 Redirect check: Current location: /, Auth status: AuthStatus.unauthenticated
2025-06-17 00:40:20.380036+0800 Runner[1340:310921] flutter: └──────────────────\342\224─────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.381187+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.381220+0800 Runner[1340:310921] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:220:21)
2025-06-17 00:40:20.381244+0800 Runner[1340:310921] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-06-17 00:40:20.381267+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.381290+0800 Runner[1340:310921] flutter: │ 🐛 Redirect: Unauthenticated user at splash. Redirecting to login.
2025-06-17 00:40:20.381313+0800 Runner[1340:310921] flutter: └──────────────────────────────────────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.381290+0800 Runner[1340:310921] flutter: │ 🐛 Redirect: Unauthenticated user at splash. Redirecting to login.
2025-06-17 00:40:20.381313+0800 Runner[1340:310921] flutter: └──────────────────────────────────────\342─────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.383206+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.383261+0800 Runner[1340:310921] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:183:17)
2025-06-17 00:40:20.383286+0800 Runner[1340:310921] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-06-17 00:40:20.383309+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.383331+0800 Runner[1340:310921] flutter: │ 🐛 Redirect check: Current location: /login, Auth status: AuthStatus.unauthenticated
2025-06-17 00:40:20.383353+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.384309+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.384349+0800 Runner[1340:310921] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:225:21)
2025-06-17 00:40:20.384372+0800 Runner[1340:310921] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-06-17 00:40:20.384396+0800 Runner[1340:310921] flutter: ├┄──────────────────────────────────────
2025-06-17 00:40:20.384349+0800 Runner[1340:310921] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:225:21)
2025-06-17 00:40:20.384372+0800 Runner[1340:310921] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-06-17 00:40:20.384396+0800 Runner[1340:310921] flutter: ├┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.384417+0800 Runner[1340:310921] flutter: │ 🐛 Redirect: Unauthenticated user accessing public route (/login). Allowed.
2025-06-17 00:40:20.384438+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.569387+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.569495+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   GlobalAuthService.setNavigatorContext (package:sweatmint/core/services/global_auth_service.dart:31:13)<…>
2025-06-17 00:40:20.569526+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   MyApp.build.<anonymous closure>.<anonymous closure>.<anonymous closure> (package:sweatmint/main.dart:438:35)<…>
2025-06-17 00:40:20.569560+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.569585+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 GlobalAuthService: Navigator context set<…>
2025-06-17 00:40:20.569612+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.569728+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.569759+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   MyApp.build.<anonymous closure>.<anonymous closure>.<anonymous closure> (package:sweatmint/main.dart:439:25)<…>
2025-06-17 00:40:20.569795+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)<…>
2025-06-17 00:40:20.569821+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.569846+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 GlobalAuthService: Navigator context set successfully<…>
2025-06-17 00:40:20.569876+0800 Runner[1340:310921] flutter: \^[[38;5;12m└─────┄┄┄┄┄<…>
2025-06-17 00:40:20.569846+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 GlobalAuthService: Navigator context set successfully<…>
2025-06-17 00:40:20.569876+0800 Runner[1340:310921] flutter: \^[[38;5;12m└─────\342\224──────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.573380+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.573447+0800 Runner[1340:310921] flutter: │ #0   main.<anonymous closure>.<anonymous closure> (package:sweatmint/main.dart:314:27)
2025-06-17 00:40:20.573476+0800 Runner[1340:310921] flutter: │ #1   new Future.microtask.<anonymous closure> (dart:async/future.dart:287:40)
2025-06-17 00:40:20.573506+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.573579+0800 Runner[1340:310921] flutter: │ 🐛 ✅ HealthProvider健康数据清理完成
2025-06-17 00:40:20.573607+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.574002+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.574038+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:72:14)<…>
2025-06-17 00:40:20.574066+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   main.<anonymous closure>.<anonymous closure> (package:sweatmint/main.dart:349:42)<…>
2025-06-17 00:40:20.574093+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.574181+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 00:40:20.573 (+0:29:05.180267)<…>
2025-06-17 00:40:20.574207+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.574231+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 HomeProvider: 开始清除用户相关缓存<…>
2025-06-17 00:40:20.574258+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.574461+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.574505+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.574531+0800 Runner[1340:310921] flutter: │ #1   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:76:22)
2025-06-17 00:40:20.574557+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.574676+0800 Runner[1340:310921] flutter: │ 00:40:20.574 (+0:29:05.180732)
2025-06-17 00:40:20.574709+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.574733+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 dashboard 的缓┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.574733+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 dashboard 的缓\345\255存
2025-06-17 00:40:20.574758+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.574979+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.575016+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.575044+0800 Runner[1340:310921] flutter: │ #1   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:77:22)
2025-06-17 00:40:20.575069+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.575092+0800 Runner[1340:310921] flutter: │ 00:40:20.574 (+0:29:05.181268)
2025-06-17 00:40:20.575118+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.575143+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 user 的缓存
2025-06-17 00:40:20.575170+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.575289+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.575319+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.575345+0800 Runner[1340:310921] flutter: │ #1   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:78:22)
2025-06-17 00:40:20.575369+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄──────────
2025-06-17 00:40:20.575319+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.575345+0800 Runner[1340:310921] flutter: │ #1   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:78:22)
2025-06-17 00:40:20.575369+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄──────────
2025-06-17 00:40:20.575319+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.575345+0800 Runner[1340:310921] flutter: │ #1   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:78:22)
2025-06-17 00:40:20.575369+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄
2025-06-17 00:40:20.575393+0800 Runner[1340:310921] flutter: │ 00:40:20.575 (+0:29:05.181593)
2025-06-17 00:40:20.575417+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄──────────
2025-06-17 00:40:20.575319+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.575345+0800 Runner[1340:310921] flutter: │ #1   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:78:22)
2025-06-17 00:40:20.575369+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄
2025-06-17 00:40:20.575393+0800 Runner[1340:310921] flutter: │ 00:40:20.575 (+0:29:05.181593)
2025-06-17 00:40:20.575417+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.575440+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 vip 的缓存
2025-06-17 00:40:20.575542+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.575674+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.575703+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.575729+0800 Runner[1340:310921] flutter: │ #1   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:79:22)
2025-06-17 00:40:20.575818+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.575849+0800 Runner[1340:310921] flutter: │ 00:40:20.575 (+0:29:05.181977)
2025-06-17 00:40:20.575872+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.575895+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 daily_tasks 的缓存
2025-06-17 00:40:20.575922+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.576042+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.576070+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.576096+0800 Runner[1340:310921] flutter: │ #1   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:80:22)
2025-06-17 00:40:20.576127+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.576148+0800 Runner[1340:310921] flutter: │ 00:40:20.575 (+0:29:05.182347)
2025-06-17 00:40:20.576174+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.576510+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 addon_tasks 的缓存
2025-06-17 00:40:20.576539+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.576655+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.576687+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.576713+0800 Runner[1340:310921] flutter: │ #1   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:81:22)
2025-06-17 00:40:20.576736+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.576907+0800 Runner[1340:310921] flutter: │ 00:40:20.576 (+0:29:05.182960)
2025-06-17 00:40:20.576935+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.576960+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 today_summary 的缓存
2025-06-17 00:40:20.576984+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.601373+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.601572+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.601612+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.601672+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.601699+0800 Runner[1340:310921] flutter: │ 00:40:20.600 (+0:29:05.207207)
2025-06-17 00:40:20.601727+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.601699+0800 Runner[1340:310921] flutter: │ 00:40:20.600 (+0:29:05.207207)
2025-06-17 00:40:20.601727+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.601763+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 daily_tasks，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.601790+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.603203+0800 Runner[1340:310921] flutter: ┌───────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.601699+0800 Runner[1340:310921] flutter: │ 00:40:20.600 (+0:29:05.207207)
2025-06-17 00:40:20.601727+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.601763+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 daily_tasks，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.601790+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.603203+0800 Runner[1340:310921] flutter: ┌───────\342────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.603262+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.603284+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.603310+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.603987+0800 Runner[1340:310921] flutter: │ 00:40:20.602 (+0:29:05.209326)
2025-06-17 00:40:20.604035+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.604067+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 vip，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.604101+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.608866+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.608949+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.608974+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.609002+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.609029+0800 Runner[1340:310921] flutter: │ 00:40:20.608 (+0:29:05.215032)
2025-06-17 00:40:20.609056+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.609082+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 today_summary，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.609107+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.610141+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.610333+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.610540+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.610660+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.610903+0800 Runner[1340:310921] flutter: │ 00:40:20.609 (+0:29:05.216283)
2025-06-17 00:40:20.610994+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.611330+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 user，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.611359+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.611813+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.611843+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.611864+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.611889+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.612157+0800 Runner[1340:310921] flutter: │ 00:40:20.611 (+0:29:05.218021)
2025-06-17 00:40:20.612185+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.612208+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 rewards，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.612232+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.612446+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.612478+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.612499+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.612523+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.612749+0800 Runner[1340:310921] flutter: │ 00:40:20.612 (+0:29:05.218722)
2025-06-17 00:40:20.612775+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.612807+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 health_data，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.612831+0800 Runner[1340:310921] flutter: └──────────────────────────────────────────────────────────────────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.612807+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 health_data，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.612831+0800 Runner[1340:310921] flutter: └──────────────────────────────────────────────────────────────────\342\224─────────────────────────────────────────────────────
2025-06-17 00:40:20.613124+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.613154+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.613179+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.613204+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.613492+0800 Runner[1340:310921] flutter: │ 00:40:20.613 (+0:29:05.219400)
2025-06-17 00:40:20.613520+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.613543+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 exchange，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.613567+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.613750+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────
2025-06-17 00:40:20.613750+0800 Runner[1340:310921] flutter: ┌────────────────────────────────────────\342\224───────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.613780+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.613802+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.613909+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.613937+0800 Runner[1340:310921] flutter: │ 00:40:20.613 (+0:29:05.220046)
2025-06-17 00:40:20.613960+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.613983+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.613983+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除\345完成，前缀 addon_tasks，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.614148+0800 Runner[1340:310921] flutter: └───────────────────────────────────────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.613983+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除\345完成，前缀 addon_tasks，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.614148+0800 Runner[1340:310921] flutter: └───────────────────────────────────────\342\224────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.616783+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.616847+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.616873+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.616899+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.616935+0800 Runner[1340:310921] flutter: │ 00:40:20.616 (+0:29:05.222974)
2025-06-17 00:40:20.616959+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.616935+0800 Runner[1340:310921] flutter: │ 00:40:20.616 (+0:29:05.222974)
2025-06-17 00:40:20.616959+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.616983+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 addon_tasks，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.617007+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.617358+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.617407+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.617431+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.617456+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.617478+0800 Runner[1340:310921] flutter: │ 00:40:20.617 (+0:29:05.223615)
2025-06-17 00:40:20.617501+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.617525+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 vip，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.617550+0800 Runner[1340:310921] flutter: └──────────────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.617525+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 vip，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.617550+0800 Runner[1340:310921] flutter: └──────────────\342─────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.617745+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.617777+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.617798+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.617822+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.617907+0800 Runner[1340:310921] flutter: │ 00:40:20.617 (+0:29:05.224039)
2025-06-17 00:40:20.617933+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.617957+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.617957+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，\345\211前缀 daily_tasks，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.617983+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.618185+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.618217+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.618242+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.618265+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.618353+0800 Runner[1340:310921] flutter: │ 00:40:20.618 (+0:29:05.224474)
2025-06-17 00:40:20.618381+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.618404+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 today_summary，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.618428+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.618603+0800 Runner[1340:310921] flutter: ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\342\224───────────────────
2025-06-17 00:40:20.618635+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.618657+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.618682+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.618706+0800 Runner[1340:310921] flutter: │ 00:40:20.618 (+0:29:05.224904)
2025-06-17 00:40:20.618729+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.618755+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 user，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.618825+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.620429+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.620477+0800 Runner[1340:310921] flutter: │ #0   main.<anonymous closure> (package:sweatmint/main.dart:340:21)
2025-06-17 00:40:20.620505+0800 Runner[1340:310921] flutter: │ #1   new ListenableProxyProvider2.<anonymous closure> (package:provider/src/listenable_provider.dart:149:48)
2025-06-17 00:40:20.620532+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.620556+0800 Runner[1340:310921] flutter: │ 🐛 ┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.620556+0800 Runner[1340:310921] flutter: │ 🐛 \360\237\224🔄 ProxyProvider updating HomeProvider. Auth status: AuthStatus.unauthenticated
2025-06-17 00:40:20.620579+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.620714+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.620747+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   main.<anonymous closure> (package:sweatmint/main.dart:344:23)<…>
2025-06-17 00:40:20.620774+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   new ListenableProxyProvider2.<anonymous closure> (package:provider/src/listenable_provider.dart:149:48)<…>
2025-06-17 00:40:20.620803+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.620826+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🚨 认证状态变为未认证，清理HomeProvider缓存数据<…>
2025-06-17 00:40:20.620852+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.653924+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.654017+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:72:14)<…>
2025-06-17 00:40:20.654047+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   main.<anonymous closure>.<anonymous closure> (package:sweatmint/main.dart:349:42)<…>
2025-06-17 00:40:20.654079+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.654122+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 00:40:20.653 (+0:29:05.260104)<…>
2025-06-17 00:40:20.654150+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.654180+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 HomeProvider: 开始清除用户相关缓存<…>
2025-06-17 00:40:20.654209+0800 Runner[1340:310921] flutter: \^[[38;5;12m└──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\342\224──────────────────────<…>
2025-06-17 00:40:20.654445+0800 Runner[1340:310921] flutter: ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\342\224──────────────────────<…>
2025-06-17 00:40:20.654445+0800 Runner[1340:310921] flutter: ┌────────────────────────────────────────\342───────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.654567+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.654657+0800 Runner[1340:310921] flutter: │ #1   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:76:22)
2025-06-17 00:40:20.654807+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.654918+0800 Runner[1340:310921] flutter: │ 00:40:20.654 (+0:29:05.260727)
2025-06-17 00:40:20.655077+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.655217+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 dashboard 的缓存
2025-06-17 00:40:20.655344+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.655756+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.655788+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.655816+0800 Runner[1340:310921] flutter: │ #1   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:77:22)
2025-06-17 00:40:20.655894+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.655982+0800 Runner[1340:310921] flutter: │ 00:40:20.655 (+0:29:05.261998)
2025-06-17 00:40:20.656061+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.656246+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 user 的缓存
2025-06-17 00:40:20.656346+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.656679+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.656770+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.656969+0800 Runner[1340:310921] flutter: │ #1   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:78:22)
2025-06-17 00:40:20.657086+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.657201+0800 Runner[1340:310921] flutter: │ 00:40:20.656 (+0:29:05.262931)
2025-06-17 00:40:20.657244+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.657367+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 vip 的缓存
2025-06-17 00:40:20.657486+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.657770+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.657841+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.657990+0800 Runner[1340:310921] flutter: │ #1   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:79:22)
2025-06-17 00:40:20.658094+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.658199+0800 Runner[1340:310921] flutter: │ 00:40:20.657 (+0:29:05.264041)
2025-06-17 00:40:20.658339+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.658473+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 daily_tasks 的缓存
2025-06-17 00:40:20.658636+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.658872+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.658908+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.659025+0800 Runner[1340:310921] flutter: │ #1   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:80:22)
2025-06-17 00:40:20.659154+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.659283+0800 Runner[1340:310921] flutter: │ 00:40:20.658 (+0:29:05.265170)
2025-06-17 00:40:20.659435+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.659570+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 addon_tasks 的缓存
2025-06-17 00:40:20.659704+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.660024+0800 Runner[1340:310921] flutter: ┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\342\224──
2025-06-17 00:40:20.660055+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:182:14)
2025-06-17 00:40:20.660080+0800 Runner[1340:310921] flutter: │ #1   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:81:22)
2025-06-17 00:40:20.660107+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.660129+0800 Runner[1340:310921] flutter: │ 00:40:20.659 (+0:29:05.266313)
2025-06-17 00:40:20.660153+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.660177+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 today_summary 的缓存
2025-06-17 00:40:20.660201+0800 Runner[1340:310921] flutter: └────────────────┄
2025-06-17 00:40:20.660177+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 today_summary 的缓存
2025-06-17 00:40:20.660201+0800 Runner[1340:310921] flutter: └────────────────\342─────────────────────────────────────────────────────────────────────────────────────┄
2025-06-17 00:40:20.660177+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除前缀为 today_summary 的缓存
2025-06-17 00:40:20.660201+0800 Runner[1340:310921] flutter: └────────────────\342─────────────────────────────────────────────────────────────────────────────────────\342\224──────────────────
2025-06-17 00:40:20.661016+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.661056+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.661078+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.661102+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.661188+0800 Runner[1340:310921] flutter: │ 00:40:20.660 (+0:29:05.267247)
2025-06-17 00:40:20.661212+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.661235+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 dashboard，内存缓存 1 个，持久化缓存 1 个
2025-06-17 00:40:20.661283+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.661832+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.661867+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   CacheService.clearUserCaches (package:sweatmint/core/services/cache_service.dart:157:14)<…>
2025-06-17 00:40:20.661889+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.661913+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.662101+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 00:40:20.661 (+0:29:05.268086)<…>
2025-06-17 00:40:20.662132+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.662155+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 CacheService: 用┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.662155+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 CacheService: 用\346\210户相关缓存清除完成<…>
2025-06-17 00:40:20.662180+0800 Runner[1340:310921] flutter: \^[[38;5;12m└─────────────────────────────────────────────────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.662155+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 CacheService: 用\346\210户相关缓存清除完成<…>
2025-06-17 00:40:20.662180+0800 Runner[1340:310921] flutter: \^[[38;5;12m└─────────────────────────────────────────────────\342──────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.662311+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.662374+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:361:17)<…>
2025-06-17 00:40:20.662510+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.662596+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.662798+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 🧹 AuthProvider: 应用级用户缓存已清理<…>
2025-06-17 00:40:20.662986+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.665947+0800 Runner[1340:310921] flutter: \^[[38;5;208m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.666010+0800 Runner[1340:310921] flutter: \^[[38;5;208m│ Looking up a deactivated widget's ancestor is unsafe.<…>
2025-06-17 00:40:20.666041+0800 Runner[1340:310921] flutter: \^[[38;5;208m│ At this point the state of the widget's element tree is no longer stable.<…>
2025-06-17 00:40:20.666070+0800 Runner[1340:310921] flutter: \^[[38;5;208m│ To safely refer to a widget's ancestor in its dispose() method, save a reference to the ancestor by calling dependOnInheritedWidgetOfExactType() in the widget's didChangeDependencies() method.<…>
2025-06-17 00:40:20.666101+0800 Runner[1340:310921] flutter: \^[[38;5;208m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.666141+0800 Runner[1340:310921] flutter: \^[[38;5;208m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:374:21)<…>
2025-06-17 00:40:20.666166+0800 Runner[1340:310921] flutter: \^[[38;5;208m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.666194+0800 Runner[1340:310921] flutter: \^[[38;5;208m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.666219+0800 Runner[1340:310921] flutter: \^[[38;5;208m│ ⚠️ ⚠️ AuthProvider: 无法通过context获取HealthService进行缓存清理<…>
2025-06-17 00:40:20.666248+0800 Runner[1340:310921] flutter: \^[[38;5;208m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.666514+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.666549+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:387:15)<…>
2025-06-17 00:40:20.666575+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.666605+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.666683+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 统一设备会话管理模型登出流程完成，所有数据已┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.666683+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 统一设备会话管理模型登出流程完成，所有数据已\346清理<…>
2025-06-17 00:40:20.666713+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.667303+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.667340+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.667370+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.667402+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.667425+0800 Runner[1340:310921] flutter: │ 00:40:20.667 (+0:29:05.273540)
2025-06-17 00:40:20.667452+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.667425+0800 Runner[1340:310921] flutter: │ 00:40:20.667 (+0:29:05.273540)
2025-06-17 00:40:20.667452+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄
2025-06-17 00:40:20.667475+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 dashboard，内存缓存 0 个，持久化缓存 1 个
2025-06-17 00:40:20.667499+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.667635+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.667635+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────\342──────────<…>
2025-06-17 00:40:20.667665+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:92:14)<…>
2025-06-17 00:40:20.667688+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.667714+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄<…>
2025-06-17 00:40:20.667737+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 00:40:20.667 (+0:29:05.273922)<…>
2025-06-17 00:40:20.667762+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄<…>
2025-06-17 00:40:20.667737+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 00:40:20.667 (+0:29:05.273922)<…>
2025-06-17 00:40:20.667762+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄<…>
2025-06-17 00:40:20.667737+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 00:40:20.667 (+0:29:05.273922)<…>
2025-06-17 00:40:20.667762+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.667786+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 HomeProvider: 用户缓存清除完成<…>
2025-06-17 00:40:20.667902+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.668143+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.668186+0800 Runner[1340:310921] flutter: │ #0   main.<anonymous closure>.<anonymous closure> (package:sweatmint/main.dart:350:27)
2025-06-17 00:40:20.668287+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.668440+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.668587+0800 Runner[1340:310921] flutter: │ ┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.668587+0800 Runner[1340:310921] flutter: │ \360\237🐛 ✅ HomeProvider缓存清理完成
2025-06-17 00:40:20.668733+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.669089+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.669121+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.669142+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.669166+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.669190+0800 Runner[1340:310921] flutter: │ 00:40:20.668 (+0:29:05.275350)
2025-06-17 00:40:20.669213+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.669235+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 dashboard，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.669369+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.669528+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────
2025-06-17 00:40:20.669528+0800 Runner[1340:310921] flutter: ┌────────────────────────\342\224───────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.669566+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.669590+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.669617+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.669676+0800 Runner[1340:310921] flutter: │ 00:40:20.669 (+0:29:05.275814)
2025-06-17 00:40:20.669701+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.669723+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 vip，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.669747+0800 Runner[1340:310921] flutter: └────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\342\224────────
2025-06-17 00:40:20.669898+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.669930+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.669955+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.669979+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.670001+0800 Runner[1340:310921] flutter: │ 00:40:20.669 (+0:29:05.276189)
2025-06-17 00:40:20.670025+0800 Runner[1340:310921] flutter: ┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.670001+0800 Runner[1340:310921] flutter: │ 00:40:20.669 (+0:29:05.276189)
2025-06-17 00:40:20.670025+0800 Runner[1340:310921] flutter: \342├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.670001+0800 Runner[1340:310921] flutter: │ 00:40:20.669 (+0:29:05.276189)
2025-06-17 00:40:20.670025+0800 Runner[1340:310921] flutter: \342├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.670050+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 today_summary，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.670073+0800 Runner[1340:310921] flutter: └──────────────────────────────────────────────────────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.670001+0800 Runner[1340:310921] flutter: │ 00:40:20.669 (+0:29:05.276189)
2025-06-17 00:40:20.670025+0800 Runner[1340:310921] flutter: \342├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.670050+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 today_summary，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.670073+0800 Runner[1340:310921] flutter: └──────────────────────────────────────────────────────\342─────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.670225+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.670254+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.670275+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.670299+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.670394+0800 Runner[1340:310921] flutter: │ 00:40:20.670 (+0:29:05.276512)
2025-06-17 00:40:20.670419+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.670394+0800 Runner[1340:310921] flutter: │ 00:40:20.670 (+0:29:05.276512)
2025-06-17 00:40:20.670419+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.670394+0800 Runner[1340:310921] flutter: │ 00:40:20.670 (+0:29:05.276512)
2025-06-17 00:40:20.670419+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄┄┄┄
2025-06-17 00:40:20.670441+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 user，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.670465+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.670795+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────
2025-06-17 00:40:20.670795+0800 Runner[1340:310921] flutter: ┌─────────────────────────────\342\224──────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.670824+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.670846+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.670869+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.670893+0800 Runner[1340:310921] flutter: │ 00:40:20.670 (+0:29:05.277075)
2025-06-17 00:40:20.670917+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.670948+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 daily_tasks，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.670973+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.671109+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.671137+0800 Runner[1340:310921] flutter: │ #0   CacheManager.clearByPrefix (package:sweatmint/core/data/cache_manager.dart:203:14)
2025-06-17 00:40:20.671158+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.671182+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.671203+0800 Runner[1340:310921] flutter: │ 00:40:20.671 (+0:29:05.277403)
2025-06-17 00:40:20.671227+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.671203+0800 Runner[1340:310921] flutter: │ 00:40:20.671 (+0:29:05.277403)
2025-06-17 00:40:20.671227+0800 Runner[1340:310921] flutter: ├┄\342\224┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
2025-06-17 00:40:20.671252+0800 Runner[1340:310921] flutter: │ 🐛 CacheManager.clearByPrefix: 清除完成，前缀 addon_tasks，内存缓存 0 个，持久化缓存 0 个
2025-06-17 00:40:20.671277+0800 Runner[1340:310921] flutter: └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.671371+0800 Runner[1340:310921] flutter: \^[[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.671401+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #0   HomeProvider.clearUserCache (package:sweatmint/features/home/<USER>/providers/home_provider.dart:92:14)<…>
2025-06-17 00:40:20.671426+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-06-17 00:40:20.671451+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.671479+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 00:40:20.671 (+0:29:05.277674)<…>
2025-06-17 00:40:20.671506+0800 Runner[1340:310921] flutter: \^[[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄<…>
2025-06-17 00:40:20.671530+0800 Runner[1340:310921] flutter: \^[[38;5;12m│ 💡 HomeProvider: 用户缓存清除完成<…>
2025-06-17 00:40:20.671555+0800 Runner[1340:310921] flutter: \^[[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────<…>
2025-06-17 00:40:20.671637+0800 Runner[1340:310921] flutter: ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
2025-06-17 00:40:20.671664+0800 Runner[1340:310921] flutter: │ #0   main.<anonymous closure>.<anonymous closure> (package:sweatmint/main.dart:350:27)
2025-06-17 00:40:20.671687+0800 Runner[1340:310921] flutter: │ #1   <asynchronous suspension>
2025-06-17 00:40:20.671710+0800 Runner[1340:310921] flutter: ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄
2025-06-17 00:40:20.671734+0800 Runner[1340:310921] flutter: │ 🐛 ✅ HomeProvider缓存清理完成
2025-06-17 00:40:20.671758+0800 Runner[1340:310921] flutter: └────────────────────────┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\342\224┄┄┄┄
2025-06-17 00:40:20.671734+0800 Runner[1340:310921] flutter: │ 🐛 ✅ HomeProvider缓存清理完成
2025-06-17 00:40:20.671758+0800 Runner[1340:310921] flutter: └────────────────────────\342\224───────────────────────────────────────────────────────────────────────────────────────────────