# SweatMint 健康数据基线管理实施指导

## 🎯 **实施目标**

解决SweatMint健康数据系统的核心问题：
- ❌ 用户授权即获得10000步，任务瞬间完成
- ❌ 多设备登录造成数据混乱
- ❌ 设备切换时任务进度丢失
- ❌ 缺乏防作弊机制

## 📋 **实施清单**

### **Phase 1: 后端基线管理 (1周)**

#### **1.1 创建数据模型**
```bash
# 在running目录执行
cd /Users/<USER>/Documents/工作/sweatmint/running
source .venv/bin/activate

# 创建迁移文件
python manage.py makemigrations users --name=add_health_baseline
```

在 `running/users/models.py` 添加：
```python
class UserHealthBaseline(models.Model):
    """用户健康数据基线"""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    device_id = models.CharField(max_length=255)
    platform = models.CharField(max_length=50)    # 'ios' 或 'android'
    health_source = models.CharField(max_length=50)  # 'apple_health' 或 'health_connect'
    
    # 基线数据（授权时刻的设备数据）
    baseline_steps = models.PositiveIntegerField(default=0)
    baseline_distance = models.FloatField(default=0.0)
    baseline_calories = models.FloatField(default=0.0)
    baseline_timestamp = models.DateTimeField()
    
    # 设备管理
    is_active = models.BooleanField(default=True)
    device_fingerprint = models.TextField()
    data_quality_score = models.FloatField(default=100.0)
    
    class Meta:
        verbose_name = '用户健康数据基线'
        unique_together = ['user', 'device_id']

class DeviceSession(models.Model):
    """设备会话管理"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    device_id = models.CharField(max_length=255, unique=True)
    device_name = models.CharField(max_length=100)
    platform = models.CharField(max_length=50)
    is_active = models.BooleanField(default=True)
    health_authorized = models.BooleanField(default=False)
    health_auth_time = models.DateTimeField(null=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'is_active']),
        ]
```

#### **1.2 创建API端点**
在 `running/tasks/views.py` 添加：
```python
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def initialize_health_baseline(request):
    """初始化健康数据基线"""
    device_id = request.data.get('device_id')
    platform = request.data.get('platform')
    raw_health_data = request.data.get('raw_health_data')
    
    # 检查设备冲突
    existing_session = DeviceSession.objects.filter(
        user=request.user, 
        is_active=True
    ).exclude(device_id=device_id).first()
    
    if existing_session:
        return Response({
            'status': 'device_conflict',
            'current_device': existing_session.device_name,
        }, status=409)
    
    # 创建基线
    baseline, created = UserHealthBaseline.objects.update_or_create(
        user=request.user,
        device_id=device_id,
        defaults={
            'platform': platform,
            'health_source': 'apple_health' if platform == 'ios' else 'health_connect',
            'baseline_steps': raw_health_data.get('steps', 0),
            'baseline_distance': raw_health_data.get('distance', 0.0),
            'baseline_calories': raw_health_data.get('calories', 0.0),
            'baseline_timestamp': timezone.now(),
            'is_active': True,
        }
    )
    
    return Response({'status': 'success'})

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def sync_health_data(request):
    """同步健康数据 (计算净增量)"""
    device_id = request.data.get('device_id')
    raw_data = request.data.get('health_data')
    
    # 获取基线
    try:
        baseline = UserHealthBaseline.objects.get(
            user=request.user,
            device_id=device_id,
            is_active=True
        )
    except UserHealthBaseline.DoesNotExist:
        return Response({'error': '未找到健康数据基线'}, status=400)
    
    # 计算净增量
    effective_data = {
        'steps': max(0, raw_data.get('steps', 0) - baseline.baseline_steps),
        'distance': max(0.0, raw_data.get('distance', 0.0) - baseline.baseline_distance),
        'calories': max(0.0, raw_data.get('calories', 0.0) - baseline.baseline_calories),
    }
    
    # 更新任务进度
    from tasks.services import TaskService
    task_service = TaskService()
    updated_tasks = task_service.update_tasks_with_health_data(
        request.user, effective_data
    )
    
    return Response({
        'status': 'success',
        'effective_data': effective_data,
        'updated_tasks': updated_tasks
    })
```

#### **1.3 配置URL路由**
在 `running/tasks/urls.py` 添加：
```python
urlpatterns = [
    # 现有URL...
    path('health/initialize-baseline/', views.initialize_health_baseline),
    path('health/sync/', views.sync_health_data),
]
```

### **Phase 2: 前端基线集成 (1周)**

#### **2.1 增强HealthServiceImpl**
在 `running-web/lib/core/services/health_service_impl.dart` 添加：
```dart
/// 初始化健康数据基线
Future<BaselineInitResult> initializeHealthBaseline() async {
  try {
    // 1. 获取设备信息
    final deviceId = await _getDeviceId();
    final platform = Platform.isIOS ? 'ios' : 'android';
    
    // 2. 获取原始健康数据
    final rawHealthData = await _getCurrentRawHealthData();
    
    // 3. 调用API初始化基线
    final response = await _apiClient.post(
      '/api/app/v1/tasks/health/initialize-baseline/',
      data: {
        'device_id': deviceId,
        'platform': platform,
        'raw_health_data': rawHealthData,
      },
    );
    
    if (response.statusCode == 200) {
      return BaselineInitResult.success(response.data);
    } else if (response.statusCode == 409) {
      return BaselineInitResult.deviceConflict(response.data);
    } else {
      throw HealthDataException('基线初始化失败');
    }
  } catch (e) {
    return BaselineInitResult.error(e.toString());
  }
}

/// 同步健康数据 (发送原始数据，后端计算净增量)
@override
Future<HealthSyncResult> syncHealthData() async {
  try {
    final deviceId = await _getDeviceId();
    final rawHealthData = await _getCurrentRawHealthData();
    
    final response = await _apiClient.post(
      '/api/app/v1/tasks/health/sync/',
      data: {
        'device_id': deviceId,
        'health_data': rawHealthData,
      },
    );
    
    if (response.statusCode == 200) {
      return HealthSyncResult.success(response.data['effective_data']);
    } else {
      return HealthSyncResult.error('同步失败');
    }
  } catch (e) {
    return HealthSyncResult.error(e.toString());
  }
}

Future<String> _getDeviceId() async {
  final deviceInfoPlugin = DeviceInfoPlugin();
  if (Platform.isIOS) {
    final iosInfo = await deviceInfoPlugin.iosInfo;
    return iosInfo.identifierForVendor ?? 'unknown_ios_device';
  } else {
    final androidInfo = await deviceInfoPlugin.androidInfo;
    return androidInfo.id;
  }
}
```

#### **2.2 修改HealthProvider**
在 `running-web/lib/features/home/<USER>/providers/health_provider.dart`：
```dart
/// 完整的健康数据初始化流程
Future<bool> initializeHealthData() async {
  bool result = false;
  await executeAsyncAction(() async {
    // 1. 初始化基线
    final initResult = await _healthService.initializeHealthBaseline();
    
    if (initResult.isSuccess) {
      _hasPermissions = true;
      await refreshHealthData();
      result = true;
    } else if (initResult.isDeviceConflict) {
      // 显示设备冲突对话框
      final userConfirmed = await _showDeviceSwitchDialog();
      if (userConfirmed) {
        // 处理设备切换逻辑
        result = await _handleDeviceSwitch();
      }
    } else {
      throw HealthDataException(initResult.errorMessage ?? '初始化失败');
    }
  });
  return result;
}
```

### **Phase 3: 数据迁移 (3天)**

#### **3.1 迁移脚本**
创建 `running/users/management/commands/migrate_health_data.py`：
```python
from django.core.management.base import BaseCommand
from users.models import User, UserHealthBaseline

class Command(BaseCommand):
    def handle(self, *args, **options):
        # 为现有用户创建基线数据
        users = User.objects.filter(is_active=True)
        for user in users:
            # 重置所有用户的健康数据基线
            UserHealthBaseline.objects.update_or_create(
                user=user,
                defaults={
                    'device_id': 'migrated_device',
                    'platform': 'unknown',
                    'baseline_steps': 0,
                    'baseline_distance': 0.0,
                    'baseline_calories': 0.0,
                    'baseline_timestamp': timezone.now(),
                    'is_active': True,
                }
            )
        
        self.stdout.write('健康数据基线迁移完成')
```

### **Phase 4: 测试验证 (2天)**

#### **4.1 测试场景**
```bash
# 后端测试
cd /Users/<USER>/Documents/工作/sweatmint/running
source .venv/bin/activate

# 单元测试
python manage.py test users.tests.test_health_baseline
python manage.py test tasks.tests.test_health_sync

# 前端测试
cd /Users/<USER>/Documents/工作/sweatmint/running-web
flutter test test/health_service_test.dart
```

#### **4.2 验证清单**
- [ ] 新用户首次授权后，基线正确设置
- [ ] 净增量计算准确（原始10000步 - 基线10000步 = 有效0步）
- [ ] 设备冲突检测正常工作
- [ ] 设备切换后任务进度保持
- [ ] API响应时间 < 1秒

## 🔧 **关键代码文件位置**

### **后端文件**
```
running/
├── users/models.py                    # 添加基线模型
├── tasks/views.py                     # 添加API端点
├── tasks/urls.py                      # 配置路由
└── users/management/commands/         # 迁移脚本
```

### **前端文件**
```
running-web/lib/
├── core/services/health_service_impl.dart     # 增强服务
├── features/home/<USER>/providers/      # 修改Provider
└── test/health_service_test.dart              # 测试文件
```

## 🚀 **执行命令**

### **后端部署**
```bash
cd /Users/<USER>/Documents/工作/sweatmint/running
source .venv/bin/activate

# 1. 创建迁移
python manage.py makemigrations

# 2. 执行迁移
python manage.py migrate

# 3. 数据迁移
python manage.py migrate_health_data

# 4. 测试
python manage.py test
```

### **前端部署**
```bash
cd /Users/<USER>/Documents/工作/sweatmint/running-web

# 1. 代码检查
flutter analyze

# 2. 测试
flutter test

# 3. 构建
flutter build apk --release
flutter build ios --release
```

## 📊 **预期结果**

### **修复前**
- 用户授权后立即获得10000步 ❌
- 可以多设备同时登录 ❌
- 设备切换后数据丢失 ❌

### **修复后**
- 用户授权后从0步开始计算 ✅
- 强制单设备登录 ✅
- 设备切换后保持进度 ✅

这样就能彻底解决健康数据的公平性和一致性问题！ 