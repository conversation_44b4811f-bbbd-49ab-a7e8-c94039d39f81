-- LLDB integration loaded --
2025-07-16 17:23:32.954721+0800 Runner[12979:9399738] [PreviewsAgentExecutorLibrary] Looking up debug dylib relative path
2025-07-16 17:23:32.955408+0800 Runner[12979:9399738] [PreviewsAgentExecutorLibrary] Found debug dylib relative path string `Runner.debug.dylib`
2025-07-16 17:23:32.955417+0800 Runner[12979:9399738] [PreviewsAgentExecutorLibrary] Looking up debug dylib entry point name
2025-07-16 17:23:32.955420+0800 Runner[12979:9399738] [PreviewsAgentExecutorLibrary] No debug dylib entry point name defined.
2025-07-16 17:23:32.955424+0800 Runner[12979:9399738] [PreviewsAgentExecutorLibrary] Looking up debug dylib install name
2025-07-16 17:23:32.955427+0800 Runner[12979:9399738] [PreviewsAgentExecutorLibrary] Found debug dylib install name string `@rpath/Runner.debug.dylib`
2025-07-16 17:23:32.955431+0800 Runner[12979:9399738] [PreviewsAgentExecutorLibrary] Looking for Previews JIT link entry point.
2025-07-16 17:23:32.961312+0800 Runner[12979:9399738] [PreviewsAgentExecutorLibrary] No Previews JIT entry point found.
2025-07-16 17:23:32.961326+0800 Runner[12979:9399738] [PreviewsAgentExecutorLibrary] Gave PreviewsInjection a chance to run and it returned, continuing with debug dylib.
2025-07-16 17:23:32.961331+0800 Runner[12979:9399738] [PreviewsAgentExecutorLibrary] Looking for main entry point.
2025-07-16 17:23:32.961335+0800 Runner[12979:9399738] [PreviewsAgentExecutorLibrary] Opening debug dylib with '@rpath/Runner.debug.dylib'
2025-07-16 17:23:32.961352+0800 Runner[12979:9399738] [PreviewsAgentExecutorLibrary] Debug dylib handle: 0xaf4299d0
2025-07-16 17:23:32.961362+0800 Runner[12979:9399738] [PreviewsAgentExecutorLibrary] No entry point found. Checking for alias.
2025-07-16 17:23:32.961368+0800 Runner[12979:9399738] [PreviewsAgentExecutorLibrary] Calling provided entry point.
2025-07-16 17:23:33.113015+0800 Runner[12979:9399738] [UIFocus] FlutterView implements focusItemsInRect: - caching for linear focus movement is limited as long as this view is on screen.
🔥 [v13.0] HealthKit管理器初始化完成 - 严格遵循v13.0规范
2025-07-16 17:23:33.263267+0800 Runner[12979:9399992] flutter: The Dart VM service is listening on http://127.0.0.1:64284/CPrv9Fon4rY=/
2025-07-16 17:23:33.785267+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:33.785385+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   main (package:sweatmint/main.dart:58:11)<…>
2025-07-16 17:23:33.785415+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _runMain.<anonymous closure> (dart:ui/hooks.dart:320:23)<…>
2025-07-16 17:23:33.785442+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:33.785467+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🚀 SweatMint 启动 - 阶段1：Splash Screen启动(0-200ms) - 1752657813701<…>
2025-07-16 17:23:33.785491+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:33.785777+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:33.785827+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   main (package:sweatmint/main.dart:61:11)<…>
2025-07-16 17:23:33.785854+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _runMain.<anonymous closure> (dart:ui/hooks.dart:320:23)<…>
2025-07-16 17:23:33.785881+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:33.785961+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ Logger和基础服务初始化完成<…>
2025-07-16 17:23:33.785989+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:33.790138+0800 Runner[12979:9399738] flutter: 🚀 ServiceLocator: 开始初始化服务定位器
2025-07-16 17:23:33.793628+0800 Runner[12979:9399738] flutter: ✅ ServiceLocator: 核心基础服务注册完成
2025-07-16 17:23:33.798717+0800 Runner[12979:9399738] flutter: ✅ ServiceLocator: 性能监控服务注册完成
2025-07-16 17:23:33.799824+0800 Runner[12979:9399738] flutter: ✅ ServiceLocator: 业务服务注册完成
2025-07-16 17:23:33.806153+0800 Runner[12979:9399738] flutter: ✅ ServiceLocator: 所有服务初始化完成
2025-07-16 17:23:33.806494+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:33.806540+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   main (package:sweatmint/main.dart:66:13)<…>
2025-07-16 17:23:33.806567+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:33.806595+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:33.806677+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ ServiceLocator: 服务定位器初始化完成<…>
2025-07-16 17:23:33.806709+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:33.807031+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:33.807079+0800 Runner[12979:9399738] flutter: │ #0   main (package:sweatmint/main.dart:76:13)
2025-07-16 17:23:33.807103+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:33.807128+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:33.807153+0800 Runner[12979:9399738] flutter: │ 🐛 ⏰ 阶段1延时等待96ms以达到最小持续时间
2025-07-16 17:23:33.807177+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:33.907368+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:33.907483+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   main (package:sweatmint/main.dart:80:11)<…>
2025-07-16 17:23:33.907532+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:33.907575+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:33.907618+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 阶段1完成，总耗时：205ms<…>
2025-07-16 17:23:33.907660+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:33.973324+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:33.973430+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   main.<anonymous closure> (package:sweatmint/main.dart:89:17)<…>
2025-07-16 17:23:33.973459+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _ScreenUtilInitState.build (package:flutter_screenutil/src/screenutil_init.dart:192:30)<…>
2025-07-16 17:23:33.973501+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:33.973527+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ ScreenUtil 初始化完成 - 设计稿尺寸: 375x812<…>
2025-07-16 17:23:33.973551+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:33.996671+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:33.996828+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   new AuthProvider (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:89:13)<…>
2025-07-16 17:23:33.996870+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   main.<anonymous closure>.<anonymous closure> (package:sweatmint/main.dart:287:38)<…>
2025-07-16 17:23:33.996904+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:33.996934+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider初始化完成 - 等待initializeBusinessLogic调用<…>
2025-07-16 17:23:33.996971+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.000412+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.000502+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   main.<anonymous closure>.<anonymous closure> (package:sweatmint/main.dart:288:25)<…>
2025-07-16 17:23:34.000549+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _CreateInheritedProviderState.value (package:provider/src/inherited_provider.dart:749:36)<…>
2025-07-16 17:23:34.000590+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.000646+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider创建完成，业务逻辑将在Loading阶段初始化<…>
2025-07-16 17:23:34.000675+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.004785+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.004859+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   MyApp.build.<anonymous closure> (package:sweatmint/main.dart:308:17)<…>
2025-07-16 17:23:34.004888+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   Consumer.buildWithChild (package:provider/src/consumer.dart:179:19)<…>
2025-07-16 17:23:34.004917+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.004947+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 阶段1：显示基础MaterialApp界面 - 认证状态: AuthStatus.initial<…>
2025-07-16 17:23:34.004975+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.293273+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.293380+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:183:17)
2025-07-16 17:23:34.293413+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:34.293443+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.293470+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect check: Current location: /, Auth status: AuthStatus.initial
2025-07-16 17:23:34.293508+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.297197+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.293470+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect check: Current location: /, Auth status: AuthStatus.initial
2025-07-16 17:23:34.293508+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.297197+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:34.297281+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:196:19)
2025-07-16 17:23:34.297312+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:34.297339+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.297363+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect: At splash screen, letting SplashScreen handle navigation.
2025-07-16 17:23:34.297406+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.420886+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.421028+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _SplashScreenState.initState (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:29:13)<…>
2025-07-16 17:23:34.421065+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5842:55)<…>
2025-07-16 17:23:34.421099+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.421130+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🚀 SplashScreen: 开屏页面初始化<…>
2025-07-16 17:23:34.421159+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.425841+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.425922+0800 Runner[12979:9399738] flutter: │ #0   _SplashScreenState.build (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:102:13)
2025-07-16 17:23:34.425947+0800 Runner[12979:9399738] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-16 17:23:34.425983+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.426007+0800 Runner[12979:9399738] flutter: │ 🐛 🎨 SplashScreen: 构建UI
2025-07-16 17:23:34.426033+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.435867+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.435987+0800 Runner[12979:9399738] flutter: │ #0   _SplashScreenState.build.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:116:17)
2025-07-16 17:23:34.436019+0800 Runner[12979:9399738] flutter: │ #1   _Selector0State.buildWithChild.<anonymous closure> (package:provider/src/selector.dart:86:45)
2025-07-16 17:23:34.436050+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.436081+0800 Runner[12979:9399738] flutter: │ 🐛 🔄 SplashScreen: Provider状态更新 - AuthStatus.initial, Stage: LoginStage.stage1Startup, 业务逻辑完成: false
2025-07-16 17:23:34.436119+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.750651+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.750758+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _SplashScreenState._startStage2Loading (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:90:13)<…>
2025-07-16 17:23:34.750812+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _SplashScreenState.initState.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:33:7)<…>
2025-07-16 17:23:34.750855+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.750905+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔄 SplashScreen: 开始阶段2 Loading数据准备阶段<…>
2025-07-16 17:23:34.750939+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.752356+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.750905+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔄 SplashScreen: 开始阶段2 Loading数据准备阶段<…>
2025-07-16 17:23:34.750939+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.752356+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:34.752440+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:105:13)<…>
2025-07-16 17:23:34.752468+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _SplashScreenState._startStage2Loading (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:93:18)<…>
2025-07-16 17:23:34.752496+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.752992+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🚀 AuthProvider: 开始执行阶段2: Loading数据准备阶段<…>
2025-07-16 17:23:34.753458+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.758623+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.758816+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:183:17)
2025-07-16 17:23:34.758849+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:34.758879+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.758930+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect check: Current location: /, Auth status: AuthStatus.initial
2025-07-16 17:23:34.758956+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.759135+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.759166+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:196:19)
2025-07-16 17:23:34.759449+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:34.759708+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.759860+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect: At splash screen, letting SplashScreen handle navigation.
2025-07-16 17:23:34.760031+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.762579+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.762655+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:115:15)<…>
2025-07-16 17:23:34.762687+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _SplashScreenState._startStage2Loading (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:93:18)<…>
2025-07-16 17:23:34.762719+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.762748+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📍 开始阶段2.1: 认证状态检查 (200-800ms)<…>
2025-07-16 17:23:34.762782+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.763520+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.763562+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:178:13)<…>
2025-07-16 17:23:34.763592+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:116:13)<…>
2025-07-16 17:23:34.763622+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.763648+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔐 开始阶段2.1认证状态检查 - 1752657814763<…>
2025-07-16 17:23:34.763674+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.763815+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.763848+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:182:15)<…>
2025-07-16 17:23:34.763875+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:116:13)<…>
2025-07-16 17:23:34.763903+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.763964+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📋 步骤1: 初始化 ApiClient / TokenManager<…>
2025-07-16 17:23:34.763993+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.764952+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.764992+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeNetworkComponents (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:236:17)<…>
2025-07-16 17:23:34.765022+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:183:13)<…>
2025-07-16 17:23:34.765048+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.765071+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🌐 初始化ApiClient<…>
2025-07-16 17:23:34.765097+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.774834+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.774915+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeNetworkComponents (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:240:15)<…>
2025-07-16 17:23:34.774949+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:183:13)<…>
2025-07-16 17:23:34.774979+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.775006+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 网络组件初始化完成<…>
2025-07-16 17:23:34.775033+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└│ #0   AuthProvider._initializeNetworkComponents (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:240:15)<…>
2025-07-16 17:23:34.774949+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:183:13)<…>
2025-07-16 17:23:34.774979+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.775006+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 网络组件初始化完成<…>
2025-07-16 17:23:34.775033+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:23:34.777643+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.777699+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:186:15)<…>
2025-07-16 17:23:34.777744+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:34.777775+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.777804+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📋 步骤1.1: 初始化全局应用生命周期管理器<…>
2025-07-16 17:23:34.777832+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.791867+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.791945+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeGlobalLifecycleManager (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:258:17)<…>
2025-07-16 17:23:34.791976+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:34.792008+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.791867+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.791945+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeGlobalLifecycleManager (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:258:17)<…>
2025-07-16 17:23:34.791976+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:34.792008+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342<…>
2025-07-16 17:23:34.792097+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 全局应用生命周期管理器初始化完成<…>
2025-07-16 17:23:34.792130+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.792264+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.791867+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.791945+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeGlobalLifecycleManager (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:258:17)<…>
2025-07-16 17:23:34.791976+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:34.792008+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342<…>
2025-07-16 17:23:34.792097+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 全局应用生命周期管理器初始化完成<…>
2025-07-16 17:23:34.792130+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.792264+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:34.792295+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:190:15)<…>
2025-07-16 17:23:34.792319+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:34.792351+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.791867+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.791945+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeGlobalLifecycleManager (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:258:17)<…>
2025-07-16 17:23:34.791976+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:34.792008+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342<…>
2025-07-16 17:23:34.792097+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 全局应用生命周期管理器初始化完成<…>
2025-07-16 17:23:34.792130+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.792264+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:34.792295+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:190:15)<…>
2025-07-16 17:23:34.792319+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:34.792351+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342<…>
2025-07-16 17:23:34.792392+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📋 步骤2: 读取本地Token<…>
2025-07-16 17:23:34.792418+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.798518+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.798576+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   MyApp.build.<anonymous closure> (package:sweatmint/main.dart:308:17)<…>
2025-07-16 17:23:34.798606+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   Consumer.buildWithChild (package:provider/src/consumer.dart:179:19)<…>
2025-07-16 17:23:34.798635+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.798661+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 阶段1：显示基础MaterialApp界面 - 认证状态: AuthStatus.initial<…>
2025-07-16 17:23:34.798687+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.819339+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.819441+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:183:17)
2025-07-16 17:23:34.819475+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:34.819504+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.819546+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect check: Current location: /, Auth status: AuthStatus.initial
2025-07-16 17:23:34.819574+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.819546+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect check: Current location: /, Auth status: AuthStatus.initial
2025-07-16 17:23:34.819574+0800 Runner[12979:9399738] flutter: └\342
2025-07-16 17:23:34.820769+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.820830+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:196:19)
2025-07-16 17:23:34.820853+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:34.820877+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.820902+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect: At splash screen, letting SplashScreen handle navigation.
2025-07-16 17:23:34.820927+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.837578+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.837715+0800 Runner[12979:9399738] flutter: │ #0   _SplashScreenState.build.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:116:17)
2025-07-16 17:23:34.837751+0800 Runner[12979:9399738] flutter: │ #1   _Selector0State.buildWithChild.<anonymous closure> (package:provider/src/selector.dart:86:45)
2025-07-16 17:23:34.837785+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.837814+0800 Runner[12979:9399738] flutter: │ 🐛 🔄 SplashScreen: Provider状态更新 - AuthStatus.initial, Stage: LoginStage.stage2Loading, 业务逻辑完成: false
2025-07-16 17:23:34.837859+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.861171+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.861236+0800 Runner[12979:9399738] flutter: │ #0   TokenManager.getAccessToken (package:sweatmint/core/services/token_manager.dart:130:12)
2025-07-16 17:23:34.861262+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.861285+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.861236+0800 Runner[12979:9399738] flutter: │ #0   TokenManager.getAccessToken (package:sweatmint/core/services/token_manager.dart:130:12)
2025-07-16 17:23:34.861262+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.861285+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:34.861354+0800 Runner[12979:9399738] flutter: │ 17:23:34.860 (+0:00:01.091765)
2025-07-16 17:23:34.861378+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.861402+0800 Runner[12979:9399738] flutter: │ 🐛 TokenManager: Returning existing valid token.
2025-07-16 17:23:34.861428+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.862816+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.862878+0800 Runner[12979:9399738] flutter: │ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:194:15)
2025-07-16 17:23:34.862900+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.862921+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.862983+0800 Runner[12979:9399738] flutter: │ 🐛 🔍 Token状态: AccessToken=存在, RefreshToken=存在
2025-07-16 17:23:34.863006+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.863134+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.863167+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:198:17)<…>
2025-07-16 17:23:34.863283+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:34.863582+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.863664+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 步骤3: 发现Access Token，验证有效性<…>
2025-07-16 17:23:34.863734+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:23:34.864087+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:34.864087+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:34.864120+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:229:13)<…>
2025-07-16 17:23:34.864141+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:34.864400+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├│ #1   <asynchronous suspension><…>
2025-07-16 17:23:34.864400+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342<…>
2025-07-16 17:23:34.864441+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 阶段2.1完成，耗时: 100ms，认证状态: AuthStatus.authenticated<…>
2025-07-16 17:23:34.864581+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.864866+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.864866+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:34.864901+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:135:15)<…>
2025-07-16 17:23:34.865033+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:34.865155+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.865312+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📍 跳过阶段2.2: 健康数据流程由HealthDataFlowService独立处理<…>
2025-07-16 17:23:34.865413+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.865583+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.865583+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:34.865583+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-16 17:23:34.865612+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:138:15)<…>
2025-07-16 17:23:34.865632+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:34.865655+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:34.865676+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📍 开始阶段2.3: 业务数据同步 (1.5s-2.5s)<…>
2025-07-16 17:23:34.865742+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.866199+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.866199+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:34.866199+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-16 17:23:34.866231+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:269:13)<…>
2025-07-16 17:23:34.866263+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:139:13)<…>
2025-07-16 17:23:34.866287+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.866308+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔄 开始阶段2.3: 业务数据同步<…>
2025-07-16 17:23:34.866332+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.867338+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.867383+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._preloadHomePageData (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:293:15)<…>
2025-07-16 17:23:34.867411+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:274:9)<…>
2025-07-16 17:23:34.867438+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.867460+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🏠 开始预加载首页数据<…>
2025-07-16 17:23:34.867485+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.870803+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.870903+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider._warmupCriticalData (package:sweatmint/features/home/<USER>/providers/home_provider.dart:127:14)
2025-07-16 17:23:34.870934+0800 Runner[12979:9399738] flutter: │ #1   new HomeProvider (package:sweatmint/features/home/<USER>/providers/home_provider.dart:44:5)
2025-07-16 17:23:34.870962+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.871014+0800 Runner[12979:9399738] flutter: │ 17:23:34.870 (+0:00:01.101798)
2025-07-16 17:23:34.871039+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.871221+0800 Runner[12979:9399738] flutter: │ 🐛 HomeProvider: 开始预热关键数据缓存
2025-07-16 17:23:34.871254+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.872195+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.872237+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   CacheManager.warmup (package:sweatmint/core/data/cache_manager.dart:432:14)<…>
2025-07-16 17:23:34.872269+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   HomeProvider._warmupCriticalData (package:sweatmint/features/home/<USER>/providers/home_provider.dart:129:26)<…>
2025-07-16 17:23:34.872294+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.872318+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:34.872 (+0:00:01.103378)<…>
2025-07-16 17:23:34.872344+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.872371+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 CacheManager.warmup: 开始预热缓存，keys: [dashboard_home]<…>
2025-07-16 17:23:34.872397+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.873023+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.873066+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.get (package:sweatmint/core/data/cache_manager.dart:93:14)
2025-07-16 17:23:34.873090+0800 Runner[12979:9399738] flutter: │ #1   CacheManager.warmup (package:sweatmint/core/data/cache_manager.dart:435:34)
2025-07-16 17:23:34.873114+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.873136+0800 Runner[12979:9399738] flutter: │ 17:23:34.872 (+0:00:01.104232)
2025-07-16 17:23:34.873160+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.873183+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.get: 获取缓存 dashboard_home
2025-07-16 17:23:34.873222+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.874428+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.874479+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HomeProvider.updateDataSource (package:sweatmint/features/home/<USER>/providers/home_provider.dart:58:12)<…>
2025-07-16 17:23:34.874504+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   main.<anonymous closure>.<anonymous closure> (package:sweatmint/main.dart:274:28)<…>
2025-07-16 17:23:34.874530+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.874553+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:34.874 (+0:00:01.105564)<…>
2025-07-16 17:23:34.874575+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:34.874597+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 HomeProvider: 数据源已更新，保持现有状态不变<…>
2025-07-16 17:23:34.874619+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:23:34.874597+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 HomeProvider: 数据源已更新，保持现有状态不变<…>
2025-07-16 17:23:34.874619+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:23:34.878471+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.878630+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   ViewModelMixin.executeAsyncAction (package:sweatmint/core/mixins/view_model_mixin.dart:77:13)<…>
2025-07-16 17:23:34.878666+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   HomeProvider.loadHomeData (package:sweatmint/features/home/<USER>/providers/home_provider.dart:274:11)<…>
2025-07-16 17:23:34.878694+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.881267+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 HomeDashboardData operation: Starting...<…>
2025-07-16 17:23:34.881310+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.883877+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.883952+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:276:16)<…>
2025-07-16 17:23:34.883980+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   ViewModelMixin.executeAsyncAction (package:sweatmint/core/mixins/view_model_mixin.dart:80:36)<…>
2025-07-16 17:23:34.884005+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.884027+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:34.883 (+0:00:01.114868)<…>
2025-07-16 17:23:34.884049+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.884071+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 开始加载首页聚合数据 (forceRefresh: false)<…>
2025-07-16 17:23:34.884108+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.885115+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.884071+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 开始加载首页聚合数据 (forceRefresh: false)<…>
2025-07-16 17:23:34.884108+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.885115+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:34.885159+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:21:12)<…>
2025-07-16 17:23:34.885185+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:289:65)<…>
2025-07-16 17:23:34.885211+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.885311+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:34.884 (+0:00:01.116322)<…>
2025-07-16 17:23:34.885335+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.885357+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 DashboardRemoteDataSource: 开始获取首页聚合数据 (forceRefresh: false)<…>
2025-07-16 17:23:34.885507+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.885640+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.885667+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.get (package:sweatmint/core/data/cache_manager.dart:93:14)
2025-07-16 17:23:34.885692+0800 Runner[12979:9399738] flutter: │ #1   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:38:47)
2025-07-16 17:23:34.885718+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.885745+0800 Runner[12979:9399738] flutter: │ 17:23:34.885 (+0:00:01.116893)
2025-07-16 17:23:34.885769+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.885790+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.get: 获取缓存 dashboard_home
2025-07-16 17:23:34.885812+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.886277+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.886311+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeVipService (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:321:15)<…>
2025-07-16 17:23:34.886341+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:275:9)<…>
2025-07-16 17:23:34.886364+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.886385+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 💎 初始化VIP服务<…>
2025-07-16 17:23:34.886406+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.886625+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.886657+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeVipService (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:323:15)<…>
2025-07-16 17:23:34.886680+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:275:9)<…>
2025-07-16 17:23:34.886710+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.887017+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ VIP服务初始化完成<…>
2025-07-16 17:23:34.887042+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:23:34.887304+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.887334+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeEventSyncService (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:332:15)<…>
2025-07-16 17:23:34.888156+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:276:9)<…>
2025-07-16 17:23:34.888185+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.888208+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔄 初始化事件同步服务<…>
2025-07-16 17:23:34.888230+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.888452+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.888480+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeEventSyncService (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:334:15)<…>
2025-07-16 17:23:34.888501+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:276:9)<…>
2025-07-16 17:23:34.888522+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.888604+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 事件同步服务初始化完成<…>
2025-07-16 17:23:34.888719+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
Thread Performance Checker: Thread running at QOS_CLASS_USER_INTERACTIVE waiting on a thread without a QoS class specified. Investigate ways to avoid priority inversions
PID: 12979, TID: 9399738
Backtrace
=================================================================
3   Flutter                             0x000000010596f4ac _ZNSt3_fl10__function6__funcIZN7flutter8Animator10AwaitVSyncEvE3$_0NS_9allocatorIS4_EEFvNS_10unique_ptrINS2_20FrameTimingsRecorderENS_14default_deleteIS8_EEEEEEclEOSB_ + 592
4   Flutter                             0x000000010599f964 _ZNSt3_fl10__function6__funcIZN7flutter11VsyncWaiter12FireCallbackEN3fml9TimePointES5_bE3$_0NS_9allocatorIS6_EEFvvEEclEv + 956
5   Flutter                             0x00000001055e68f8 _ZN3fml15MessageLoopImpl10FlushTasksENS_9FlushTypeE + 340
6   Flutter                             0x00000001055eab24 _ZN3fml17MessageLoopDarwin11OnTimerFireEP16__CFRunLoopTimerPS0_ + 32
7   CoreFoundation                      0x0000000192119bb0 55B9BA28-4C5C-3FE7-9C47-4983337D6E83 + 793520
8   CoreFoundation                      0x00000001920dade4 55B9BA28-4C5C-3FE7-9C47-4983337D6E83 + 536036
9   CoreFoundation                      0x00000001920840fc 55B9BA28-4C5C-3FE7-9C47-4983337D6E83 + 180476
10  CoreFoundation                      0x00000001920cd5bc 55B9BA28-4C5C-3FE7-9C47-4983337D6E83 + 480700
11  CoreFoundation                      0x00000001920d1d20 CFRunLoopRunSpecific + 584
12  GraphicsServices                    0x00000001ca1a1998 GSEventRunModal + 160
13  UIKitCore                           0x000000019436434c 1242978A-2C2C-3781-8D6C-9777EDCE2804 + 3609420
14  UIKitCore                           0x0000000194363fc4 UIApplicationMain + 312
15  libswiftUIKit.dylib                 0x000000019a2eeddc $s5UIKit17UIApplicationMainys5Int32VAD_SpySpys4Int8VGGSgSSSgAJtF + 100
16  Runner.debug.dylib                  0x0000000100ecb354 $sSo21UIApplicationDelegateP5UIKitE4mainyyFZ + 128
17  Runner.debug.dylib                  0x0000000100ecb2c4 $s6Runner11AppDelegateC5$mainyyFZ + 44
18  Runner.debug.dylib                  0x0000000100ecb400 __debug_main_executable_dylib_entry_point + 28
19  dyld                                0x00000001af890344 199941A5-95EE-3054-8E54-AE6387A9FA9A + 82756
2025-07-16 17:23:34.927986+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.928051+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.get (package:sweatmint/core/data/cache_manager.dart:123:20)
2025-07-16 17:23:34.928075+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.928101+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.928122+0800 Runner[12979:9399738] flutter: │ 17:23:34.927 (+0:00:01.159088)
2025-07-16 17:23:34.928145+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.928051+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.get (package:sweatmint/core/data/cache_manager.dart:123:20)
2025-07-16 17:23:34.928075+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.928101+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.928122+0800 Runner[12979:9399738] flutter: │ 17:23:34.927 (+0:00:01.159088)
2025-07-16 17:23:34.928145+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:34.928166+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.get: 清理过期持久化缓存 dashboard_home
2025-07-16 17:23:34.928190+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.928463+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.928568+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.get (package:sweatmint/core/data/cache_manager.dart:132:14)
2025-07-16 17:23:34.928728+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.928952+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.929186+0800 Runner[12979:9399738] flutter: │ 17:23:34.928 (+0:00:01.159676)
2025-07-16 17:23:34.929405+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.929570+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.get: 缓存未命中 dashboard_home
2025-07-16 17:23:34.929732+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.932495+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:34.932574+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:21:12)<…>
2025-07-16 17:23:34.932603+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   HomeProvider._warmupCriticalData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:134:67)<…>
2025-07-16 17:23:34.932628+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.932648+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:34.930 (+0:00:01.162093)<…>
2025-07-16 17:23:34.932672+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:34.932763+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 DashboardRemoteDataSource: 开始获取首页聚合数据 (forceRefresh: false)<…>
2025-07-16 17:23:34.932796+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:34.933500+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.933550+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.get (package:sweatmint/core/data/cache_manager.dart:93:14)
2025-07-16 17:23:34.933572+0800 Runner[12979:9399738] flutter: │ #1   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:38:47)
2025-07-16 17:23:34.933594+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.934673+0800 Runner[12979:9399738] flutter: │ 17:23:34.933 (+0:00:01.164448)
2025-07-16 17:23:34.934710+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.934732+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.get: 获取缓存 dashboard_home
2025-07-16 17:23:34.934758+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.936410+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.936497+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.get (package:sweatmint/core/data/cache_manager.dart:123:20)
2025-07-16 17:23:34.936662+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.936843+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.937045+0800 Runner[12979:9399738] flutter: │ 17:23:34.935 (+0:00:01.167152)
2025-07-16 17:23:34.937157+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.937288+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.get: 清理过期持久化缓存 dashboard_home
2025-07-16 17:23:34.937439+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.938012+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.938088+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.get (package:sweatmint/core/data/cache_manager.dart:132:14)
2025-07-16 17:23:34.938218+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.938326+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.938828+0800 Runner[12979:9399738] flutter: │ 17:23:34.937 (+0:00:01.168982)
2025-07-16 17:23:34.938965+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.938828+0800 Runner[12979:9399738] flutter: │ 17:23:34.937 (+0:00:01.168982)
2025-07-16 17:23:34.938965+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:34.939077+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.get: 缓存未命中 dashboard_home
2025-07-16 17:23:34.939224+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.944122+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.944205+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.get (package:sweatmint/core/data/cache_manager.dart:132:14)
2025-07-16 17:23:34.944232+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.944258+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.944277+0800 Runner[12979:9399738] flutter: │ 17:23:34.943 (+0:00:01.175191)
2025-07-16 17:23:34.944299+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.944355+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.get: 缓存未命中 dashboard_home
2025-07-16 17:23:34.944381+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.969668+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.969901+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:34.969938+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor.onRequest (package:dio/src/interceptors/log.dart:68:13)
2025-07-16 17:23:34.969970+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.970002+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: *** Request ***
2025-07-16 17:23:34.970035+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.975539+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.975632+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:34.975657+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-07-16 17:23:34.975681+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.975705+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: uri: http://192.168.100.232:8000/api/app/v1/dashboard/
2025-07-16 17:23:34.975733+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.976146+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.976146+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:34.976180+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:34.976200+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor.onRequest (package:dio/src/interceptors/log.dart:94:13)
2025-07-16 17:23:34.976222+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.977295+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient:
2025-07-16 17:23:34.977327+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.980109+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.980257+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:34.980329+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor.onRequest (package:dio/src/interceptors/log.dart:68:13)
2025-07-16 17:23:34.980399+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.980466+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: *** Request ***
2025-07-16 17:23:34.980558+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.980793+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.980833+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:34.981124+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-07-16 17:23:34.981148+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.981173+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: uri: http://192.168.100.232:8000/api/app/v1/dashboard/
2025-07-16 17:23:34.981197+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.981402+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.981547+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:34.981638+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor.onRequest (package:dio/src/interceptors/log.dart:94:13)
2025-07-16 17:23:34.981727+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.981808+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient:
2025-07-16 17:23:34.981881+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.988270+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.988353+0800 Runner[12979:9399738] flutter: │ #0   TokenManager.getAccessToken (package:sweatmint/core/services/token_manager.dart:130:12)
2025-07-16 17:23:34.988389+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.988419+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.988353+0800 Runner[12979:9399738] flutter: │ #0   TokenManager.getAccessToken (package:sweatmint/core/services/token_manager.dart:130:12)
2025-07-16 17:23:34.988389+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.988419+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:34.988443+0800 Runner[12979:9399738] flutter: │ 17:23:34.988 (+0:00:01.219355)
2025-07-16 17:23:34.988471+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.988492+0800 Runner[12979:9399738] flutter: │ 🐛 TokenManager: Returning existing valid token.
2025-07-16 17:23:34.988520+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.988796+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.988824+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:80:23)
2025-07-16 17:23:34.988927+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.989048+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.989264+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: Using TokenManager.getAccessToken() for API request
2025-07-16 17:23:34.989348+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.991293+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.991343+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:85:23)
2025-07-16 17:23:34.991369+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.991392+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.991422+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: Added authorization header for /api/app/v1/dashboard/
2025-07-16 17:23:34.991445+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.991422+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: Added authorization header for /api/app/v1/dashboard/
2025-07-16 17:23:34.991445+0800 Runner[12979:9399738] flutter: └\342
2025-07-16 17:23:34.991925+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.991962+0800 Runner[12979:9399738] flutter: │ #0   TokenManager.getAccessToken (package:sweatmint/core/services/token_manager.dart:130:12)
2025-07-16 17:23:34.991983+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.992006+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.992029+0800 Runner[12979:9399738] flutter: │ 17:23:34.991 (+0:00:01.223135)
2025-07-16 17:23:34.992050+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.992029+0800 Runner[12979:9399738] flutter: │ 17:23:34.991 (+0:00:01.223135)
2025-07-16 17:23:34.992050+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:34.992220+0800 Runner[12979:9399738] flutter: │ 🐛 TokenManager: Returning existing valid token.
2025-07-16 17:23:34.992370+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.992558+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.992585+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:80:23)
2025-07-16 17:23:34.992710+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.992867+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.992986+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: Using TokenManager.getAccessToken() for API request
2025-07-16 17:23:34.993072+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:34.993282+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:34.993438+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:85:23)
2025-07-16 17:23:34.993545+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:34.993651+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:34.993766+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: Added authorization header for /api/app/v1/dashboard/
2025-07-16 17:23:34.993888+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.306737+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.306837+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:35.306863+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor.onResponse (package:dio/src/interceptors/log.dart:101:13)
2025-07-16 17:23:35.306886+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.306908+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: *** Response ***
2025-07-16 17:23:35.306931+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.307650+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.307691+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:35.307711+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-07-16 17:23:35.307734+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.308538+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: uri: http://192.168.100.232:8000/api/app/v1/dashboard/
2025-07-16 17:23:35.308567+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.308915+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.308963+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:35.308983+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor._printResponse (package:dio/src/interceptors/log.dart:136:13)
2025-07-16 17:23:35.309004+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.309485+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient:
2025-07-16 17:23:35.309685+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.310157+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.310208+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:35.310228+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor.onResponse (package:dio/src/interceptors/log.dart:101:13)
2025-07-16 17:23:35.310308+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.310441+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: *** Response ***
2025-07-16 17:23:35.310556+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.310905+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.310937+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:35.310957+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-07-16 17:23:35.311065+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.311162+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: uri: http://192.168.100.232:8000/api/app/v1/dashboard/
2025-07-16 17:23:35.311314+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.311617+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.311650+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:35.311676+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor._printResponse (package:dio/src/interceptors/log.dart:136:13)
2025-07-16 17:23:35.311786+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.311892+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient:
2025-07-16 17:23:35.311987+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.314958+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.315114+0800 Runner[12979:9399738] flutter: │ #0   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:62:14)
2025-07-16 17:23:35.315142+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.315177+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.315259+0800 Runner[12979:9399738] flutter: │ 17:23:35.313 (+0:00:01.545153)
2025-07-16 17:23:35.315282+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.315411+0800 Runner[12979:9399738] flutter: │ 🐛 Dashboard API 原始响应: {code: 200, message: 操作成功, data: {user_profile: {user_id: S0401025, email: <EMAIL>, username: test_user2, avatar: avatar_14.png, member_level: {id: 20, name: Level 3, level: 3, min_exp: 10000, max_exp: 99999, daily_task_count: 4, extra_task_count: 2, benefits: Complete 4 daily tasks, activate 2 extra tasks}, exp: 11586, swmt_balance: 10995.2, usdt_balance: 0.********, total_commission: 0.0, is_agent: false, is_active_member: false, referral_code: R8728568, referral_count: 0, account_status: Active, can_withdraw_status: Withdrawal allowed, agent_id: null}, vip_status: {has_vip: true, vip_info: {level_id: 15, name: VIP 2, level: 2, upgrade_time: 2025-05-31T16:50:24.485657Z, refund_active: false, refund_required_days: 26, completed_days: 0, refund_progress: 0, refund_status_label: 未开始, active_plans_count: 0, vip_level: {name: VIP 2, level: 2, swmt_bonus_rate: 1.4, exp_bonus_rate: 1.5, upgrade_fee: 300.0, refund_enabled: true, refund_days: 26}}, features: [{name: 任务加成, description: 所有任务SWMT奖励增加40%, bonus_rate: 0.4, is_active: true}, {name: 经验加成, description: 获得经验值增加50%, bonus_rate: 0.5, is_active: true}, {name: 兑换手续费, description: 享受商品兑换手续费减免, physical_rate: 0.05, virtual_rate: 0.05, is_active: true}, {name: 专属任务, description: 解锁VIP专属任务, special_tasks: true, is_active: true}], availableLevel: {id: 16, name: VIP 3, level: 3, price: {amount: 500.00, currency: USDT, refund_days: 28}, swmt_bonus_rate: 1.6, exp_bonus_rate: 1.7}, additional_stats: {additional_swmt_earned: 2657.92, additional_exp_earned: 2725, actual_refund_amount: 0.0, description: {swmt: 通过VIP加成功能获得的总额外SWMT, exp: 通过VIP加成功能获得的总额外XP, refund: VIP返还计划实际返还的USDT金额}}}, today_summary: {date: 2025-07-16, total_tasks: 4, completed_tasks: 0, completion_percentage: 0, earned_today: {swmt: 0.00, exp: 0}, current_streak: 0, health_data: {steps: 0, distance: 0.0, calories: 0}, next_tasks: [{id: 116, name: 距离02-L3-6.5, type: distance, icon: null, total_swmt: 617.40, total_exp: 937}, {id: 112, name: 步数01-L3-4000步, type: steps, icon: null, total_swmt: 967.40, total_exp: 841}, {id: 113, name: 步数02-L3-5000步-权重2, type: steps, icon: null, total_swmt: 526.40, total_exp: 157}], all_tasks_completed: false}, daily_tasks: {total_tasks: 4, completed_tasks: 0, completion_percentage: 0, user_level: {name: Level 3, level: 3}, user_vip: {has_vip: true, name: VIP 2, level: 2}, vip_refund: null, tasks: [{id: 116, name: 距离02-L3-6.5, type: distance, icon: distance_icon.png, description: <p>距离02-L3-6.5</p>, rewards: {base: {swmt: 441.00, exp: 625}, vip_bonus: {swmt: 176.40, exp: 312}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 617.40, exp: 937}}, requirements: {steps_required: null, distance_required: 6.5, ad_duration: null, verification_type: health_app}, progress: {current_steps: null, current_distance: null, percentage: 0, last_updated: 2025-07-16 10:09:27}, status: pending, unlock_time: 2025-07-16T00:00:00, expire_time: 2025-07-16T23:59:59.999999, can_complete: false, completed_at: null}, {id: 112, name: 步数01-L3-4000步, type: steps, icon: steps_icon.png, description: <p>步数01-L3-4000步</p>, rewards: {base: {swmt: 691.00, exp: 561}, vip_bonus: {swmt: 276.40, exp: 280}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 967.40, exp: 841}}, requirements: {steps_required: 4000, distance_required: null, ad_duration: null, verification_type: health_app}, progress: {current_steps: 0, current_distance: null, percentage: 0, last_updated: 2025-07-16 10:09:27}, status: pending, unlock_time: 2025-07-16T00:00:00, expire_time: 2025-07-16T23:59:59.999999, can_complete: false, completed_at: null}, {id: 113, name: 步数02-L3-5000步-权重2, type: steps, icon: steps_icon.png, description: <p>步数02-L3-5000步-权重2</p>, rewards: {base: {swmt: 376.00, exp: 105}, vip_bonus: {swmt: 150.40, exp: 52}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 526.40, exp: 157}}, requirements: {steps_required: 5000, distance_required: null, ad_duration: null, verification_type: health_app}, progress: {current_steps: 0, current_distance: null, percentage: 0, last_updated: 2025-07-16 10:09:27}, status: pending, unlock_time: 2025-07-16T00:00:00, expire_time: 2025-07-16T23:59:59.999999, can_complete: false, completed_at: null}, {id: 119, name: 广告02-L3-100权重-40秒, type: ad, icon: ad_icon.png, description: <p>广告02-L3-100权重-40秒</p>, rewards: {base: {swmt: 657.00, exp: 850}, vip_bonus: {swmt: 262.80, exp: 425}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 919.80, exp: 1275}}, requirements: {steps_required: null, distance_required: null, ad_duration: 40, verification_type: system}, progress: {current_steps: null, current_distance: null, percentage: 0, last_updated: 2025-07-16 10:09:27}, status: pending, unlock_time: 2025-07-16T00:00:00, expire_time: 2025-07-16T23:59:59.999999, can_complete: true, completed_at: null}]}, addon_tasks: {active_addon_tasks: 0, completed_addon_tasks: 0, max_addon_tasks: 2, available_slots: 2, current_member_level: {name: Level 3, level: 20}, addon_tasks: [{id: 142, name: 拉1人-L123, icon: share_icon.png, description: <p>拉1人-L123</p>, referral_required: 1, swmt_bonus_rate: 1.3, exp_bonus_rate: 1.2, progress: {current_referrals: 0, required_referrals: 1, percentage: 0, status: pending}, expire_time: null, unlock_requirements: Refer 1 new user(s) to unlock this task., can_activate: true, unlock_message: 需要再邀请 1 位新用户才能完成此任务 (当前: 0/1), status: pending, completion_order: 1, is_next_pending_referral: false}, {id: 139, name: 拉2人-L123, icon: share_icon.png, description: <p>拉2人-L123</p>, referral_required: 2, swmt_bonus_rate: 1.4, exp_bonus_rate: 1.22, progress: {current_referrals: 0, required_referrals: 2, percentage: 0, status: pending}, expire_time: null, unlock_requirements: Refer 2 new user(s) to unlock this task., can_activate: true, unlock_message: 需要再邀请 2 位新用户才能完成此任务 (当前: 0/2), status: pending, completion_order: 2, is_next_pending_referral: false}]}}, timestamp: 1752657815, _meta: null}
2025-07-16 17:23:35.315998+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.317899+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.318048+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.set (package:sweatmint/core/data/cache_manager.dart:146:14)
2025-07-16 17:23:35.318081+0800 Runner[12979:9399738] flutter: │ #1   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:75:28)
2025-07-16 17:23:35.318109+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.318181+0800 Runner[12979:9399738] flutter: │ 17:23:35.317 (+0:00:01.548672)
2025-07-16 17:23:35.318217+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.318239+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.set: 设置缓存 dashboard_home
2025-07-16 17:23:35.318299+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.318239+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.set: 设置缓存 dashboard_home
2025-07-16 17:23:35.318299+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.326037+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.326129+0800 Runner[12979:9399738] flutter: │ #0   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:62:14)
2025-07-16 17:23:35.326159+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.326186+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.326213+0800 Runner[12979:9399738] flutter: │ 17:23:35.325 (+0:00:01.557021)
2025-07-16 17:23:35.326238+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.326375+0800 Runner[12979:9399738] flutter: │ 🐛 Dashboard API 原始响应: {code: 200, message: 操作成功, data: {user_profile: {user_id: S0401025, email: <EMAIL>, username: test_user2, avatar: avatar_14.png, member_level: {id: 20, name: Level 3, level: 3, min_exp: 10000, max_exp: 99999, daily_task_count: 4, extra_task_count: 2, benefits: Complete 4 daily tasks, activate 2 extra tasks}, exp: 11586, swmt_balance: 10995.2, usdt_balance: 0.********, total_commission: 0.0, is_agent: false, is_active_member: false, referral_code: R8728568, referral_count: 0, account_status: Active, can_withdraw_status: Withdrawal allowed, agent_id: null}, vip_status: {has_vip: true, vip_info: {level_id: 15, name: VIP 2, level: 2, upgrade_time: 2025-05-31T16:50:24.485657Z, refund_active: false, refund_required_days: 26, completed_days: 0, refund_progress: 0, refund_status_label: 未开始, active_plans_count: 0, vip_level: {name: VIP 2, level: 2, swmt_bonus_rate: 1.4, exp_bonus_rate: 1.5, upgrade_fee: 300.0, refund_enabled: true, refund_days: 26}}, features: [{name: 任务加成, description: 所有任务SWMT奖励增加40%, bonus_rate: 0.4, is_active: true}, {name: 经验加成, description: 获得经验值增加50%, bonus_rate: 0.5, is_active: true}, {name: 兑换手续费, description: 享受商品兑换手续费减免, physical_rate: 0.05, virtual_rate: 0.05, is_active: true}, {name: 专属任务, description: 解锁VIP专属任务, special_tasks: true, is_active: true}], availableLevel: {id: 16, name: VIP 3, level: 3, price: {amount: 500.00, currency: USDT, refund_days: 28}, swmt_bonus_rate: 1.6, exp_bonus_rate: 1.7}, additional_stats: {additional_swmt_earned: 2657.92, additional_exp_earned: 2725, actual_refund_amount: 0.0, description: {swmt: 通过VIP加成功能获得的总额外SWMT, exp: 通过VIP加成功能获得的总额外XP, refund: VIP返还计划实际返还的USDT金额}}}, today_summary: {date: 2025-07-16, total_tasks: 4, completed_tasks: 0, completion_percentage: 0, earned_today: {swmt: 0.00, exp: 0}, current_streak: 0, health_data: {steps: 0, distance: 0.0, calories: 0}, next_tasks: [{id: 116, name: 距离02-L3-6.5, type: distance, icon: null, total_swmt: 617.40, total_exp: 937}, {id: 112, name: 步数01-L3-4000步, type: steps, icon: null, total_swmt: 967.40, total_exp: 841}, {id: 113, name: 步数02-L3-5000步-权重2, type: steps, icon: null, total_swmt: 526.40, total_exp: 157}], all_tasks_completed: false}, daily_tasks: {total_tasks: 4, completed_tasks: 0, completion_percentage: 0, user_level: {name: Level 3, level: 3}, user_vip: {has_vip: true, name: VIP 2, level: 2}, vip_refund: null, tasks: [{id: 116, name: 距离02-L3-6.5, type: distance, icon: distance_icon.png, description: <p>距离02-L3-6.5</p>, rewards: {base: {swmt: 441.00, exp: 625}, vip_bonus: {swmt: 176.40, exp: 312}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 617.40, exp: 937}}, requirements: {steps_required: null, distance_required: 6.5, ad_duration: null, verification_type: health_app}, progress: {current_steps: null, current_distance: null, percentage: 0, last_updated: 2025-07-16 10:09:27}, status: pending, unlock_time: 2025-07-16T00:00:00, expire_time: 2025-07-16T23:59:59.999999, can_complete: false, completed_at: null}, {id: 112, name: 步数01-L3-4000步, type: steps, icon: steps_icon.png, description: <p>步数01-L3-4000步</p>, rewards: {base: {swmt: 691.00, exp: 561}, vip_bonus: {swmt: 276.40, exp: 280}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 967.40, exp: 841}}, requirements: {steps_required: 4000, distance_required: null, ad_duration: null, verification_type: health_app}, progress: {current_steps: 0, current_distance: null, percentage: 0, last_updated: 2025-07-16 10:09:27}, status: pending, unlock_time: 2025-07-16T00:00:00, expire_time: 2025-07-16T23:59:59.999999, can_complete: false, completed_at: null}, {id: 113, name: 步数02-L3-5000步-权重2, type: steps, icon: steps_icon.png, description: <p>步数02-L3-5000步-权重2</p>, rewards: {base: {swmt: 376.00, exp: 105}, vip_bonus: {swmt: 150.40, exp: 52}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 526.40, exp: 157}}, requirements: {steps_required: 5000, distance_required: null, ad_duration: null, verification_type: health_app}, progress: {current_steps: 0, current_distance: null, percentage: 0, last_updated: 2025-07-16 10:09:27}, status: pending, unlock_time: 2025-07-16T00:00:00, expire_time: 2025-07-16T23:59:59.999999, can_complete: false, completed_at: null}, {id: 119, name: 广告02-L3-100权重-40秒, type: ad, icon: ad_icon.png, description: <p>广告02-L3-100权重-40秒</p>, rewards: {base: {swmt: 657.00, exp: 850}, vip_bonus: {swmt: 262.80, exp: 425}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 919.80, exp: 1275}}, requirements: {steps_required: null, distance_required: null, ad_duration: 40, verification_type: system}, progress: {current_steps: null, current_distance: null, percentage: 0, last_updated: 2025-07-16 10:09:27}, status: pending, unlock_time: 2025-07-16T00:00:00, expire_time: 2025-07-16T23:59:59.999999, can_complete: true, completed_at: null}]}, addon_tasks: {active_addon_tasks: 0, completed_addon_tasks: 0, max_addon_tasks: 2, available_slots: 2, current_member_level: {name: Level 3, level: 20}, addon_tasks: [{id: 142, name: 拉1人-L123, icon: share_icon.png, description: <p>拉1人-L123</p>, referral_required: 1, swmt_bonus_rate: 1.3, exp_bonus_rate: 1.2, progress: {current_referrals: 0, required_referrals: 1, percentage: 0, status: pending}, expire_time: null, unlock_requirements: Refer 1 new user(s) to unlock this task., can_activate: true, unlock_message: 需要再邀请 1 位新用户才能完成此任务 (当前: 0/1), status: pending, completion_order: 1, is_next_pending_referral: false}, {id: 139, name: 拉2人-L123, icon: share_icon.png, description: <p>拉2人-L123</p>, referral_required: 2, swmt_bonus_rate: 1.4, exp_bonus_rate: 1.22, progress: {current_referrals: 0, required_referrals: 2, percentage: 0, status: pending}, expire_time: null, unlock_requirements: Refer 2 new user(s) to unlock this task., can_activate: true, unlock_message: 需要再邀请 2 位新用户才能完成此任务 (当前: 0/2), status: pending, completion_order: 2, is_next_pending_referral: false}]}}, timestamp: 1752657815, _meta: null}
2025-07-16 17:23:35.326708+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.326946+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.326980+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.set (package:sweatmint/core/data/cache_manager.dart:146:14)
2025-07-16 17:23:35.327017+0800 Runner[12979:9399738] flutter: │ #1   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:75:28)
2025-07-16 17:23:35.327114+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.327481+0800 Runner[12979:9399738] flutter: │ 17:23:35.326 (+0:00:01.558116)
2025-07-16 17:23:35.327511+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.327622+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.set: 设置缓存 dashboard_home
2025-07-16 17:23:35.327720+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.327622+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.set: 设置缓存 dashboard_home
2025-07-16 17:23:35.327720+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.355936+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.356017+0800 Runner[12979:9399738] flutter: │ #0   CacheManager._safeWrite (package:sweatmint/core/data/cache_manager.dart:26:14)
2025-07-16 17:23:35.356047+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.356073+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.356096+0800 Runner[12979:9399738] flutter: │ 17:23:35.355 (+0:00:01.586911)
2025-07-16 17:23:35.356119+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.356140+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager: Successfully wrote key "cache_dashboard_home" to secure storage (direct write)
2025-07-16 17:23:35.356164+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.357051+0800 Runner[12979:9399738] flutter: ┌\342\224
2025-07-16 17:23:35.357099+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.set (package:sweatmint/core/data/cache_manager.dart:164:14)
2025-07-16 17:23:35.357119+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.357141+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.357161+0800 Runner[12979:9399738] flutter: │ 17:23:35.356 (+0:00:01.588223)
2025-07-16 17:23:35.357183+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.357204+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.set: 缓存设置成功 dashboard_home (过期时间: 2025-07-16 17:28:35.318647)
2025-07-16 17:23:35.357250+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.357604+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.357638+0800 Runner[12979:9399738] flutter: │ #0   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:76:16)
2025-07-16 17:23:35.357659+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.357682+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.357803+0800 Runner[12979:9399738] flutter: │ 17:23:35.357 (+0:00:01.588811)
2025-07-16 17:23:35.357916+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.358036+0800 Runner[12979:9399738] flutter: │ 🐛 DashboardRemoteDataSource: 数据已缓存
2025-07-16 17:23:35.358202+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.358717+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:35.358763+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:82:14)<…>
2025-07-16 17:23:35.358787+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:35.358908+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.359169+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.358 (+0:00:01.589797)<…>
2025-07-16 17:23:35.359199+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.359286+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 DashboardRemoteDataSource: 首页聚合数据获取成功 (网络请求)<…>
2025-07-16 17:23:35.359431+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:35.360079+0800 Runner[12979:9399738] flutter: ┌<…>
2025-07-16 17:23:35.360079+0800 Runner[12979:9399738] flutter: ┌\342<…>
2025-07-16 17:23:35.360079+0800 Runner[12979:9399738] flutter: ┌\342\342\224
2025-07-16 17:23:35.360226+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:294:18)
2025-07-16 17:23:35.360313+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.360481+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.360699+0800 Runner[12979:9399738] flutter: │ 17:23:35.359 (+0:00:01.591186)
2025-07-16 17:23:35.360778+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.360879+0800 Runner[12979:9399738] flutter: │ 🐛 ----- 首页原始DTO数据开始 -----
2025-07-16 17:23:35.361016+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.362587+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.362683+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:295:18)
2025-07-16 17:23:35.362708+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.362733+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.362778+0800 Runner[12979:9399738] flutter: │ 17:23:35.362 (+0:00:01.593625)
2025-07-16 17:23:35.362799+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.362835+0800 Runner[12979:9399738] flutter: │ 🐛 DashboardDto.todaySummaryJson = {date: 2025-07-16, total_tasks: 4, completed_tasks: 0, completion_percentage: 0, earned_today: {swmt: 0.00, exp: 0}, current_streak: 0, health_data: {steps: 0, distance: 0.0, calories: 0}, next_tasks: [{id: 116, name: 距离02-L3-6.5, type: distance, icon: null, total_swmt: 617.40, total_exp: 937}, {id: 112, name: 步数01-L3-4000步, type: steps, icon: null, total_swmt: 967.40, total_exp: 841}, {id: 113, name: 步数02-L3-5000步-权重2, type: steps, icon: null, total_swmt: 526.40, total_exp: 157}], all_tasks_completed: false}
2025-07-16 17:23:35.362859+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.363245+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.363274+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:300:20)
2025-07-16 17:23:35.363377+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.363516+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.363596+0800 Runner[12979:9399738] flutter: │ 17:23:35.363 (+0:00:01.594455)
2025-07-16 17:23:35.363753+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.363898+0800 Runner[12979:9399738] flutter: │ 🐛 原始API返回 earned_today = {swmt: 0.00, exp: 0}
2025-07-16 17:23:35.364180+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.364636+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.364636+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:35.364669+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:306:22)
2025-07-16 17:23:35.364690+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.364712+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.364892+0800 Runner[12979:9399738] flutter: │ 17:23:35.364 (+0:00:01.595641)
2025-07-16 17:23:35.364916+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.364949+0800 Runner[12979:9399738] flutter: │ 🐛 原始API返回 earned_today.swmt = 0.00 (类型: String)
2025-07-16 17:23:35.364971+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.365183+0800 Runner[12979:9399738] flutter: ┌\342\224
2025-07-16 17:23:35.365183+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:35.365211+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:307:22)
2025-07-16 17:23:35.365438+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.365463+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.365483+0800 Runner[12979:9399738] flutter: │ 17:23:35.365 (+0:00:01.596400)
2025-07-16 17:23:35.365636+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.365707+0800 Runner[12979:9399738] flutter: │ 🐛 原始API返回 earned_today.exp = 0 (类型: int)
2025-07-16 17:23:35.365839+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.366137+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.366181+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:314:18)
2025-07-16 17:23:35.366321+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.366493+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.366650+0800 Runner[12979:9399738] flutter: │ 17:23:35.365 (+0:00:01.597272)
2025-07-16 17:23:35.366724+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.366859+0800 Runner[12979:9399738] flutter: │ 🐛 ----- 首页原始DTO数据结束 -----
2025-07-16 17:23:35.367020+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.371281+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌
2025-07-16 17:23:35.371281+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:35.371382+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:192:12)<…>
2025-07-16 17:23:35.371415+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)<…>
2025-07-16 17:23:35.371446+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.371474+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.370 (+0:00:01.602246)<…>

2025-07-16 17:23:35.371500+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.371525+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 HomeDashboardData.fromDto - 开始解析聚合数据<…>
2025-07-16 17:23:35.371550+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<<\342\200…>
2025-07-16 17:23:35.371686+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.371730+0800 Runner[12979:9399738] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:202:14)
2025-07-16 17:23:35.371756+0800 Runner[12979:9399738] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-16 17:23:35.371781+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.371805+0800 Runner[12979:9399738] flutter: │ 17:23:35.371 (+0:00:01.602930)
2025-07-16 17:23:35.371829+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.371853+0800 Runner[12979:9399738] flutter: │ 🐛 ===== Dashboard API 响应数据 =====
2025-07-16 17:23:35.371877+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.372217+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.372250+0800 Runner[12979:9399738] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:203:14)
2025-07-16 17:23:35.372278+0800 Runner[12979:9399738] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-16 17:23:35.372303+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.372327+0800 Runner[12979:9399738] flutter: │ 17:23:35.372 (+0:00:01.603462)
2025-07-16 17:23:35.372351+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.372384+0800 Runner[12979:9399738] flutter: │ 🐛 UserProfile数据: {user_id: S0401025, email: <EMAIL>, username: test_user2, avatar: avatar_14.png, member_level: {id: 20, name: Level 3, level: 3, min_exp: 10000, max_exp: 99999, daily_task_count: 4, extra_task_count: 2, benefits: Complete 4 daily tasks, activate 2 extra tasks}, exp: 11586, swmt_balance: 10995.2, usdt_balance: 0.********, total_commission: 0.0, is_agent: false, is_active_member: false, referral_code: R8728568, referral_count: 0, account_status: Active, can_withdraw_status: Withdrawal allowed, agent_id: null}
2025-07-16 17:23:35.372433+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.372711+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.372742+0800 Runner[12979:9399738] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:204:14)
2025-07-16 17:23:35.372769+0800 Runner[12979:9399738] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-16 17:23:35.372795+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.372850+0800 Runner[12979:9399738] flutter: │ 17:23:35.372 (+0:00:01.603957)
2025-07-16 17:23:35.372876+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.372923+0800 Runner[12979:9399738] flutter: │ 🐛 VipStatus数据: {has_vip: true, vip_info: {level_id: 15, name: VIP 2, level: 2, upgrade_time: 2025-05-31T16:50:24.485657Z, refund_active: false, refund_required_days: 26, completed_days: 0, refund_progress: 0, refund_status_label: 未开始, active_plans_count: 0, vip_level: {name: VIP 2, level: 2, swmt_bonus_rate: 1.4, exp_bonus_rate: 1.5, upgrade_fee: 300.0, refund_enabled: true, refund_days: 26}}, features: [{name: 任务加成, description: 所有任务SWMT奖励增加40%, bonus_rate: 0.4, is_active: true}, {name: 经验加成, description: 获得经验值增加50%, bonus_rate: 0.5, is_active: true}, {name: 兑换手续费, description: 享受商品兑换手续费减免, physical_rate: 0.05, virtual_rate: 0.05, is_active: true}, {name: 专属任务, description: 解锁VIP专属任务, special_tasks: true, is_active: true}], availableLevel: {id: 16, name: VIP 3, level: 3, price: {amount: 500.00, currency: USDT, refund_days: 28}, swmt_bonus_rate: 1.6, exp_bonus_rate: 1.7}, additional_stats: {additional_swmt_earned: 2657.92, additional_exp_earned: 2725, actual_refund_amount: 0.0, description: {swmt: 通过VIP加成功能获得的总额外SWMT, exp: 通过VIP加成功能获得的总额外XP, refund: VIP返还计划实际返还的USDT金额}}}
2025-07-16 17:23:35.373164+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.373366+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.373366+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:35.373366+0800 Runner[12979:9399738] flutter: ┌\342\342\224
2025-07-16 17:23:35.373400+0800 Runner[12979:9399738] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:205:14)
2025-07-16 17:23:35.373427+0800 Runner[12979:9399738] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-16 17:23:35.373455+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.373485+0800 Runner[12979:9399738] flutter: │ 17:23:35.373 (+0:00:01.604593)
2025-07-16 17:23:35.373509+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.373543+0800 Runner[12979:9399738] flutter: │ 🐛 TodaySummary数据: {date: 2025-07-16, total_tasks: 4, completed_tasks: 0, completion_percentage: 0, earned_today: {swmt: 0.00, exp: 0}, current_streak: 0, health_data: {steps: 0, distance: 0.0, calories: 0}, next_tasks: [{id: 116, name: 距离02-L3-6.5, type: distance, icon: null, total_swmt: 617.40, total_exp: 937}, {id: 112, name: 步数01-L3-4000步, type: steps, icon: null, total_swmt: 967.40, total_exp: 841}, {id: 113, name: 步数02-L3-5000步-权重2, type: steps, icon: null, total_swmt: 526.40, total_exp: 157}], all_tasks_completed: false}
2025-07-16 17:23:35.373605+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.373982+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.374016+0800 Runner[12979:9399738] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:206:14)
2025-07-16 17:23:35.374043+0800 Runner[12979:9399738] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-16 17:23:35.374070+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.374092+0800 Runner[12979:9399738] flutter: │ 17:23:35.373 (+0:00:01.605212)
2025-07-16 17:23:35.374116+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.374186+0800 Runner[12979:9399738] flutter: │ 🐛 DailyTasks数据: {total_tasks: 4, completed_tasks: 0, completion_percentage: 0, user_level: {name: Level 3, level: 3}, user_vip: {has_vip: true, name: VIP 2, level: 2}, vip_refund: null, tasks: [{id: 116, name: 距离02-L3-6.5, type: distance, icon: distance_icon.png, description: <p>距离02-L3-6.5</p>, rewards: {base: {swmt: 441.00, exp: 625}, vip_bonus: {swmt: 176.40, exp: 312}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 617.40, exp: 937}}, requirements: {steps_required: null, distance_required: 6.5, ad_duration: null, verification_type: health_app}, progress: {current_steps: null, current_distance: null, percentage: 0, last_updated: 2025-07-16 10:09:27}, status: pending, unlock_time: 2025-07-16T00:00:00, expire_time: 2025-07-16T23:59:59.999999, can_complete: false, completed_at: null}, {id: 112, name: 步数01-L3-4000步, type: steps, icon: steps_icon.png, description: <p>步数01-L3-4000步</p>, rewards: {base: {swmt: 691.00, exp: 561}, vip_bonus: {swmt: 276.40, exp: 280}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 967.40, exp: 841}}, requirements: {steps_required: 4000, distance_required: null, ad_duration: null, verification_type: health_app}, progress: {current_steps: 0, current_distance: null, percentage: 0, last_updated: 2025-07-16 10:09:27}, status: pending, unlock_time: 2025-07-16T00:00:00, expire_time: 2025-07-16T23:59:59.999999, can_complete: false, completed_at: null}, {id: 113, name: 步数02-L3-5000步-权重2, type: steps, icon: steps_icon.png, description: <p>步数02-L3-5000步-权重2</p>, rewards: {base: {swmt: 376.00, exp: 105}, vip_bonus: {swmt: 150.40, exp: 52}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 526.40, exp: 157}}, requirements: {steps_required: 5000, distance_required: null, ad_duration: null, verification_type: health_app}, progress: {current_steps: 0, current_distance: null, percentage: 0, last_updated: 2025-07-16 10:09:27}, status: pending, unlock_time: 2025-07-16T00:00:00, expire_time: 2025-07-16T23:59:59.999999, can_complete: false, completed_at: null}, {id: 119, name: 广告02-L3-100权重-40秒, type: ad, icon: ad_icon.png, description: <p>广告02-L3-100权重-40秒</p>, rewards: {base: {swmt: 657.00, exp: 850}, vip_bonus: {swmt: 262.80, exp: 425}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 919.80, exp: 1275}}, requirements: {steps_required: null, distance_required: null, ad_duration: 40, verification_type: system}, progress: {current_steps: null, current_distance: null, percentage: 0, last_updated: 2025-07-16 10:09:27}, status: pending, unlock_time: 2025-07-16T00:00:00, expire_time: 2025-07-16T23:59:59.999999, can_complete: true, completed_at: null}]}
2025-07-16 17:23:35.374408+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.374696+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.374730+0800 Runner[12979:9399738] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:207:14)
2025-07-16 17:23:35.374756+0800 Runner[12979:9399738] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-16 17:23:35.374780+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.374882+0800 Runner[12979:9399738] flutter: │ 17:23:35.374 (+0:00:01.605930)
2025-07-16 17:23:35.374913+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.374955+0800 Runner[12979:9399738] flutter: │ 🐛 AddonTasks数据: {active_addon_tasks: 0, completed_addon_tasks: 0, max_addon_tasks: 2, available_slots: 2, current_member_level: {name: Level 3, level: 20}, addon_tasks: [{id: 142, name: 拉1人-L123, icon: share_icon.png, description: <p>拉1人-L123</p>, referral_required: 1, swmt_bonus_rate: 1.3, exp_bonus_rate: 1.2, progress: {current_referrals: 0, required_referrals: 1, percentage: 0, status: pending}, expire_time: null, unlock_requirements: Refer 1 new user(s) to unlock this task., can_activate: true, unlock_message: 需要再邀请 1 位新用户才能完成此任务 (当前: 0/1), status: pending, completion_order: 1, is_next_pending_referral: false}, {id: 139, name: 拉2人-L123, icon: share_icon.png, description: <p>拉2人-L123</p>, referral_required: 2, swmt_bonus_rate: 1.4, exp_bonus_rate: 1.22, progress: {current_referrals: 0, required_referrals: 2, percentage: 0, status: pending}, expire_time: null, unlock_requirements: Refer 2 new user(s) to unlock this task., can_activate: true, unlock_message: 需要再邀请 2 位新用户才能完成此任务 (当前: 0/2), status: pending, completion_order: 2, is_next_pending_referral: false}]}
2025-07-16 17:23:35.375359+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.375501+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.375530+0800 Runner[12979:9399738] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:208:14)
2025-07-16 17:23:35.375558+0800 Runner[12979:9399738] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-16 17:23:35.375584+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.375605+0800 Runner[12979:9399738] flutter: │ 17:23:35.375 (+0:00:01.606742)
2025-07-16 17:23:35.375629+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.375655+0800 Runner[12979:9399738] flutter: │ 🐛 ================================
2025-07-16 17:23:35.375679+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.377898+0800 Runner[12979:9399738] flutter: ┌\342\224
2025-07-16 17:23:35.377898+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:35.377940+0800 Runner[12979:9399738] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:215:18)
2025-07-16 17:23:35.377969+0800 Runner[12979:9399738] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-16 17:23:35.377997+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.378021+0800 Runner[12979:9399738] flutter: │ 17:23:35.377 (+0:00:01.609106)
2025-07-16 17:23:35.378045+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.378067+0800 Runner[12979:9399738] flutter: │ 🐛 已解析userProfile，username: test_user2
2025-07-16 17:23:35.378150+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.382129+0800 Runner[12979:9399738] flutter: 🔍 VipStatusDataDto解析additionalStats:
2025-07-16 17:23:35.382267+0800 Runner[12979:9399738] flutter:   json.keys: [has_vip, vip_info, features, availableLevel, additional_stats]
2025-07-16 17:23:35.382320+0800 Runner[12979:9399738] flutter:   additionalStats字段存在(驼峰): false
2025-07-16 17:23:35.382356+0800 Runner[12979:9399738] flutter:   additional_stats字段存在(下划线): true
2025-07-16 17:23:35.382385+0800 Runner[12979:9399738] flutter:   解析后的additionalStats != null: true
2025-07-16 17:23:35.382435+0800 Runner[12979:9399738] flutter:   additionalStats类型: _Map<String, dynamic>
2025-07-16 17:23:35.382489+0800 Runner[12979:9399738] flutter:   additionalStats内容: {additional_swmt_earned: 2657.92, additional_exp_earned: 2725, actual_refund_amount: 0.0, description: {swmt: 通过VIP加成功能获得的总额外SWMT, exp: 通过VIP加成功能获得的总额外XP, refund: VIP返还计划实际返还的USDT金额}}
2025-07-16 17:23:35.382932+0800 Runner[12979:9399738] flutter:   ✅ AdditionalStatsDto解析成功: SWMT=2657.92, XP=2725.0
2025-07-16 17:23:35.386071+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.386134+0800 Runner[12979:9399738] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:230:18)
2025-07-16 17:23:35.386163+0800 Runner[12979:9399738] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-16 17:23:35.386187+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.386208+0800 Runner[12979:9399738] flutter: │ 17:23:35.385 (+0:00:01.617107)
2025-07-16 17:23:35.386231+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.386253+0800 Runner[12979:9399738] flutter: │ 🐛 已解析vipStatus，hasVip: true
2025-07-16 17:23:35.386356+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.387099+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.387099+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:35.387131+0800 Runner[12979:9399738] flutter: │ #0   new TodaySummaryDataDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:166:12)
2025-07-16 17:23:35.387153+0800 Runner[12979:9399738] flutter: │ #1   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:242:59)
2025-07-16 17:23:35.387177+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:35.388419+0800 Runner[12979:9399738] flutter: │ 17:23:35.386 (+0:00:01.618308)
2025-07-16 17:23:35.388444+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:35.388419+0800 Runner[12979:9399738] flutter: │ 17:23:35.386 (+0:00:01.618308)
2025-07-16 17:23:35.388444+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:35.388479+0800 Runner[12979:9399738] flutter: │ 🐛 TodaySummaryDataDto.fromJson: received json for earned_today: {swmt: 0.00, exp: 0}
2025-07-16 17:23:35.388505+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.389106+0800 Runner[12979:9399738] flutter: ┌\342\224
2025-07-16 17:23:35.388419+0800 Runner[12979:9399738] flutter: │ 17:23:35.386 (+0:00:01.618308)
2025-07-16 17:23:35.388444+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:35.388479+0800 Runner[12979:9399738] flutter: │ 🐛 TodaySummaryDataDto.fromJson: received json for earned_today: {swmt: 0.00, exp: 0}
2025-07-16 17:23:35.388505+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.389106+0800 Runner[12979:9399738] flutter: ┌\342\342\224
2025-07-16 17:23:35.388419+0800 Runner[12979:9399738] flutter: │ 17:23:35.386 (+0:00:01.618308)
2025-07-16 17:23:35.388444+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:35.388479+0800 Runner[12979:9399738] flutter: │ 🐛 TodaySummaryDataDto.fromJson: received json for earned_today: {swmt: 0.00, exp: 0}
2025-07-16 17:23:35.388505+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.389106+0800 Runner[12979:9399738] flutter: ┌\342\342\224
2025-07-16 17:23:35.389142+0800 Runner[12979:9399738] flutter: │ #0   new EarnedTodayDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:34:12)
2025-07-16 17:23:35.389165+0800 Runner[12979:9399738] flutter: │ #1   new TodaySummaryDataDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:178:28)
2025-07-16 17:23:35.389190+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.389277+0800 Runner[12979:9399738] flutter: │ 17:23:35.388 (+0:00:01.620291)
2025-07-16 17:23:35.389371+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.389494+0800 Runner[12979:9399738] flutter: │ 🐛 EarnedTodayDto.fromJson - 原始数据: {swmt: 0.00, exp: 0}
2025-07-16 17:23:35.389632+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.389867+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.389898+0800 Runner[12979:9399738] flutter: │ #0   new EarnedTodayDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:35:12)
2025-07-16 17:23:35.389923+0800 Runner[12979:9399738] flutter: │ #1   new TodaySummaryDataDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:178:28)
2025-07-16 17:23:35.389949+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.389979+0800 Runner[12979:9399738] flutter: │ 17:23:35.389 (+0:00:01.621098)
2025-07-16 17:23:35.390003+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.389979+0800 Runner[12979:9399738] flutter: │ 17:23:35.389 (+0:00:01.621098)
2025-07-16 17:23:35.390003+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:23:35.389979+0800 Runner[12979:9399738] flutter: │ 17:23:35.389 (+0:00:01.621098)
2025-07-16 17:23:35.390003+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.390028+0800 Runner[12979:9399738] flutter: │ 🐛 EarnedTodayDto.fromJson - swmt类型: String, 值: 0.00
2025-07-16 17:23:35.390053+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.389979+0800 Runner[12979:9399738] flutter: │ 17:23:35.389 (+0:00:01.621098)
2025-07-16 17:23:35.390003+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.390028+0800 Runner[12979:9399738] flutter: │ 🐛 EarnedTodayDto.fromJson - swmt类型: String, 值: 0.00
2025-07-16 17:23:35.390053+0800 Runner[12979:9399738] flutter: └\342
2025-07-16 17:23:35.389979+0800 Runner[12979:9399738] flutter: │ 17:23:35.389 (+0:00:01.621098)
2025-07-16 17:23:35.390003+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.390028+0800 Runner[12979:9399738] flutter: │ 🐛 EarnedTodayDto.fromJson - swmt类型: String, 值: 0.00
2025-07-16 17:23:35.390053+0800 Runner[12979:9399738] flutter: └\342\342\224
2025-07-16 17:23:35.390194+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.389979+0800 Runner[12979:9399738] flutter: │ 17:23:35.389 (+0:00:01.621098)
2025-07-16 17:23:35.390003+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.390028+0800 Runner[12979:9399738] flutter: │ 🐛 EarnedTodayDto.fromJson - swmt类型: String, 值: 0.00
2025-07-16 17:23:35.390053+0800 Runner[12979:9399738] flutter: └\342\342\224
2025-07-16 17:23:35.390194+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:35.389979+0800 Runner[12979:9399738] flutter: │ 17:23:35.389 (+0:00:01.621098)
2025-07-16 17:23:35.390003+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.390028+0800 Runner[12979:9399738] flutter: │ 🐛 EarnedTodayDto.fromJson - swmt类型: String, 值: 0.00
2025-07-16 17:23:35.390053+0800 Runner[12979:9399738] flutter: └\342\342\224
2025-07-16 17:23:35.390194+0800 Runner[12979:9399738] flutter: ┌\342\342\224
2025-07-16 17:23:35.390224+0800 Runner[12979:9399738] flutter: │ #0   new EarnedTodayDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:36:12)
2025-07-16 17:23:35.390246+0800 Runner[12979:9399738] flutter: │ #1   new TodaySummaryDataDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:178:28)
2025-07-16 17:23:35.390271+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.390363+0800 Runner[12979:9399738] flutter: │ 17:23:35.390 (+0:00:01.621436)
2025-07-16 17:23:35.390393+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.390416+0800 Runner[12979:9399738] flutter: │ 🐛 EarnedTodayDto.fromJson - exp类型: int, 值: 0
2025-07-16 17:23:35.390440+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.390601+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.390638+0800 Runner[12979:9399738] flutter: │ #0   new EarnedTodayDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:69:12)
2025-07-16 17:23:35.390664+0800 Runner[12979:9399738] flutter: │ #1   new TodaySummaryDataDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:178:28)
2025-07-16 17:23:35.390688+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.390712+0800 Runner[12979:9399738] flutter: │ 17:23:35.390 (+0:00:01.621829)
2025-07-16 17:23:35.390737+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.390759+0800 Runner[12979:9399738] flutter: │ 🐛 EarnedTodayDto.fromJson - 解析后: swmt=0.00, exp=0
2025-07-16 17:23:35.390783+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.391891+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.391930+0800 Runner[12979:9399738] flutter: │ #0   new TodaySummary.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:135:12)
2025-07-16 17:23:35.391956+0800 Runner[12979:9399738] flutter: │ #1   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:248:39)
2025-07-16 17:23:35.391981+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.392004+0800 Runner[12979:9399738] flutter: │ 17:23:35.391 (+0:00:01.623113)
2025-07-16 17:23:35.392027+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.392052+0800 Runner[12979:9399738] flutter: │ 🐛 Converting TodaySummaryDto to TodaySummary entity
2025-07-16 17:23:35.392078+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.392555+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.392590+0800 Runner[12979:9399738] flutter: │ #0   new EarnedToday.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:13:12)
2025-07-16 17:23:35.392617+0800 Runner[12979:9399738] flutter: │ #1   new TodaySummary.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:156:49)
2025-07-16 17:23:35.392643+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.392666+0800 Runner[12979:9399738] flutter: │ 17:23:35.392 (+0:00:01.623787)
2025-07-16 17:23:35.392690+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.392712+0800 Runner[12979:9399738] flutter: │ 🐛 Converting EarnedTodayDto to EarnedToday entity
2025-07-16 17:23:35.392777+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.392948+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.392979+0800 Runner[12979:9399738] flutter: │ #0   new EarnedToday.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:14:12)
2025-07-16 17:23:35.393009+0800 Runner[12979:9399738] flutter: │ #1   new TodaySummary.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:156:49)
2025-07-16 17:23:35.393034+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.393060+0800 Runner[12979:9399738] flutter: │ 17:23:35.392 (+0:00:01.624188)
2025-07-16 17:23:35.393084+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.393112+0800 Runner[12979:9399738] flutter: │ 🐛 Original swmt value (String): "0.00"
2025-07-16 17:23:35.393217+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.393112+0800 Runner[12979:9399738] flutter: │ 🐛 Original swmt value (String): "0.00"
2025-07-16 17:23:35.393217+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.393386+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.393112+0800 Runner[12979:9399738] flutter: │ 🐛 Original swmt value (String): "0.00"
2025-07-16 17:23:35.393217+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.393386+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:35.393112+0800 Runner[12979:9399738] flutter: │ 🐛 Original swmt value (String): "0.00"
2025-07-16 17:23:35.393217+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.393386+0800 Runner[12979:9399738] flutter: ┌\342\342\224
2025-07-16 17:23:35.393418+0800 Runner[12979:9399738] flutter: │ #0   new EarnedToday.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:15:12)
2025-07-16 17:23:35.393444+0800 Runner[12979:9399738] flutter: │ #1   new TodaySummary.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:156:49)
2025-07-16 17:23:35.393468+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.393490+0800 Runner[12979:9399738] flutter: │ 17:23:35.393 (+0:00:01.624629)
2025-07-16 17:23:35.393516+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.393538+0800 Runner[12979:9399738] flutter: │ 🐛 Original exp value (int): 0
2025-07-16 17:23:35.393562+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.393930+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.393965+0800 Runner[12979:9399738] flutter: │ #0   new EarnedToday.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:37:16)
2025-07-16 17:23:35.393992+0800 Runner[12979:9399738] flutter: │ #1   new TodaySummary.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:156:49)
2025-07-16 17:23:35.394017+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:35.394045+0800 Runner[12979:9399738] flutter: │ 17:23:35.393 (+0:00:01.625169)
2025-07-16 17:23:35.394070+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:35.394045+0800 Runner[12979:9399738] flutter: │ 17:23:35.393 (+0:00:01.625169)
2025-07-16 17:23:35.394070+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.394045+0800 Runner[12979:9399738] flutter: │ 17:23:35.393 (+0:00:01.625169)
2025-07-16 17:23:35.394070+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.394092+0800 Runner[12979:9399738] flutter: │ 🐛 Cleaned swmt string: "0.00"
2025-07-16 17:23:35.394161+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.394045+0800 Runner[12979:9399738] flutter: │ 17:23:35.393 (+0:00:01.625169)
2025-07-16 17:23:35.394070+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.394092+0800 Runner[12979:9399738] flutter: │ 🐛 Cleaned swmt string: "0.00"
2025-07-16 17:23:35.394161+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.394907+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.394945+0800 Runner[12979:9399738] flutter: │ #0   new EarnedToday.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:45:12)
2025-07-16 17:23:35.394971+0800 Runner[12979:9399738] flutter: │ #1   new TodaySummary.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:156:49)
2025-07-16 17:23:35.394996+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.395018+0800 Runner[12979:9399738] flutter: │ 17:23:35.394 (+0:00:01.626119)
2025-07-16 17:23:35.395045+0800 Runner[12979:9399738] flutter: ├

2025-07-16 17:23:35.395067+0800 Runner[12979:9399738] flutter: │ 🐛 Final parsed values: swmt=0.0, exp=0
2025-07-16 17:23:35.395111+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.396552+0800 Runner[12979:9399738] flutter: ┌\342\224
2025-07-16 17:23:35.396552+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:35.396596+0800 Runner[12979:9399738] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:249:18)
2025-07-16 17:23:35.396624+0800 Runner[12979:9399738] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-16 17:23:35.396649+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.396723+0800 Runner[12979:9399738] flutter: │ 17:23:35.396 (+0:00:01.627764)
2025-07-16 17:23:35.396746+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.396768+0800 Runner[12979:9399738] flutter: │ 🐛 已解析todaySummary，SWMT: 0.0，EXP: 0
2025-07-16 17:23:35.396790+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.402524+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.402577+0800 Runner[12979:9399738] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:282:18)
2025-07-16 17:23:35.402606+0800 Runner[12979:9399738] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-16 17:23:35.402636+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.402660+0800 Runner[12979:9399738] flutter: │ 17:23:35.402 (+0:00:01.633703)
2025-07-16 17:23:35.402684+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.402706+0800 Runner[12979:9399738] flutter: │ 🐛 已解析dailyTaskList，完成任务: 0/4
2025-07-16 17:23:35.402787+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.403864+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.403909+0800 Runner[12979:9399738] flutter: │ #0   new AddonTaskListDataDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:191:14)
2025-07-16 17:23:35.403937+0800 Runner[12979:9399738] flutter: │ #1   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:294:61)
2025-07-16 17:23:35.403963+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.403987+0800 Runner[12979:9399738] flutter: │ 17:23:35.403 (+0:00:01.635067)
2025-07-16 17:23:35.404012+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.404037+0800 Runner[12979:9399738] flutter: │ 🐛 AddonTaskListDataDto.fromJson - 原始数据: (active_addon_tasks, completed_addon_tasks, max_addon_tasks, ..., current_member_level, addon_tasks)
2025-07-16 17:23:35.404063+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.404957+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.404994+0800 Runner[12979:9399738] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:76:12)
2025-07-16 17:23:35.405022+0800 Runner[12979:9399738] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-16 17:23:35.405048+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:35.405135+0800 Runner[12979:9399738] flutter: │ 17:23:35.404 (+0:00:01.636146)
2025-07-16 17:23:35.405163+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:35.405135+0800 Runner[12979:9399738] flutter: │ 17:23:35.404 (+0:00:01.636146)
2025-07-16 17:23:35.405163+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.405135+0800 Runner[12979:9399738] flutter: │ 17:23:35.404 (+0:00:01.636146)
2025-07-16 17:23:35.405163+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.405185+0800 Runner[12979:9399738] flutter: │ 🐛 AddonTaskItemDto.fromJson - 处理原始数据
2025-07-16 17:23:35.405215+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.405135+0800 Runner[12979:9399738] flutter: │ 17:23:35.404 (+0:00:01.636146)
2025-07-16 17:23:35.405163+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.405185+0800 Runner[12979:9399738] flutter: │ 🐛 AddonTaskItemDto.fromJson - 处理原始数据
2025-07-16 17:23:35.405215+0800 Runner[12979:9399738] flutter: └\342\342\224
2025-07-16 17:23:35.405135+0800 Runner[12979:9399738] flutter: │ 17:23:35.404 (+0:00:01.636146)
2025-07-16 17:23:35.405163+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.405185+0800 Runner[12979:9399738] flutter: │ 🐛 AddonTaskItemDto.fromJson - 处理原始数据
2025-07-16 17:23:35.405215+0800 Runner[12979:9399738] flutter: └\342\342\224
2025-07-16 17:23:35.405404+0800 Runner[12979:9399738] flutter: ┌\342\224
2025-07-16 17:23:35.405135+0800 Runner[12979:9399738] flutter: │ 17:23:35.404 (+0:00:01.636146)
2025-07-16 17:23:35.405163+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.405185+0800 Runner[12979:9399738] flutter: │ 🐛 AddonTaskItemDto.fromJson - 处理原始数据
2025-07-16 17:23:35.405215+0800 Runner[12979:9399738] flutter: └\342\342\224
2025-07-16 17:23:35.405404+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:35.405437+0800 Runner[12979:9399738] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:77:12)
2025-07-16 17:23:35.405460+0800 Runner[12979:9399738] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-16 17:23:35.405485+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.405506+0800 Runner[12979:9399738] flutter: │ 17:23:35.405 (+0:00:01.636604)
2025-07-16 17:23:35.405530+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.405555+0800 Runner[12979:9399738] flutter: │ 🐛 id: 142 (类型: int)
2025-07-16 17:23:35.405580+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.405858+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.405890+0800 Runner[12979:9399738] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:115:14)
2025-07-16 17:23:35.405979+0800 Runner[12979:9399738] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-16 17:23:35.406075+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.406163+0800 Runner[12979:9399738] flutter: │ 17:23:35.405 (+0:00:01.637070)
2025-07-16 17:23:35.406336+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.406432+0800 Runner[12979:9399738] flutter: │ 🐛 🔧 解析字段: id=142 (int)
2025-07-16 17:23:35.406515+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.406827+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.406859+0800 Runner[12979:9399738] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:116:14)
2025-07-16 17:23:35.406980+0800 Runner[12979:9399738] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-16 17:23:35.407124+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.407206+0800 Runner[12979:9399738] flutter: │ 17:23:35.406 (+0:00:01.638012)
2025-07-16 17:23:35.407363+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.407481+0800 Runner[12979:9399738] flutter: │ 🐛 🔧 解析字段: unlock_requirements=Refer 1 new user(s) to unlock this task. (String)
2025-07-16 17:23:35.407629+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.408593+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.408639+0800 Runner[12979:9399738] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:76:12)
2025-07-16 17:23:35.408670+0800 Runner[12979:9399738] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-16 17:23:35.408697+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:35.408727+0800 Runner[12979:9399738] flutter: │ 17:23:35.408 (+0:00:01.639752)
2025-07-16 17:23:35.408751+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:35.408727+0800 Runner[12979:9399738] flutter: │ 17:23:35.408 (+0:00:01.639752)
2025-07-16 17:23:35.408751+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:23:35.408774+0800 Runner[12979:9399738] flutter: │ 🐛 AddonTaskItemDto.fromJson - 处理原始数据
2025-07-16 17:23:35.408800+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.408727+0800 Runner[12979:9399738] flutter: │ 17:23:35.408 (+0:00:01.639752)
2025-07-16 17:23:35.408751+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:23:35.408774+0800 Runner[12979:9399738] flutter: │ 🐛 AddonTaskItemDto.fromJson - 处理原始数据
2025-07-16 17:23:35.408800+0800 Runner[12979:9399738] flutter: └\342\342\224
2025-07-16 17:23:35.408727+0800 Runner[12979:9399738] flutter: │ 17:23:35.408 (+0:00:01.639752)
2025-07-16 17:23:35.408751+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:23:35.408774+0800 Runner[12979:9399738] flutter: │ 🐛 AddonTaskItemDto.fromJson - 处理原始数据
2025-07-16 17:23:35.408800+0800 Runner[12979:9399738] flutter: └\342\342\224
2025-07-16 17:23:35.408964+0800 Runner[12979:9399738] flutter: ┌\342\224
2025-07-16 17:23:35.408727+0800 Runner[12979:9399738] flutter: │ 17:23:35.408 (+0:00:01.639752)
2025-07-16 17:23:35.408751+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:23:35.408774+0800 Runner[12979:9399738] flutter: │ 🐛 AddonTaskItemDto.fromJson - 处理原始数据
2025-07-16 17:23:35.408800+0800 Runner[12979:9399738] flutter: └\342\342\224
2025-07-16 17:23:35.408964+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:35.408995+0800 Runner[12979:9399738] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:77:12)
2025-07-16 17:23:35.409019+0800 Runner[12979:9399738] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-16 17:23:35.409046+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.409134+0800 Runner[12979:9399738] flutter: │ 17:23:35.408 (+0:00:01.640178)
2025-07-16 17:23:35.409159+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.409182+0800 Runner[12979:9399738] flutter: │ 🐛 id: 139 (类型: int)
2025-07-16 17:23:35.409205+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.409512+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.409553+0800 Runner[12979:9399738] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:115:14)
2025-07-16 17:23:35.409580+0800 Runner[12979:9399738] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-16 17:23:35.409608+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.409630+0800 Runner[12979:9399738] flutter: │ 17:23:35.409 (+0:00:01.640726)
2025-07-16 17:23:35.409654+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.409678+0800 Runner[12979:9399738] flutter: │ 🐛 🔧 解析字段: id=139 (int)
2025-07-16 17:23:35.409704+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.409859+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.409889+0800 Runner[12979:9399738] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:116:14)
2025-07-16 17:23:35.409915+0800 Runner[12979:9399738] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-16 17:23:35.409942+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.409966+0800 Runner[12979:9399738] flutter: │ 17:23:35.409 (+0:00:01.641081)
2025-07-16 17:23:35.409990+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.410013+0800 Runner[12979:9399738] flutter: │ 🐛 🔧 解析字段: unlock_requirements=Refer 2 new user(s) to unlock this task. (String)
2025-07-16 17:23:35.410044+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.411615+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.411655+0800 Runner[12979:9399738] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:301:18)
2025-07-16 17:23:35.411684+0800 Runner[12979:9399738] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-16 17:23:35.411709+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.411731+0800 Runner[12979:9399738] flutter: │ 17:23:35.411 (+0:00:01.642835)
2025-07-16 17:23:35.411757+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.411780+0800 Runner[12979:9399738] flutter: │ 🐛 已解析addonTaskList，活跃任务: 0/2
2025-07-16 17:23:35.411804+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.411780+0800 Runner[12979:9399738] flutter: │ 🐛 已解析addonTaskList，活跃任务: 0/2
2025-07-16 17:23:35.411804+0800 Runner[12979:9399738] flutter: └\342
2025-07-16 17:23:35.412029+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌
2025-07-16 17:23:35.411780+0800 Runner[12979:9399738] flutter: │ 🐛 已解析addonTaskList，活跃任务: 0/2
2025-07-16 17:23:35.411804+0800 Runner[12979:9399738] flutter: └\342
2025-07-16 17:23:35.412029+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:35.412066+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:314:14)<…>
2025-07-16 17:23:35.412092+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)<…>
2025-07-16 17:23:35.412135+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:35.412164+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.411 (+0:00:01.643253)<…>
2025-07-16 17:23:35.412191+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.412214+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 HomeDashboardData解析完成<…>
2025-07-16 17:23:35.412273+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:35.412566+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:35.412566+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:35.412602+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:353:18)<…>
2025-07-16 17:23:35.412628+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:35.412653+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.412714+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.412 (+0:00:01.643799)<…>
2025-07-16 17:23:35.412737+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.412758+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔄 开始更新首页状态 - homeData可用性检查<…>
2025-07-16 17:23:35.412780+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:35.412989+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.413021+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:354:18)
2025-07-16 17:23:35.413043+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.413070+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:35.413205+0800 Runner[12979:9399738] flutter: │ 17:23:35.412 (+0:00:01.644243)
2025-07-16 17:23:35.413231+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:35.413205+0800 Runner[12979:9399738] flutter: │ 17:23:35.412 (+0:00:01.644243)
2025-07-16 17:23:35.413231+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.413205+0800 Runner[12979:9399738] flutter: │ 17:23:35.412 (+0:00:01.644243)
2025-07-16 17:23:35.413231+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.413253+0800 Runner[12979:9399738] flutter: │ 🐛 homeData.userProfile: 可用
2025-07-16 17:23:35.413278+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.413205+0800 Runner[12979:9399738] flutter: │ 17:23:35.412 (+0:00:01.644243)
2025-07-16 17:23:35.413231+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.413253+0800 Runner[12979:9399738] flutter: │ 🐛 homeData.userProfile: 可用
2025-07-16 17:23:35.413278+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.413425+0800 Runner[12979:9399738] flutter: ┌\342\224
2025-07-16 17:23:35.413205+0800 Runner[12979:9399738] flutter: │ 17:23:35.412 (+0:00:01.644243)
2025-07-16 17:23:35.413231+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.413253+0800 Runner[12979:9399738] flutter: │ 🐛 homeData.userProfile: 可用
2025-07-16 17:23:35.413278+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.413425+0800 Runner[12979:9399738] flutter: ┌\342\342\224
2025-07-16 17:23:35.413205+0800 Runner[12979:9399738] flutter: │ 17:23:35.412 (+0:00:01.644243)
2025-07-16 17:23:35.413231+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.413253+0800 Runner[12979:9399738] flutter: │ 🐛 homeData.userProfile: 可用
2025-07-16 17:23:35.413278+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.413425+0800 Runner[12979:9399738] flutter: ┌\342\342\224
2025-07-16 17:23:35.413457+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:355:18)
2025-07-16 17:23:35.413483+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.413509+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.413533+0800 Runner[12979:9399738] flutter: │ 17:23:35.413 (+0:00:01.644684)
2025-07-16 17:23:35.413557+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.413579+0800 Runner[12979:9399738] flutter: │ 🐛 homeData.vipStatus: 可用
2025-07-16 17:23:35.413603+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.413749+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.413783+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:356:18)
2025-07-16 17:23:35.413813+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.413837+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.413858+0800 Runner[12979:9399738] flutter: │ 17:23:35.413 (+0:00:01.645008)
2025-07-16 17:23:35.413883+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.413905+0800 Runner[12979:9399738] flutter: │ 🐛 homeData.todaySummary: 可用
2025-07-16 17:23:35.413929+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.414426+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌
2025-07-16 17:23:35.414426+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342
2025-07-16 17:23:35.414426+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-16 17:23:35.414460+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:372:20)<…>
2025-07-16 17:23:35.414487+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:35.414516+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:35.414539+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.414 (+0:00:01.645670)<…>
2025-07-16 17:23:35.414563+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.414587+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 用户档案数据已更新: test_user2, exp=11586<…>
2025-07-16 17:23:35.414613+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:35.414779+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:35.414817+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:378:20)<…>
2025-07-16 17:23:35.414841+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:35.414865+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.414889+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.414 (+0:00:01.646027)<…>
2025-07-16 17:23:35.414913+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.414889+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.414 (+0:00:01.646027)<…>
2025-07-16 17:23:35.414913+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342<…>
2025-07-16 17:23:35.414937+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ VIP状态数据已更新: hasVip=true<…>
2025-07-16 17:23:35.414962+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:35.415339+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:35.415377+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:384:20)<…>
2025-07-16 17:23:35.415403+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:35.415483+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.415644+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.415 (+0:00:01.646544)<…>
2025-07-16 17:23:35.415767+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.415918+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 今日摘要数据已更新: SWMT=0.0, EXP=0<…>
2025-07-16 17:23:35.416032+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└…>
2025-07-16 17:23:35.415918+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 今日摘要数据已更新: SWMT=0.0, EXP=0<…>
2025-07-16 17:23:35.416032+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:23:35.416321+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:35.416353+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:390:20)<…>
2025-07-16 17:23:35.416510+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:35.416643+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.416845+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.416 (+0:00:01.647557)<…>
2025-07-16 17:23:35.416974+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.416845+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.416 (+0:00:01.647557)<…>
2025-07-16 17:23:35.416974+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:35.417083+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 每日任务数据已更新: 0/4<…>
2025-07-16 17:23:35.417213+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:35.417487+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:35.417711+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:396:20)<…>
2025-07-16 17:23:35.417857+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:35.417968+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.418096+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.417 (+0:00:01.648730)<…>
2025-07-16 17:23:35.418209+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.418096+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.417 (+0:00:01.648730)<…>
2025-07-16 17:23:35.418209+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:35.418345+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 附加任务数据已更新: 0/2<…>
2025-07-16 17:23:35.418491+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:35.418711+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:35.418857+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:399:18)<…>
2025-07-16 17:23:35.418880+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:35.418977+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.419069+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.418 (+0:00:01.649956)<…>
2025-07-16 17:23:35.419097+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.419120+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🎉 首页聚合数据状态更新成功 - 所有状态已原子性更新并通知UI<…>
2025-07-16 17:23:35.419148+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:35.419418+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.419506+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:422:16)
2025-07-16 17:23:35.419662+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.419802+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.419941+0800 Runner[12979:9399738] flutter: │ 17:23:35.419 (+0:00:01.650667)
2025-07-16 17:23:35.420065+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.420178+0800 Runner[12979:9399738] flutter: │ 🐛 ✅ Dashboard数据加载完成，健康数据由独立Health API处理
2025-07-16 17:23:35.420337+0800 Runner[12979:9399738] flutter: └立Health API处理
2025-07-16 17:23:35.420337+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.420563+0800 Runner[12979:9399738] flutter: ┌立Health API处理
2025-07-16 17:23:35.420337+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.420563+0800 Runner[12979:9399738] flutter: ┌\342立Health API处理
2025-07-16 17:23:35.420337+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.420563+0800 Runner[12979:9399738] flutter: ┌\342\342\224
2025-07-16 17:23:35.420593+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:427:18)
2025-07-16 17:23:35.420684+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.420851+0800 Runner[12979:9399738] flutter: ├立Health API处理
2025-07-16 17:23:35.420337+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.420563+0800 Runner[12979:9399738] flutter: ┌\342\342\224
2025-07-16 17:23:35.420593+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:427:18)
2025-07-16 17:23:35.420684+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.420851+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:35.420949+0800 Runner[12979:9399738] flutter: │ 17:23:35.420 (+0:00:01.651813)
2025-07-16 17:23:35.421117+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.421238+0800 Runner[12979:9399738] flutter: │ 🐛 ----- 转换后的Entity数据详情 -----
2025-07-16 17:23:35.421386+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.421599+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.421636+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:428:18)
2025-07-16 17:23:35.421656+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.421763+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.421834+0800 Runner[12979:9399738] flutter: │ 17:23:35.421 (+0:00:01.652848)
2025-07-16 17:23:35.421857+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.421880+0800 Runner[12979:9399738] flutter: │ 🐛 用户名: test_user2
2025-07-16 17:23:35.422039+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.422281+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.422308+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:429:18)
2025-07-16 17:23:35.422327+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.422351+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.422372+0800 Runner[12979:9399738] flutter: │ 17:23:35.422 (+0:00:01.653532)
2025-07-16 17:23:35.422394+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.422413+0800 Runner[12979:9399738] flutter: │ 🐛 VIP状态: true
2025-07-16 17:23:35.422435+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.422570+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.422598+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:430:18)
2025-07-16 17:23:35.422617+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.422640+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.422662+0800 Runner[12979:9399738] flutter: │ 17:23:35.422 (+0:00:01.653833)
2025-07-16 17:23:35.422685+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.422704+0800 Runner[12979:9399738] flutter: │ 🐛 今日SWMT (原值): 0.0 (类型: double)
2025-07-16 17:23:35.422728+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.422704+0800 Runner[12979:9399738] flutter: │ 🐛 今日SWMT (原值): 0.0 (类型: double)
2025-07-16 17:23:35.422728+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.422850+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.422876+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:431:18)
2025-07-16 17:23:35.422895+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.422917+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.422937+0800 Runner[12979:9399738] flutter: │ 17:23:35.422 (+0:00:01.654116)
2025-07-16 17:23:35.422958+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.422985+0800 Runner[12979:9399738] flutter: │ 🐛 今日XP (原值): 0 (类型: int)
2025-07-16 17:23:35.423007+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.423119+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.423147+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:432:18)
2025-07-16 17:23:35.423171+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.423192+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.423212+0800 Runner[12979:9399738] flutter: │ 17:23:35.423 (+0:00:01.654385)
2025-07-16 17:23:35.423236+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.423212+0800 Runner[12979:9399738] flutter: │ 17:23:35.423 (+0:00:01.654385)
2025-07-16 17:23:35.423236+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:23:35.423212+0800 Runner[12979:9399738] flutter: │ 17:23:35.423 (+0:00:01.654385)
2025-07-16 17:23:35.423236+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.423257+0800 Runner[12979:9399738] flutter: │ 🐛 当前XP总量: 11586
2025-07-16 17:23:35.423279+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.423212+0800 Runner[12979:9399738] flutter: │ 17:23:35.423 (+0:00:01.654385)
2025-07-16 17:23:35.423236+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:35.423257+0800 Runner[12979:9399738] flutter: │ 🐛 当前XP总量: 11586
2025-07-16 17:23:35.423279+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:35.423463+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.423491+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:435:20)
2025-07-16 17:23:35.423513+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.423536+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.423561+0800 Runner[12979:9399738] flutter: │ 17:23:35.423 (+0:00:01.654728)
2025-07-16 17:23:35.423584+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.423603+0800 Runner[12979:9399738] flutter: │ 🐛 会员等级: 3
2025-07-16 17:23:35.423626+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.423760+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.423787+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:436:20)
2025-07-16 17:23:35.423809+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.423833+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.423855+0800 Runner[12979:9399738] flutter: │ 17:23:35.423 (+0:00:01.655028)
2025-07-16 17:23:35.423877+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.423897+0800 Runner[12979:9399738] flutter: │ 🐛 会员等级最小经验: 10000
2025-07-16 17:23:35.423918+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.424116+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.424145+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:437:20)
2025-07-16 17:23:35.424170+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.424191+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.424221+0800 Runner[12979:9399738] flutter: │ 17:23:35.424 (+0:00:01.655382)
2025-07-16 17:23:35.424244+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.424264+0800 Runner[12979:9399738] flutter: │ 🐛 会员等级最大经验: 99999
2025-07-16 17:23:35.424285+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.424634+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.424662+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:440:20)
2025-07-16 17:23:35.424684+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.424706+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.424727+0800 Runner[12979:9399738] flutter: │ 17:23:35.424 (+0:00:01.655893)
2025-07-16 17:23:35.424750+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.424771+0800 Runner[12979:9399738] flutter: │ 🐛 当前经验进度: 1.8%
2025-07-16 17:23:35.424792+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.424913+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.424945+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:447:20)
2025-07-16 17:23:35.424966+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.424988+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.425011+0800 Runner[12979:9399738] flutter: │ 17:23:35.424 (+0:00:01.656176)
2025-07-16 17:23:35.425032+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.425052+0800 Runner[12979:9399738] flutter: │ 🐛 每日任务完成情况: 0/4
2025-07-16 17:23:35.425073+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.425011+0800 Runner[12979:9399738] flutter: │ 17:23:35.424 (+0:00:01.656176)
2025-07-16 17:23:35.425032+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.425052+0800 Runner[12979:9399738] flutter: │ 🐛 每日任务完成情况: 0/4
2025-07-16 17:23:35.425073+0800 Runner[12979:9399738] flutter: └\342
2025-07-16 17:23:35.425011+0800 Runner[12979:9399738] flutter: │ 17:23:35.424 (+0:00:01.656176)
2025-07-16 17:23:35.425032+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.425052+0800 Runner[12979:9399738] flutter: │ 🐛 每日任务完成情况: 0/4
2025-07-16 17:23:35.425073+0800 Runner[12979:9399738] flutter: └\342\342\224
2025-07-16 17:23:35.425222+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.425011+0800 Runner[12979:9399738] flutter: │ 17:23:35.424 (+0:00:01.656176)
2025-07-16 17:23:35.425032+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.425052+0800 Runner[12979:9399738] flutter: │ 🐛 每日任务完成情况: 0/4
2025-07-16 17:23:35.425073+0800 Runner[12979:9399738] flutter: └\342\342\224
2025-07-16 17:23:35.425222+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:35.425249+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:450:20)
2025-07-16 17:23:35.425271+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.425295+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.425316+0800 Runner[12979:9399738] flutter: │ 17:23:35.425 (+0:00:01.656485)
2025-07-16 17:23:35.425337+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.425359+0800 Runner[12979:9399738] flutter: │ 🐛 附加任务情况: 已完成 0/2, 已激活 0
2025-07-16 17:23:35.425381+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.425494+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.425520+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:453:18)
2025-07-16 17:23:35.425541+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.425566+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.425586+0800 Runner[12979:9399738] flutter: │ 17:23:35.425 (+0:00:01.656757)
2025-07-16 17:23:35.425609+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.425586+0800 Runner[12979:9399738] flutter: │ 17:23:35.425 (+0:00:01.656757)
2025-07-16 17:23:35.425609+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:23:35.425633+0800 Runner[12979:9399738] flutter: │ 🐛 ----- 转换后的Entity数据详情结束 -----
2025-07-16 17:23:35.425668+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.425807+0800 Runner[12979:9399738] flutter: 💡 HomeDashboardData operation: Succeeded.
2025-07-16 17:23:35.426047+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌
2025-07-16 17:23:35.425807+0800 Runner[12979:9399738] flutter: 💡 HomeDashboardData operation: Succeeded.
2025-07-16 17:23:35.426047+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:35.426095+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._preloadHomePageData (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:306:17)<…>
2025-07-16 17:23:35.426126+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:35.426152+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.426176+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 首页数据预加载完成 - userProfile: ✅, todaySummary: ✅<…>
2025-07-16 17:23:35.426258+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:23:35.426491+0800 Runner[12979:9399738] flutter: ┌\342\224<…>
2025-07-16 17:23:35.426491+0800 Runner[12979:9399738] flutter: ┌\342\224
2025-07-16 17:23:35.426558+0800 Runner[12979:9399738] flutter: │ #0   AuthProvider._preloadHomePageData (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:307:17)
2025-07-16 17:23:35.426664+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.426807+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.426965+0800 Runner[12979:9399738] flutter: │ 🐛    预加载用户: test_user2
2025-07-16 17:23:35.427083+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.427302+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.427335+0800 Runner[12979:9399738] flutter: │ #0   AuthProvider._preloadHomePageData (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:308:17)
2025-07-16 17:23:35.427428+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.427518+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.428062+0800 Runner[12979:9399738] flutter: │ 🐛    预加载今日SWMT: 0.0
2025-07-16 17:23:35.428087+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.428205+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:35.428259+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:279:15)<…>
2025-07-16 17:23:35.428577+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:35.428606+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.428632+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 阶段2.3: 业务数据同步完成<…>
2025-07-16 17:23:35.428657+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:23:35.429197+0800 Runner[12979:9399738] flutter: ┌\342\224<…>
2025-07-16 17:23:35.429197+0800 Runner[12979:9399738] flutter: ┌\342\224
2025-07-16 17:23:35.429251+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:183:17)
2025-07-16 17:23:35.429275+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:35.429301+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.429326+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect check: Current location: /, Auth status: AuthStatus.authenticated
2025-07-16 17:23:35.429350+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.429497+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.429526+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:196:19)
2025-07-16 17:23:35.429548+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:35.429696+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.429787+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect: At splash screen, letting SplashScreen handle navigation.
2025-07-16 17:23:35.429954+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.430486+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:35.430535+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:281:15)<…>
2025-07-16 17:23:35.430563+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:35.430588+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.431052+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: UI状态更新通知已发送<…>
2025-07-16 17:23:35.431086+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:35.431428+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.431464+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:183:17)
2025-07-16 17:23:35.431487+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:35.431512+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.431809+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect check: Current location: /, Auth status: AuthStatus.authenticated
2025-07-16 17:23:35.431845+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.431997+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.432030+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:196:19)
2025-07-16 17:23:35.432086+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:35.432111+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.432134+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect: At splash screen, letting SplashScreen handle navigation.
2025-07-16 17:23:35.432207+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.432563+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:35.432600+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:143:15)<…>
2025-07-16 17:23:35.432624+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:35.432649+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.432831+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 阶段2 Loading数据准备完成<…>
2025-07-16 17:23:35.432857+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:35.432831+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 阶段2 Loading数据准备完成<…>
2025-07-16 17:23:35.432857+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342<…>
2025-07-16 17:23:35.433706+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:35.432831+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 阶段2 Loading数据准备完成<…>
2025-07-16 17:23:35.432857+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342<…>
2025-07-16 17:23:35.433706+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:35.432831+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 阶段2 Loading数据准备完成<…>
2025-07-16 17:23:35.432857+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342<…>
2025-07-16 17:23:35.433706+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-16 17:23:35.433786+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._ensureMinimumLoadingDuration (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:356:15)<…>
2025-07-16 17:23:35.433818+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:146:13)<…>
2025-07-16 17:23:35.433845+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.433868+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ⏱️ 等待最小加载时长: 821ms<…>
2025-07-16 17:23:35.433893+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:35.436882+0800 Runner[12979:9399738] flutter: \^[[38;5;208m┌<…>
2025-07-16 17:23:35.436953+0800 Runner[12979:9399738] flutter: \^[[38;5;208m│ #0   CacheManager._safeWrite (package:sweatmint/core/data/cache_manager.dart:30:16)<…>
2025-07-16 17:23:35.436983+0800 Runner[12979:9399738] flutter: \^[[38;5;208m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:35.437010+0800 Runner[12979:9399738] flutter: \^[[38;5;208m├<…>
2025-07-16 17:23:35.437035+0800 Runner[12979:9399738] flutter: \^[[38;5;208m│ 17:23:35.436 (+0:00:01.668006)<…>
2025-07-16 17:23:35.437110+0800 Runner[12979:9399738] flutter: \^[[38;5;208m├<…>
2025-07-16 17:23:35.437138+0800 Runner[12979:9399738] flutter: \^[[38;5;208m│ ⚠️ CacheManager: Direct write failed for key "cache_dashboard_home", trying delete-then-write<…>
2025-07-16 17:23:35.437164+0800 Runner[12979:9399738] flutter: \^[[38;5;208m└<…>
2025-07-16 17:23:35.438683+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:35.438683+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:35.438750+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   MyApp.build.<anonymous closure> (package:sweatmint/main.dart:308:17)<…>
2025-07-16 17:23:35.438784+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   Consumer.buildWithChild (package:provider/src/consumer.dart:179:19)<…>
2025-07-16 17:23:35.438825+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.438852+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 阶段1：显示基础MaterialApp界面 - 认证状态: AuthStatus.authenticated<…>
2025-07-16 17:23:35.438880+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:35.445416+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.445546+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:183:17)
2025-07-16 17:23:35.445576+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:35.445603+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.445630+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect check: Current location: /, Auth status: AuthStatus.authenticated
2025-07-16 17:23:35.445658+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.446728+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.446759+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:196:19)
2025-07-16 17:23:35.446784+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:35.446824+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.446850+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect: At splash screen, letting SplashScreen handle navigation.
2025-07-16 17:23:35.446874+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.450223+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.450302+0800 Runner[12979:9399738] flutter: │ #0   _SplashScreenState.build.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:116:17)
2025-07-16 17:23:35.450331+0800 Runner[12979:9399738] flutter: │ #1   _Selector0State.buildWithChild.<anonymous closure> (package:provider/src/selector.dart:86:45)
2025-07-16 17:23:35.450360+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.450384+0800 Runner[12979:9399738] flutter: │ 
2025-07-16 17:23:35.450384+0800 Runner[12979:9399738] flutter: │ \360\237\220🐛 🔄 SplashScreen: Provider状态更新 - AuthStatus.authenticated, Stage: LoginStage.stage3Complete, 业务逻辑完成: false
2025-07-16 17:23:35.450414+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.479036+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.479247+0800 Runner[12979:9399738] flutter: │ #0   CacheManager._safeWrite (package:sweatmint/core/data/cache_manager.dart:33:16)
2025-07-16 17:23:35.479437+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.479521+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.480192+0800 Runner[12979:9399738] flutter: │ 17:23:35.478 (+0:00:01.709727)
2025-07-16 17:23:35.480229+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.480257+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager: Successfully wrote key "cache_dashboard_home" to secure storage (delete-then-write)
2025-07-16 17:23:35.480298+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.481185+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.481226+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.set (package:sweatmint/core/data/cache_manager.dart:164:14)
2025-07-16 17:23:35.481246+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.481269+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.481761+0800 Runner[12979:9399738] flutter: │ 17:23:35.480 (+0:00:01.712236)
2025-07-16 17:23:35.481787+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.481809+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.set: 缓存设置成功 dashboard_home (过期时间: 2025-07-16 17:28:35.327863)
2025-07-16 17:23:35.481831+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.482063+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.482127+0800 Runner[12979:9399738] flutter: │ #0   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:76:16)
2025-07-16 17:23:35.482172+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.482274+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.482446+0800 Runner[12979:9399738] flutter: │ 17:23:35.481 (+0:00:01.713225)
2025-07-16 17:23:35.482552+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.482653+0800 Runner[12979:9399738] flutter: │ 🐛 DashboardRemoteDataSource: 数据已缓存
2025-07-16 17:23:35.482755+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.483968+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:35.484168+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:82:14)<…>
2025-07-16 17:23:35.484254+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:35.484339+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.484533+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.483 (+0:00:01.714805)<…>
2025-07-16 17:23:35.484576+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.484533+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.483 (+0:00:01.714805)<…>
2025-07-16 17:23:35.484576+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:35.484655+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 DashboardRemoteDataSource: 首页聚合数据获取成功 (网络请求)<…>
2025-07-16 17:23:35.484886+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:35.485892+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.485955+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.set (package:sweatmint/core/data/cache_manager.dart:146:14)
2025-07-16 17:23:35.485977+0800 Runner[12979:9399738] flutter: │ #1   CacheManager.warmup (package:sweatmint/core/data/cache_manager.dart:440:21)
2025-07-16 17:23:35.486000+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.486021+0800 Runner[12979:9399738] flutter: │ 17:23:35.485 (+0:00:01.716878)
2025-07-16 17:23:35.486053+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.486080+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.set: 设置缓存 dashboard_home
2025-07-16 17:23:35.486191+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.486080+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.set: 设置缓存 dashboard_home
2025-07-16 17:23:35.486191+0800 Runner[12979:9399738] flutter: └\342
2025-07-16 17:23:35.506541+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.507449+0800 Runner[12979:9399738] flutter: │ #0   CacheManager._safeWrite (package:sweatmint/core/data/cache_manager.dart:26:14)
2025-07-16 17:23:35.507515+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.507550+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.507577+0800 Runner[12979:9399738] flutter: │ 17:23:35.506 (+0:00:01.737375)
2025-07-16 17:23:35.507635+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.507699+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager: Successfully wrote key "cache_dashboard_home" to secure storage (direct write)
2025-07-16 17:23:35.507729+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.508283+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.508397+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.set (package:sweatmint/core/data/cache_manager.dart:164:14)
2025-07-16 17:23:35.508473+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.508553+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.508584+0800 Runner[12979:9399738] flutter: │ 17:23:35.507 (+0:00:01.739206)
2025-07-16 17:23:35.508609+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.508632+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.set: 缓存设置成功 dashboard_home (过期时间: 2025-07-16 17:28:35.486268)
2025-07-16 17:23:35.508793+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.509038+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.509069+0800 Runner[12979:9399738] flutter: │ #0   CacheManager.warmup (package:sweatmint/core/data/cache_manager.dart:441:22)
2025-07-16 17:23:35.509093+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.509117+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.509146+0800 Runner[12979:9399738] flutter: │ 17:23:35.508 (+0:00:01.740206)
2025-07-16 17:23:35.509169+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.509253+0800 Runner[12979:9399738] flutter: │ 🐛 CacheManager.warmup: 预热成功 dashboard_home
2025-07-16 17:23:35.509279+0800 Runner[12979:9399738] flutter: └

2025-07-16 17:23:35.509512+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:35.509557+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   CacheManager.warmup (package:sweatmint/core/data/cache_manager.dart:451:14)<…>
2025-07-16 17:23:35.509632+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:35.509764+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.509918+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:35.509 (+0:00:01.740750)<…>
2025-07-16 17:23:35.510036+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.510143+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 CacheManager.warmup: 预热完成<…>
2025-07-16 17:23:35.510321+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:35.510669+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.510718+0800 Runner[12979:9399738] flutter: │ #0   HomeProvider._warmupCriticalData (package:sweatmint/features/home/<USER>/providers/home_provider.dart:143:14)
2025-07-16 17:23:35.510748+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:35.510771+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.510810+0800 Runner[12979:9399738] flutter: │ 17:23:35.510 (+0:00:01.741827)
2025-07-16 17:23:35.510902+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.511032+0800 Runner[12979:9399738] flutter: │ 🐛 HomeProvider: 关键数据缓存预热完成
2025-07-16 17:23:35.511176+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:35.925727+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:35.926019+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _SplashScreenState.initState.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:40:17)<…>
2025-07-16 17:23:35.926195+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   Timer._createTimer.<anonymous closure> (dart:async-patch/timer_patch.dart:18:15)<…>
2025-07-16 17:23:35.926381+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:35.926588+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ⏰ SplashScreen: 最小显示时间已到<…>
2025-07-16 17:23:35.926748+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:35.929169+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:35.929401+0800 Runner[12979:9399738] flutter: │ #0   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:63:15)
2025-07-16 17:23:35.929627+0800 Runner[12979:9399738] flutter: │ #1   _SplashScreenState.initState.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:42:9)
2025-07-16 17:23:35.929924+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:35.930332+0800 Runner[12979:9399738] flutter: │ 🐛 ⏳ SplashScreen: 导航条件检查 - 最小时间:true, 业务逻辑完成:false
2025-07-16 17:23:35.930510+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.258229+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.258571+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider._ensureMinimumLoadingDuration (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:359:13)<…>
2025-07-16 17:23:36.258743+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:36.258917+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.259091+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 最小加载时长已满足<…>
2025-07-16 17:23:36.259250+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.260393+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.260576+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:150:15)<…>
2025-07-16 17:23:36.260737+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:36.260898+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.263330+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 完整业务逻辑初始化完成，允许SplashScreen跳转<…>
2025-07-16 17:23:36.263461+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.264874+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.265095+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:183:17)
2025-07-16 17:23:36.265205+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:36.265316+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.265420+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect check: Current location: /, Auth status: AuthStatus.authenticated
2025-07-16 17:23:36.265606+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.266210+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.266334+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:196:19)
2025-07-16 17:23:36.267147+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:36.267284+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.267401+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect: At splash screen, letting SplashScreen handle navigation.
2025-07-16 17:23:36.267510+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.269168+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.269318+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:171:15)<…>
2025-07-16 17:23:36.269425+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:36.269565+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.270796+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔧 AuthProvider: 业务逻辑初始化过程结束<…>
2025-07-16 17:23:36.270975+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<<\342\200…>
2025-07-16 17:23:36.271713+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<\342\200…>
2025-07-16 17:23:36.271713+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.273042+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _SplashScreenState._startStage2Loading.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:94:15)<…>
2025-07-16 17:23:36.273161+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   Future._propagateToListeners.handleValueCallback (dart:async/future_impl.dart:951:45)<…>
2025-07-16 17:23:36.273281+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.274515+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ SplashScreen: 阶段2 Loading数据准备完成<…>
2025-07-16 17:23:36.274678+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.277546+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.277749+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   MyApp.build.<anonymous closure> (package:sweatmint/main.dart:308:17)<…>
2025-07-16 17:23:36.277874+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   Consumer.buildWithChild (package:provider/src/consumer.dart:179:19)<…>
2025-07-16 17:23:36.277997+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.278356+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 阶段1：显示基础MaterialApp界面 - 认证状态: AuthStatus.authenticated<…>
2025-07-16 17:23:36.278491+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.297559+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.297662+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:183:17)
2025-07-16 17:23:36.297695+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:36.297723+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.297750+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect check: Current location: /, Auth status: AuthStatus.authenticated
2025-07-16 17:23:36.297775+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.298703+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.298735+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:196:19)
2025-07-16 17:23:36.298761+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:36.298801+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.298824+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect: At splash screen, letting SplashScreen handle navigation.
2025-07-16 17:23:36.298848+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.301862+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.301924+0800 Runner[12979:9399738] flutter: │ #0   _SplashScreenState.build.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:116:17)
2025-07-16 17:23:36.301949+0800 Runner[12979:9399738] flutter: │ #1   _Selector0State.buildWithChild.<anonymous closure> (package:provider/src/selector.dart:86:45)
2025-07-16 17:23:36.301972+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.301996+0800 Runner[12979:9399738] flutter: │ 🐛 🔄 SplashScreen: Provider状态更新 - AuthStatus.authenticated, Stage: LoginStage.stage3Complete, 业务逻辑完成: true
2025-07-16 17:23:36.302017+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:36.302171+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224
2025-07-16 17:23:36.302171+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:36.302200+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _SplashScreenState.build.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:125:19)<…>
2025-07-16 17:23:36.302746+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _Selector0State.buildWithChild.<anonymous closure> (package:provider/src/selector.dart:86:45)<…>
2025-07-16 17:23:36.302775+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.302809+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ SplashScreen: 真正的业务逻辑初始化完成 - AuthStatus.authenticated<…>
2025-07-16 17:23:36.302831+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.310544+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.310654+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:57:15)<…>
2025-07-16 17:23:36.310709+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _SplashScreenState.build.<anonymous closure>.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:129:13)<…>
2025-07-16 17:23:36.310750+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.310789+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ SplashScreen: 满足导航条件，准备跳转 - 业务逻辑已完成<…>
2025-07-16 17:23:36.310928+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.312173+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.312230+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _SplashScreenState._performNavigation (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:75:15)<…>
2025-07-16 17:23:36.312277+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:61:7)<…>
2025-07-16 17:23:36.312316+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.312389+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🏠 SplashScreen: 用户已登录，跳转到首页<…>
2025-07-16 17:23:36.312428+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.314949+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.315008+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:183:17)
2025-07-16 17:23:36.315034+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:36.315061+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.315085+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect check: Current location: /home, Auth status: AuthStatus.authenticated
2025-07-16 17:23:36.315108+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.315396+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.315426+0800 Runner[12979:9399738] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:214:19)
2025-07-16 17:23:36.315454+0800 Runner[12979:9399738] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-16 17:23:36.315478+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.315536+0800 Runner[12979:9399738] flutter: │ 🐛 Redirect: Authenticated user accessing /home. Allowed.
2025-07-16 17:23:36.315560+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.316400+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌
2025-07-16 17:23:36.316400+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342
2025-07-16 17:23:36.316400+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-16 17:23:36.316464+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _SplashScreenState._performNavigation (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:79:15)<…>
2025-07-16 17:23:36.316508+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:61:7)<…>
2025-07-16 17:23:36.316548+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.316768+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📍 阶段4权限弹窗判断将在MainLayoutScreen中执行<…>
2025-07-16 17:23:36.316813+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.346881+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.346991+0800 Runner[12979:9399738] flutter: │ #0   _MainLayoutScreenState.build (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:151:13)
2025-07-16 17:23:36.347023+0800 Runner[12979:9399738] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-16 17:23:36.347050+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.347074+0800 Runner[12979:9399738] flutter: │ 🐛 Building MainLayoutScreen with selected index: 0
2025-07-16 17:23:36.347098+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.438508+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.438639+0800 Runner[12979:9399738] flutter: │ #0   AppBottomNavigationBar.build (package:sweatmint/features/main_layout/presentation/widgets/app_bottom_navigation_bar.dart:48:13)
2025-07-16 17:23:36.438680+0800 Runner[12979:9399738] flutter: │ #1   StatelessElement.build (package:flutter/src/widgets/framework.dart:5781:49)
2025-07-16 17:23:36.438708+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.438735+0800 Runner[12979:9399738] flutter: │ 🐛 Building AppBottomNavigationBar with index: 0
2025-07-16 17:23:36.438767+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.441149+0800 Runner[12979:9399738] flutter: \^[[38;5;244m┌<…>
2025-07-16 17:23:36.441200+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│ #0   AppBottomNavigationBar.build.<anonymous closure> (package:sweatmint/features/main_layout/presentation/widgets/app_bottom_navigation_bar.dart:74:21)<…>
2025-07-16 17:23:36.441229+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│ #1   new _GrowableList.generate (dart:core-patch/growable_array.dart:140:28)<…>
2025-07-16 17:23:36.441259+0800 Runner[12979:9399738] flutter: \^[[38;5;244m├<…>
2025-07-16 17:23:36.441288+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│  Item 0: isActive=true, path=assets/images/icons/navigation/ic_home_active.svg<…>
2025-07-16 17:23:36.441315+0800 Runner[12979:9399738] flutter: \^[[38;5;244m└<…>
2025-07-16 17:23:36.443235+0800 Runner[12979:9399738] flutter: \^[[38;5;244m┌<…>
2025-07-16 17:23:36.443278+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│ #0   AppBottomNavigationBar.build.<anonymous closure> (package:sweatmint/features/main_layout/presentation/widgets/app_bottom_navigation_bar.dart:74:21)<…>
2025-07-16 17:23:36.443315+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│ #1   new _GrowableList.generate (dart:core-patch/growable_array.dart:140:28)<…>
2025-07-16 17:23:36.443340+0800 Runner[12979:9399738] flutter: \^[[38;5;244m├<…>
2025-07-16 17:23:36.443491+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│  Item 1: isActive=false, path=assets/images/icons/navigation/ic_tasks_inactive.svg<…>
2025-07-16 17:23:36.443529+0800 Runner[12979:9399738] flutter: \^[[38;5;244m└<…>
2025-07-16 17:23:36.446540+0800 Runner[12979:9399738] flutter: \^[[38;5;244m┌<…>
2025-07-16 17:23:36.446540+0800 Runner[12979:9399738] flutter: \^[[38;5;244m┌\342<…>
2025-07-16 17:23:36.446540+0800 Runner[12979:9399738] flutter: \^[[38;5;244m┌\342\342\224<…>
2025-07-16 17:23:36.446619+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│ #0   AppBottomNavigationBar.build.<anonymous closure> (package:sweatmint/features/main_layout/presentation/widgets/app_bottom_navigation_bar.dart:74:21)<…>
2025-07-16 17:23:36.446649+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│ #1   new _GrowableList.generate (dart:core-patch/growable_array.dart:140:28)<…>
2025-07-16 17:23:36.446676+0800 Runner[12979:9399738] flutter: \^[[38;5;244m├<…>
2025-07-16 17:23:36.447752+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│  Item 2: isActive=false, path=assets/images/icons/navigation/ic_rewards_inactive.svg<…>
2025-07-16 17:23:36.447783+0800 Runner[12979:9399738] flutter: \^[[38;5;244m└<…>
2025-07-16 17:23:36.449782+0800 Runner[12979:9399738] flutter: \^[[38;5;244m┌<…>
2025-07-16 17:23:36.449839+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│ #0   AppBottomNavigationBar.build.<anonymous closure> (package:sweatmint/features/main_layout/presentation/widgets/app_bottom_navigation_bar.dart:74:21)<…>
2025-07-16 17:23:36.449864+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│ #1   new _GrowableList.generate (dart:core-patch/growable_array.dart:140:28)<…>
2025-07-16 17:23:36.449890+0800 Runner[12979:9399738] flutter: \^[[38;5;244m├<…>
2025-07-16 17:23:36.450203+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│  Item 3: isActive=false, path=assets/images/icons/navigation/ic_social_inactive.svg<…>
2025-07-16 17:23:36.450234+0800 Runner[12979:9399738] flutter: \^[[38;5;244m└<…>
2025-07-16 17:23:36.451988+0800 Runner[12979:9399738] flutter: \^[[38;5;244m┌<…>
2025-07-16 17:23:36.452028+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│ #0   AppBottomNavigationBar.build.<anonymous closure> (package:sweatmint/features/main_layout/presentation/widgets/app_bottom_navigation_bar.dart:74:21)<…>
2025-07-16 17:23:36.452054+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│ #1   new _GrowableList.generate (dart:core-patch/growable_array.dart:140:28)<…>
2025-07-16 17:23:36.452079+0800 Runner[12979:9399738] flutter: \^[[38;5;244m├<…>
2025-07-16 17:23:36.452189+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│  Item 4: isActive=false, path=assets/images/icons/navigation/ic_profile_inactive.svg<…>
2025-07-16 17:23:36.452222+0800 Runner[12979:9399738] flutter: \^[[38;5;244m└<…>
2025-07-16 17:23:36.452189+0800 Runner[12979:9399738] flutter: \^[[38;5;244m│  Item 4: isActive=false, path=assets/images/icons/navigation/ic_profile_inactive.svg<…>
2025-07-16 17:23:36.452222+0800 Runner[12979:9399738] flutter: \^[[38;5;244m└\342<…>
2025-07-16 17:23:36.486739+0800 Runner[12979:9399738] flutter: 🔍 [UserAvatarWidget] 原始头像URL: avatar_14.png
2025-07-16 17:23:36.487177+0800 Runner[12979:9399738] flutter: 🔗 [UserAvatarWidget] 相对路径转换为完整URL: avatar_14.png -> http://192.168.100.232:8000/media/avatars/avatar_14.png
2025-07-16 17:23:36.557987+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.558120+0800 Runner[12979:9399738] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:283:12)
2025-07-16 17:23:36.558151+0800 Runner[12979:9399738] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-16 17:23:36.558183+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.558229+0800 Runner[12979:9399738] flutter: │ 17:23:36.557 (+0:00:02.788411)
2025-07-16 17:23:36.558262+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.558290+0800 Runner[12979:9399738] flutter: │ 🐛 EarnedCard 构建参数: swmt=0.0, exp=11586, levelMinExp=10000, levelMaxExp=99999, todayXpIncrease=0
2025-07-16 17:23:36.558320+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.562284+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.562340+0800 Runner[12979:9399738] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:304:12)
2025-07-16 17:23:36.562368+0800 Runner[12979:9399738] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-16 17:23:36.562394+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.562478+0800 Runner[12979:9399738] flutter: │ 17:23:36.561 (+0:00:02.792895)
2025-07-16 17:23:36.562504+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.562527+0800 Runner[12979:9399738] flutter: │ 🐛 🎯 EarnedCard进度条详细计算:
2025-07-16 17:23:36.562552+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.563432+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.563495+0800 Runner[12979:9399738] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:305:12)
2025-07-16 17:23:36.563523+0800 Runner[12979:9399738] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-16 17:23:36.563550+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.563664+0800 Runner[12979:9399738] flutter: │ 17:23:36.562 (+0:00:02.794044)
2025-07-16 17:23:36.563696+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.563723+0800 Runner[12979:9399738] flutter: │ 🐛   - 用户总经验: 11586
2025-07-16 17:23:36.563748+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.564470+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.564470+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:36.564470+0800 Runner[12979:9399738] flutter: ┌\342\342\224
2025-07-16 17:23:36.564510+0800 Runner[12979:9399738] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:306:12)
2025-07-16 17:23:36.564535+0800 Runner[12979:9399738] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-16 17:23:36.564559+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.564696+0800 Runner[12979:9399738] flutter: │ 17:23:36.563 (+0:00:02.795134)
2025-07-16 17:23:36.564727+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.564750+0800 Runner[12979:9399738] flutter: │ 🐛   - 当前等级范围: 10000 - 99999
2025-07-16 17:23:36.564774+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.564696+0800 Runner[12979:9399738] flutter: │ 17:23:36.563 (+0:00:02.795134)
2025-07-16 17:23:36.564727+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.564750+0800 Runner[12979:9399738] flutter: │ 🐛   - 当前等级范围: 10000 - 99999
2025-07-16 17:23:36.564774+0800 Runner[12979:9399738] flutter: └\342
2025-07-16 17:23:36.565486+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.564696+0800 Runner[12979:9399738] flutter: │ 17:23:36.563 (+0:00:02.795134)
2025-07-16 17:23:36.564727+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.564750+0800 Runner[12979:9399738] flutter: │ 🐛   - 当前等级范围: 10000 - 99999
2025-07-16 17:23:36.564774+0800 Runner[12979:9399738] flutter: └\342
2025-07-16 17:23:36.565486+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:36.565534+0800 Runner[12979:9399738] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:307:12)
2025-07-16 17:23:36.565559+0800 Runner[12979:9399738] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-16 17:23:36.565586+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.565610+0800 Runner[12979:9399738] flutter: │ 17:23:36.564 (+0:00:02.796166)
2025-07-16 17:23:36.565635+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.565750+0800 Runner[12979:9399738] flutter: │ 🐛   - 等级经验范围: 89999.0
2025-07-16 17:23:36.565780+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.566471+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.566510+0800 Runner[12979:9399738] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:308:12)
2025-07-16 17:23:36.566536+0800 Runner[12979:9399738] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-16 17:23:36.566568+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.566590+0800 Runner[12979:9399738] flutter: │ 17:23:36.565 (+0:00:02.797158)
2025-07-16 17:23:36.566616+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.566647+0800 Runner[12979:9399738] flutter: │ 🐛   - 在当前等级中的经验: 1586.0
2025-07-16 17:23:36.566671+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.567370+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.567370+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:36.567370+0800 Runner[12979:9399738] flutter: ┌\342\342\224
2025-07-16 17:23:36.567404+0800 Runner[12979:9399738] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:309:12)
2025-07-16 17:23:36.567430+0800 Runner[12979:9399738] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-16 17:23:36.567455+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.567793+0800 Runner[12979:9399738] flutter: │ 17:23:36.566 (+0:00:02.798054)
2025-07-16 17:23:36.567824+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.567793+0800 Runner[12979:9399738] flutter: │ 17:23:36.566 (+0:00:02.798054)
2025-07-16 17:23:36.567824+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:23:36.567793+0800 Runner[12979:9399738] flutter: │ 17:23:36.566 (+0:00:02.798054)
2025-07-16 17:23:36.567824+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:36.567846+0800 Runner[12979:9399738] flutter: │ 🐛   - 进度百分比: 1.76%
2025-07-16 17:23:36.567869+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.567793+0800 Runner[12979:9399738] flutter: │ 17:23:36.566 (+0:00:02.798054)
2025-07-16 17:23:36.567824+0800 Runner[12979:9399738] flutter: ├\342\342\224
2025-07-16 17:23:36.567846+0800 Runner[12979:9399738] flutter: │ 🐛   - 进度百分比: 1.76%
2025-07-16 17:23:36.567869+0800 Runner[12979:9399738] flutter: └\342
2025-07-16 17:23:36.569123+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.569162+0800 Runner[12979:9399738] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:310:12)
2025-07-16 17:23:36.569187+0800 Runner[12979:9399738] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-16 17:23:36.569212+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.569244+0800 Runner[12979:9399738] flutter: │ 17:23:36.568 (+0:00:02.799808)
2025-07-16 17:23:36.569269+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.569295+0800 Runner[12979:9399738] flutter: │ 🐛   - 显示格式: 11,586 / 99,999
2025-07-16 17:23:36.569319+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.570009+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.570046+0800 Runner[12979:9399738] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:312:12)
2025-07-16 17:23:36.570073+0800 Runner[12979:9399738] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-16 17:23:36.570097+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.570119+0800 Runner[12979:9399738] flutter: │ 17:23:36.569 (+0:00:02.800700)
2025-07-16 17:23:36.570142+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.570119+0800 Runner[12979:9399738] flutter: │ 17:23:36.569 (+0:00:02.800700)
2025-07-16 17:23:36.570142+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:23:36.570272+0800 Runner[12979:9399738] flutter: │ 🐛 经验值显示修复: 总经验=11,586, 下级上限=99,999, 当前等级进度=1.8%
2025-07-16 17:23:36.570306+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.570119+0800 Runner[12979:9399738] flutter: │ 17:23:36.569 (+0:00:02.800700)
2025-07-16 17:23:36.570142+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:23:36.570272+0800 Runner[12979:9399738] flutter: │ 🐛 经验值显示修复: 总经验=11,586, 下级上限=99,999, 当前等级进度=1.8%
2025-07-16 17:23:36.570306+0800 Runner[12979:9399738] flutter: └\342
2025-07-16 17:23:36.576862+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.570119+0800 Runner[12979:9399738] flutter: │ 17:23:36.569 (+0:00:02.800700)
2025-07-16 17:23:36.570142+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:23:36.570272+0800 Runner[12979:9399738] flutter: │ 🐛 经验值显示修复: 总经验=11,586, 下级上限=99,999, 当前等级进度=1.8%
2025-07-16 17:23:36.570306+0800 Runner[12979:9399738] flutter: └\342
2025-07-16 17:23:36.576862+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:36.576939+0800 Runner[12979:9399738] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:316:12)
2025-07-16 17:23:36.576969+0800 Runner[12979:9399738] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-16 17:23:36.576999+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.577023+0800 Runner[12979:9399738] flutter: │ 17:23:36.576 (+0:00:02.807419)
2025-07-16 17:23:36.577050+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.577078+0800 Runner[12979:9399738] flutter: │ 🐛 XP增长显示: hasXpIncrease=false, todayXpIncrease=EarnedCard.todayXpIncrease, 进度=0.017622418026866964
2025-07-16 17:23:36.577102+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.619400+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.619546+0800 Runner[12979:9399738] flutter: │ #0   main.<anonymous closure>.<anonymous closure> (package:sweatmint/main.dart:234:27)
2025-07-16 17:23:36.619582+0800 Runner[12979:9399738] flutter: │ #1   new ListenableProxyProvider2.<anonymous closure> (package:provider/src/listenable_provider.dart:149:48)
2025-07-16 17:23:36.619611+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.619637+0800 Runner[12979:9399738] flutter: │ 🐛 🔄 HealthProvider: 更新服务依赖，保持现有状态
2025-07-16 17:23:36.619662+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.623946+0800 Runner[12979:9399738] flutter: 🎯 Overview进度条详细计算:
2025-07-16 17:23:36.624030+0800 Runner[12979:9399738] flutter:   - 用户总经验: 11586
2025-07-16 17:23:36.624095+0800 Runner[12979:9399738] flutter:   - 当前等级范围: 10000 - 99999
2025-07-16 17:23:36.624135+0800 Runner[12979:9399738] flutter:   - 等级经验范围: 89999.0
2025-07-16 17:23:36.624171+0800 Runner[12979:9399738] flutter:   - 在当前等级中的经验: 1586.0
2025-07-16 17:23:36.624208+0800 Runner[12979:9399738] flutter:   - 进度百分比: 1.76%
2025-07-16 17:23:36.624325+0800 Runner[12979:9399738] flutter:   - 显示格式: 11,586 / 99,999 EXP
2025-07-16 17:23:36.626711+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.626769+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:443:15)
2025-07-16 17:23:36.626796+0800 Runner[12979:9399738] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:229:28)
2025-07-16 17:23:36.626825+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.626883+0800 Runner[12979:9399738] flutter: │ 🐛 📊 Overview无健康数据，显示0值
2025-07-16 17:23:36.626909+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.627708+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.627743+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:444:15)
2025-07-16 17:23:36.627766+0800 Runner[12979:9399738] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:229:28)
2025-07-16 17:23:36.627790+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.628448+0800 Runner[12979:9399738] flutter: │ 🐛    权限状态 - 步数:false, 距离:false, 卡路里:false
2025-07-16 17:23:36.628473+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.628448+0800 Runner[12979:9399738] flutter: │ 🐛    权限状态 - 步数:false, 距离:false, 卡路里:false
2025-07-16 17:23:36.628473+0800 Runner[12979:9399738] flutter: └\342
2025-07-16 17:23:36.680513+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.680616+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _MainLayoutScreenState._initializeEventTriggeredSyncService (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:84:15)<…>
2025-07-16 17:23:36.680652+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState.initState.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:46:5)<…>
2025-07-16 17:23:36.680681+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:36.680709+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🚀 开始完整初始化EventTriggeredSyncService...<…>
2025-07-16 17:23:36.680738+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.681029+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.681029+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.681066+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService.initializeLifecycleListener (package:sweatmint/core/services/event_triggered_sync_service.dart:158:12)<…>
2025-07-16 17:23:36.681096+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._initializeEventTriggeredSyncService (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:89:33)<…>
2025-07-16 17:23:36.681125+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.681495+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:36.680 (+0:00:02.912244)<…>
2025-07-16 17:23:36.681526+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.681553+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🎯 EventTriggeredSyncService 已初始化并开始监听App生命周期。<…>
2025-07-16 17:23:36.681579+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:23:36.681696+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.681696+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.681727+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _MainLayoutScreenState._initializeEventTriggeredSyncService (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:90:15)<…>
2025-07-16 17:23:36.681755+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState.initState.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:46:5)<…>

2025-07-16 17:23:36.681781+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.682186+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ EventTriggeredSyncService生命周期监听已初始化<…>
2025-07-16 17:23:36.682222+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:23:36.685645+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.685725+0800 Runner[12979:9399738] flutter: │ #0   main.<anonymous closure>.<anonymous closure> (package:sweatmint/main.dart:254:27)
2025-07-16 17:23:36.685757+0800 Runner[12979:9399738] flutter: │ #1   new ListenableProxyProvider2.<anonymous closure> (package:provider/src/listenable_provider.dart:149:48)
2025-07-16 17:23:36.685785+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.685908+0800 Runner[12979:9399738] flutter: │ 🐛 🔄 TaskProvider: 更新服务依赖，保持现有状态
2025-07-16 17:23:36.685939+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.686832+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.686889+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService.initialize (package:sweatmint/core/services/event_triggered_sync_service.dart:176:12)<…>
2025-07-16 17:23:36.686920+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._initializeEventTriggeredSyncService (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:112:35)<…>
2025-07-16 17:23:36.686947+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.686971+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:36.686 (+0:00:02.918045)<…>
2025-07-16 17:23:36.687005+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:36.687029+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔧 EventTriggeredSyncService 开始初始化<…>
2025-07-16 17:23:36.687054+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:23:36.687029+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔧 EventTriggeredSyncService 开始初始化<…>
2025-07-16 17:23:36.687054+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342<…>
2025-07-16 17:23:36.687912+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.687029+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔧 EventTriggeredSyncService 开始初始化<…>
2025-07-16 17:23:36.687054+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342<…>
2025-07-16 17:23:36.687912+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-16 17:23:36.687029+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔧 EventTriggeredSyncService 开始初始化<…>
2025-07-16 17:23:36.687054+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342<…>
2025-07-16 17:23:36.687912+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-16 17:23:36.687948+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService.initialize (package:sweatmint/core/services/event_triggered_sync_service.dart:177:12)<…>
2025-07-16 17:23:36.687977+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._initializeEventTriggeredSyncService (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:112:35)<…>
2025-07-16 17:23:36.688002+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.688026+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:36.687 (+0:00:02.919168)<…>
2025-07-16 17:23:36.688050+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.688155+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📋 HomeProvider: 已注入<…>
2025-07-16 17:23:36.688185+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.688155+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📋 HomeProvider: 已注入<…>
2025-07-16 17:23:36.688185+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342<…>
2025-07-16 17:23:36.688155+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📋 HomeProvider: 已注入<…>
2025-07-16 17:23:36.688185+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\342\224<…>
2025-07-16 17:23:36.688328+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.688155+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📋 HomeProvider: 已注入<…>
2025-07-16 17:23:36.688185+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\342\224<…>
2025-07-16 17:23:36.688328+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.688365+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService.initialize (package:sweatmint/core/services/event_triggered_sync_service.dart:178:12)<…>
2025-07-16 17:23:36.688394+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._initializeEventTriggeredSyncService (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:112:35)<…>
2025-07-16 17:23:36.688422+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.688450+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:36.688 (+0:00:02.919592)<…>
2025-07-16 17:23:36.688478+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.688450+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:36.688 (+0:00:02.919592)<…>
2025-07-16 17:23:36.688478+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:36.688502+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📋 TaskProvider: 已注入<…>
2025-07-16 17:23:36.688526+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.688871+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.688871+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.688903+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService.initialize (package:sweatmint/core/services/event_triggered_sync_service.dart:179:12)<…>
2025-07-16 17:23:36.688930+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._initializeEventTriggeredSyncService (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:112:35)<…>
2025-07-16 17:23:36.688960+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.688983+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:36.688 (+0:00:02.920135)<…>
2025-07-16 17:23:36.689008+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.689147+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📋 HealthProvider: 已注入<…>
2025-07-16 17:23:36.689178+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.689685+0800 Runner[12979:9399738] flutter: ┌<…>
2025-07-16 17:23:36.689685+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:36.689720+0800 Runner[12979:9399738] flutter: │ #0   EventTriggeredSyncService._startQueueProcessor (package:sweatmint/core/services/event_triggered_sync_service.dart:367:12)
2025-07-16 17:23:36.689747+0800 Runner[12979:9399738] flutter: │ #1   EventTriggeredSyncService.initialize (package:sweatmint/core/services/event_triggered_sync_service.dart:182:5)
2025-07-16 17:23:36.689772+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.689796+0800 Runner[12979:9399738] flutter: │ 17:23:36.689 (+0:00:02.920942)
2025-07-16 17:23:36.689822+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.689847+0800 Runner[12979:9399738] flutter: │ 🐛 ⚙️ 同步队列处理器已启动
2025-07-16 17:23:36.689945+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.689847+0800 Runner[12979:9399738] flutter: │ 🐛 ⚙️ 同步队列处理器已启动
2025-07-16 17:23:36.689945+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:36.690323+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.690364+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService._startPeriodicSync (package:sweatmint/core/services/event_triggered_sync_service.dart:777:12)<…>
2025-07-16 17:23:36.690392+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   EventTriggeredSyncService.initialize (package:sweatmint/core/services/event_triggered_sync_service.dart:185:5)<…>
2025-07-16 17:23:36.690417+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.690443+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:36.690 (+0:00:02.921580)<…>
2025-07-16 17:23:36.690468+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.690494+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ⏰ 定时同步器已启动，每2分钟执行一次<…>
2025-07-16 17:23:36.690519+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.690697+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.690731+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService.initialize (package:sweatmint/core/services/event_triggered_sync_service.dart:188:12)<…>
2025-07-16 17:23:36.690759+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._initializeEventTriggeredSyncService (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:112:35)<…>
2025-07-16 17:23:36.690785+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.690808+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:36.690 (+0:00:02.921959)<…>
2025-07-16 17:23:36.690837+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.690864+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 事件触发式同步服务已初始化（包含定时健康数据同步）<…>
2025-07-16 17:23:36.690889+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.691147+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.691147+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.691181+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService.initialize (package:sweatmint/core/services/event_triggered_sync_service.dart:189:12)<…>
2025-07-16 17:23:36.691209+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._initializeEventTriggeredSyncService (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:112:35)<…>
2025-07-16 17:23:36.691234+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.691257+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:36.691 (+0:00:02.922408)<…>
2025-07-16 17:23:36.691286+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.691310+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ⏰ 定时同步器已启动，间隔：2分钟<…>
2025-07-16 17:23:36.691334+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.691429+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.691429+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.691461+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _MainLayoutScreenState._initializeEventTriggeredSyncService (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:118:17)<…>
2025-07-16 17:23:36.691492+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState.initState.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:46:5)<…>
2025-07-16 17:23:36.691524+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:36.691552+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ EventTriggeredSyncService核心初始化完成<…>
2025-07-16 17:23:36.691576+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.691674+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.691705+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _MainLayoutScreenState._initializeEventTriggeredSyncService (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:119:17)<…>
2025-07-16 17:23:36.691876+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState.initState.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:46:5)<…>
2025-07-16 17:23:36.691910+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.691935+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🎯 定时同步器已启动，每2分钟执行健康数据同步<…>
2025-07-16 17:23:36.691959+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.692181+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.692215+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _MainLayoutScreenState._initializeEventTriggeredSyncService (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:120:17)<…>
2025-07-16 17:23:36.692239+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState.initState.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:46:5)<…>
2025-07-16 17:23:36.692266+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.692693+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📱 App生命周<…>
2025-07-16 17:23:36.692693+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📱 App生命周\346\234期监听已启动，支持前后台切换处理<…>
2025-07-16 17:23:36.692734+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.692978+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.693018+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _MainLayoutScreenState._triggerStage4PermissionCheck (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:58:13)<…>
2025-07-16 17:23:36.693047+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState.initState.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:49:7)<…>
2025-07-16 17:23:36.693073+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.693096+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔍 MainLayoutScreen: 开始阶段4 - 权限弹窗判断<…>
2025-07-16 17:23:36.693153+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.695078+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.695129+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   ViewModelMixin.executeAsyncAction (package:sweatmint/core/mixins/view_model_mixin.dart:77:13)<…>
2025-07-16 17:23:36.695159+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   HealthProvider._initializeHealthService (package:sweatmint/features/home/<USER>/providers/health_provider.dart:139:13)<…>
2025-07-16 17:23:36.695189+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.695224+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 Async operation: Starting...<…>
2025-07-16 17:23:36.695253+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.696575+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.696616+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   new HealthPermissionProvider (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:25:13)<…>
2025-07-16 17:23:36.696645+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   main.<anonymous closure>.<anonymous closure> (package:sweatmint/main.dart:154:36)<…>
2025-07-16 17:23:36.696672+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.696698+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🏗️ HealthPermissionProvider 构造函数被调用<…>
2025-07-16 17:23:36.696774+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.697151+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.697185+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.updateService (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:92:13)<…>
2025-07-16 17:23:36.697212+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   main.<anonymous closure>.<anonymous closure> (package:sweatmint/main.dart:159:28)<…>
2025-07-16 17:23:36.697238+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.697261+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔄 HealthPermissionProvider: 更新健康服务实例<…>
2025-07-16 17:23:36.697294+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.697821+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.697821+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<<…>
2025-07-16 17:23:36.697821+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<\342…>
2025-07-16 17:23:36.697858+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthProvider._initializeHealthService.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/health_provider.dart:152:21)<…>
2025-07-16 17:23:36.697885+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   ViewModelMixin.executeAsyncAction (package:sweatmint/core/mixins/view_model_mixin.dart:80:36)<…>
2025-07-16 17:23:36.697910+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:36.697985+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔍 HealthProvider: 从HealthPermissionProvider获取权限状态 - 步\342\224<…>
2025-07-16 17:23:36.697985+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔍 HealthProvider: 从HealthPermissionProvider获取权限状态 - 步\346\225数:false, 距离:false, 卡路里:false<…>
2025-07-16 17:23:36.698017+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:23:36.697985+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔍 HealthProvider: 从HealthPermissionProvider获取权限状态 - 步\346\225数:false, 距离:false, 卡路里:false<…>
2025-07-16 17:23:36.698017+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342<…>
2025-07-16 17:23:36.698246+0800 Runner[12979:9399738] flutter: \^[[38;5;208m┌<…>
2025-07-16 17:23:36.698280+0800 Runner[12979:9399738] flutter: \^[[38;5;208m│ #0   ViewModelMixin.executeAsyncAction (package:sweatmint/core/mixins/view_model_mixin.dart:60:15)<…>
2025-07-16 17:23:36.698308+0800 Runner[12979:9399738] flutter: \^[[38;5;208m│ #1   HealthProvider.refreshHealthData (package:sweatmint/features/home/<USER>/providers/health_provider.dart:203:11)<…>
2025-07-16 17:23:36.698333+0800 Runner[12979:9399738] flutter: \^[[38;5;208m├<…>
2025-07-16 17:23:36.698357+0800 Runner[12979:9399738] flutter: \^[[38;5;208m│ ⚠️ Async operation: Already in progress. Ignoring new request.<…>
2025-07-16 17:23:36.698382+0800 Runner[12979:9399738] flutter: \^[[38;5;208m└\342\224<…>
2025-07-16 17:23:36.698875+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.698875+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.698910+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _OverviewCardState._getHealthData (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:113:15)<…>
2025-07-16 17:23:36.698939+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _OverviewCardState.initState.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:63:7)<…>
2025-07-16 17:23:36.698968+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:36.698992+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ Overview: AuthProvider业务逻辑已完成，直接获取健康数据<…>
2025-07-16 17:23:36.699016+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.699702+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.699742+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _OverviewCardState._performHealthDataRequest (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:154:13)<…>
2025-07-16 17:23:36.699769+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _OverviewCardState._getHealthData (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:114:7)<…>
2025-07-16 17:23:36.699794+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.699900+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🌐 Overview: 调用health/status API获取最新健康数据<…>
2025-07-16 17:23:36.699932+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:23:36.700497+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.700497+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.700536+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthServiceImpl.getHealthStatus (package:sweatmint/core/services/health_service_impl.dart:373:14)<…>
2025-07-16 17:23:36.700565+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _OverviewCardState._performHealthDataRequest (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:156:44)<…>
2025-07-16 17:23:36.700591+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.700614+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:36.700 (+0:00:02.931712)<…>
2025-07-16 17:23:36.700639+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.700662+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🌐 HealthServiceImpl: 从后端获取当天累计运动增量...<…>
2025-07-16 17:23:36.700830+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.701174+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.701207+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthServiceImpl.checkDetailedPermissions (package:sweatmint/core/services/health_service_impl.dart:169:14)<…>
2025-07-16 17:23:36.701234+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   HealthServiceImpl.getHealthStatus (package:sweatmint/core/services/health_service_impl.dart:376:33)<…>
2025-07-16 17:23:36.701260+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.701284+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:36.701 (+0:00:02.932422)<…>
2025-07-16 17:23:36.701315+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.701340+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔍 HealthServiceImpl: 检查详细权限状态（v10.0最终版）...<…>
2025-07-16 17:23:36.701367+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:23:36.701876+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.701876+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.701912+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthKitManager.checkRealPermissions (package:sweatmint/core/health/health_kit_manager.dart:52:15)<…>
2025-07-16 17:23:36.701939+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   HealthServiceImpl.checkDetailedPermissions (package:sweatmint/core/services/health_service_impl.dart:172:53)<…>
2025-07-16 17:23:36.701964+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.701987+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔍 HealthKitManager: 使用原生权限检查（v13.1优化版，移除数据查询验证）<…>
2025-07-16 17:23:36.702011+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.706941+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.706941+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:36.707021+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthProvider._initializeHealthService.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/health_provider.dart:173:17)<…>
2025-07-16 17:23:36.707052+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:36.707079+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.707118+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 健康服务初始化完成<…>
2025-07-16 17:23:36.707146+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.707184+0800 Runner[12979:9399738] flutter: 💡 null operation: Succeeded.

2025-07-16 17:23:36.707333+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.707365+0800 Runner[12979:9399738] flutter: │ #0   _HomeScreenState.initState.<anonymous closure> (package:sweatmint/features/home/<USER>/screens/home_screen.dart:41:17)
2025-07-16 17:23:36.707392+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:36.707416+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.707439+0800 Runner[12979:9399738] flutter: │ 🐛 ✅ HomeScreen: HealthProvider初始化完成
2025-07-16 17:23:36.707463+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.707577+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌
2025-07-16 17:23:36.707577+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342
2025-07-16 17:23:36.707577+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-16 17:23:36.707610+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _HomeScreenState.initState.<anonymous closure> (package:sweatmint/features/home/<USER>/screens/home_screen.dart:55:17)<…>
2025-07-16 17:23:36.707637+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:36.707662+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├
2025-07-16 17:23:36.707577+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-16 17:23:36.707610+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _HomeScreenState.initState.<anonymous closure> (package:sweatmint/features/home/<USER>/screens/home_screen.dart:55:17)<…>
2025-07-16 17:23:36.707637+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:36.707662+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:36.707684+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ HomeScreen: 检测到预加载数据，跳过重复加载<…>
2025-07-16 17:23:36.707709+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
✅ [v13.0] 开始基于数据查询的权限验证...
🔥 [v13.0规范修复] <…>
✅ [v13.0] 开始基于数据查询的权限验证...
🔥 [v13.0规范修复] \346\235权限验证时间范围: 7天数据查询
  startDate (7天前): 2025-07-09 09:23:36 +0000
  endDate (现在): 2025-07-16 09:23:36 +0000
🔍 [v13.0] 开始验证steps权限...
🚀 [v13.0规范] 执行steps的7天数据权限查询...
🔍 [v13.0] 开始验证distance权限...
🚀 [v13.0规范] 执行distance的7天数据权限查询...
🔍 [v13.0] 开始验证calories权限...
🚀 [v13.0规范] 执行calories的7天数据权限查询...
⚠️ [v13.0] calories查询出错: No data available for the specified predicate. (代码: 11)
⚠️ [v13.0规范] calories查询遇到系统错误，标记为未确定
⚠️ [v13.0] distance查询出错: No data available for the specified predicate. (代码: 11)
⚠️ [v13.0规范] distance查询遇到系统错误，标记为未确定
✅ [v13.0规范] steps权限验证成功！HKStatistics有效，7天数据总和: 314.0
✅ [v13.0规范修复] 基于7天数据查询的权限验证完成: ["calories": "notDetermined", "distance": "notDetermined", "steps": "authorized"]
2025-07-16 17:23:36.892387+0800 Runner[12979:9399738] flutter: 🎯 Overview进度条详细计算:
2025-07-16 17:23:36.892538+0800 Runner[12979:9399738] flutter:   - 用户总经验: 11586
2025-07-16 17:23:36.892582+0800 Runner[12979:9399738] flutter:   - 当前等级范围: 10000 - 99999
2025-07-16 17:23:36.892623+0800 Runner[12979:9399738] flutter:   - 等级经验范围: 89999.0
2025-07-16 17:23:36.892663+0800 Runner[12979:9399738] flutter:   - 在当前等级中的经验: 1586.0
2025-07-16 17:23:36.892700+0800 Runner[12979:9399738] flutter:   - 进度百分比: 1.76%
2025-07-16 17:23:36.892798+0800 Runner[12979:9399738] flutter:   - 显示格式: 11,586 / 99,999 EXP
2025-07-16 17:23:36.893185+0800 Runner[12979:9399738] flutter: ┌\342\224
2025-07-16 17:23:36.893224+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:443:15)
2025-07-16 17:23:36.893253+0800 Runner[12979:9399738] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:229:28)
2025-07-16 17:23:36.893279+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.893451+0800 Runner[12979:9399738] flutter: │ 🐛 📊 Overview无健康数据，显示0值
2025-07-16 17:23:36.893485+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.893665+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.893714+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:444:15)
2025-07-16 17:23:36.893746+0800 Runner[12979:9399738] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:229:28)
2025-07-16 17:23:36.893771+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.893793+0800 Runner[12979:9399738] flutter: │ 🐛    权限状态 - 步数:false, 距离:false, 卡路里:false
2025-07-16 17:23:36.893829+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.950743+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.950844+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthKitManager.checkRealPermissions (package:sweatmint/core/health/health_kit_manager.dart:57:15)<…>
2025-07-16 17:23:36.950876+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:36.950908+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.950940+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ HealthKitManager: 原生层权限检查结果 - {calories: notDetermined, distance: notDetermined, steps: authorized}<…>
2025-07-16 17:23:36.950970+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.951133+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.951168+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthServiceImpl.checkDetailedPermissions (package:sweatmint/core/services/health_service_impl.dart:174:14)<…>
2025-07-16 17:23:36.951215+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:36.951348+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.951456+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:36.951 (+0:00:03.182370)<…>
2025-07-16 17:23:36.951563+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.951756+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ HealthServiceImpl: 详细权限状态（v10.0最终版） - {calories: notDetermined, distance: notDetermined, steps: authorized}<…>
2025-07-16 17:23:36.951847+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.952078+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.952078+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:36.952111+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthServiceImpl.getHealthStatus (package:sweatmint/core/services/health_service_impl.dart:377:14)<…>
2025-07-16 17:23:36.952239+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:36.952324+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.952464+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:36.951 (+0:00:03.183333)<…>
2025-07-16 17:23:36.952587+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.952680+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔍 HealthServiceImpl: 当前权限状态 - {calories: notDetermined, distance: notDetermined, steps: authorized}<…>
2025-07-16 17:23:36.952793+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.953195+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:36.953226+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthServiceImpl.getHealthStatus (package:sweatmint/core/services/health_service_impl.dart:386:14)<…>
2025-07-16 17:23:36.953335+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:36.953480+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.953561+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:36.953 (+0:00:03.184417)<…>
2025-07-16 17:23:36.953634+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:36.953793+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📋 HealthServiceImpl: 传递权限参数给后端 - {steps: true, distance: false, calories: false}<…>
2025-07-16 17:23:36.953928+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:36.965564+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.965654+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:36.965683+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor.onRequest (package:dio/src/interceptors/log.dart:68:13)
2025-07-16 17:23:36.965710+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.965791+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: *** Request ***
2025-07-16 17:23:36.965821+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.968207+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.965791+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: *** Request ***
2025-07-16 17:23:36.965821+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.968207+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:36.968262+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:36.968290+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-07-16 17:23:36.968316+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.968345+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: uri: http://192.168.100.232:8000/api/app/v1/health/status/?permissions%5Bsteps%5D=true&permissions%5Bdistance%5D=false&permissions%5Bcalories%5D=false
2025-07-16 17:23:36.968372+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.968471+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.968500+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:36.968523+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor.onRequest (package:dio/src/interceptors/log.dart:94:13)
2025-07-16 17:23:36.968548+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.968569+0800 Runner[12979:9399738] flutter: │ 
2025-07-16 17:23:36.968569+0800 Runner[12979:9399738] flutter: │ \360\237\220🐛 ApiClient:
2025-07-16 17:23:36.968594+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.968569+0800 Runner[12979:9399738] flutter: │ \360\237\220🐛 ApiClient:
2025-07-16 17:23:36.968594+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:36.971853+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.968569+0800 Runner[12979:9399738] flutter: │ \360\237\220🐛 ApiClient:
2025-07-16 17:23:36.968594+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:36.971853+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:36.971924+0800 Runner[12979:9399738] flutter: │ #0   TokenManager.getAccessToken (package:sweatmint/core/services/token_manager.dart:130:12)

2025-07-16 17:23:36.971954+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:36.971983+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.972007+0800 Runner[12979:9399738] flutter: │ 17:23:36.971 (+0:00:03.203016)
2025-07-16 17:23:36.972031+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.972056+0800 Runner[12979:9399738] flutter: │ 🐛 TokenManager: Returning existing valid token.
2025-07-16 17:23:36.972082+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.972056+0800 Runner[12979:9399738] flutter: │ 🐛 TokenManager: Returning existing valid token.
2025-07-16 17:23:36.972082+0800 Runner[12979:9399738] flutter: └\342
2025-07-16 17:23:36.972056+0800 Runner[12979:9399738] flutter: │ 🐛 TokenManager: Returning existing valid token.
2025-07-16 17:23:36.972082+0800 Runner[12979:9399738] flutter: └\342\342\224
2025-07-16 17:23:36.972161+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.972056+0800 Runner[12979:9399738] flutter: │ 🐛 TokenManager: Returning existing valid token.
2025-07-16 17:23:36.972082+0800 Runner[12979:9399738] flutter: └\342\342\224
2025-07-16 17:23:36.972161+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:36.972187+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:80:23)
2025-07-16 17:23:36.972490+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:36.972519+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:36.972542+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: Using TokenManager.getAccessToken() for API request
2025-07-16 17:23:36.972566+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:36.972764+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:36.972805+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:85:23)
2025-07-16 17:23:36.972830+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:36.972854+0800 Runner[12979:9399738] flutter: ├\342\224
2025-07-16 17:23:36.972877+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: Added authorization header for /api/app/v1/health/status/
2025-07-16 17:23:36.972901+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.025323+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.025482+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:37.025518+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor.onResponse (package:dio/src/interceptors/log.dart:101:13)
2025-07-16 17:23:37.025548+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.025574+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: *** Response ***
2025-07-16 17:23:37.025605+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.026084+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.026414+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:37.026461+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-07-16 17:23:37.026493+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.026651+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient: uri: http://192.168.100.232:8000/api/app/v1/health/status/?permissions%5Bsteps%5D=true&permissions%5Bdistance%5D=false&permissions%5Bcalories%5D=false
2025-07-16 17:23:37.026882+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.027401+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.027478+0800 Runner[12979:9399738] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-16 17:23:37.027557+0800 Runner[12979:9399738] flutter: │ #1   LogInterceptor._printResponse (package:dio/src/interceptors/log.dart:136:13)
2025-07-16 17:23:37.027764+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.027885+0800 Runner[12979:9399738] flutter: │ 🐛 ApiClient:
2025-07-16 17:23:37.027955+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.029918+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:37.029982+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthServiceImpl.getHealthStatus (package:sweatmint/core/services/health_service_impl.dart:395:14)<…>
2025-07-16 17:23:37.030004+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:37.030031+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:37.030054+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:23:37.029 (+0:00:03.260806)<…>
2025-07-16 17:23:37.030077+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:37.030103+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ HealthServiceImpl: 从后端获取当天运动增量成功<…>
2025-07-16 17:23:37.030220+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:37.030414+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.030442+0800 Runner[12979:9399738] flutter: │ #0   HealthServiceImpl.getHealthStatus (package:sweatmint/core/services/health_service_impl.dart:396:14)
2025-07-16 17:23:37.030461+0800 Runner[12979:9399738] flutter: │ #1   <asynchronous suspension>
2025-07-16 17:23:37.030493+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.030816+0800 Runner[12979:9399738] flutter: │ 17:23:37.030 (+0:00:03.261660)
2025-07-16 17:23:37.030841+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.030867+0800 Runner[12979:9399738] flutter: │ 🐛 运动增量数据: {daily_increment: {steps: null, distance: null, calories: null}, permissions: {steps: false, distance: false, calories: false}, permission_details: {steps_status: not_authorized, distance_status: not_authorized, calories_status: not_authorized}, sync_count: 1, has_session: true, message: 当天运动增量获取成功（权限感知响应）}
2025-07-16 17:23:37.030889+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.031388+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:37.031426+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _OverviewCardState._performHealthDataRequest (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:173:17)<…>
2025-07-16 17:23:37.031454+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:37.031477+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:37.031426+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _OverviewCardState._performHealthDataRequest (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:173:17)<…>
2025-07-16 17:23:37.031454+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-16 17:23:37.031477+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:37.032865+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ Overview: health/status API返回数据成功<…>
2025-07-16 17:23:37.032947+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:37.036048+0800 Runner[12979:9399738] flutter: 🎯 Overview进度条详细计算:
2025-07-16 17:23:37.036241+0800 Runner[12979:9399738] flutter:   - 用户总经验: 11586
2025-07-16 17:23:37.036285+0800 Runner[12979:9399738] flutter:   - 当前等级范围: 10000 - 99999
2025-07-16 17:23:37.036324+0800 Runner[12979:9399738] flutter:   - 等级经验范围: 89999.0
2025-07-16 17:23:37.036354+0800 Runner[12979:9399738] flutter:   - 在当前等级中的经验: 1586.0
2025-07-16 17:23:37.036399+0800 Runner[12979:9399738] flutter:   - 进度百分比: 1.76%
2025-07-16 17:23:37.036599+0800 Runner[12979:9399738] flutter:   - 显示格式: 11,586 / 99,999 EXP
2025-07-16 17:23:37.037277+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.037344+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:387:15)
2025-07-16 17:23:37.037373+0800 Runner[12979:9399738] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:229:28)
2025-07-16 17:23:37.037398+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.037422+0800 Runner[12979:9399738] flutter: │ 🐛 🎯 Overview API返回的完整权限数据: {steps: false, distance: false, calories: false}
2025-07-16 17:23:37.037446+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.037747+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.037821+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:388:15)
2025-07-16 17:23:37.038009+0800 Runner[12979:9399738] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:229:28)
2025-07-16 17:23:37.038163+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.037747+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.037821+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:388:15)
2025-07-16 17:23:37.038009+0800 Runner[12979:9399738] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:229:28)
2025-07-16 17:23:37.038163+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:23:37.038590+0800 Runner[12979:9399738] flutter: │ 🐛 🎯 permissions类型: _Map<String, dynamic>
2025-07-16 17:23:37.038616+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.039950+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.040012+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:390:17)
2025-07-16 17:23:37.040036+0800 Runner[12979:9399738] flutter: │ #1   _LinkedHashMapMixin.forEach (dart:_compact_hash:764:13)
2025-07-16 17:23:37.040062+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.040235+0800 Runner[12979:9399738] flutter: │ 🐛 🎯   权限 steps: false (类型: bool)
2025-07-16 17:23:37.040263+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.040458+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.040504+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:390:17)
2025-07-16 17:23:37.040631+0800 Runner[12979:9399738] flutter: │ #1   _LinkedHashMapMixin.forEach (dart:_compact_hash:764:13)
2025-07-16 17:23:37.040800+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.040956+0800 Runner[12979:9399738] flutter: │ 🐛 🎯   权限 distance: false (类型: bool)
2025-07-16 17:23:37.041069+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.041451+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.041488+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:390:17)
2025-07-16 17:23:37.041554+0800 Runner[12979:9399738] flutter: │ #1   _LinkedHashMapMixin.forEach (dart:_compact_hash:764:13)
2025-07-16 17:23:37.041673+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.041787+0800 Runner[12979:9399738] flutter: │ 🐛 🎯   权限 calories: false (类型: bool)
2025-07-16 17:23:37.041902+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.041787+0800 Runner[12979:9399738] flutter: │ 🐛 🎯   权限 calories: false (类型: bool)
2025-07-16 17:23:37.041902+0800 Runner[12979:9399738] flutter: └\342
2025-07-16 17:23:37.042453+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.042519+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:399:15)
2025-07-16 17:23:37.042659+0800 Runner[12979:9399738] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:229:28)
2025-07-16 17:23:37.042788+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.043662+0800 Runner[12979:9399738] flutter: │ 🐛 🎯 权限判断结果:
2025-07-16 17:23:37.043696+0800 Runner[12979:9399738] flutter: └\342\224
2025-07-16 17:23:37.044000+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.044183+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:400:15)
2025-07-16 17:23:37.044206+0800 Runner[12979:9399738] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:229:28)
2025-07-16 17:23:37.044231+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.044254+0800 Runner[12979:9399738] flutter: │ 🐛 🎯   steps: false → false
2025-07-16 17:23:37.044279+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.044780+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.044810+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:401:15)
2025-07-16 17:23:37.044833+0800 Runner[12979:9399738] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:229:28)
2025-07-16 17:23:37.044860+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.044882+0800 Runner[12979:9399738] flutter: │ 🐛 🎯   distance: false → false
2025-07-16 17:23:37.044958+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.045210+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.045683+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:402:15)
2025-07-16 17:23:37.045792+0800 Runner[12979:9399738] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:229:28)
2025-07-16 17:23:37.045921+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.046018+0800 Runner[12979:9399738] flutter: │ 🐛 🎯   calories: false → false
2025-07-16 17:23:37.046113+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.046481+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.046018+0800 Runner[12979:9399738] flutter: │ 🐛 🎯   calories: false → false
2025-07-16 17:23:37.046113+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.046481+0800 Runner[12979:9399738] flutter: ┌\342
2025-07-16 17:23:37.046522+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:410:15)
2025-07-16 17:23:37.046549+0800 Runner[12979:9399738] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:229:28)
2025-07-16 17:23:37.046660+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.046742+0800 Runner[12979:9399738] flutter: │ 🐛 🎯 Overview使用独立Health API数据 - 职责分离
2025-07-16 17:23:37.047035+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.047469+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.048100+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:411:15)
2025-07-16 17:23:37.048136+0800 Runner[12979:9399738] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:229:28)
2025-07-16 17:23:37.048160+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.048255+0800 Runner[12979:9399738] flutter: │ 🐛    权限状态 - 步数:false, 距离:false, 卡路里:false
2025-07-16 17:23:37.048279+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:37.048753+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:23:37.048812+0800 Runner[12979:9399738] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:412:15)
2025-07-16 17:23:37.048899+0800 Runner[12979:9399738] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:229:28)
2025-07-16 17:23:37.049024+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:23:37.064209+0800 Runner[12979:9399738] flutter: │ 🐛    显示数据 - 步数:0, 距离:0.0, 卡路里:0
2025-07-16 17:23:37.064276+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:23:39.699295+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.699808+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   _MainLayoutScreenState._triggerStage4PermissionCheck.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:65:17)<…>
2025-07-16 17:23:39.700073+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   new Future.delayed.<anonymous closure> (dart:async/future.dart:419:42)<…>
2025-07-16 17:23:39.700363+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.700681+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📍 阶段4：首页显示完成，开始健康权限检查<…>
2025-07-16 17:23:39.700919+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.700681+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📍 阶段4：首页显示完成，开始健康权限检查<…>
2025-07-16 17:23:39.700919+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342<…>
2025-07-16 17:23:39.706018+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.706320+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:708:13)<…>
2025-07-16 17:23:39.706513+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._triggerStage4PermissionCheck.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:70:40)<…>
2025-07-16 17:23:39.706721+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:23:39.706918+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔍 HealthPermissionProvider: 阶段4权限弹窗判断（基于阶段2.2结果）<…>
2025-07-16 17:23:39.707094+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.708227+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:39.706918+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔍 HealthPermissionProvider: 阶段4权限弹窗判断（基于阶段2.2结果）<…>
2025-07-16 17:23:39.707094+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.708227+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:39.708468+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:711:13)<…>
2025-07-16 17:23:39.708647+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._triggerStage4PermissionCheck.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:70:40)<…>
2025-07-16 17:23:39.708857+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.709652+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 📊 使用阶段2.2已检查的权限状态:<…>
2025-07-16 17:23:39.709858+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.710733+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.711960+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:712:13)<…>
2025-07-16 17:23:39.712141+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._triggerStage4PermissionCheck.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:70:40)<…>
2025-07-16 17:23:39.712319+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.714072+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡   📱 步数权限: notDetermined<…>
2025-07-16 17:23:39.714423+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.715515+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.715515+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:23:39.715937+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:713:13)<…>
2025-07-16 17:23:39.716141+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._triggerStage4PermissionCheck.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:70:40)<…>
2025-07-16 17:23:39.716317+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.717356+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡   📱 距离权限: notDetermined<…>
2025-07-16 17:23:39.717514+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.718220+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.718404+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:714:13)<…>
2025-07-16 17:23:39.718815+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._triggerStage4PermissionCheck.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:70:40)<…>
2025-07-16 17:23:39.719012+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.719187+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡   📱 卡路里权限: notDetermined<…>
2025-07-16 17:23:39.719356+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.721685+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.721911+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:722:13)<…>
2025-07-16 17:23:39.722084+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._triggerStage4PermissionCheck.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:70:40)<…>
2025-07-16 17:23:39.722417+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.722588+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔍 阶段4权限完整性判断结果:<…>
2025-07-16 17:23:39.722722+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.723449+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.723630+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:723:13)<…>
2025-07-16 17:23:39.723820+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._triggerStage4PermissionCheck.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:70:40)<…>
2025-07-16 17:23:39.724072+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.724248+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡   📱 步数权限: notDetermined (❌)<…>
2025-07-16 17:23:39.724394+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.725136+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.726490+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:724:13)<…>
2025-07-16 17:23:39.726685+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._triggerStage4PermissionCheck.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:70:40)<…>
2025-07-16 17:23:39.726805+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.727456+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡   📱 距离权限: notDetermined (❌)<…>
2025-07-16 17:23:39.727579+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.728171+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.727456+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡   📱 距离权限: notDetermined (❌)<…>
2025-07-16 17:23:39.727579+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.728171+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342<…>
2025-07-16 17:23:39.728326+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:725:13)<…>
2025-07-16 17:23:39.728787+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._triggerStage4PermissionCheck.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:70:40)<…>
2025-07-16 17:23:39.728910+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.729021+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡   📱 卡路里权限: notDetermined (❌)<…>
2025-07-16 17:23:39.729264+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└路里权限: notDetermined (❌)<…>
2025-07-16 17:23:39.729264+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:23:39.729863+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.730005+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:726:13)<…>
2025-07-16 17:23:39.730853+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._triggerStage4PermissionCheck.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:70:40)<…>
2025-07-16 17:23:39.731025+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.731168+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡   🎯 所有权限状态: false<…>
2025-07-16 17:23:39.731316+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.732086+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.732257+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:753:17)<…>
2025-07-16 17:23:39.732462+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._triggerStage4PermissionCheck.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:70:40)<…>
2025-07-16 17:23:39.732773+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.733151+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ❌ 阶段4确认：权限不完整，显示授权弹窗<…>
2025-07-16 17:23:39.733396+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.734264+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.734644+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:754:17)<…>
2025-07-16 17:23:39.735063+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._triggerStage4PermissionCheck.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:70:40)<…>
2025-07-16 17:23:39.735328+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.735741+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ✅ 已授权权限: 无<…>
2025-07-16 17:23:39.735975+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.736626+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.736782+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:755:17)<…>
2025-07-16 17:23:39.737066+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState._triggerStage4PermissionCheck.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:70:40)<…>
2025-07-16 17:23:39.737317+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.737713+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ❌ 缺失的权限: steps, distance, calories<…>
2025-07-16 17:23:39.738071+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.739981+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.740299+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.showPermissionDialogIfNeeded (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:249:13)<…>
2025-07-16 17:23:39.740533+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:758:15)<…>
2025-07-16 17:23:39.740644+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.740777+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔍 HealthPermissionProvider: 检查是否需要显示权限对话框<…>
2025-07-16 17:23:39.741268+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.740777+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🔍 HealthPermissionProvider: 检查是否需要显示权限对话框<…>
2025-07-16 17:23:39.741268+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342<…>
2025-07-16 17:23:39.742566+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.742717+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.showPermissionDialogIfNeeded (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:274:13)<…>
2025-07-16 17:23:39.742817+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:758:15)<…>
2025-07-16 17:23:39.742915+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.743001+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🚨 HealthPermissionProvider: 权限不足，需要显示权限对话框<…>
2025-07-16 17:23:39.743096+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.743796+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.743916+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider.showPermissionDialogIfNeeded (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:275:13)<…>
2025-07-16 17:23:39.743999+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   HealthPermissionProvider.checkPermissionOnAppStart (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:758:15)<…>
2025-07-16 17:23:39.744102+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.744411+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ❌ 缺失的权限: steps, distance, calories<…>
2025-07-16 17:23:39.744515+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.746076+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.746221+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider._showPermissionDialogWithRealTimeUpdate (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:294:13)<…>
2025-07-16 17:23:39.746513+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   HealthPermissionProvider.showPermissionDialogIfNeeded (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:281:28)<…>
2025-07-16 17:23:39.746617+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.746683+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🎯 显示智能权限对话框（支持实时更新）<…>
2025-07-16 17:23:39.746747+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:23:39.780243+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:23:39.780398+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   HealthPermissionProvider._showPermissionDialogWithRealTimeUpdate.<anonymous closure>.<anonymous closure> (package:sweatmint/features/health/presentation/providers/health_permission_provider.dart:332:21)<…>
2025-07-16 17:23:39.780448+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   _StatefulBuilderState.build (package:flutter/src/widgets/basic.dart:7992:55)<…>
2025-07-16 17:23:39.780494+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:23:39.780565+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 🎯 弹窗状态 - 已授权: 0/3, 按钮: Grant Access<…>
2025-07-16 17:23:39.780621+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:25:36.700447+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:25:36.700938+0800 Runner[12979:9399738] flutter: │ #0   EventTriggeredSyncService._startPeriodicSync.<anonymous closure> (package:sweatmint/core/services/event_triggered_sync_service.dart:783:19)
2025-07-16 17:25:36.701212+0800 Runner[12979:9399738] flutter: │ #1   _Timer._runTimers (dart:isolate-patch/timer_impl.dart:410:19)
2025-07-16 17:25:36.701517+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:25:36.701776+0800 Runner[12979:9399738] flutter: │ 17:25:36.695 (+0:02:02.927105)
2025-07-16 17:25:36.702013+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:25:36.702403+0800 Runner[12979:9399738] flutter: │ 🐛 ⏰ 定时同步时间到，触发v14.1轻量化健康数据同步
2025-07-16 17:25:36.702643+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:25:36.713433+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:25:36.713741+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService._triggerPeriodicHealthSync (package:sweatmint/core/services/event_triggered_sync_service.dart:801:14)<…>
2025-07-16 17:25:36.713931+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   EventTriggeredSyncService._startPeriodicSync.<anonymous closure> (package:sweatmint/core/services/event_triggered_sync_service.dart:784:9)<…>
2025-07-16 17:25:36.714181+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:25:36.714380+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:25:36.712 (+0:02:02.944002)<…>
2025-07-16 17:25:36.714571+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:25:36.714745+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ⏰ 开始2分钟定时健康数据同步（智能轻量化）<…>
2025-07-16 17:25:36.715505+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:25:36.718468+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:25:36.718737+0800 Runner[12979:9399738] flutter: │ #0   EventTriggeredSyncService._isPeriodicSyncNeeded (package:sweatmint/core/services/event_triggered_sync_service.dart:993:16)
2025-07-16 17:25:36.718923+0800 Runner[12979:9399738] flutter: │ #1   EventTriggeredSyncService._triggerPeriodicHealthSync (package:sweatmint/core/services/event_triggered_sync_service.dart:804:12)
2025-07-16 17:25:36.719128+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:25:36.719315+0800 Runner[12979:9399738] flutter: │ 17:25:36.717 (+0:02:02.949076)
2025-07-16 17:25:36.719483+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:25:36.719668+0800 Runner[12979:9399738] flutter: │ 🐛 ⚡ 跳过定时同步：应用不在前台 (null)
2025-07-16 17:25:36.720341+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:25:36.721272+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:25:36.721530+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService._triggerPeriodicHealthSync (package:sweatmint/core/services/event_triggered_sync_service.dart:805:16)<…>
2025-07-16 17:25:36.722018+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   EventTriggeredSyncService._startPeriodicSync.<anonymous closure> (package:sweatmint/core/services/event_triggered_sync_service.dart:784:9)<…>
2025-07-16 17:25:36.722212+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:25:36.722394+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:25:36.720 (+0:02:02.951882)<…>
2025-07-16 17:25:36.722557+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:25:36.723398+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ⚡ 智能跳过定时同步（条件不满足）<…>
2025-07-16 17:25:36.723584+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:27:36.696978+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:27:36.697306+0800 Runner[12979:9399738] flutter: │ #0   EventTriggeredSyncService._startPeriodicSync.<anonymous closure> (package:sweatmint/core/services/event_triggered_sync_service.dart:783:19)
2025-07-16 17:27:36.697485+0800 Runner[12979:9399738] flutter: │ #1   _Timer._runTimers (dart:isolate-patch/timer_impl.dart:410:19)
2025-07-16 17:27:36.697672+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:27:36.697862+0800 Runner[12979:9399738] flutter: │ 17:27:36.695 (+0:04:02.927164)
2025-07-16 17:27:36.698025+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:27:36.697862+0800 Runner[12979:9399738] flutter: │ 17:27:36.695 (+0:04:02.927164)
2025-07-16 17:27:36.698025+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:27:36.698312+0800 Runner[12979:9399738] flutter: │ 🐛 ⏰ 定时同步时间到，触发v14.1轻量化健康数据同步
2025-07-16 17:27:36.698500+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:27:36.699440+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌
2025-07-16 17:27:36.697862+0800 Runner[12979:9399738] flutter: │ 17:27:36.695 (+0:04:02.927164)
2025-07-16 17:27:36.698025+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:27:36.698312+0800 Runner[12979:9399738] flutter: │ 🐛 ⏰ 定时同步时间到，触发v14.1轻量化健康数据同步
2025-07-16 17:27:36.698500+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:27:36.699440+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:27:36.700657+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService._triggerPeriodicHealthSync (package:sweatmint/core/services/event_triggered_sync_service.dart:801:14)<…>
2025-07-16 17:27:36.700855+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   EventTriggeredSyncService._startPeriodicSync.<anonymous closure> (package:sweatmint/core/services/event_triggered_sync_service.dart:784:9)<…>
2025-07-16 17:27:36.701072+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├
2025-07-16 17:27:36.697862+0800 Runner[12979:9399738] flutter: │ 17:27:36.695 (+0:04:02.927164)
2025-07-16 17:27:36.698025+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:27:36.698312+0800 Runner[12979:9399738] flutter: │ 🐛 ⏰ 定时同步时间到，触发v14.1轻量化健康数据同步
2025-07-16 17:27:36.698500+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:27:36.699440+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:27:36.700657+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService._triggerPeriodicHealthSync (package:sweatmint/core/services/event_triggered_sync_service.dart:801:14)<…>
2025-07-16 17:27:36.700855+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   EventTriggeredSyncService._startPeriodicSync.<anonymous closure> (package:sweatmint/core/services/event_triggered_sync_service.dart:784:9)<…>
2025-07-16 17:27:36.701072+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:27:36.701235+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:27:36.698 (+0:04:02.930029)<…>
2025-07-16 17:27:36.701442+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:27:36.702666+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ⏰ 开始2分钟定时健康数据同步（智能轻量化）<…>
2025-07-16 17:27:36.702883+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└
2025-07-16 17:27:36.697862+0800 Runner[12979:9399738] flutter: │ 17:27:36.695 (+0:04:02.927164)
2025-07-16 17:27:36.698025+0800 Runner[12979:9399738] flutter: ├\342
2025-07-16 17:27:36.698312+0800 Runner[12979:9399738] flutter: │ 🐛 ⏰ 定时同步时间到，触发v14.1轻量化健康数据同步
2025-07-16 17:27:36.698500+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:27:36.699440+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-16 17:27:36.700657+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService._triggerPeriodicHealthSync (package:sweatmint/core/services/event_triggered_sync_service.dart:801:14)<…>
2025-07-16 17:27:36.700855+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   EventTriggeredSyncService._startPeriodicSync.<anonymous closure> (package:sweatmint/core/services/event_triggered_sync_service.dart:784:9)<…>
2025-07-16 17:27:36.701072+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342\224<…>
2025-07-16 17:27:36.701235+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:27:36.698 (+0:04:02.930029)<…>
2025-07-16 17:27:36.701442+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:27:36.702666+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ⏰ 开始2分钟定时健康数据同步（智能轻量化）<…>
2025-07-16 17:27:36.702883+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>
2025-07-16 17:27:36.703835+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:27:36.704059+0800 Runner[12979:9399738] flutter: │ #0   EventTriggeredSyncService._isPeriodicSyncNeeded (package:sweatmint/core/services/event_triggered_sync_service.dart:993:16)
2025-07-16 17:27:36.705725+0800 Runner[12979:9399738] flutter: │ #1   EventTriggeredSyncService._triggerPeriodicHealthSync (package:sweatmint/core/services/event_triggered_sync_service.dart:804:12)
2025-07-16 17:27:36.705903+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:27:36.706096+0800 Runner[12979:9399738] flutter: │ 17:27:36.703 (+0:04:02.934415)
2025-07-16 17:27:36.706302+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:27:36.707619+0800 Runner[12979:9399738] flutter: │ 🐛 ⚡ 跳过定时同步：应用不在前台 (null)
2025-07-16 17:27:36.707767+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:27:36.708469+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:27:36.708791+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService._triggerPeriodicHealthSync (package:sweatmint/core/services/event_triggered_sync_service.dart:805:16)<…>
2025-07-16 17:27:36.708946+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   EventTriggeredSyncService._startPeriodicSync.<anonymous closure> (package:sweatmint/core/services/event_triggered_sync_service.dart:784:9)<…>
2025-07-16 17:27:36.709116+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:27:36.709254+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:27:36.707 (+0:04:02.939237)<…>
2025-07-16 17:27:36.709389+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<<\342\200…>
2025-07-16 17:27:36.709524+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ⚡ 智能跳过定时同步（条件不满足）<…>
2025-07-16 17:27:36.711218+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:29:36.699669+0800 Runner[12979:9399738] flutter: ┌
2025-07-16 17:29:36.700225+0800 Runner[12979:9399738] flutter: │ #0   EventTriggeredSyncService._startPeriodicSync.<anonymous closure> (package:sweatmint/core/services/event_triggered_sync_service.dart:783:19)
2025-07-16 17:29:36.700506+0800 Runner[12979:9399738] flutter: │ #1   _Timer._runTimers (dart:isolate-patch/timer_impl.dart:410:19)
2025-07-16 17:29:36.700800+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:29:36.701061+0800 Runner[12979:9399738] flutter: │ 17:29:36.697 (+0:06:02.928869)
2025-07-16 17:29:36.701306+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:29:36.701574+0800 Runner[12979:9399738] flutter: │ 🐛 ⏰ 定时同步时间到，触发v14.1轻量化健康数据同步
2025-07-16 17:29:36.702084+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:29:36.703412+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:29:36.703744+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService._triggerPeriodicHealthSync (package:sweatmint/core/services/event_triggered_sync_service.dart:801:14)<…>
2025-07-16 17:29:36.704681+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   EventTriggeredSyncService._startPeriodicSync.<anonymous closure> (package:sweatmint/core/services/event_triggered_sync_service.dart:784:9)<…>
2025-07-16 17:29:36.704999+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:29:36.705252+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:29:36.702 (+0:06:02.933767)<…>
2025-07-16 17:29:36.705497+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:29:36.706716+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ⏰ 开始2分钟定时健康数据同步（智能轻量化）<…>
2025-07-16 17:29:36.707026+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:29:36.708273+0800 Runner[12979:9399738] flutter: ┌<…>
2025-07-16 17:29:36.708273+0800 Runner[12979:9399738] flutter: ┌\342\224
2025-07-16 17:29:36.708572+0800 Runner[12979:9399738] flutter: │ #0   EventTriggeredSyncService._isPeriodicSyncNeeded (package:sweatmint/core/services/event_triggered_sync_service.dart:993:16)
2025-07-16 17:29:36.709981+0800 Runner[12979:9399738] flutter: │ #1   EventTriggeredSyncService._triggerPeriodicHealthSync (package:sweatmint/core/services/event_triggered_sync_service.dart:804:12)
2025-07-16 17:29:36.710191+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:29:36.710346+0800 Runner[12979:9399738] flutter: │ 17:29:36.707 (+0:06:02.938690)
2025-07-16 17:29:36.710526+0800 Runner[12979:9399738] flutter: ├
2025-07-16 17:29:36.711484+0800 Runner[12979:9399738] flutter: │ 🐛 ⚡ 跳过定时同步：应用不在前台 (null)
2025-07-16 17:29:36.711700+0800 Runner[12979:9399738] flutter: └
2025-07-16 17:29:36.712576+0800 Runner[12979:9399738] flutter: \^[[38;5;12m┌<…>
2025-07-16 17:29:36.712800+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService._triggerPeriodicHealthSync (package:sweatmint/core/services/event_triggered_sync_service.dart:805:16)<…>
2025-07-16 17:29:36.713223+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   EventTriggeredSyncService._startPeriodicSync.<anonymous closure> (package:sweatmint/core/services/event_triggered_sync_service.dart:784:9)<…>
2025-07-16 17:29:36.713434+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:29:36.712800+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService._triggerPeriodicHealthSync (package:sweatmint/core/services/event_triggered_sync_service.dart:805:16)<…>
2025-07-16 17:29:36.713223+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   EventTriggeredSyncService._startPeriodicSync.<anonymous closure> (package:sweatmint/core/services/event_triggered_sync_service.dart:784:9)<…>
2025-07-16 17:29:36.713434+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342<…>
2025-07-16 17:29:36.713638+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:29:36.711 (+0:06:02.943211)<…>
2025-07-16 17:29:36.713805+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:29:36.714669+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ⚡ 智能跳过定时同步（条件不满足）<…>
2025-07-16 17:29:36.714929+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└<…>
2025-07-16 17:29:36.712800+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #0   EventTriggeredSyncService._triggerPeriodicHealthSync (package:sweatmint/core/services/event_triggered_sync_service.dart:805:16)<…>
2025-07-16 17:29:36.713223+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ #1   EventTriggeredSyncService._startPeriodicSync.<anonymous closure> (package:sweatmint/core/services/event_triggered_sync_service.dart:784:9)<…>
2025-07-16 17:29:36.713434+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├\342<…>
2025-07-16 17:29:36.713638+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 17:29:36.711 (+0:06:02.943211)<…>
2025-07-16 17:29:36.713805+0800 Runner[12979:9399738] flutter: \^[[38;5;12m├<…>
2025-07-16 17:29:36.714669+0800 Runner[12979:9399738] flutter: \^[[38;5;12m│ 💡 ⚡ 智能跳过定时同步（条件不满足）<…>
2025-07-16 17:29:36.714929+0800 Runner[12979:9399738] flutter: \^[[38;5;12m└\342\224<…>

