# Celery 任务修复指南

本文档旨在指导修复 SweatMint 项目后端 Celery 定时任务遇到的问题，确保系统稳定运行和业务逻辑正确执行。

## 0. 系统Celery任务总览

经过代码检查，确认系统中存在以下Celery定时任务：

### 0.1 agents应用任务

1. **`agents.tasks.weekly_commission_settlement`**：
   - **功能**：每周一00:00执行的代理佣金结算任务
   - **执行过程**：创建新的结算周期，并分发子任务处理每个代理的佣金计算
   - **预期结果**：处理所有活跃代理的佣金结算

2. **`agents.tasks.process_agent_commissions_in_batch`**：
   - **功能**：批量处理代理佣金计算
   - **执行过程**：处理指定代理列表的佣金计算，计算一个结算周期内的佣金
   - **预期结果**：返回处理结果统计（成功数、失败数和跳过数）

3. **`agents.tasks.finalize_commission_settlement`**：
   - **功能**：完成佣金结算过程
   - **执行过程**：处理所有批次结果，更新结算周期状态，检查结算预警
   - **预期结果**：更新结算周期状态为completed

4. **`agents.tasks.clear_agent_cache_daily`**：
   - **功能**：每日清理代理缓存的任务
   - **执行过程**：清理所有代理用户的缓存数据
   - **预期结果**：确保缓存数据不会过期太久

5. **`agents.tasks.check_pending_settlements`**：
   - **功能**：定期检查处理中的结算周期是否超时
   - **执行过程**：检查所有处于processing状态的结算周期，触发预警
   - **预期结果**：发现并处理超时的结算周期

### 0.2 authentication应用任务

1. **`authentication.tasks.cleanup_expired_tokens`**：
   - **功能**：清理过期的令牌
   - **执行过程**：找出过期的令牌，将其标记为非活跃，添加到黑名单
   - **预期结果**：移除过期令牌，释放资源

2. **`authentication.tasks.cleanup_blacklist`**：
   - **功能**：清理黑名单中的过期记录
   - **执行过程**：删除黑名单中超过指定天数的记录
   - **预期结果**：防止黑名单不断增长，优化性能

3. **`authentication.tasks.invalidate_user_tokens`**：
   - **功能**：使指定用户的所有令牌失效
   - **执行过程**：将用户的所有活跃令牌标记为非活跃，添加到黑名单
   - **预期结果**：用于用户注销或安全问题时立即撤销所有令牌

### 0.3 tasks应用任务

1. **`tasks.reset_daily_tasks`**：
   - **功能**：每日任务与用户状态管理综合任务
   - **执行过程**：通过DailyTasksManager执行结算前一天完成的附加任务对每日任务的加成奖励，重置并分配新的每日任务，检查并修复没有任务的用户
   - **预期结果**：每日任务重置并正确分配给用户

2. **`tasks.tasks.update_active_member_status`**：
   - **功能**：更新活跃会员状态
   - **执行过程**：检查用户是否连续7天登录且完成每日任务，或90天内有充值记录
   - **预期结果**：准确标记活跃会员状态

3. **`tasks.tasks.cache_monitoring.cache_monitoring_data`**：
   - **功能**：定期缓存监控数据
   - **执行过程**：收集系统监控数据并缓存，包括格式分布统计、转换成功率、任务类型转换分析等
   - **预期结果**：提高监控数据查询效率

### 0.4 vip应用任务

1. **`vip.tasks.process_vip_refunds`**：
   - **功能**：处理所有用户的VIP返还计划
   - **执行过程**：检查用户是否完成前一天的所有每日任务，并根据检查结果更新VIP返还进度或失败标记
   - **预期结果**：返还进度达到目标时向用户返还相应金额的USDT

2. **`vip.tasks.check_stalled_refund_plans`**：
   - **功能**：检测停滞的VIP返还计划并尝试修复
   - **执行过程**：检查活跃但长时间未更新的返还计划，可能是由于系统故障或任务执行失败导致
   - **预期结果**：修复由于系统故障导致的停滞返还计划

### 0.5 stats应用任务

1. **`stats.tasks.update_daily_stats`**：
   - **功能**：每日数据统计更新任务
   - **执行过程**：收集并记录用户统计、资金统计、SWMT数据等
   - **预期结果**：更新运营数据统计、资金数据统计，记录系统日志

## 1. 核心任务执行失败：`AttributeError` in `TaskMetricsService`

**问题描述：**

Celery worker 日志显示，关键的午夜任务 `tasks.tasks.update_active_member_status` 和 `tasks.reset_daily_tasks` 因以下错误而执行失败：

```
AttributeError: 'TaskMetricsService' object has no attribute 'record_concurrency_metrics'. Did you mean: 'record_cache_metrics'?
```

这直接导致用户每日任务无法正确重置，活跃会员状态可能也未按预期更新。

**影响模块：**

*   `running/tasks/tasks/member_status.py` (调用方)
*   `running/tasks/tasks/daily_reset.py` (调用方)
*   `running/tasks/metrics.py` (被调用方，包含TaskMetricsService类定义)

**问题分析：**

通过代码检查，我们发现这是一个典型的方法名称错误问题。`TaskMetricsService` 类中存在 `record_cache_metrics` 方法，但是部分任务错误地调用了不存在的 `record_concurrency_metrics` 方法。

在 `running/tasks/metrics.py` 文件中，`TaskMetricsService` 类提供了以下方法：
- `record_task_metrics`
- `record_task_reset_metrics`
- `record_cache_metrics`
- `record_error_metrics`
- `record_reward_metrics` (在文件后半部分)

但没有 `record_concurrency_metrics` 方法。而 `running/tasks/tasks/member_status.py` 和 `running/tasks/tasks/daily_reset.py` 错误地尝试调用这个不存在的方法。

**修复步骤：**

1.  **修正 `member_status.py` 中的方法调用**：
    *   打开 `running/tasks/tasks/member_status.py` 文件。
    *   找到错误调用 `metrics.record_concurrency_metrics(` 的代码行。
    *   将 `record_concurrency_metrics` 修改为 `record_cache_metrics`。
    ```python
    # 将原来的代码行:
    metrics.record_concurrency_metrics(...)
    # 修改为:
    metrics.record_cache_metrics(...)
    ```

2.  **修正 `daily_reset.py` 中的方法调用**：
    *   打开 `running/tasks/tasks/daily_reset.py` 文件。
    *   同样查找 `metrics.record_concurrency_metrics(` 的代码行。
    *   将 `record_concurrency_metrics` 修改为 `record_cache_metrics`。
    ```python
    # 将原来的代码行:
    metrics.record_concurrency_metrics(...)
    # 修改为:
    metrics.record_cache_metrics(...)
    ```

3.  **确认参数匹配**：
    *   确保修改后的方法调用参数与 `record_cache_metrics` 方法的参数匹配。
    *   根据 `metrics.py` 中的定义，`record_cache_metrics` 方法需要以下参数：
    ```python
    def record_cache_metrics(self, cache_type: str, hit: bool, duration: float):
    ```
    *   如果原调用参数不符合此格式，需要相应调整。

## 2. VIP 返还处理失败 (`vip.tasks.process_vip_refunds`)

**问题描述：**

Celery worker 日志显示 `vip.tasks.process_vip_refunds` 任务在 2025-05-08 00:05:00 执行完成，但其返回结果为 `{'success_count': 0, 'fail_count': 4, 'error_count': 0}`。这表明有4个VIP返还计划处理失败。

虽然后续的 `vip.tasks.check_stalled_refund_plans` 任务在 09:00 成功执行并修复了4个计划，但仍需调查初始处理失败的原因，以防止问题复发或隐藏更深层次的问题。

**影响模块：**

*   `running/vip/tasks.py` (特别是 `process_vip_refunds` 函数)

**修复步骤：**

1.  **代码审查与日志增强**：
    *   审查 `running/vip/tasks.py` 文件中 `process_vip_refunds` 函数的逻辑。
    *   在代码中，该函数会遍历所有活跃的VIP返还计划，通过 `VIPRefundService` 进行处理。
    *   函数会计算成功、失败和错误的数量，但日志信息有限。
    *   增强日志记录以捕获更多失败信息：
        ```python
        # running/vip/tasks.py - process_vip_refunds
        # 在处理用户VIP返还计划前添加更多日志
        logger.info(f"处理用户 {user_id} 的VIP返还计划...")
        
        # 在处理失败时添加更详细的日志
        if not result:
            fail_count += 1
            logger.error(f"用户 {user_id} 的VIP返还计划处理失败，查看VIPRefundService日志获取详情")
        ```

2.  **分析失败原因**：
    *   根据日志审查 `VIPRefundService` 类的 `process_refund` 方法，确认失败的具体原因。
    *   检查相关数据库表的完整性，包括：
        - `vip_uservip` - 用户VIP信息
        - `vip_viprefundplan` - VIP返还计划
        - `tasks_usertask` - 用户任务（用于检查每日任务完成情况）
    *   关注数据库中失败的具体计划记录，了解其状态和处理历史。

3.  **修复建议**：
    *   确认 `VIPRefundService.process_refund` 方法对异常情况的处理逻辑。
    *   考虑添加更多错误处理和恢复机制。
    *   增加对边缘情况的处理，例如用户没有任何任务记录的情况。

## 3. 验证修复

### 3.1 快速验证方法（无需等待0:00）

以下方法可以立即验证修复是否有效，而无需等待下一个定时任务执行周期：

1.  **使用 Django Shell 直接调用任务函数（推荐）**：
    ```bash
    # 激活虚拟环境
    cd /Users/<USER>/Documents/工作/sweatmint/running/
    source .venv/bin/activate
    
    # 打开 Django Shell
    python manage.py shell
    
    # 在Shell中测试已修复的任务
    >>> from tasks.tasks.member_status import update_active_member_status
    >>> update_active_member_status()
    >>> from tasks.tasks.daily_reset import reset_daily_tasks
    >>> reset_daily_tasks()
    >>> from vip.tasks import process_vip_refunds
    >>> process_vip_refunds()
    ```
    此方法直接调用任务函数，可以立即查看函数返回结果和日志输出。缺点是不会通过 Celery Worker 执行，可能与实际调度时的环境略有不同。

2.  **使用 Celery 命令行工具手动执行任务**：
    ```bash
    # 激活虚拟环境
    cd /Users/<USER>/Documents/工作/sweatmint/running/
    source .venv/bin/activate
    
    # 手动触发任务（通过Celery Worker执行）
    celery -A core call tasks.tasks.update_active_member_status
    celery -A core call tasks.reset_daily_tasks
    celery -A core call vip.tasks.process_vip_refunds
    ```
    此方法通过 Celery Worker 执行任务，与实际调度时的环境更接近。但可能不会立即显示详细日志，需查看 `celery_worker.log` 获取执行结果。

3.  **使用 Django 管理命令（若有）**：
    ```bash
    cd /Users/<USER>/Documents/工作/sweatmint/running/
    source .venv/bin/activate
    
    # 如果系统中有对应的管理命令
    python manage.py reset_daily_tasks
    ```
    此方法取决于是否已实现相应的 Django 管理命令。

**验证修复效果的步骤**：

1.  **验证 `AttributeError` 修复**：
    * 使用上述方法执行 `update_active_member_status` 和 `reset_daily_tasks` 任务
    * 查看输出或 `celery_worker.log`，确认不再出现 `AttributeError` 错误
    * 检查函数是否正常完成（例如，检查每日任务是否已重置、会员状态是否已更新）

2.  **验证 VIP 返还处理**：
    * 执行 `process_vip_refunds` 任务
    * 检查返回结果中的 `success_count`、`fail_count` 和 `error_count` 值
    * 查看日志中是否有详细的失败原因记录（如果修复包含了增强日志的部分）
    * 查询数据库中相关记录，确认返还处理是否正确完成

### 3.2 长期监控

1.  **部署修复**：将修改后的代码部署到测试或生产环境。
2.  **监控 Celery 日志**：
    *   在下一个午夜任务执行周期，密切关注 `celery_worker.log` 和 `celery_beat.log`。
    *   确认 `tasks.tasks.update_active_member_status` 和 `tasks.reset_daily_tasks` 不再出现 `AttributeError` 并且成功执行。
    *   观察 `vip.tasks.process_vip_refunds` 的执行结果，确认 `fail_count` 是否减少或为0。
3.  **业务数据验证**：
    *   （在测试环境中）检查用户任务是否已按预期重置。
    *   （在测试环境中）检查相关的活跃会员状态是否正确更新。
    *   （在测试环境中）检查VIP返还记录，确认之前失败的案例是否已通过 `check_stalled_refund_plans` 修正，以及新的返还是否能成功处理。
4.  **回归测试**：执行相关的回归测试用例，确保修复没有引入新的问题。

---

## 4. 其他周期性Celery任务健康检查

为确保整体系统的健康运行，应对所有周期性任务进行检查：

### 4.1 日志分析

1. **收集执行记录**：
   - 检查 `celery_worker.log` 中的所有任务执行记录
   - 关注任务的开始和结束时间、返回结果和错误信息

2. **识别潜在问题**：
   - 检查任务是否按计划执行
   - 检查任务是否成功完成（特别是返回正确的结果）
   - 检查是否有频繁失败的任务
   - 检查任务执行时间是否异常（太长或太短）

### 4.2 具体任务检查清单

| 任务名称 | 功能 | 检查要点 |
|---------|------|---------|
| `agents.tasks.weekly_commission_settlement` | 每周一执行的代理佣金结算 | 是否成功创建结算周期，是否正确分发子任务 |
| `agents.tasks.clear_agent_cache_daily` | 每日清理代理缓存 | 是否能够正确清理所有代理的缓存 |
| `agents.tasks.check_pending_settlements` | 检查处理中的结算周期 | 是否能发现并处理超时的结算周期 |
| `authentication.tasks.cleanup_blacklist` | 清理黑名单中的过期记录 | 是否正确删除过期记录，缓存是否正确清理 |
| `authentication.tasks.invalidate_user_tokens` | 使指定用户的令牌失效 | 是否能正确处理用户令牌 |
| `stats.tasks.update_daily_stats` | 每日数据统计更新 | 数据是否正确收集和存储 |
| `tasks.tasks.cache_monitoring.cache_monitoring_data` | 缓存监控数据 | 数据是否正确缓存，缓存时间是否合理 |
| `vip.tasks.check_stalled_refund_plans` | 检测停滞的VIP返还计划 | 是否能正确识别和修复停滞的计划 |

### 4.3 调度配置检查

检查 `settings.py` 或专门的Celery配置文件中的 `CELERY_BEAT_SCHEDULE` 配置，确保所有任务都有合理的执行计划。例如：

```python
CELERY_BEAT_SCHEDULE = {
    'update_active_member_status': {
        'task': 'tasks.tasks.update_active_member_status',
        'schedule': crontab(hour=0, minute=0),  # 新加坡时间0:00执行
        'options': {'expires': 3600}
    },
    'reset_daily_tasks': {
        'task': 'tasks.reset_daily_tasks',
        'schedule': crontab(hour=0, minute=0),  # 新加坡时间0:00执行
        'options': {'expires': 3600}
    },
    # ... 其他任务配置
}
```

确认任务的执行时间、过期时间和重试策略是否合理。

### 4.4 资源使用分析

1. **CPU与内存使用**：
   - 监控任务执行期间的CPU和内存使用情况
   - 确认是否有任务导致资源使用异常高

2. **数据库负载**：
   - 分析任务执行期间的数据库查询数量和执行时间
   - 确认是否有任务导致数据库负载过高

3. **并发处理**：
   - 检查任务之间是否有资源竞争或锁定问题
   - 确认任务的并发执行是否符合预期

---

## 5. 任务测试结果分析

经过对关键Celery任务的测试，我们发现了以下情况：

### 5.1 AttributeError问题修复验证

通过代码检查，确认`tasks/tasks/member_status.py`和`tasks/tasks/daily_reset.py`中已正确使用`record_cache_metrics`方法，而不是错误的`record_concurrency_metrics`方法。任务在执行时也不再报告AttributeError错误，这表明该问题已成功修复。

```python
# member_status.py中正确的代码
metrics.record_cache_metrics(
    cache_type='member_status_update',
    hit=False,
    duration=0.0
)

# daily_reset.py中正确的代码
metrics.record_cache_metrics(
    cache_type='daily_management_overall',
    hit=False,
    duration=0.0
)
```

### 5.2 新发现问题：用户关联关系错误

虽然AttributeError问题已修复，但测试中发现了一个新问题：在执行`update_active_member_status`任务时，所有29个用户处理都失败，错误信息为：

```
'User' object has no attribute 'login_records'
```

这表明`User`模型与`login_records`之间的关联关系设置有问题。根据`member_status.py`中的代码，该任务试图访问用户的登录记录和完成的任务来确定活跃状态：

```python
# 检查登录记录
has_login = user.login_records.filter(
    login_time__date=check_date
).exists()
```

**问题分析：**

1. 可能缺少相关模型的反向关系定义
2. 可能是数据库迁移问题导致关系未正确建立
3. 可能是代码与数据库结构不匹配

**解决方案：**

1. 检查User模型和Login记录模型之间的关系定义
   - 在User模型中可能需要添加related_name属性
   - 在Login记录模型中检查外键关系是否正确

2. 临时解决方案：修改`member_status.py`中的代码，使用正向查询而不是反向关系：
   ```python
   # 从相关的登录记录模型直接查询
   from users.models import LoginRecord  # 假设模型名为LoginRecord
   
   # 将反向查询:
   # has_login = user.login_records.filter(...)
   
   # 替换为正向查询:
   has_login = LoginRecord.objects.filter(
       user=user,
       login_time__date=check_date
   ).exists()
   ```

### 5.3 其他任务状态分析

1. **`reset_daily_tasks`任务**：
   - 返回状态: `success_with_no_errors_but_run_returned_false`
   - 处理用户数: 29
   - 成功操作数: 0
   - 错误操作数: 0
   
   这表明任务没有报错，但也没有执行实际操作，可能是因为没有需要重置的任务或相关业务逻辑条件未满足。

2. **`process_vip_refunds`任务**：
   - 成功计数: 0
   - 失败计数: 0
   - 错误计数: 0
   
   测试结果显示没有处理任何VIP返还计划，可能是因为系统中当前没有活跃的VIP返还计划。

3. **`check_stalled_refund_plans`任务**：
   - 修复计数: 0
   - 计划总数: 0
   
   没有发现停滞的返还计划，这是符合预期的结果。

### 5.4 后续修复建议

1. **优先解决User模型与login_records关系问题**:
   - 检查users/models.py中的User模型定义
   - 确认登录记录模型与User的关联方式
   - 修复反向关系名称或调整代码使用方式

2. **改进错误处理机制**:
   - 在遇到结构性错误时提供更清晰的错误信息
   - 添加更多的异常处理来防止任务完全失败

3. **测试验证流程**:
   - 修复问题后，重新运行测试
   - 确认所有任务可以正常执行
   - 逐步验证业务功能是否正常

**维护者**: BOSS
**最后更新日期**: 2025年5月8日
