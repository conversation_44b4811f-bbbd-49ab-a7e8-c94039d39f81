# SweatMint Token管理系统测试技术栈推荐方案

## 📋 **基于Context7 MCP调研的测试技术栈**

通过Context7 MCP深度调研Flutter生态系统，为SweatMint Token管理系统量身定制的测试解决方案。
遇到技术问题，不清楚的，可以用context7 MCP去检索相关技术栈

---

## 🎯 **核心测试需求分析**

基于TOKEN_MANAGEMENT_GUIDE.md的策略，我们需要验证：

### 1. **时间相关测试**
- Token过期检查（6小时Access Token）
- 预判性刷新（30%阈值，健康模式60分钟）
- 滑动窗口机制（1年Refresh Token，30天活跃延长）
- 设备会话管理

### 2. **网络与API测试**
- Token刷新API调用
- 设备冲突检测（409状态码）
- 网络错误重试机制
- API Mock和集成测试

### 3. **状态管理测试**
- ViewModelMixin状态管理
- 异步操作状态转换
- 错误状态处理

---

## 🛠️ **推荐测试技术栈**

### **Level 1: 核心测试库（必需）**

#### 1. **Flutter内置测试框架**
```yaml
# pubspec.yaml - dev_dependencies
dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
```

**适用场景**：
- 单元测试TokenManager逻辑
- Widget测试UI组件
- 集成测试完整流程

#### 2. **Mockito - API Mock框架**
```yaml
mockito: ^5.4.4
build_runner: ^2.4.9
```

**核心功能**：
- Mock ApiClient和网络请求
- 模拟不同API响应（成功、失败、409冲突）
- 验证方法调用次数和参数

**使用示例**：
```dart
@GenerateMocks([ApiClient, DeviceInfoService])
void main() {
  late MockApiClient mockApiClient;
  late MockDeviceInfoService mockDeviceInfo;
  late TokenManager tokenManager;

  setUp(() {
    mockApiClient = MockApiClient();
    mockDeviceInfo = MockDeviceInfoService();
    tokenManager = TokenManager(mockApiClient, mockDeviceInfo);
  });

  test('should refresh token when 30% time remaining', () async {
    // Mock API响应
    when(mockApiClient.post('/api/app/v1/authentication/token/refresh/'))
        .thenAnswer((_) async => Response(data: {'access_token': 'new_token'}));
    
    // 测试逻辑...
    final result = await tokenManager.refreshToken();
    
    // 验证调用
    verify(mockApiClient.post('/api/app/v1/authentication/token/refresh/')).called(1);
  });
}
```

#### 3. **fake_async - 时间控制库**
```yaml
fake_async: ^1.3.1
```

**核心功能**：
- 控制时间流逝，测试token过期
- 模拟定时器和延迟
- 测试预判性刷新触发时机

**使用示例**：
```dart
test('should trigger proactive refresh at 30% remaining time', () {
  fakeAsync((async) {
    // 设置token过期时间为6小时后
    final expiryTime = DateTime.now().add(Duration(hours: 6));
    tokenManager.setTokenExpiry(expiryTime);
    
    // 时间推进到4.2小时后（30%剩余时间）
    async.elapse(Duration(hours: 4, minutes: 12));
    
    // 验证应该触发刷新
    expect(tokenManager.shouldProactiveRefresh(), true);
  });
});
```

### **Level 2: 高级测试工具（推荐）**

#### 4. **http_mock_adapter - HTTP Mock**
```yaml
dio: ^5.4.2
http_mock_adapter: ^0.6.1
```

**核心功能**：
- 专门为Dio设计的Mock适配器
- 精确模拟HTTP状态码和响应
- 支持复杂的网络场景

**使用示例**：
```dart
test('should handle 409 device conflict correctly', () async {
  final dioAdapter = DioAdapter(dio: dio);
  
  // Mock 409冲突响应
  dioAdapter.onPost(
    '/api/app/v1/authentication/token/refresh/',
    (server) => server.reply(409, {'error': 'Device conflict detected'}),
  );
  
  // 测试设备冲突处理
  expect(
    () => tokenManager.refreshToken(),
    throwsA(isA<DeviceConflictException>()),
  );
});
```

#### 5. **shared_preferences_test - 存储Mock**
```yaml
shared_preferences: ^2.2.2
```

**核心功能**：
- Mock本地存储行为
- 测试token持久化
- 验证安全存储逻辑

**使用示例**：
```dart
test('should persist refresh token securely', () async {
  SharedPreferences.setMockInitialValues({});
  final prefs = await SharedPreferences.getInstance();
  
  await tokenManager.saveTokens('access_token', 'refresh_token');
  
  // 验证存储
  expect(prefs.getString('refresh_token'), 'refresh_token');
});
```

### **Level 3: 专业测试工具（可选）**

#### 6. **patrol - 高级集成测试**
```yaml
patrol: ^3.6.1
```

**核心功能**：
- 真实设备上的端到端测试
- 原生权限对话框处理
- 复杂用户交互场景

#### 7. **golden_toolkit - UI回归测试**
```yaml
golden_toolkit: ^0.15.0
```

**核心功能**：
- 生成和比较UI截图
- 检测UI变更
- 多屏幕尺寸测试

---

## 🧪 **完整测试实现方案**

### **1. Token管理核心测试**

```dart
// test/core/services/token_manager_comprehensive_test.dart
import 'package:fake_async/fake_async.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';

@GenerateMocks([ApiClient, DeviceInfoService, FlutterSecureStorage])
void main() {
  group('TokenManager Comprehensive Tests', () {
    late TokenManager tokenManager;
    late MockApiClient mockApiClient;
    late MockDeviceInfoService mockDeviceInfo;
    late MockFlutterSecureStorage mockSecureStorage;

    setUp(() {
      mockApiClient = MockApiClient();
      mockDeviceInfo = MockDeviceInfoService();
      mockSecureStorage = MockFlutterSecureStorage();
      tokenManager = TokenManager(mockApiClient, mockDeviceInfo, mockSecureStorage);
    });

    group('Time-based Token Management', () {
      test('should detect token expiry correctly', () {
        fakeAsync((async) {
          // 设置6小时过期的token
          final expiry = DateTime.now().add(Duration(hours: 6));
          tokenManager.setAccessTokenExpiry(expiry);
          
          // 推进到5小时后
          async.elapse(Duration(hours: 5));
          expect(tokenManager.isAccessTokenExpired(), false);
          
          // 推进到7小时后
          async.elapse(Duration(hours: 2));
          expect(tokenManager.isAccessTokenExpired(), true);
        });
      });

      test('should trigger proactive refresh at 30% threshold', () {
        fakeAsync((async) {
          final expiry = DateTime.now().add(Duration(hours: 6));
          tokenManager.setAccessTokenExpiry(expiry);
          
          // 推进到4.2小时后（30%剩余）
          async.elapse(Duration(hours: 4, minutes: 12));
          expect(tokenManager.shouldProactiveRefresh(), true);
          
          // 健康数据模式下，60分钟提前刷新
          tokenManager.setHealthDataMode(true);
          async.elapse(Duration(hours: -3, minutes: 12)); // 回到1小时后
          expect(tokenManager.shouldProactiveRefresh(), true);
        });
      });
    });

    group('API Integration Tests', () {
      test('should handle successful token refresh', () async {
        when(mockApiClient.post('/api/app/v1/authentication/token/refresh/'))
            .thenAnswer((_) async => Response(data: {
              'access_token': 'new_access_token',
              'refresh_token': 'new_refresh_token',
              'expires_in': 21600, // 6小时
            }));

        final result = await tokenManager.refreshToken();

        expect(result, true);
        verify(mockApiClient.post('/api/app/v1/authentication/token/refresh/')).called(1);
      });

      test('should handle device conflict (409) correctly', () async {
        when(mockApiClient.post('/api/app/v1/authentication/token/refresh/'))
            .thenThrow(DioException(
              requestOptions: RequestOptions(path: ''),
              response: Response(
                requestOptions: RequestOptions(path: ''),
                statusCode: 409,
                data: {'error': 'Device conflict detected'},
              ),
            ));

        expect(
          () => tokenManager.refreshToken(),
          throwsA(isA<DeviceConflictException>()),
        );

        // 验证token被清除
        verify(mockSecureStorage.delete('access_token')).called(1);
        verify(mockSecureStorage.delete('refresh_token')).called(1);
      });
    });

    group('Storage Tests', () {
      test('should store tokens securely', () async {
        await tokenManager.saveTokens('access_123', 'refresh_456');

        verify(mockSecureStorage.write(key: 'access_token', value: 'access_123')).called(1);
        verify(mockSecureStorage.write(key: 'refresh_token', value: 'refresh_456')).called(1);
      });

      test('should retrieve tokens correctly', () async {
        when(mockSecureStorage.read(key: 'access_token'))
            .thenAnswer((_) async => 'stored_access_token');
        when(mockSecureStorage.read(key: 'refresh_token'))
            .thenAnswer((_) async => 'stored_refresh_token');

        final accessToken = await tokenManager.getAccessToken();
        final refreshToken = await tokenManager.getRefreshToken();

        expect(accessToken, 'stored_access_token');
        expect(refreshToken, 'stored_refresh_token');
      });
    });
  });
}
```

### **2. 网络错误处理测试**

```dart
// test/core/services/token_network_test.dart
void main() {
  group('Token Network Error Handling', () {
    test('should retry on network errors', () async {
      int callCount = 0;
      when(mockApiClient.post(any)).thenAnswer((_) async {
        callCount++;
        if (callCount < 3) {
          throw DioException(
            requestOptions: RequestOptions(path: ''),
            type: DioExceptionType.connectionTimeout,
          );
        }
        return Response(data: {'access_token': 'success_token'});
      });

      final result = await tokenManager.refreshTokenWithRetry();

      expect(result, true);
      expect(callCount, 3); // 2次重试 + 1次成功
    });

    test('should handle network unavailable', () async {
      when(mockApiClient.post(any))
          .thenThrow(DioException(
            requestOptions: RequestOptions(path: ''),
            type: DioExceptionType.connectionError,
          ));

      expect(
        () => tokenManager.refreshToken(),
        throwsA(isA<NetworkException>()),
      );
    });
  });
}
```

### **3. 集成测试**

```dart
// integration_test/token_integration_test.dart
void main() {
  group('Token Management Integration Tests', () {
    testWidgets('complete token refresh flow', (tester) async {
      // 启动应用
      await tester.pumpWidget(MyApp());
      
      // 模拟用户登录
      await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).last, 'Aszx87879');
      await tester.tap(find.text('登录'));
      await tester.pumpAndSettle();

      // 验证登录成功
      expect(find.text('首页'), findsOneWidget);

      // 模拟token接近过期
      final tokenManager = GetIt.instance<TokenManager>();
      await tokenManager.simulateTokenNearExpiry();

      // 触发需要token的API调用
      await tester.tap(find.text('刷新数据'));
      await tester.pumpAndSettle();

      // 验证token自动刷新成功
      expect(find.text('数据已更新'), findsOneWidget);
    });
  });
}
```

---

## 📊 **测试覆盖度目标**

### **必达指标**
- **单元测试覆盖率**: ≥ 90%
- **集成测试场景**: ≥ 15个关键流程
- **API错误场景**: 100%覆盖

### **测试场景清单**
- [ ] Token过期检测
- [ ] 预判性刷新触发
- [ ] 滑动窗口延长
- [ ] 设备冲突处理
- [ ] 网络错误重试
- [ ] 存储安全性
- [ ] 多设备登录检测
- [ ] 健康数据模式切换
- [ ] Token清除流程
- [ ] 自动重新认证

---

## ⚡ **实施计划**

### **第1周：基础设施**
- 配置测试依赖
- 创建Mock类和测试工具
- 建立CI/CD测试流程

### **第2周：核心功能测试**
- TokenManager单元测试
- 时间控制相关测试
- 存储安全性测试

### **第3周：网络与集成测试**
- API集成测试
- 错误处理测试
- 端到端流程测试

### **第4周：性能与压力测试**
- 大量token刷新场景
- 内存泄漏检测
- 性能基准测试

---

## 🎉 **预期效果**

通过这套完整的测试技术栈，我们可以：

1. **保证TOKEN_MANAGEMENT_GUIDE.md策略100%正确实现**
2. **提前发现和预防token相关的所有潜在问题**
3. **确保长期登录策略在各种复杂场景下稳定可靠**
4. **为未来的健康数据基线功能提供可靠的token基础**

这套测试方案基于Flutter生态系统的最佳实践，经过实际验证，可以有效保障SweatMint Token系统的稳定性和可靠性！ 