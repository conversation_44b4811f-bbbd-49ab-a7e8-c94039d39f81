# Tasks页面聚合API文档

## 概述

Tasks聚合API是为SweatMint应用的Tasks页面专门设计的聚合数据接口，旨在通过单一API调用获取Tasks页面所需的全部数据，避免前端发起多个分散的API请求，提升性能和用户体验。

## 设计背景

### 问题分析
在Tasks聚合API开发之前，Tasks页面需要调用多个独立API：
- `/api/app/v1/tasks/daily/` - 每日任务数据
- `/api/app/v1/tasks/addon/` - 附加任务数据  
- `/api/app/v1/users/profile/` - 用户基本信息
- `/api/app/v1/vip/status/` - VIP状态信息
- `/api/app/v1/tasks/health/data/` - 健康数据
- `/api/app/v1/tasks/reward/history/` - 奖励历史

这种方式会导致：
1. **性能问题**：5-6个API请求 = 5-6次数据库查询 + 5-6次网络往返
2. **数据一致性问题**：多个API调用之间可能存在时序差异
3. **维护复杂度**：前端需要处理多个API的加载状态和错误处理

### 解决方案
采用聚合API模式，将所有相关数据在后端统一查询和聚合，通过单一接口返回：
- **性能提升**：1个API请求 = 1次优化查询 + 1次网络往返
- **数据一致性**：所有数据在单一事务中获取
- **简化前端**：统一的加载状态和错误处理

## API详情

### 基本信息
- **接口路径**: `/api/app/v1/tasks/api/dashboard/`
- **请求方法**: `GET`
- **认证要求**: 需要Bearer Token认证
- **响应格式**: JSON

### 请求示例
```bash
curl -X GET "https://api.sweatmint.com/api/app/v1/tasks/api/dashboard/" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     -H "Content-Type: application/json"
```

### 响应结构

#### 成功响应 (200 OK)
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "top_section": {
      "user_info": {
        "user_id": "123",
        "avatar": "https://example.com/avatar.jpg",
        "username": "用户名",
        "member_level": {
          "name": "Level 1",
          "level": 2,
          "progress_percentage": 65.5
        }
      },
      "vip_status": {
        "has_vip": true,
        "vip_level_name": "VIP 3",
        "vip_level": 3,
        "swmt_bonus_percentage": 50.0,
        "exp_bonus_percentage": 30.0
      }
    },
    "countdown_section": {
      "countdown_info": {
        "hours_until_reset": 8,
        "minutes_until_reset": 45,
        "seconds_until_reset": 30,
        "reset_time_singapore": "2024-01-15T04:00:00+08:00",
        "current_time_singapore": "2024-01-14T19:14:30+08:00"
      },
      "dynamic_tip": {
        "tip_text": "晚上好！坚持完成今日任务～",
        "tip_type": "evening",
        "show_icon": true
      }
    },
    "daily_tasks_section": {
      "summary": {
        "total_tasks": 5,
        "completed_tasks": 3,
        "completion_percentage": 60,
        "all_tasks_completed": false
      },
      "tasks": [
        {
          "id": "456",
          "task_id": "789",
          "name": "每日步行",
          "description": "完成8000步行走",
          "task_type": "daily",
          "status": "completed",
          "progress": 8500,
          "target": 8000,
          "progress_percentage": 100,
          "icon_url": "https://example.com/walk_icon.png",
          "rewards": {
            "base_swmt": "10.00",
            "base_exp": 50,
            "vip_bonus_swmt": "5.00",
            "vip_bonus_exp": 15,
            "total_swmt": "15.00",
            "total_exp": 65
          },
          "can_complete": false,
          "completion_time": "2024-01-14T14:30:00+08:00"
        }
      ]
    },
    "addon_tasks_section": {
      "summary": {
        "active_addon_tasks": 2,
        "max_addon_tasks": 3,
        "can_activate_more": true
      },
      "active_tasks": [
        {
          "id": "101",
          "task_id": "201",
          "name": "邀请好友",
          "description": "邀请1位新用户注册",
          "task_type": "addon",
          "status": "in_progress",
          "icon_url": "https://example.com/invite_icon.png",
          "bonus_multiplier": 1.5,
          "activation_time": "2024-01-14T08:00:00+08:00"
        }
      ],
      "available_tasks": [
        {
          "id": "301",
          "task_id": "301",
          "name": "社交分享",
          "description": "分享应用到社交媒体",
          "task_type": "addon",
          "icon_url": "https://example.com/share_icon.png",
          "bonus_multiplier": 1.2,
          "can_activate": true
        }
      ]
    },
    "today_summary_section": {
      "earned_today": {
        "swmt": "45.50",
        "exp": 180
      },
      "health_data": {
        "steps": 8500,
        "distance": 6.8,
        "calories": 320
      },
      "current_streak": 7,
      "next_tasks": [
        {
          "id": "501",
          "name": "观看广告",
          "type": "daily",
          "icon": "https://example.com/ad_icon.png",
          "total_swmt": "5.00",
          "total_exp": 25
        }
      ],
      "completion_status": {
        "all_tasks_completed": false,
        "completion_percentage": 60,
        "total_tasks": 5,
        "completed_tasks": 3
      }
    }
  },
  "timestamp": **********,
  "_meta": {
    "data_completeness": "5/5",
    "processing_time_ms": 150,
    "cache_used": false
  }
}
```

#### 错误响应 (500 Internal Server Error)
```json
{
  "code": 500,
  "message": "获取Tasks页面数据失败，请稍后重试。",
  "details": "Database connection timeout",
  "timestamp": **********,
  "_meta": {
    "processing_time_ms": 5000,
    "error_type": "DatabaseError"
  }
}
```

## 数据模块说明

### 1. 顶部区域 (top_section)
展示用户基本信息和VIP状态，基于以下现有API逻辑：
- `users/views_app.py - UserProfileView`
- `vip/app_views.py - UserVIPStatusView`

**包含数据**：
- 用户ID、头像、用户名
- 会员等级信息和进度
- VIP状态和加成比例

### 2. 倒计时区域 (countdown_section)
展示任务重置倒计时和动态提示文本。

**包含数据**：
- 距离下次重置的小时、分钟、秒数
- 重置时间（新加坡时区4:00）
- 根据时间段生成的动态提示

### 3. 每日任务区域 (daily_tasks_section)
展示用户的每日任务列表和完成情况，基于现有的：
- `tasks/app_views.py - DailyTaskViewSet.list()`

**包含数据**：
- 任务统计摘要（总数、完成数、完成百分比）
- 详细任务列表（进度、奖励、状态等）

### 4. 附加任务区域 (addon_tasks_section)
展示附加任务的激活状态和可用任务，基于现有的：
- `tasks/app_views.py - AddonTaskViewSet.list()`

**包含数据**：
- 附加任务统计（已激活数、最大限制）
- 已激活的附加任务列表
- 可激活的附加任务列表

### 5. 今日摘要区域 (today_summary_section)
展示今日收益、健康数据和任务完成情况，基于现有的：
- `tasks/app_views.py - today_summary`
- `tasks/app_views.py - get_earned_today`
- `tasks/app_views.py - get_user_health_data`

**包含数据**：
- 今日收益（SWMT和EXP）
- 健康数据（步数、距离、卡路里）
- 连续完成天数
- 下一个可执行任务
- 整体完成状态

## 技术实现

### 架构设计
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   前端调用      │───▶│  TasksDashboard  │───▶│  TasksDashboard │
│                 │    │     ViewSet      │    │     Service     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                ┌────────────────────────┴────────────────────────┐
                                ▼                                                 ▼
                    ┌─────────────────────┐                         ┌─────────────────────┐
                    │  现有API函数调用     │                         │   数据库聚合查询     │
                    │                     │                         │                     │
                    │ • get_earned_today  │                         │ • UserTask.objects  │
                    │ • get_user_health   │                         │ • UserVIP.objects   │
                    │ • get_next_tasks    │                         │ • Task.objects      │
                    │ • get_user_streak   │                         │ • User.objects      │
                    └─────────────────────┘                         └─────────────────────┘
```

### 性能优化
1. **事务管理**: 使用`transaction.atomic()`确保数据一致性
2. **查询优化**: 使用`select_related()`减少数据库查询次数
3. **缓存策略**: 为静态数据（如VIP配置）添加缓存
4. **日志监控**: 详细的性能和错误日志记录

### 错误处理
1. **分层异常处理**: 服务层捕获业务异常，视图层处理HTTP异常
2. **降级策略**: 单个模块失败时返回默认值，不影响其他模块
3. **详细日志**: 记录每个模块的执行状态和错误信息

## 部署配置

### URL配置
已在`running/core/urls.py`中添加：
```python
path('tasks/api/', include('api.tasks.urls')),  # Tasks页面聚合数据API
```

### API路径
- 开发环境: `http://localhost:8000/api/app/v1/tasks/api/dashboard/`
- 生产环境: `https://api.sweatmint.com/api/app/v1/tasks/api/dashboard/`

### 依赖关系
此API依赖以下现有模块：
- `tasks/app_views.py` - 任务相关功能函数
- `users/views_app.py` - 用户资料管理
- `vip/app_views.py` - VIP状态管理
- `config/models.py` - 系统配置

## 测试验证

### 功能测试
1. **数据完整性测试**: 验证所有5个数据模块都能正常返回
2. **边界条件测试**: 测试新用户、VIP用户、无任务用户等场景
3. **性能测试**: 验证响应时间在合理范围内（< 500ms）

### 错误场景测试
1. **数据库连接失败**: 验证降级策略
2. **部分模块异常**: 验证其他模块不受影响
3. **高并发访问**: 验证系统稳定性

## 监控和日志

### 日志文件
- **位置**: `running/logs/tasks_api.log`
- **级别**: DEBUG, INFO, WARNING, ERROR
- **格式**: `%(levelname)s %(asctime)s %(name)s %(process)d %(thread)d %(message)s`

### 关键监控指标
1. **响应时间**: 平均响应时间、95%分位数
2. **成功率**: API调用成功率
3. **数据完整性**: 各模块数据返回成功率
4. **错误率**: 不同类型错误的发生频率

### 告警规则
- 响应时间 > 1秒
- 成功率 < 99%
- 连续错误 > 10次

## 版本更新记录

### v1.0.0 (2024-01-14)
- ✅ 初始版本发布
- ✅ 实现5个数据模块的聚合
- ✅ 添加详细的API文档
- ✅ 配置URL路由和权限控制
- ✅ 实现错误处理和日志记录

### 后续计划
- 🔄 添加缓存策略优化性能
- 🔄 实现GraphQL支持（可选）
- 🔄 添加数据压缩减少传输大小
- 🔄 支持部分数据更新（增量更新）

## 使用指南

### 前端集成
```javascript
// Flutter/Dart 示例
class TasksApiService {
  static Future<TasksDashboardData> getTasksDashboard() async {
    final response = await dio.get('/api/app/v1/tasks/api/dashboard/');
    
    if (response.data['code'] == 200) {
      return TasksDashboardData.fromJson(response.data['data']);
    } else {
      throw ApiException(response.data['message']);
    }
  }
}
```

### 错误处理建议
```javascript
try {
  final dashboardData = await TasksApiService.getTasksDashboard();
  // 处理成功数据
} catch (e) {
  if (e is ApiException) {
    // 显示用户友好的错误信息
    showError(e.message);
  } else {
    // 处理网络错误等
    showError('网络连接失败，请检查网络设置');
  }
}
```

### 缓存策略建议
```javascript
// 建议在前端添加短时间缓存（1-2分钟）
class TasksCache {
  static final _cache = <String, CacheEntry>{};
  static const _cacheTimeout = Duration(minutes: 1);
  
  static Future<TasksDashboardData> getCachedDashboard() async {
    final cacheKey = 'tasks_dashboard_${userId}';
    final cached = _cache[cacheKey];
    
    if (cached != null && !cached.isExpired) {
      return cached.data;
    }
    
    final freshData = await TasksApiService.getTasksDashboard();
    _cache[cacheKey] = CacheEntry(freshData, DateTime.now());
    return freshData;
  }
}
```

## 联系信息

如有技术问题或功能需求，请联系：
- **开发团队**: SweatMint Backend Team
- **文档更新**: 2024-01-14
- **API版本**: v1.0.0 

## 3. 手动健康数据同步API

### 3.1 基本信息

- **端点**: `POST /api/app/v1/tasks/api/manual-health-sync/`
- **认证**: 需要Bearer Token
- **速率限制**: 
  - **用户级别**: 每小时最多10次
  - **短期限制**: 每30秒最多1次
- **内容类型**: `application/json`

### 3.2 功能说明

提供用户主动触发健康数据同步的功能，包含完整的速率保护机制。用于Tasks页面中每日任务（步数、距离）的手动更新按钮。

### 3.3 请求格式

```json
{
  "steps": 8500,
  "distance": 6.2,
  "calories": 320,
  "date": "2024-01-15",
  "device_id": "iPhone12_ABC123",
  "data_source": "apple_health"
}
```

#### 参数说明

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `steps` | integer | 是 | 今日步数 |
| `distance` | float | 是 | 今日距离（公里） |
| `calories` | integer | 否 | 今日卡路里 |
| `date` | string | 否 | 数据日期（YYYY-MM-DD），默认今日 |
| `device_id` | string | 是 | 设备唯一标识 |
| `data_source` | string | 否 | 数据来源（apple_health/google_fit） |

### 3.4 响应格式

#### 成功响应 (200 OK)

```json
{
  "code": 200,
  "message": "手动健康数据同步成功",
  "data": {
    "health_data_log_id": 12345,
    "affected_tasks": [
      {
        "task_id": 1,
        "name": "每日步数",
        "previous_progress": {
          "current_steps": 0,
          "percentage": 0
        },
        "current_progress": {
          "current_steps": 8500,
          "percentage": 100
        },
        "status": "completed",
        "rewards_granted": {
          "swmt": "10.50",
          "exp": 50
        }
      }
    ],
    "total_rewards": {
      "swmt": "10.50",
      "exp": 50
    },
    "level_up": null,
    "vip_refund_updated": false,
    "sync_type": "manual",
    "next_sync_available_at": "2024-01-15T10:30:30Z"
  },
  "timestamp": **********
}
```

#### 速率限制错误 (429 Too Many Requests)

```json
{
  "code": 429,
  "message": "手动同步过于频繁，请30秒后再试",
  "data": {
    "retry_after_seconds": 30,
    "sync_type": "manual"
  },
  "timestamp": **********
}
```

#### 数据验证错误 (400 Bad Request)

```json
{
  "code": 400,
  "message": "健康数据不能为空",
  "data": null,
  "timestamp": **********
}
```

### 3.5 核心特性

#### 🔒 速率保护机制

1. **短期保护**: 30秒内最多1次同步
2. **长期保护**: 每小时最多10次同步
3. **缓存键**: `manual_health_sync:{user_id}`

#### 🔄 自动缓存失效

手动同步成功后自动清除：
- Tasks聚合数据缓存
- Dashboard相关缓存
- 用户任务状态缓存

#### 📈 任务进度更新

- 自动检测受影响的健康相关任务
- 实时更新任务进度和状态
- 计算并发放任务奖励

### 3.6 集成Event-Triggered Sync

手动同步成功后会触发Event-Triggered Sync系统：

```javascript
// 前端触发方式
await syncService.triggerHealthDataSync({
  data: {
    steps: 8500,
    distance: 6.2,
    sync_type: 'manual',
    sync_timestamp: '2024-01-15T10:00:00Z'
  }
});
```

### 3.7 错误处理

| 错误码 | HTTP状态 | 说明 | 处理建议 |
|--------|----------|------|----------|
| 429 | 429 | 请求过于频繁 | 显示倒计时，禁用按钮 |
| 400 | 400 | 请求数据无效 | 检查健康数据格式 |
| 403 | 403 | 权限不足 | 引导用户重新授权 |
| 500 | 500 | 服务器错误 | 显示重试按钮 |

### 3.8 前端集成示例

```dart
// 使用ManualHealthSyncButton组件
ManualHealthSyncButton(
  taskInfo: {
    'task_id': 1,
    'task_type': 'steps',
    'required_value': 10000
  },
  onSyncSuccess: () {
    // 刷新任务列表
    _refreshTasksList();
  },
  onSyncError: (error) {
    // 显示错误提示
    _showErrorSnackBar(error);
  },
)
```

## 4. 错误代码说明

### 4.1 Tasks Dashboard API错误

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 500 | 服务器内部错误 | 检查日志，联系技术支持 |
| 401 | 认证失败 | 重新登录获取Token |
| 403 | 权限不足 | 检查用户权限配置 |

### 4.2 手动健康同步API错误

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 429 | 请求频率超限 | 等待冷却时间后重试 |
| 400 | 数据格式错误 | 检查健康数据完整性 |
| 403 | 健康权限不足 | 重新授权健康数据访问 | 