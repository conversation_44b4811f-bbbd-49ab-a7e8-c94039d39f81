# SweatMint 统一设备会话管理模型

**版本**: v2.0 (已审查并优化)
**创建时间**: 2025年1月  
**更新时间**: 2025年6月16日
**优先级**: 🔴 **P0 - 最高优先级**  
**目标**: 统一设备会话和健康会话管理，确保数据一致性和安全性。

---

## ⭐️ **v2.0 审查与优化总结**

经过严谨审查，v1.0 版本虽已提出统一模型的正确方向，但在字段设计和逻辑上仍存在冗余和待改进之处。v2.0 版本基于"**极致简化、职责单一**"的原则进行了以下核心优化：

1.  **彻底废除 `UserHealthBaseline` 模型**：原用于记录"首次授权"的 `UserHealthBaseline` 模型被完全移除。其职责（识别用户与设备的首次绑定关系）由 `UnifiedUserSession` 内部的一个 `is_first_on_device` 标志位取代，实现了模型的真正统一。
2.  **精简并重命名基线字段**：移除了多个与基线相关的冗余字段，只保留一组核心基线字段 `session_baseline_*`，其定义清晰明确：**本次会话开始时的设备健康数据**。
3.  **修正基线计算逻辑**：彻底废除了"读取昨日快照计算基线"的错误逻辑，严格遵循"**登录即重置**"原则，基线数据唯一来源是用户登录时提供的 `health_data`。
4.  **内聚安全逻辑**：将设备污染检测等核心安全检查，以内聚方法的形式实现在 `UnifiedUserSession` 模型管理器中，使模型职责更清晰。

---

## 🎯 **设计目标**

### **统一管理原则**
1. **单一数据源**：设备会话和健康会话统一管理，避免数据不一致。
2. **原子性操作**：登录/登出操作确保设备状态和健康状态同步更新。
3. **安全隔离**：用户切换时确保完全的数据隔离。
4. **状态一致性**：设备状态、健康状态、FCM状态保持一致。
5. **审计完整性**：所有操作都有完整的审计日志。

---

## 🏗️ **统一设备会话模型 (v2.0)**

### **核心模型：UnifiedUserSession**
```python
# /running/users/models.py

class UnifiedUserSessionManager(models.Manager):
    def get_active_session_for_user(self, user):
        """获取指定用户的活跃会话"""
        return self.filter(user=user, is_active=True).first()

    def handle_device_conflict(self, user, device_id):
        """处理设备冲突和用户多设备登录"""
        # 强制登出占用此设备的其他用户会话
        conflicting_device_sessions = self.filter(device_id=device_id, is_active=True).exclude(user=user)
        for session in conflicting_device_sessions:
            session.force_logout(reason='device_conflict', detail="新用户登录，强制登出旧会话")

        # 强制登出此用户在其他设备上的会话
        conflicting_user_sessions = self.filter(user=user, is_active=True).exclude(device_id=device_id)
        for session in conflicting_user_sessions:
            session.force_logout(reason='multi_device_login', detail="用户在其他设备登录，强制登出")

class UnifiedUserSession(models.Model):
    """
    v2.0 统一用户会话模型 - 整合设备、认证、健康数据管理
    """
    # 核心关联
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sessions')
    device_id = models.CharField(max_length=255, db_index=True)

    # 安全与认证信息
    device_fingerprint = models.JSONField(default=dict, help_text='设备指纹')
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    fcm_token = models.TextField(blank=True, help_text='FCM推送令牌')
    
    # --- 健康数据核心字段 ---
    # 【会话基线】：本次会话开始时，设备上的健康数据总量
    session_baseline_steps = models.PositiveIntegerField()
    session_baseline_distance = models.FloatField()
    session_baseline_calories = models.PositiveIntegerField()
    
    # 会话生命周期
    is_active = models.BooleanField(default=True, db_index=True)
    login_time = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    logout_time = models.DateTimeField(null=True, blank=True)
    logout_reason = models.CharField(max_length=50, blank=True)

    # --- 审计与安全标志 ---
    is_first_on_device = models.BooleanField(default=False, help_text="是否是此用户首次在此设备上创建会话")
    contamination_detected = models.BooleanField(default=False, help_text="创建时是否检测到设备污染")
    
    # 关联快照 (用于审计和跨天计算)
    logout_snapshot = models.ForeignKey(
        'DailyHealthSnapshot',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='closing_session'
    )
    
    objects = UnifiedUserSessionManager()

    class Meta:
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['device_id', 'is_active']),
        ]
        # 确保同一设备、同一用户在同一时间只能有一个活跃会话
        constraints = [
            models.UniqueConstraint(fields=['device_id'], condition=models.Q(is_active=True), name='unique_active_device'),
            models.UniqueConstraint(fields=['user'], condition=models.Q(is_active=True), name='unique_active_user'),
        ]

    def __str__(self):
        return f"{self.user.email} on {self.device_id[:8]}... ({'Active' if self.is_active else 'Inactive'})"

    def force_logout(self, reason, detail):
        """安全强制登出"""
        self.is_active = False
        self.logout_time = timezone.now()
        self.logout_reason = reason
        self.save()
        # FCM推送与审计日志记录逻辑...
        
    def calculate_increment(self, current_health_data):
        """计算当前会话的净增量"""
        return {
            'steps': max(0, current_health_data['steps'] - self.session_baseline_steps),
            'distance': max(0.0, current_health_data['distance'] - self.session_baseline_distance),
            'calories': max(0, current_health_data['calories'] - self.session_baseline_calories),
        }

```

### **关联模型：DailyHealthSnapshot (保持不变)**
`DailyHealthSnapshot` 模型的目标是为**特定时间点**（如登出、每日重置）的健康数据**创建存证**，其职责与会话管理不同，因此予以保留。

---

## 🔐 **v2.0 统一登录与会话创建流程**

```python
# /running/users/services/session_manager.py (示例)

from .models import UnifiedUserSession, DailyHealthSnapshot

def unified_secure_login_and_create_session(user, device_info, health_data):
    """
    v2.0 统一安全登录与会话创建流程 (原子性操作)
    """
    device_id = device_info['device_id']
    
    # 1. 处理设备冲突 (用户在其他设备登录, 或其他用户在此设备登录)
    UnifiedUserSession.objects.handle_device_conflict(user, device_id)
    
    # 2. 检测设备污染与首次使用状态
    contamination_detected = UnifiedUserSession.objects.filter(device_id=device_id).exclude(user=user).exists()
    is_first_on_device = not UnifiedUserSession.objects.filter(user=user, device_id=device_id).exists()
    
    # 3. 创建新的统一会话 (核心步骤)
    #    基线数据直接使用登录时提供的 health_data，严格遵循"登录即重置"原则。
    session = UnifiedUserSession.objects.create(
        user=user,
        device_id=device_id,
        device_fingerprint=device_info.get('fingerprint'),
        ip_address=device_info.get('ip'),
        fcm_token=device_info.get('fcm_token'),
        
        # 【核心】直接设置会话基线
        session_baseline_steps=health_data['steps'],
        session_baseline_distance=health_data['distance'],
        session_baseline_calories=health_data['calories'],
        
        is_active=True,
        is_first_on_device=is_first_on_device,
        contamination_detected=contamination_detected,
    )
    
    # 4. 记录安全审计日志...
    
    return session

def unified_secure_logout(session, health_data, reason='user_logout'):
    """
    v2.0 统一安全登出处理
    """
    # 1. (可选) 为本次登出创建一个最终快照作为存证
    snapshot = DailyHealthSnapshot.objects.create(
        user=session.user,
        device_id=session.device_id,
        snapshot_date=timezone.now().date(),
        snapshot_steps=health_data['steps'],
        snapshot_distance=health_data['distance'],
        snapshot_calories=health_data['calories'],
        snapshot_type='logout',
    )
    
    # 2. 结束会话，并关联登出快照
    session.is_active = False
    session.logout_time = timezone.now()
    session.logout_reason = reason
    session.logout_snapshot = snapshot
    session.save()
    
    # 3. 记录安全审计日志...
    return session
```

---

## 📊 **v2.0 数据迁移策略**

### **从现有模型迁移**
由于 `UserHealthBaseline` 被废除，迁移逻辑也变得更直接：
```python
def migrate_to_unified_sessions_v2():
    """
    从旧的 DeviceSession 和 UserHealthSession 迁移到 UnifiedUserSession
    """
    # 遍历所有活跃的旧设备会话，因为它们代表了一次登录事件
    for old_device_session in DeviceSession.objects.filter(is_active=True).iterator():
        user = old_device_session.user
        device_id = old_device_session.device_id
        
        # 查找对应的旧健康会话，以获取基线数据
        old_health_session = UserHealthSession.objects.filter(
            user=user, 
            device_id=device_id, 
            is_active=True
        ).first()
        
        if not old_health_session:
            # 如果没有对应的健康会话，无法确定基线，跳过此条迁移
            continue
            
        # 检查是否是首次使用
        is_first_on_device = not UnifiedUserSession.objects.filter(user=user, device_id=device_id).exists()

        # 创建统一会话
        UnifiedUserSession.objects.create(
            user=user,
            device_id=device_id,
            device_fingerprint=old_device_session.device_fingerprint,
            ip_address=old_device_session.ip_address,
            fcm_token=old_device_session.fcm_token,
            
            # 从旧健康会话迁移基线数据
            session_baseline_steps=old_health_session.daily_baseline_steps,
            session_baseline_distance=old_health_session.daily_baseline_distance,
            session_baseline_calories=old_health_session.daily_baseline_calories,
            
            is_active=True,
            login_time=old_device_session.created_time,
            is_first_on_device=is_first_on_device,
        )
```

---

## 📝 **v2.0 总结**

v2.0 统一设备会话管理模型通过**彻底的模型整合**和**逻辑简化**，实现了：

1.  **结构清晰**：单一会话模型，消除了数据冗余和不一致的风险。
2.  **逻辑正确**：严格遵循"登录即重置基线"的核心原则。
3.  **安全性内聚**：设备冲突与污染检测逻辑被整合到模型管理中。
4.  **可维护性提升**：更简单的模型和流程，极大降低了未来开发和维护的复杂度。

**这个 v2.0 统一模型是构建一个真正稳定、公平、可扩展的健康数据系统的正确基石。** 