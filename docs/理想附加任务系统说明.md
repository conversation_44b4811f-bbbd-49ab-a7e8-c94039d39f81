# 理想附加任务系统说明

## 1. 引言：附加任务的价值

附加任务是 SweatMint 平台激励用户参与、提升活跃度、引导特定行为及增强用户粘性的重要手段。它们为主线任务提供了额外的挑战和奖励维度，使用户的平台体验更加丰富和富有成就感。

一个设计精良的附加任务系统，能够有效地驱动用户增长、促进核心功能的采用，并最终提升平台的整体价值。

## 2. 核心设计原则

理想的附加任务系统应遵循以下核心原则：

*   **公平性 (Fairness)**: 确保所有用户在相似条件下，拥有平等的机会去完成任务并获得奖励。规则对所有人透明一致。
*   **可预测性 (Predictability)**: 任务的完成条件、顺序和奖励机制清晰明确，用户能够理解其行为与结果之间的联系，收益可预期。
*   **强激励性 (Strong Motivation)**: 任务目标设计合理，奖励吸引人，能够有效引导用户完成平台期望的关键行为。
*   **用户友好 (User-Friendly)**: 任务的发现、追踪和完成流程简单直观，用户能够轻松理解并参与。
*   **系统稳健与后端主导 (Robustness & Backend-Driven)**: 核心的完成逻辑、顺序判定和奖励计算由后端系统精确控制，保证数据准确性、流程稳定性和防作弊能力。
*   **灵活可配 (Flexibility & Configurability)**: 管理员能够方便快捷地创建、调整和管理附加任务，以适应不同的运营需求和市场变化。

## 3. 理想的附加任务生命周期

### 3.1. 任务定义与创建 (Admin Perspective)

*   **多样化的任务类型**: 管理员可以创建多种类型的附加任务，如邀请好友、完成特定操作组合、达成特定活跃度指标等。
*   **明确的参数配置**: 
    *   **任务目标**: 清晰量化的完成条件（如邀请X名有效好友）。
    *   **奖励机制**: 明确的奖励类型（SWMT、经验值、道具等）和数量。
    *   **有效期**: 任务的起止时间或持续周期。
    *   **目标用户**: 可根据用户等级、注册时间、行为标签等进行定向投放 (可选)。
    *   **静态优先级 (`Task.priority`)**: 管理员为任务设定的一个基础优先级，用于在分配或某些场景下的初步筛选。

### 3.2. 任务分配 (System & User Perspective)

*   **自动化分配**: 系统每日（或按特定周期）自动为符合条件的用户分配附加任务。
*   **个性化与上限**: 分配可考虑用户等级、历史行为等因素，并遵循用户可承接的附加任务数量上限。
*   **邀请类任务的特殊分配逻辑**: 
    *   当系统为用户分配多个邀请类附加任务时，会根据一个预定义的、公平且透明的多层排序规则（例如：优先按“所需邀请人数”少的，其次按管理员设定的“任务静态优先级”高的，最后按任务ID小的），为用户的这些邀请任务实例（`UserTask`）**预设一个明确的“完成优先级顺序号 (`completion_order`)”**。
    *   这个顺序号将决定在用户邀请数同时满足多个任务时，哪一个任务被优先完成。

### 3.3. 任务激活/开始 (User & System Perspective)

*   **邀请类任务**: 通常无需用户手动激活。一旦分配给用户，即视为“待处理 (`pending`)”状态，等待用户通过邀请行为来达成目标。
*   **其他行为类附加任务**: 
    *   部分任务可能也采用自动激活模式。
    *   部分任务可能需要用户在任务列表中明确点击“开始”或“接受”按钮，以确认参与意愿，此时任务状态可从 `pending` 变为 `active`。

### 3.4. 进度追踪 (User Perspective)

*   **可视化进度**: 用户可以在任务界面清晰地看到自己各项附加任务的当前进度（例如，“已邀请 2/5 好友”，“已完成每日任务 3/3 次”）。
*   **实时反馈**: 关键进度的更新应尽可能实时，增强用户的参与感。

### 3.5. 任务完成 (System & User Perspective)

*   **后端主导验证**: 任务的完成验证主要由后端系统自动进行，确保准确性和安全性。
*   **邀请类任务的完成机制**: 
    1.  **事件触发**: 当用户成功邀请一个新用户（或总有效邀请数增加）时，该事件会通知后端系统。
    2.  **资格检查**: 后端检查该用户当前所有“待处理”且“已达标”（即当前总邀请数 >= 任务所需邀请数）的邀请类附加任务。
    3.  **顺序判定**: 对所有符合上述条件的任务，严格按照它们在分配时被赋予的“完成优先级顺序号 (`completion_order`)”进行排序。
    4.  **唯一完成**: **系统仅将排序最靠前（顺序号最小）的那一个任务标记为“已完成 (`completed`)”**。
    5.  **后续处理**: 其他同样达标但顺序靠后的任务，在本次邀请事件中不会被完成，它们将等待用户下一次（或后续）新的邀请行为来重新评估完成资格。
    *   **核心理念**: 一次用户的邀请数增长事件，严格按预设顺序仅促成一个邀请任务的完成，避免了不确定的多重完成和收益混乱。
*   **其他行为类附加任务的完成**: 
    *   用户完成指定操作后，系统通过数据校验自动确认完成。
    *   或在某些情况下，用户可能需要手动点击“领取奖励”或“标记完成”，触发后端最终验证。

### 3.6. 奖励结算与发放 (System & User Perspective)

*   **即时或周期性结算**: 任务完成后，奖励应尽可能即时发放到用户账户。
*   **透明记录**: 用户可以在账户明细中查看到附加任务奖励的来源和数量。
*   **附加奖励的叠加效应 (如适用)**: 如果附加任务提供的是对其他任务（如每日任务）的百分比加成，其结算逻辑（例如在次日统一计算）也应清晰透明，并准确应用到用户的最终收益中。

## 4. 主要附加任务类型示例（侧重邀请任务）

### 4.1. 邀请好友任务

这是附加任务系统中的核心类型之一，其理想运作模式是：
*   **多样化的邀请目标**: 例如，“邀请X名好友”、“邀请Y名好友达到Z等级”、“邀请好友完成首次购买”等。
*   **清晰的完成顺序**: 通过 `completion_order` 确保公平和可预测。
*   **后端自动化处理**: 用户只需专注于邀请，系统自动处理后续的完成判定和奖励发放，体验流畅。

### 4.2. 行为激励任务 (示例)

*   **连续活跃任务**: 如“连续登录7天”、“本周完成10场对战”。
*   **功能探索任务**: 如“首次使用XX功能”、“在YY模块消费任意金额”。
*   **成就挑战任务**: 如“累计获得Z场胜利”、“达到新的里程碑等级”。
*   这类任务的完成通常依赖于系统对用户特定行为数据的追踪和验证。

## 5. 用户体验亮点

*   **目标明确**: 用户清楚知道需要做什么来获得额外奖励。
*   **过程公平**: 特别是邀请任务，完成顺序不再随机，多劳多得的原则得到更好体现。
*   **奖励及时**: 完成任务后能快速获得正反馈。
*   **激励有效**: 任务设计能激发用户的参与热情和探索欲望。
*   **系统可靠**: 用户相信任务的追踪和奖励发放是准确无误的。

## 6. 系统管理视角

*   **高效配置**: 管理员可以方便地创建、修改、上线/下线附加任务。
*   **数据洞察**: 系统提供任务参与度、完成率、奖励发放等关键数据的统计分析，辅助运营决策。
*   **风险可控**: 后端主导的逻辑能有效防止作弊行为，保障系统经济模型的稳定。
*   **灵活调整**: 可以根据运营活动或用户反馈，快速调整任务参数和策略。

---
**本说明旨在描绘一个理想化的附加任务系统框架，具体实现时需结合平台特性和技术条件进行细化。**