# VIP卡片文案状态管理文档

## 概述
本文档定义了首页顶部VIP卡片在不同状态下的文案显示规则。VIP卡片UI保持不变，仅根据用户VIP返还活动状态动态显示不同文案内容。

**重要说明**: VIP卡片仅对VIP用户显示（`vip_status.has_vip === true`），非VIP用户显示的是upgrade提示卡片，不适用此文档规则。

## 卡片基础结构
```
┌─────────────────────────────────────────────┐
│  [VIP勋章图标]  VIP等级文案              [Upgrade] │
│                收益文案                        │
│                状态描述文案                     │
│                [状态操作按钮]                   │
└─────────────────────────────────────────────┘
```

## 固定元素

### 右上角功能按钮
- **位置**: 卡片右上角（VIP勋章下方）
- **样式**: 白色背景，紫色文字，59w x 22h
- **显示逻辑**: 
  - **非最高等级VIP (Level 1-3)**: 
    - 文案: "Upgrade"
    - 功能: 跳转VIP购买页面 (`/vip-purchase`)
    - 字体: 10.sp
  - **最高等级VIP (Level 4+)**:
    - 文案: "VIP Info" 
    - 功能: 跳转VIP购买页面 (`/vip-purchase`) - 查看VIP详情
    - 字体: 9.sp (稍小以适应文字长度)
- **显示条件**: 所有VIP状态下都显示
- **说明**: 最高等级用户通过VIP购买页面可以查看VIP详情和帮助朋友购买

## VIP返还活动状态文案规则

### 1. 激活状态 (Active)
**适用条件**: VIP返还活动正在进行中，用户需要继续完成每日任务

**文案结构**:
```
等级文本: VIP {level}: DAY {completed_days}/{target_days} ⚡
收益文本: Earn {swmt_rate}x SWMT & {xp_rate}x XP daily  
状态描述: Complete {remaining_days} more for a full refund!
操作按钮: "Check Progress"
```

**示例**:
```
VIP 2: DAY 10/30 ⚡
Earn 1.5x SWMT & 1.3x XP daily
Complete 20 more for a full refund!
[Check Progress]
```

**营销重点**: 激励持续行动，展示进度成就感

### 2. 完成状态 (Completed)  
**适用条件**: VIP返还活动已完成，用户已获得全额退款

**文案结构**:
```
等级文本: VIP {level}: 🎉 FREE UPGRADE!
收益文本: Earn {swmt_rate}x SWMT & {xp_rate}x XP daily
状态描述: ✅ 100% Refunded! Your VIP was FREE!
操作按钮: "View Refund Details"
```

**示例**:
```
VIP 2: 🎉 FREE UPGRADE!
Earn 1.5x SWMT & 1.3x XP daily
✅ 100% Refunded! Your VIP was FREE!
[View Refund Details]
```

**营销重点**: 庆祝成功，强化免费价值感

### 3. 失败状态 (Failed)
**适用条件**: VIP返还活动失败，但VIP权益仍然有效

**文案结构**:
```
等级文本: VIP {level}: 💎 Premium Benefits Active
收益文本: Earn {swmt_rate}x SWMT & {xp_rate}x XP daily  
状态描述: Still enjoying premium benefits!
操作按钮: "Upgrade to VIP {next_level}"
```

**示例**:
```
VIP 2: 💎 Premium Benefits Active
Earn 1.5x SWMT & 1.3x XP daily
Still enjoying premium benefits!
[Upgrade to VIP 3]
```

**营销重点**: 减少挫败感，引导重新购买更高等级

### 4. 最高等级状态 (Ultimate)
**适用条件**: 用户已达到最高VIP等级

**文案结构**:
```
等级文本: VIP {level}: 👑 ULTIMATE VIP
收益文本: Earn {swmt_rate}x SWMT & {xp_rate}x XP daily
状态描述: Exclusive top-tier benefits unlocked!
操作按钮: "Elite Benefits"
右上角按钮: "VIP Center"
```

**示例**:
```
VIP 4: 👑 ULTIMATE VIP
Earn 2.0x SWMT & 1.8x XP daily
Exclusive top-tier benefits unlocked!
[Elite Benefits]  [VIP Info]
```

**营销重点**: 展示尊贵地位，强化身份认同

### 5. 等待激活状态 (Waiting to Start)
**适用条件**: 刚购买VIP，返还活动将于第二天开始

**文案结构**:
```
等级文本: VIP {level}: DAY 0/{target_days} ⏰
收益文本: Earn {swmt_rate}x SWMT & {xp_rate}x XP daily
状态描述: Challenge starts tomorrow - Get ready!
操作按钮: "Check Progress"
```

**示例**:
```
VIP 2: DAY 0/30 ⏰
Earn 1.5x SWMT & 1.3x XP daily
Challenge starts tomorrow - Get ready!
[Check Progress]
```

**营销重点**: 建立期待感，强化承诺

## 状态判断逻辑

### 基于Dashboard API实际返回数据 (严格按后端代码)

**API数据来源**: `/api/dashboard/home/<USER>
**数据来源**: UserVIPStatusView.get() 方法

### ⚠️ 关键字段名修正
**Dashboard Service代码中存在字段名不一致问题**:
- 期望 `'hasVip'` 但实际返回 `'has_vip'`
- 期望 `'userId'` 但实际返回 `'user_id'`

### 状态判断优先级 (基于实际API字段)
```python
# 基于UserVIPStatusView实际返回结构
vip_status = {
    "has_vip": boolean,                # 是否有VIP (必须为true才显示卡片)
    "vip_info": {                      # VIP详细信息
        "level_id": number,
        "name": string,               # VIP等级名称
        "level": number,              # VIP等级数字
        "upgrade_time": string,       # ISO格式时间戳
        "refund_active": boolean,     # 返还是否激活
        "vip_level": {                # VIP等级详情
            "id": number,
            "name": string,
            "level": number,
            "upgrade_fee": number,
            "swmt_bonus_rate": number,    # SWMT加成倍率 (如1.5)
            "exp_bonus_rate": number,     # XP加成倍率 (如1.3)
            "refund_enabled": boolean,
            "refund_days": number,        # 返还周期天数
            "swmt_bonus_percentage": number,  # SWMT加成百分比
            "exp_bonus_percentage": number    # XP加成百分比
        },
        "refund_progress": number,        # 返还进度 (仅当有活跃计划时)
        "refund_status_label": string,    # 返还状态标签
        "active_plans_count": number      # 活跃计划数量
    },
    "features": array,                    # VIP特权列表
    "additional_stats": object,           # 额外统计信息
    "refund_active": boolean,             # 返还激活状态 (顶级字段，与vip_info.refund_active相同)
    "refund_plans_count": number,         # 返还计划总数
    "active_plans_count": number          # 活跃计划数量 (顶级字段)
}
```

### 状态判断逻辑 (基于实际字段)
```javascript
// 1. 最高等级状态 (优先级最高)
if (vip_status.vip_info.level >= 4) {
    return 'ULTIMATE';
}

// 2. 等待激活状态 
if (vip_status.vip_info.refund_active && vip_status.vip_info.active_plans_count === 0) {
    return 'WAITING_TO_START';
}

// 3. 激活状态
if (vip_status.vip_info.refund_active && vip_status.vip_info.active_plans_count > 0) {
    return 'ACTIVE';
}

// 4. 完成/失败状态 (需要调用额外API获取详细状态)
// 调用 /api/vip/refund-plans/ 获取最新计划的status字段
if (latestPlanStatus === 'completed') {
    return 'COMPLETED';
}
if (latestPlanStatus === 'failed') {
    return 'FAILED';
}

// 5. 默认状态 (有VIP但无返还活动)
return 'NO_REFUND_ACTIVITY';
```

### 变量映射 (基于实际API字段)
- `{level}`: `vip_status.vip_info.level`
- `{name}`: `vip_status.vip_info.name`
- `{swmt_rate}`: `vip_status.vip_info.vip_level.swmt_bonus_rate`
- `{xp_rate}`: `vip_status.vip_info.vip_level.exp_bonus_rate`
- `{next_level}`: `vip_status.vip_info.level + 1`
- `{max_level}`: `4` (固定值)
- `{target_days}`: `vip_status.vip_info.vip_level.refund_days`
- `{completed_days}`: 需要从 `/api/vip/refund-plans/` 获取最新计划的completed_days
- `{remaining_days}`: `target_days - completed_days`

## 多VIP等级返还处理

### 同时参与多个返还活动
当用户同时参与多个VIP等级返还活动时：
- 显示**最高等级**的VIP信息 (基于 `vip_status.vip_info.level`)
- 状态基于**最高等级**的返还计划
- 操作按钮引导到详情页查看所有计划

### 示例场景
用户同时参与VIP1和VIP2返还活动：
```
VIP 2: DAY 15/25 ⚡
Earn 1.5x SWMT & 1.3x XP daily  
Multiple challenges active!
[Check All Progress]
```

## 实现注意事项

### ⚠️ Dashboard Service字段名修正
**必须修正Dashboard Service中的字段名检查**:
```python
# 当前错误的检查逻辑 (in dashboard/services.py line 441-443)
has_critical_data = (
    user_profile_data and 'userId' in user_profile_data and      # ❌ 应为 'user_id'
    vip_status_data and 'hasVip' in vip_status_data and         # ❌ 应为 'has_vip' 
    today_summary_data and 'earned_today' in today_summary_data
)

# 修正后的检查逻辑
has_critical_data = (
    user_profile_data and 'user_id' in user_profile_data and     # ✅ 正确
    vip_status_data and 'has_vip' in vip_status_data and        # ✅ 正确
    today_summary_data and 'earned_today' in today_summary_data
)
```

### API调用策略
1. **首页加载**: Dashboard API提供基本VIP状态和返还激活信息
2. **详细进度**: 仅当需要显示具体完成天数时，调用 `/api/vip/refund-plans/`
3. **缓存策略**: VIP状态数据缓存5分钟，返还计划数据实时获取

### 前端实现要点
1. **字段访问**: 使用正确的snake_case字段名访问数据
2. **状态判断**: 严格按照文档中的逻辑顺序判断
3. **错误处理**: 处理vip_info可能为null的情况
4. **性能优化**: 避免不必要的额外API调用

### 测试要点
1. **字段映射测试**: 验证所有变量能正确从API数据中获取
2. **状态切换测试**: 测试不同VIP状态下的文案显示
3. **边界情况测试**: 测试最高等级、无返还活动等情况
4. **API一致性测试**: 确保Dashboard API与VIP API数据一致

## 更新记录
- **Version 1.0** (2025-01-27): 初始版本，定义6种主要状态的文案规则
- **Version 1.1** (2025-01-27): 删除"未激活返还状态"，明确VIP卡片仅对VIP用户显示
- **Version 2.0** (2025-01-27): 
  - **重大修正**: 基于实际后端代码完全重写技术实现部分
  - 修正Dashboard Service字段名不一致问题 (hasVip→has_vip, userId→user_id)
  - 更新API字段映射，基于UserVIPStatusView实际返回结构
  - 修正状态判断逻辑，使用正确的字段路径
  - 添加Dashboard Service代码修正建议
- 后续版本更新将在此记录

---
**文档维护**: SweatMint 前端开发团队  
**最后更新**: 2025-01-27  
**版本**: 2.0 (基于实际代码修正版)