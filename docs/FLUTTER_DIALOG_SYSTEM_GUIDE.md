# 🎯 SweatMint Flutter 弹窗系统设计指南

## 📋 文档概述

**文档版本**: v1.0  
**创建日期**: 2025年01月20日  
**制定者**: 项目技术总监  
**适用范围**: SweatMint Flutter前端项目

本文档基于context7 MCP技术调研结果，为SweatMint应用制定**小巧、简洁大方、高性能**的弹窗解决方案，确保用户体验的一致性和开发效率的最大化。

---

## 🎨 设计原则

### 1. **简洁至上**
- 优先使用Flutter内置组件，避免不必要的第三方依赖
- 界面设计遵循Material Design规范
- 信息层级清晰，避免过度装饰

### 2. **性能优先**
- 轻量级实现，快速渲染
- 避免复杂动画影响性能
- 内存占用最小化

### 3. **用户体验一致性**
- 所有弹窗遵循统一的视觉和交互标准
- 与SweatMint主题系统完美集成
- 支持响应式设计和自适应

### 4. **开发者友好**
- 提供统一的API接口
- 完整的使用示例和文档
- 易于维护和扩展

---

## 🏗️ 架构设计

### 系统分层
```
UI展示层 (Dialog Widgets)
    ↓
业务逻辑层 (Dialog Services)
    ↓
主题适配层 (Theme Integration)
    ↓
动画交互层 (Animation System)
```

### 核心组件
1. **CommonDialogs** - 通用弹窗组件库
2. **DialogService** - 弹窗管理服务
3. **DialogTheme** - 弹窗主题适配
4. **DialogAnimations** - 弹窗动画系统

---

## 🚀 核心实现方案

### 1. 标准确认弹窗 (AlertDialog)

#### 基础实现
```dart
/// 🎯 标准确认弹窗
/// 用于：删除确认、操作确认、警告提示等
class ConfirmDialog {
  static Future<bool?> show({
    required BuildContext context,
    required String title,
    required String content,
    String? confirmText,
    String? cancelText,
    VoidCallback? onConfirm,
  }) {
    final theme = AppTheme.of(context);
    
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: theme.colors.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: Text(
          title,
          style: theme.typography.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colors.onSurface,
          ),
        ),
        content: Text(
          content,
          style: theme.typography.bodyMedium?.copyWith(
            color: theme.colors.onSurfaceVariant,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              cancelText ?? '取消',
              style: TextStyle(color: theme.colors.outline),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(true);
              onConfirm?.call();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: Text(
              confirmText ?? '确定',
              style: TextStyle(color: theme.colors.onPrimary),
            ),
          ),
        ],
      ),
    );
  }
}
```

#### 使用示例
```dart
// 删除确认
final result = await ConfirmDialog.show(
  context: context,
  title: '删除确认',
  content: '确定要删除这个任务吗？此操作不可撤销。',
  confirmText: '删除',
  onConfirm: () => _deleteTask(),
);
```

### 2. 底部选择弹窗 (BottomSheet)

#### 基础实现
```dart
/// 🎯 底部选择弹窗
/// 用于：选项选择、功能菜单、分享操作等
class BottomSelectionDialog {
  static Future<T?> show<T>({
    required BuildContext context,
    required String title,
    required List<DialogOption<T>> options,
  }) {
    final theme = AppTheme.of(context);
    
    return showModalBottomSheet<T>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: theme.colors.surface,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20.r),
          ),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 20.w,
          vertical: 24.h,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部拖拽指示器
            Container(
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: theme.colors.outline,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            SizedBox(height: 16.h),
            
            // 标题
            Text(
              title,
              style: theme.typography.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colors.onSurface,
              ),
            ),
            SizedBox(height: 20.h),
            
            // 选项列表
            ...options.map((option) => _buildOption(context, option, theme)),
            
            // 底部安全区域
            SizedBox(height: MediaQuery.of(context).padding.bottom),
          ],
        ),
      ),
    );
  }

  static Widget _buildOption<T>(
    BuildContext context,
    DialogOption<T> option,
    AppThemeData theme,
  ) {
    return InkWell(
      onTap: () {
        Navigator.of(context).pop(option.value);
        option.onTap?.call();
      },
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 16.w),
        margin: EdgeInsets.only(bottom: 8.h),
        child: Row(
          children: [
            if (option.icon != null) ...[
              Icon(
                option.icon,
                color: option.iconColor ?? theme.colors.primary,
                size: 24.sp,
              ),
              SizedBox(width: 12.w),
            ],
            Expanded(
              child: Text(
                option.title,
                style: theme.typography.bodyLarge?.copyWith(
                  color: option.textColor ?? theme.colors.onSurface,
                ),
              ),
            ),
            if (option.trailing != null) option.trailing!,
          ],
        ),
      ),
    );
  }
}

/// 弹窗选项数据模型
class DialogOption<T> {
  final String title;
  final T value;
  final IconData? icon;
  final Color? iconColor;
  final Color? textColor;
  final Widget? trailing;
  final VoidCallback? onTap;

  const DialogOption({
    required this.title,
    required this.value,
    this.icon,
    this.iconColor,
    this.textColor,
    this.trailing,
    this.onTap,
  });
}
```

#### 使用示例
```dart
// 分享功能选择
final result = await BottomSelectionDialog.show<String>(
  context: context,
  title: '分享到',
  options: [
    DialogOption(
      title: '微信好友',
      value: 'wechat',
      icon: Icons.wechat,
      iconColor: Colors.green,
    ),
    DialogOption(
      title: '朋友圈',
      value: 'moments',
      icon: Icons.group,
      iconColor: Colors.blue,
    ),
    DialogOption(
      title: '复制链接',
      value: 'copy',
      icon: Icons.copy,
    ),
  ],
);
```

### 3. 输入表单弹窗 (Custom Dialog)

#### 基础实现
```dart
/// 🎯 输入表单弹窗
/// 用于：文本输入、表单填写、设置修改等
class InputDialog extends StatefulWidget {
  final String title;
  final String? initialValue;
  final String? hintText;
  final String? confirmText;
  final String? cancelText;
  final TextInputType? keyboardType;
  final int? maxLength;
  final String? Function(String?)? validator;

  const InputDialog({
    Key? key,
    required this.title,
    this.initialValue,
    this.hintText,
    this.confirmText,
    this.cancelText,
    this.keyboardType,
    this.maxLength,
    this.validator,
  }) : super(key: key);

  static Future<String?> show({
    required BuildContext context,
    required String title,
    String? initialValue,
    String? hintText,
    String? confirmText,
    String? cancelText,
    TextInputType? keyboardType,
    int? maxLength,
    String? Function(String?)? validator,
  }) {
    return showDialog<String>(
      context: context,
      builder: (context) => InputDialog(
        title: title,
        initialValue: initialValue,
        hintText: hintText,
        confirmText: confirmText,
        cancelText: cancelText,
        keyboardType: keyboardType,
        maxLength: maxLength,
        validator: validator,
      ),
    );
  }

  @override
  State<InputDialog> createState() => _InputDialogState();
}

class _InputDialogState extends State<InputDialog> {
  late TextEditingController _controller;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    
    return Dialog(
      backgroundColor: theme.colors.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Text(
                widget.title,
                style: theme.typography.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colors.onSurface,
                ),
              ),
              SizedBox(height: 20.h),
              
              // 输入框
              TextFormField(
                controller: _controller,
                keyboardType: widget.keyboardType,
                maxLength: widget.maxLength,
                validator: widget.validator,
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    borderSide: BorderSide(
                      color: theme.colors.primary,
                      width: 2,
                    ),
                  ),
                ),
                autofocus: true,
              ),
              SizedBox(height: 24.h),
              
              // 按钮组
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      widget.cancelText ?? '取消',
                      style: TextStyle(color: theme.colors.outline),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  ElevatedButton(
                    onPressed: () {
                      if (_formKey.currentState?.validate() ?? false) {
                        Navigator.of(context).pop(_controller.text);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colors.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: Text(
                      widget.confirmText ?? '确定',
                      style: TextStyle(color: theme.colors.onPrimary),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

#### 使用示例
```dart
// 修改昵称
final newName = await InputDialog.show(
  context: context,
  title: '修改昵称',
  initialValue: currentName,
  hintText: '请输入新昵称',
  maxLength: 20,
  validator: (value) {
    if (value?.isEmpty ?? true) {
      return '昵称不能为空';
    }
    if (value!.length < 2) {
      return '昵称至少2个字符';
    }
    return null;
  },
);
```

### 4. 加载进度弹窗

#### 基础实现
```dart
/// 🎯 加载进度弹窗
/// 用于：网络请求等待、文件处理、数据同步等
class LoadingDialog {
  static OverlayEntry? _currentOverlay;

  static void show({
    required BuildContext context,
    String? message,
    bool barrierDismissible = false,
  }) {
    if (_currentOverlay != null) {
      hide(); // 先隐藏现有的
    }

    final theme = AppTheme.of(context);
    
    _currentOverlay = OverlayEntry(
      builder: (context) => Material(
        color: Colors.black54,
        child: Center(
          child: Container(
            padding: EdgeInsets.all(24.w),
            margin: EdgeInsets.symmetric(horizontal: 40.w),
            decoration: BoxDecoration(
              color: theme.colors.surface,
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colors.primary,
                  ),
                ),
                if (message != null) ...[
                  SizedBox(height: 16.h),
                  Text(
                    message,
                    style: theme.typography.bodyMedium?.copyWith(
                      color: theme.colors.onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_currentOverlay!);
  }

  static void hide() {
    _currentOverlay?.remove();
    _currentOverlay = null;
  }

  /// 带自动管理的异步操作包装
  static Future<T> wrap<T>({
    required BuildContext context,
    required Future<T> future,
    String? message,
  }) async {
    try {
      show(context: context, message: message);
      final result = await future;
      hide();
      return result;
    } catch (e) {
      hide();
      rethrow;
    }
  }
}
```

#### 使用示例
```dart
// 手动控制
LoadingDialog.show(context: context, message: '正在处理...');
try {
  await _performAsyncOperation();
} finally {
  LoadingDialog.hide();
}

// 自动管理
final result = await LoadingDialog.wrap(
  context: context,
  message: '正在上传文件...',
  future: _uploadFile(),
);
```

---

## 🎨 主题集成

### 弹窗主题配置
```dart
/// SweatMint弹窗主题扩展
extension DialogTheme on AppThemeData {
  DialogTheme get dialogTheme => DialogTheme(
    backgroundColor: colors.surface,
    titleTextStyle: typography.titleMedium?.copyWith(
      fontWeight: FontWeight.bold,
      color: colors.onSurface,
    ),
    contentTextStyle: typography.bodyMedium?.copyWith(
      color: colors.onSurfaceVariant,
    ),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16.r),
    ),
    elevation: 8,
  );
  
  BottomSheetThemeData get bottomSheetTheme => BottomSheetThemeData(
    backgroundColor: colors.surface,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(20.r),
      ),
    ),
    elevation: 16,
  );
}
```

---

## 📱 统一调用接口

### DialogService 服务类
```dart
/// 🎯 统一弹窗管理服务
/// 提供全局弹窗调用接口，简化使用流程
class DialogService {
  static final DialogService _instance = DialogService._internal();
  factory DialogService() => _instance;
  DialogService._internal();

  // 当前context引用
  static BuildContext? _context;
  
  static void init(BuildContext context) {
    _context = context;
  }

  /// 显示确认弹窗
  static Future<bool?> confirm({
    required String title,
    required String content,
    String? confirmText,
    String? cancelText,
  }) {
    if (_context == null) throw Exception('DialogService not initialized');
    
    return ConfirmDialog.show(
      context: _context!,
      title: title,
      content: content,
      confirmText: confirmText,
      cancelText: cancelText,
    );
  }

  /// 显示选择弹窗
  static Future<T?> select<T>({
    required String title,
    required List<DialogOption<T>> options,
  }) {
    if (_context == null) throw Exception('DialogService not initialized');
    
    return BottomSelectionDialog.show<T>(
      context: _context!,
      title: title,
      options: options,
    );
  }

  /// 显示输入弹窗
  static Future<String?> input({
    required String title,
    String? initialValue,
    String? hintText,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    if (_context == null) throw Exception('DialogService not initialized');
    
    return InputDialog.show(
      context: _context!,
      title: title,
      initialValue: initialValue,
      hintText: hintText,
      keyboardType: keyboardType,
      validator: validator,
    );
  }

  /// 显示加载弹窗
  static void showLoading({String? message}) {
    if (_context == null) throw Exception('DialogService not initialized');
    
    LoadingDialog.show(
      context: _context!,
      message: message,
    );
  }

  /// 隐藏加载弹窗
  static void hideLoading() {
    LoadingDialog.hide();
  }

  /// 异步操作包装
  static Future<T> wrapLoading<T>({
    required Future<T> future,
    String? message,
  }) {
    if (_context == null) throw Exception('DialogService not initialized');
    
    return LoadingDialog.wrap(
      context: _context!,
      future: future,
      message: message,
    );
  }
}
```

---

## 🔧 使用指南

### 1. 初始化服务
```dart
// 在main_layout_screen.dart中初始化
class MainLayoutScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // 初始化弹窗服务
    DialogService.init(context);
    
    return Scaffold(
      // ... 其他代码
    );
  }
}
```

### 2. 在ViewModel中使用
```dart
class TaskViewModel extends ChangeNotifier with ViewModelMixin {
  Future<void> deleteTask(String taskId) async {
    // 确认删除
    final confirmed = await DialogService.confirm(
      title: '删除任务',
      content: '确定要删除这个任务吗？',
      confirmText: '删除',
    );
    
    if (confirmed == true) {
      await executeAsyncAction(() async {
        await _taskService.deleteTask(taskId);
        // 刷新任务列表
        await loadTasks();
      });
    }
  }

  Future<void> shareTask(TaskModel task) async {
    final platform = await DialogService.select<String>(
      title: '分享任务',
      options: [
        DialogOption(title: '微信', value: 'wechat', icon: Icons.wechat),
        DialogOption(title: '朋友圈', value: 'moments', icon: Icons.group),
        DialogOption(title: '复制链接', value: 'copy', icon: Icons.copy),
      ],
    );
    
    if (platform != null) {
      await _shareTask(task, platform);
    }
  }
}
```

### 3. 在UI组件中使用
```dart
class TaskCard extends StatelessWidget {
  final TaskModel task;
  final VoidCallback? onDelete;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        title: Text(task.title),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            PopupMenuItem(
              child: Text('删除'),
              onTap: () async {
                final confirmed = await DialogService.confirm(
                  title: '删除确认',
                  content: '确定要删除"${task.title}"吗？',
                );
                
                if (confirmed == true) {
                  onDelete?.call();
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
```

---

## ⚡ 性能优化

### 1. 懒加载优化
```dart
/// 懒加载的复杂弹窗
class LazyComplexDialog extends StatelessWidget {
  static Future<void> show(BuildContext context) {
    return showDialog(
      context: context,
      builder: (context) => const LazyComplexDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      // 异步加载复杂内容
      future: _loadComplexContent(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        return Dialog(
          child: snapshot.data,
        );
      },
    );
  }

  Future<Widget> _loadComplexContent() async {
    // 模拟复杂内容加载
    await Future.delayed(Duration(milliseconds: 100));
    return ComplexDialogContent();
  }
}
```

### 2. 内存管理
```dart
/// 带自动清理的弹窗基类
abstract class ManagedDialog extends StatefulWidget {
  @override
  State<ManagedDialog> createState() => _ManagedDialogState();
}

class _ManagedDialogState extends State<ManagedDialog> {
  late List<StreamSubscription> _subscriptions;
  late List<AnimationController> _controllers;

  @override
  void initState() {
    super.initState();
    _subscriptions = [];
    _controllers = [];
  }

  @override
  void dispose() {
    // 自动清理资源
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  // ... 子类实现具体内容
}
```

---

## 🧪 测试指南

### 单元测试示例
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

void main() {
  group('DialogService Tests', () {
    testWidgets('confirm dialog shows and returns result', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              DialogService.init(context);
              return Scaffold(
                body: ElevatedButton(
                  onPressed: () async {
                    final result = await DialogService.confirm(
                      title: 'Test',
                      content: 'Test content',
                    );
                    // 处理结果
                  },
                  child: Text('Show Dialog'),
                ),
              );
            },
          ),
        ),
      );

      // 点击按钮显示弹窗
      await tester.tap(find.byType(ElevatedButton));
      await tester.pumpAndSettle();

      // 验证弹窗显示
      expect(find.text('Test'), findsOneWidget);
      expect(find.text('Test content'), findsOneWidget);

      // 点击确定按钮
      await tester.tap(find.text('确定'));
      await tester.pumpAndSettle();

      // 验证弹窗消失
      expect(find.text('Test'), findsNothing);
    });
  });
}
```

---

## 📝 最佳实践

### 1. **弹窗类型选择指南**
- **确认操作** → `ConfirmDialog`
- **选择菜单** → `BottomSelectionDialog`
- **文本输入** → `InputDialog`
- **等待加载** → `LoadingDialog`
- **复杂交互** → 自定义Dialog

### 2. **代码规范**
```dart
// ✅ 好的实践
final result = await DialogService.confirm(
  title: '删除确认',
  content: '确定要删除这个任务吗？此操作不可撤销。',
);

// ❌ 避免的做法
showDialog(
  context: context,
  builder: (context) => AlertDialog(
    title: Text('删除确认'),
    // 直接使用原生AlertDialog，缺乏主题一致性
  ),
);
```

### 3. **异常处理**
```dart
// ✅ 带异常处理的弹窗调用
try {
  final result = await DialogService.wrapLoading(
    future: _performAsyncOperation(),
    message: '正在处理...',
  );
  
  if (result.isSuccess) {
    // 成功处理
  }
} catch (e) {
  await DialogService.confirm(
    title: '操作失败',
    content: '${e.toString()}',
    confirmText: '知道了',
  );
}
```

### 4. **用户体验优化**
- 弹窗内容简洁明了
- 重要操作提供二次确认
- 加载状态给出明确提示
- 支持键盘ESC关闭
- 避免弹窗套弹窗

---

## 🔄 与现有系统集成

### 1. 与ViewModelMixin集成
```dart
class ExampleViewModel extends ChangeNotifier with ViewModelMixin {
  Future<void> performAction() async {
    final confirmed = await DialogService.confirm(
      title: '确认操作',
      content: '是否继续执行此操作？',
    );
    
    if (confirmed == true) {
      await executeAsyncAction(() async {
        // ViewModelMixin会自动管理loading状态
        await _service.performAction();
      });
    }
  }
}
```

### 2. 与NotificationService集成
```dart
class IntegratedDialogService extends DialogService {
  static Future<void> confirmWithNotification({
    required String title,
    required String content,
    required Future<void> Function() action,
    required String successMessage,
  }) async {
    final confirmed = await DialogService.confirm(
      title: title,
      content: content,
    );
    
    if (confirmed == true) {
      try {
        await DialogService.wrapLoading(
          future: action(),
          message: '正在处理...',
        );
        
        NotificationService.showSuccessToast(successMessage);
      } catch (e) {
        NotificationService.showErrorToast('操作失败：${e.toString()}');
      }
    }
  }
}
```

---

## 📚 扩展指南

### 自定义弹窗组件
```dart
/// 自定义成功提示弹窗
class SuccessDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onConfirm;

  const SuccessDialog({
    Key? key,
    required this.title,
    required this.message,
    this.onConfirm,
  }) : super(key: key);

  static Future<void> show({
    required BuildContext context,
    required String title,
    required String message,
    VoidCallback? onConfirm,
  }) {
    return showDialog(
      context: context,
      builder: (context) => SuccessDialog(
        title: title,
        message: message,
        onConfirm: onConfirm,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 成功图标
            Container(
              width: 64.w,
              height: 64.h,
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 40.sp,
              ),
            ),
            SizedBox(height: 16.h),
            
            // 标题
            Text(
              title,
              style: theme.typography.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colors.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            
            // 消息
            Text(
              message,
              style: theme.typography.bodyMedium?.copyWith(
                color: theme.colors.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            
            // 确定按钮
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onConfirm?.call();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colors.primary,
                minimumSize: Size(double.infinity, 48.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Text(
                '知道了',
                style: TextStyle(
                  color: theme.colors.onPrimary,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```

---

## 🎯 总结

SweatMint Flutter弹窗系统通过以下特点实现了**小巧、简洁大方、高性能**的设计目标：

### ✅ **优势总结**
1. **轻量级**：基于Flutter内置组件，无额外依赖
2. **高性能**：优化的渲染机制，快速响应
3. **一致性**：统一的视觉风格和交互模式
4. **易用性**：简化的API接口，开发高效
5. **可扩展**：模块化设计，易于定制和扩展

### 🚀 **应用场景**
- ✅ 用户操作确认
- ✅ 选项选择和菜单
- ✅ 表单输入和编辑
- ✅ 加载状态展示
- ✅ 成功/错误反馈

### 📋 **维护建议**
1. 定期审查弹窗使用情况，避免过度使用
2. 收集用户反馈，持续优化交互体验
3. 保持代码简洁，避免过度封装
4. 及时更新文档，确保团队知识同步

---

**文档维护**: 此文档应随着SweatMint项目的发展持续更新，确保弹窗系统始终保持最佳状态。 