# SweatMint 单设备强制登录系统 - FCM推送通知解决方案

## 📋 **问题现状分析**

**项目**: SweatMint 健身App  
**问题**: 两台设备可以同时登录使用，违反单设备强制登录策略  
**风险等级**: 🔴 **高危** - 影响健康数据准确性和任务公平性  
**分析时间**: 2024年1月  

---

## 🔍 **根本问题与WebSocket方案缺陷**

### **❌ WebSocket方案的致命缺陷**
1. **移动应用不适用**: 应用进入后台时WebSocket连接断开
2. **电池消耗严重**: 长期保持连接消耗大量电池
3. **网络不稳定**: 移动网络切换时连接不可靠
4. **无法覆盖全场景**: 应用完全关闭时无法接收通知
5. **扩展性差**: 大量用户连接会造成服务器压力

### **✅ FCM推送通知的优势**
1. **系统级服务**: Google/Apple系统维护，即使应用关闭也能收到
2. **电池优化**: 系统统一管理推送，耗电量极低  
3. **可靠性高**: 99.9%送达率，网络波动时自动重试
4. **跨平台支持**: iOS APNs和Android FCM统一处理
5. **无限扩展**: Google基础设施支持千万级用户

---

## 🎯 **FCM推送通知解决方案架构**

### **技术架构图**
```
设备A (iPhone)              后端服务                设备B (Android)
      │                      │                         │
      │─────登录请求─────────→│                         │
      │                      │                         │
      │                      │──设备冲突检测───────────→│
      │                      │                         │
      │                      │                         │
      │                      │──FCM推送通知────────────→│(强制登出)
      │                      │  ↓                      │
      │                      │ [Google FCM]            │
      │←────登录成功──────────│                         │
      │                      │                         │
      │←────FCM Token注册────│                         │
      │                      │                         │
      │─────API调用──────────→│                         │
      │                      │                         │
      │                      │──实时设备监控───────────│
```

### **核心技术组件**
```
1. FCM推送服务 (Firebase Cloud Messaging)
   ├── 服务端: Django + FCM Admin SDK
   ├── iOS客户端: Firebase Messaging + APNs
   └── Android客户端: Firebase Messaging + FCM

2. 设备会话管理
   ├── DeviceSession模型增强
   ├── FCM Token管理
   └── 推送消息队列

3. 设备冲突处理
   ├── 登录时冲突检测
   ├── 推送通知发送
   └── 客户端强制登出
```

---

## 🏗️ **技术实现方案**

### **阶段一: FCM服务端集成 (Django)**

#### **1.1 FCM Admin SDK配置**
```python
# requirements.txt 新增依赖
firebase-admin==6.4.0
pyfcm==1.5.4

# core/settings.py - FCM配置
import firebase_admin
from firebase_admin import credentials, messaging

# Firebase Admin SDK初始化
FIREBASE_CREDENTIALS = credentials.Certificate('path/to/firebase-service-account.json')
FIREBASE_APP = firebase_admin.initialize_app(FIREBASE_CREDENTIALS)

# FCM配置
FCM_SETTINGS = {
    'DEFAULT_SOUND': True,
    'DEFAULT_BADGE': True,
    'DEFAULT_TTL': 3600,  # 1小时TTL
    'DEVICE_CONFLICT_TOPIC': 'device_conflict',
}
```

#### **1.2 DeviceSession模型增强**
```python
# users/models.py - 设备会话模型增强
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

class DeviceSession(models.Model):
    """设备会话模型 - 支持FCM推送"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='device_sessions')
    device_id = models.CharField(max_length=255, db_index=True)
    device_name = models.CharField(max_length=255, blank=True)
    platform = models.CharField(max_length=20, choices=[('ios', 'iOS'), ('android', 'Android')])
    
    # FCM相关字段
    fcm_token = models.TextField(blank=True, help_text='Firebase Cloud Messaging Token')
    fcm_token_updated = models.DateTimeField(auto_now=True)
    
    # 设备指纹
    device_fingerprint = models.JSONField(default=dict, help_text='设备指纹信息')
    
    # 会话状态
    is_active = models.BooleanField(default=True)
    created_time = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    logout_time = models.DateTimeField(null=True, blank=True)
    logout_reason = models.CharField(max_length=50, blank=True)
    
    # IP和位置信息
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    location_info = models.JSONField(default=dict, blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['device_id']),
            models.Index(fields=['fcm_token']),
        ]
        unique_together = ['user', 'device_id']
    
    def force_logout(self, reason='device_conflict'):
        """强制登出设备"""
        self.is_active = False
        self.logout_time = timezone.now()
        self.logout_reason = reason
        self.save()
        
        # 发送FCM推送通知
        FCMDeviceManager.send_logout_notification(self)
    
    def update_fcm_token(self, token):
        """更新FCM Token"""
        self.fcm_token = token
        self.fcm_token_updated = timezone.now()
        self.save()
```

#### **1.3 FCM设备管理器**
```python
# users/services/fcm_device_manager.py
import logging
from firebase_admin import messaging
from django.conf import settings
from typing import List, Optional

logger = logging.getLogger(__name__)

class FCMDeviceManager:
    """FCM设备推送管理器"""
    
    @classmethod
    def send_logout_notification(cls, device_session):
        """发送设备登出通知"""
        if not device_session.fcm_token:
            logger.warning(f"Device {device_session.device_id} has no FCM token")
            return False
            
        try:
            # 构建推送消息
            message = messaging.Message(
                data={
                    'type': 'device_logout',
                    'device_id': device_session.device_id,
                    'reason': device_session.logout_reason,
                    'timestamp': str(device_session.logout_time),
                    'action': 'force_logout',
                },
                notification=messaging.Notification(
                    title='账号安全提醒',
                    body='您的账号已在其他设备登录',
                ),
                android=messaging.AndroidConfig(
                    priority='high',
                    notification=messaging.AndroidNotification(
                        icon='ic_notification',
                        color='#FF0000',
                        sound='default',
                        channel_id='device_security',
                    )
                ),
                apns=messaging.APNSConfig(
                    headers={'apns-priority': '10'},
                    payload=messaging.APNSPayload(
                        aps=messaging.Aps(
                            alert=messaging.ApsAlert(
                                title='账号安全提醒',
                                body='您的账号已在其他设备登录'
                            ),
                            badge=1,
                            sound='default',
                            category='device_security',
                        )
                    )
                ),
                token=device_session.fcm_token,
            )
            
            # 发送推送
            response = messaging.send(message)
            logger.info(f"FCM logout notification sent: {response}")
            
            # 记录推送日志
            FCMPushLog.objects.create(
                device_session=device_session,
                message_type='device_logout',
                fcm_response=response,
                status='sent'
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send FCM logout notification: {e}")
            FCMPushLog.objects.create(
                device_session=device_session,
                message_type='device_logout',
                error_message=str(e),
                status='failed'
            )
            return False
    
    @classmethod
    def send_batch_logout_notifications(cls, device_sessions: List):
        """批量发送登出通知"""
        if not device_sessions:
            return []
            
        messages = []
        for session in device_sessions:
            if session.fcm_token:
                message = messaging.Message(
                    data={
                        'type': 'device_logout',
                        'device_id': session.device_id,
                        'reason': 'device_conflict',
                        'timestamp': str(timezone.now()),
                    },
                    token=session.fcm_token,
                )
                messages.append(message)
        
        if messages:
            try:
                response = messaging.send_all(messages)
                logger.info(f"Batch FCM notifications sent: {response.success_count}/{len(messages)}")
                return response
            except Exception as e:
                logger.error(f"Failed to send batch FCM notifications: {e}")
                return None
    
    @classmethod
    def validate_fcm_token(cls, token: str) -> bool:
        """验证FCM Token有效性"""
        try:
            # 发送测试消息验证token
            message = messaging.Message(
                data={'type': 'token_validation'},
                token=token,
            )
            messaging.send(message, dry_run=True)
            return True
        except Exception:
            return False

# FCM推送日志模型
class FCMPushLog(models.Model):
    """FCM推送日志"""
    device_session = models.ForeignKey(DeviceSession, on_delete=models.CASCADE)
    message_type = models.CharField(max_length=50)
    fcm_response = models.TextField(blank=True)
    error_message = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=[
        ('sent', '已发送'),
        ('failed', '发送失败'),
        ('delivered', '已送达'),
    ])
    created_time = models.DateTimeField(auto_now_add=True)
```

#### **1.4 登录API增强**
```python
# authentication/views.py - 登录API增强
from rest_framework import status
from rest_framework.decorators import api_view
from rest_framework.response import Response
from users.services.fcm_device_manager import FCMDeviceManager
from users.models import DeviceSession

@api_view(['POST'])
def enhanced_login(request):
    """增强的登录API - 支持FCM推送"""
    username = request.data.get('username')
    password = request.data.get('password')
    device_fingerprint = request.data.get('device_fingerprint', {})
    fcm_token = request.data.get('fcm_token')
    
    # 用户认证
    user = authenticate(username=username, password=password)
    if not user:
        return Response({'error': 'Invalid credentials'}, status=401)
    
    device_id = device_fingerprint.get('device_id')
    
    # 检查设备冲突
    conflicting_sessions = DeviceSession.objects.filter(
        user=user,
        is_active=True
    ).exclude(device_id=device_id)
    
    if conflicting_sessions.exists():
        # 发送FCM推送通知到冲突设备
        for session in conflicting_sessions:
            session.force_logout('device_conflict')
        
        logger.info(f'Device conflict resolved: user {user.id} logged out from {len(conflicting_sessions)} devices')
    
    # 创建或更新设备会话
    device_session, created = DeviceSession.objects.update_or_create(
        user=user,
        device_id=device_id,
        defaults={
            'device_name': device_fingerprint.get('device_name', ''),
            'platform': device_fingerprint.get('platform', ''),
            'device_fingerprint': device_fingerprint,
            'fcm_token': fcm_token,
            'is_active': True,
            'ip_address': get_client_ip(request),
            'logout_reason': '',
        }
    )
    
    # 生成JWT Token
    refresh = RefreshToken.for_user(user)
    access_token = refresh.access_token
    
    return Response({
        'access_token': str(access_token),
        'refresh_token': str(refresh),
        'user': UserSerializer(user).data,
        'device_session_id': device_session.id,
    })

@api_view(['POST'])
def update_fcm_token(request):
    """更新FCM Token"""
    fcm_token = request.data.get('fcm_token')
    device_id = request.data.get('device_id')
    
    if not fcm_token or not device_id:
        return Response({'error': 'Missing required fields'}, status=400)
    
    try:
        device_session = DeviceSession.objects.get(
            user=request.user,
            device_id=device_id,
            is_active=True
        )
        
        # 验证FCM Token
        if FCMDeviceManager.validate_fcm_token(fcm_token):
            device_session.update_fcm_token(fcm_token)
            return Response({'status': 'success'})
        else:
            return Response({'error': 'Invalid FCM token'}, status=400)
            
    except DeviceSession.DoesNotExist:
        return Response({'error': 'Device session not found'}, status=404)
```

### **阶段二: Flutter客户端FCM集成**

#### **2.1 FCM依赖配置**
```yaml
# pubspec.yaml - FCM依赖
dependencies:
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  firebase_analytics: ^10.7.4
  flutter_local_notifications: ^16.3.0
  device_info_plus: ^10.1.0
  crypto: ^3.0.3

# Android配置 (android/app/google-services.json)
# iOS配置 (ios/Runner/GoogleService-Info.plist)
```

#### **2.2 FCM初始化和设备管理**
```dart
// core/services/fcm_service.dart
import 'dart:async';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:logger/logger.dart';

// 顶级函数：处理后台消息
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print("Handling a background message: ${message.messageId}");
  
  // 处理设备登出通知
  if (message.data['type'] == 'device_logout') {
    await FCMService._handleDeviceLogoutInBackground(message);
  }
}

class FCMService {
  static FCMService? _instance;
  static FCMService get instance => _instance ??= FCMService._();
  
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  final _logger = Logger();
  
  String? _fcmToken;
  StreamSubscription<RemoteMessage>? _foregroundSubscription;
  StreamSubscription<RemoteMessage>? _onMessageOpenedAppSubscription;
  
  FCMService._();
  
  /// 初始化FCM服务
  Future<bool> initialize() async {
    try {
      // 1. 初始化Firebase
      await Firebase.initializeApp();
      
      // 2. 设置后台消息处理器
      FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
      
      // 3. 请求通知权限
      final settings = await _messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );
      
      if (settings.authorizationStatus != AuthorizationStatus.authorized) {
        _logger.w('FCM: Notification permission denied');
        return false;
      }
      
      // 4. 初始化本地通知
      await _initializeLocalNotifications();
      
      // 5. 获取FCM Token
      _fcmToken = await _messaging.getToken();
      _logger.i('FCM Token: $_fcmToken');
      
      // 6. 监听Token刷新
      _messaging.onTokenRefresh.listen((newToken) {
        _fcmToken = newToken;
        _uploadFCMToken(newToken);
      });
      
      // 7. 设置消息监听器
      _setupMessageListeners();
      
      // 8. 上传Token到服务器
      if (_fcmToken != null) {
        await _uploadFCMToken(_fcmToken!);
      }
      
      _logger.i('FCM Service initialized successfully');
      return true;
      
    } catch (e) {
      _logger.e('FCM initialization failed', error: e);
      return false;
    }
  }
  
  /// 初始化本地通知
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const settings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    
    await _localNotifications.initialize(
      settings,
      onDidReceiveNotificationResponse: _onNotificationTap,
    );
    
    // 创建Android通知渠道
    if (Platform.isAndroid) {
      await _createNotificationChannels();
    }
  }
  
  /// 创建Android通知渠道
  Future<void> _createNotificationChannels() async {
    const deviceSecurityChannel = AndroidNotificationChannel(
      'device_security',
      '设备安全',
      description: '设备登录安全相关通知',
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('notification'),
    );
    
    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(deviceSecurityChannel);
  }
  
  /// 设置消息监听器
  void _setupMessageListeners() {
    // 前台消息处理
    _foregroundSubscription = FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _logger.d('Received foreground message: ${message.data}');
      _handleForegroundMessage(message);
    });
    
    // 应用从后台打开时的消息处理
    _onMessageOpenedAppSubscription = FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      _logger.d('App opened from background by message: ${message.data}');
      _handleMessageInteraction(message);
    });
    
    // 检查应用启动时的初始消息
    _checkInitialMessage();
  }
  
  /// 检查应用启动时的初始消息
  Future<void> _checkInitialMessage() async {
    final RemoteMessage? initialMessage = await _messaging.getInitialMessage();
    if (initialMessage != null) {
      _logger.d('App opened from terminated state by message: ${initialMessage.data}');
      _handleMessageInteraction(initialMessage);
    }
  }
  
  /// 处理前台消息
  void _handleForegroundMessage(RemoteMessage message) {
    switch (message.data['type']) {
      case 'device_logout':
        _handleDeviceLogout(message);
        break;
      default:
        _showLocalNotification(message);
    }
  }
  
  /// 处理设备登出消息
  void _handleDeviceLogout(RemoteMessage message) {
    _logger.w('Device logout notification received: ${message.data}');
    
    // 1. 清除本地认证状态
    TokenManager.clearTokens();
    
    // 2. 显示设备冲突对话框
    _showDeviceConflictNotification(message);
    
    // 3. 跳转到登录页
    _navigateToLogin();
  }
  
  /// 后台处理设备登出
  static Future<void> _handleDeviceLogoutInBackground(RemoteMessage message) async {
    // 后台只能清除Token，UI操作需要在前台处理
    await TokenManager.clearTokens();
  }
  
  /// 显示设备冲突通知
  void _showDeviceConflictNotification(RemoteMessage message) {
    const androidDetails = AndroidNotificationDetails(
      'device_security',
      '设备安全',
      channelDescription: '设备登录安全相关通知',
      importance: Importance.high,
      priority: Priority.high,
      color: Color(0xFFFF0000),
      icon: '@mipmap/ic_launcher',
    );
    
    const iosDetails = DarwinNotificationDetails(
      categoryIdentifier: 'device_security',
      sound: 'default.aiff',
    );
    
    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
    
    _localNotifications.show(
      999, // 设备安全通知使用固定ID
      '账号安全提醒',
      '您的账号已在其他设备登录，请重新登录',
      details,
      payload: 'device_logout',
    );
  }
  
  /// 跳转到登录页
  void _navigateToLogin() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (AppRoutes.navigatorKey.currentContext != null) {
        AppRoutes.navigatorKey.currentState?.pushNamedAndRemoveUntil(
          '/login',
          (route) => false,
        );
      }
    });
  }
  
  /// 显示本地通知
  void _showLocalNotification(RemoteMessage message) async {
    const androidDetails = AndroidNotificationDetails(
      'general',
      '通用通知',
      channelDescription: '应用通用通知',
      importance: Importance.defaultImportance,
      priority: Priority.defaultPriority,
    );
    
    const iosDetails = DarwinNotificationDetails();
    
    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
    
    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? '新消息',
      message.notification?.body ?? '',
      details,
      payload: message.data.toString(),
    );
  }
  
  /// 上传FCM Token到服务器
  Future<void> _uploadFCMToken(String token) async {
    try {
      final deviceId = await _getDeviceId();
      
      final response = await ApiClient.instance.post('/auth/update-fcm-token/', data: {
        'fcm_token': token,
        'device_id': deviceId,
      });
      
      if (response.statusCode == 200) {
        _logger.i('FCM Token uploaded successfully');
      } else {
        _logger.e('Failed to upload FCM Token: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Error uploading FCM Token', error: e);
    }
  }
  
  /// 通知点击处理
  void _onNotificationTap(NotificationResponse response) {
    final payload = response.payload;
    
    if (payload == 'device_logout') {
      _navigateToLogin();
    }
  }
  
  /// 消息交互处理
  void _handleMessageInteraction(RemoteMessage message) {
    switch (message.data['type']) {
      case 'device_logout':
        _handleDeviceLogout(message);
        break;
      default:
        // 处理其他类型的消息交互
        break;
    }
  }
  
  /// 获取设备ID
  Future<String?> _getDeviceId() async {
    final fingerprint = await DeviceFingerprint.generateFingerprint();
    return fingerprint['device_id'];
  }
  
  /// 获取当前FCM Token
  String? get fcmToken => _fcmToken;
  
  /// 清理资源
  void dispose() {
    _foregroundSubscription?.cancel();
    _onMessageOpenedAppSubscription?.cancel();
  }
}
```

#### **2.3 登录流程集成FCM**
```dart
// auth/data/datasources/auth_remote_datasource_impl.dart
@override
Future<LoginResponseDto> login(LoginRequestDto request) async {
  // 1. 确保FCM已初始化
  await FCMService.instance.initialize();
  
  // 2. 生成设备指纹
  final deviceFingerprint = await DeviceFingerprint.generateFingerprint();
  
  // 3. 获取FCM Token
  final fcmToken = FCMService.instance.fcmToken;
  
  // 4. 构建登录请求
  final enhancedRequest = request.copyWith(
    deviceFingerprint: deviceFingerprint,
    fcmToken: fcmToken,
  );
  
  try {
    final response = await _apiClient.post(
      ApiEndpoints.login,
      data: enhancedRequest.toJson(),
    );
    
    final loginResponse = LoginResponseDto.fromJson(response.data);
    
    return loginResponse;
    
  } catch (e) {
    if (e is DioException && e.response?.statusCode == 409) {
      // 设备冲突处理
      final conflictData = e.response?.data;
      throw DeviceConflictException(
        message: conflictData?['message'] ?? '设备冲突',
        conflictInfo: conflictData,
      );
    }
    rethrow;
  }
}
```

#### **2.4 应用生命周期集成**
```dart
// main.dart - 集成FCM服务
class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeServices();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    FCMService.instance.dispose();
    super.dispose();
  }

  void _initializeServices() async {
    // 1. 初始化Firebase和FCM
    await FCMService.instance.initialize();
    
    // 2. 初始化TokenManager
    await TokenManager.initialize();
    
    // 3. 检查登录状态
    final isLoggedIn = await TokenManager.isLoggedIn();
    
    if (isLoggedIn) {
      // 4. 设置GlobalAuthService导航上下文
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (AppRoutes.navigatorKey.currentContext != null) {
          GlobalAuthService.setNavigatorContext(AppRoutes.navigatorKey.currentContext!);
        }
      });
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        // 应用回到前台，检查FCM状态
        _checkFCMStatus();
        break;
      case AppLifecycleState.paused:
        // 应用进入后台
        break;
      case AppLifecycleState.detached:
        // 应用完全关闭
        FCMService.instance.dispose();
        break;
    }
  }

  void _checkFCMStatus() async {
    // 检查FCM Token是否需要更新
    final currentToken = FCMService.instance.fcmToken;
    if (currentToken != null) {
      // 更新服务器上的FCM Token
      await _updateFCMTokenOnServer(currentToken);
    }
  }

  Future<void> _updateFCMTokenOnServer(String token) async {
    try {
      final deviceId = await _getDeviceId();
      await ApiClient.instance.post('/auth/update-fcm-token/', data: {
        'fcm_token': token,
        'device_id': deviceId,
      });
    } catch (e) {
      print('Failed to update FCM token: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return ScreenUtilInit(
          designSize: const Size(375, 812),
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (context, child) {
            return MaterialApp.router(
              title: 'SweatMint',
              routerConfig: AppRoutes.createRouter(authProvider),
              theme: AppTheme.lightTheme,
              localizationsDelegates: const [
                AppLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: const [
                Locale('en', ''),
                Locale('zh', ''),
              ],
            );
          },
        );
      },
    );
  }
}
```

---

## 🧪 **测试验证方案**

### **FCM推送测试**
```python
# running/tests/test_fcm_device_conflict.py
import pytest
from django.test import TestCase
from users.services.fcm_device_manager import FCMDeviceManager
from users.models import DeviceSession
from unittest.mock import patch, Mock

class FCMDeviceConflictTest(TestCase):
    
    @patch('firebase_admin.messaging.send')
    def test_send_logout_notification(self, mock_send):
        """测试FCM登出通知发送"""
        # 模拟FCM发送成功
        mock_send.return_value = 'projects/test/messages/123456'
        
        # 创建测试设备会话
        device_session = DeviceSession.objects.create(
            user=self.user,
            device_id='test_device_123',
            fcm_token='test_fcm_token_123',
            platform='android',
            is_active=True,
        )
        
        # 发送登出通知
        result = FCMDeviceManager.send_logout_notification(device_session)
        
        # 验证结果
        self.assertTrue(result)
        mock_send.assert_called_once()
        
        # 验证设备会话状态
        device_session.refresh_from_db()
        self.assertFalse(device_session.is_active)
        self.assertEqual(device_session.logout_reason, 'device_conflict')
    
    def test_login_device_conflict_resolution(self):
        """测试登录时设备冲突解决"""
        # 创建已存在的设备会话
        existing_session = DeviceSession.objects.create(
            user=self.user,
            device_id='existing_device',
            fcm_token='existing_fcm_token',
            is_active=True,
        )
        
        # 新设备登录
        response = self.client.post('/auth/login/', {
            'username': 'testuser',
            'password': 'testpass',
            'device_fingerprint': {
                'device_id': 'new_device',
                'platform': 'ios',
            },
            'fcm_token': 'new_fcm_token',
        })
        
        # 验证登录成功
        self.assertEqual(response.status_code, 200)
        
        # 验证旧设备被踢出
        existing_session.refresh_from_db()
        self.assertFalse(existing_session.is_active)
```

### **Flutter端测试**
```dart
// test/fcm_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

void main() {
  group('FCMService Tests', () {
    late FCMService fcmService;
    
    setUp(() {
      fcmService = FCMService.instance;
    });
    
    test('should initialize FCM successfully', () async {
      // Given
      when(Firebase.initializeApp()).thenAnswer((_) async => mockFirebaseApp);
      
      // When
      final result = await fcmService.initialize();
      
      // Then
      expect(result, true);
      verify(Firebase.initializeApp()).called(1);
    });
    
    test('should handle device logout message', () async {
      // Given
      final logoutMessage = RemoteMessage(
        data: {
          'type': 'device_logout',
          'device_id': 'test_device',
          'reason': 'device_conflict',
        },
      );
      
      // When
      fcmService._handleDeviceLogout(logoutMessage);
      
      // Then
      verify(TokenManager.clearTokens()).called(1);
    });
  });
}
```

---

## 📊 **实施时间表**

### **第一周: FCM后端集成**
- [ ] Firebase Admin SDK配置和集成
- [ ] DeviceSession模型增强
- [ ] FCMDeviceManager服务实现
- [ ] 登录API FCM支持

### **第二周: Flutter FCM集成**
- [ ] Firebase SDK配置
- [ ] FCMService实现
- [ ] 推送通知处理
- [ ] 应用生命周期集成

### **第三周: 测试和优化**
- [ ] FCM推送测试
- [ ] 设备冲突场景测试
- [ ] 性能优化
- [ ] 错误处理完善

### **第四周: 部署和监控**
- [ ] 生产环境FCM配置
- [ ] 推送通知监控
- [ ] 用户测试
- [ ] 文档完善

---

## 🎯 **预期效果对比**

### **❌ WebSocket方案问题**
- 应用后台时连接断开
- 电池消耗大
- 网络不稳定
- 无法覆盖应用关闭场景

### **✅ FCM方案优势**
- **即时推送**: 设备冲突检测<1秒通知
- **可靠性高**: 99.9%推送送达率，Google基础设施保障
- **电池友好**: 系统级推送，耗电量极低
- **全场景覆盖**: 应用关闭也能接收强制登出通知
- **无限扩展**: 支持百万级用户同时在线

### **技术指标**
- 🎯 设备冲突通知延迟: <1秒
- 🎯 推送送达成功率: >99%
- 🎯 用户体验满意度: >95%
- 🎯 电池消耗: <1% (相比WebSocket节省90%)

---

## 📝 **总结**

**FCM推送通知方案完美解决SweatMint单设备登录问题**：

### **🚀 技术优势**
1. **移动原生**: 专为移动应用设计，完美适配iOS/Android
2. **系统级服务**: Google/Apple维护，稳定可靠
3. **即时推送**: 毫秒级设备冲突通知
4. **全场景覆盖**: 前台、后台、关闭状态都能收到

### **📱 用户体验**
1. **无感知切换**: 设备冲突时立即收到系统通知
2. **清晰提示**: 友好的冲突处理界面
3. **安全可靠**: 防止健康数据源混乱和任务重复奖励

### **⚡ 性能表现**
1. **低功耗**: 比WebSocket节省90%电池消耗
2. **高可靠**: 99.9%推送送达率
3. **可扩展**: 支持百万级用户

**🎉 FCM方案是移动应用设备冲突处理的最佳实践，SweatMint将拥有行业领先的单设备强制登录能力！**

---

**最后更新**: 2024年1月  
**技术负责人**: SweatMint开发团队  
**优先级**: 🔴 **P0 - 最高优先级** 