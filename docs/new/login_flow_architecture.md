# SweatMint 登录流程架构设计文档

## 📋 目录

1. [架构概览](#架构概览)
2. [登录验证流程](#登录验证流程)
3. [会话管理机制](#会话管理机制)
4. [健康数据基线管理](#健康数据基线管理)
5. [步骤1-5执行逻辑](#步骤1-5执行逻辑)
6. [权限管理体系](#权限管理体系)
7. [错误处理机制](#错误处理机制)
8. [网络异常处理](#网络异常处理)
9. [关键组件说明](#关键组件说明)

---

## 🏗️ 架构概览

### 设计原则
- **数据实时性优先**：健康数据严禁使用缓存，确保数据准确性
- **用户体验至上**：提供友好的错误提示和实时进度反馈
- **离线容错能力**：网络异常时保证数据安全和功能可用
- **原子性保证**：关键操作具备事务性，确保数据一致性

### 核心架构组件
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SplashScreen  │───▶│ AuthProvider    │───▶│ MainLayout      │
│   启动页面      │    │ 认证管理        │    │ 主界面          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│HealthDataFlow   │    │UnifiedUserSession│    │HealthPermission │
│ Coordinator     │    │ 会话管理        │    │ Status          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 数据流向
1. **用户登录** → 认证验证 → 会话创建
2. **健康权限** → 权限检查 → 基线初始化
3. **数据同步** → 步骤1-4执行（SplashScreen：认证→权限→跨天→同步） → 步骤5执行（MainLayoutScreen：UI加载和权限引导） → UI更新
4. **异常处理** → 错误提示 → 重试机制

**[已知问题] 分离执行流程**：
- SplashScreen：步骤1-4（认证检查→权限检查→跨天和基线→健康数据同步）
- MainLayoutScreen：步骤5（UI数据加载和权限引导）

---

## 🔐 登录验证流程

### 认证逻辑
**目的**：验证用户身份，建立安全会话

**核心组件**：
- `AuthProvider` - 认证状态管理
- `AuthService` - 认证业务逻辑
- `TokenStorageService` - JWT令牌安全存储

**验证流程**：
```
用户输入凭据 → 本地验证 → 服务器验证 → 令牌获取 → 状态更新
     │              │           │           │           │
     ▼              ▼           ▼           ▼           ▼
  格式检查      网络连接检查   API调用    JWT存储    UI跳转
```

**关键特性**：
- **双重验证**：本地格式验证 + 服务器身份验证
- **令牌管理**：自动刷新机制，防止会话过期
- **安全存储**：使用 `flutter_secure_storage` 保护敏感信息
- **状态同步**：认证状态实时更新到UI层

---

## 🔄 会话管理机制

### 会话生命周期
**目的**：维护用户会话连续性，处理跨天和超时场景

**核心组件**：
- `UnifiedUserSession` - 统一会话模型
- `V141FlowStateController` - 流程状态控制
- `PhaseGateController` - 阶段门控制

### 会话类型
1. **新建会话**：首次登录或跨天后创建
2. **继续会话**：应用重启后恢复
3. **超时会话**：4小时无活动后重置

### 跨天处理机制
```
新加坡时间00:00 → 检测跨天 → 结算昨日数据 → 重置基线 → 创建新会话
       │              │            │            │           │
       ▼              ▼            ▼            ▼           ▼
   时区转换        会话标记      任务奖励      基线清零    新会话ID
```

**关键逻辑**：
- **时区处理**：基于新加坡时间（UTC+8）进行跨天判断
- **数据结算**：自动处理前一天的任务奖励和数据归档
- **基线重置**：清零健康数据基线，重新开始计算
- **会话连续性**：确保用户体验的平滑过渡

---

## 📊 健康数据基线管理

### 基线概念
**目的**：建立健康数据计算的起始点，确保增量计算准确性

**基线类型**：
- **步数基线**：当日首次获取的步数值
- **距离基线**：当日首次获取的距离值
- **卡路里基线**：当日首次获取的卡路里值

### 基线生命周期
```
登录成功 → 权限检查 → 获取当前值 → 设置基线 → 增量计算
    │          │           │           │          │
    ▼          ▼           ▼           ▼          ▼
 会话创建   权限验证    实时数据     基线存储   任务进度
```

### 基线管理策略
1. **初始化时机**：用户首次登录或跨天后
2. **更新策略**：仅在特定条件下更新（跨天、权限变更）
3. **存储方式**：本地安全存储 + 服务器备份
4. **一致性保证**：本地与服务器数据同步验证

**注意事项**：
- 基线数据严禁缓存，每次都从健康平台实时获取
- 跨天时必须重置基线，避免数据累积错误
- 权限变更时需要重新初始化对应的基线

---

## ⚙️ 步骤1-5执行逻辑

### 执行架构
**目的**：标准化健康数据同步流程，确保时序和原子性

**核心组件**：
- `HealthDataFlowService` - 流程执行引擎
- `HealthDataFlowCoordinator` - 统一调度器
- `SyncProgressIndicator` - 进度反馈组件

**[已知问题] 分离执行架构**：
- **SplashScreen阶段**：执行步骤1-4（通过`executeSteps1to4Only()`）
- **MainLayoutScreen阶段**：执行步骤5（通过`executeStep5Only()`）
- **注意**：并非完整的1-5连续执行，而是分离在两个不同的界面中执行

### 步骤详解

#### 步骤1：认证状态检查 (≤600ms)
- **目标**：验证用户身份，确保访问安全
- **输出**：认证状态和用户信息
- **关键方法**：`_executeStep1AuthCheck()`
- **执行位置**：SplashScreen期间

#### 步骤2：健康权限检查 (≤800ms) - v14.1架构优化
- **目标**：检查健康数据访问权限状态，建立统一状态源
- **输出**：权限状态映射表（steps/distance/calories）+ PhaseGateController状态存储
- **关键方法**：`_executeStep2PermissionCheck()`
- **执行位置**：SplashScreen期间
- **v14.1新增**：权限结果存储到PhaseGateController，避免后续重复检查
- **状态管理**：确保权限状态在所有组件间保持一致性

#### 步骤3：跨天检查和基线重置 (≤2000ms)
- **目标**：处理会话连续性和基线管理
- **输出**：基线数据和跨天处理结果
- **关键方法**：`_executeStep3CrossDayAndBaseline()`
- **执行位置**：SplashScreen期间

#### 步骤4：健康数据同步 (≤1000ms)
- **目标**：获取健康数据并同步到服务器
- **输出**：同步结果和任务状态
- **关键方法**：`_executeStep4HealthDataSync()`
- **执行位置**：SplashScreen期间

#### 步骤5：UI数据加载和权限引导 (≤200ms) - v14.1状态一致性保障
- **目标**：更新界面显示和处理权限引导，确保权限状态显示准确性
- **输出**：UI状态和用户提示
- **v14.1机制**：使用PhaseGateController存储的步骤2权限结果，避免状态不一致
- **权限弹窗**：HealthAuthorizationDialogManager接收权限参数，确保显示准确性
- **关键方法**：`_executeStep5aUIDataLoading()` + `_executeStep5bPermissionGuide()`
- **执行位置**：MainLayoutScreen中单独执行
- **[已知问题] 分离实现**：步骤5分为5a（UI数据加载）和5b（权限引导）两个子步骤

### 时序要求
```
[已知问题] 分离执行时序：
├── SplashScreen阶段（步骤1-4）: ≤4400ms
│   ├── 步骤1: ≤600ms  (14%)
│   ├── 步骤2: ≤800ms  (18%)
│   ├── 步骤3: ≤2000ms (45%)
│   └── 步骤4: ≤1000ms (23%)
└── MainLayoutScreen阶段（步骤5）: ≤200ms
    └── 步骤5: ≤200ms  (100%)
```

**注意**：由于分离执行架构，总耗时不是连续的，而是分布在两个不同的界面加载阶段。步骤3的跨天检查和基线重置是最耗时的操作。

### 原子性保证
- **事务边界**：每个步骤内部保证原子性
- **回滚机制**：步骤失败时自动回滚到安全状态
- **状态一致性**：使用 `PhaseGateController` 确保跨界面状态同步
- **并发控制**：防止多个流程同时执行造成冲突
- **[已知问题] 跨界面状态管理**：步骤1-4与步骤5之间需要通过PhaseGateController进行状态传递

---

## 🔑 权限管理体系 (v14.1统一状态源架构)

### 权限架构
**目的**：管理健康数据访问权限，确保合规性和用户隐私，保障状态一致性

**权限类型**：
- **步数权限**：`steps` - 访问步数数据
- **距离权限**：`distance` - 访问距离数据
- **卡路里权限**：`calories` - 访问卡路里数据

### v14.1权限状态管理流程
```
步骤2权限检查 → PhaseGateController存储 → 步骤5权限弹窗
      │                    │                    │
      ▼                    ▼                    ▼
   实时检查            统一状态源           准确显示
```

### v14.1权限管理策略
1. **统一状态源**：PhaseGateController作为权限状态的唯一可信来源
2. **避免重复检查**：权限结果一次检查，多处使用
3. **状态一致性**：确保所有组件看到相同的权限状态
4. **传参机制**：权限状态通过参数传递，避免重新检查
5. **独立性原则**：每种权限独立管理，互不影响
6. **用户友好**：提供清晰的权限说明和引导

**v14.1核心组件**：
- `PhaseGateController` - 权限状态统一存储
- `HealthPermissionProvider` - 权限状态检查
- `HealthAuthorizationDialogManager` - 权限引导弹窗（支持权限参数）
- `PermissionGuideWidget` - 权限说明界面

**v14.1修复成果**：
- ✅ 权限状态显示一致性：弹窗显示与实际权限状态完全一致
- ✅ 避免重复检查：步骤2检查后，后续组件直接使用结果
- ✅ 状态传递优化：权限结果通过参数传递，提升性能

---

## 🚨 错误处理机制

### 错误分类体系
**目的**：提供用户友好的错误反馈和智能重试机制

**核心组件**：
- `ErrorMessageManager` - 错误消息管理器
- `UserFriendlyError` - 用户友好错误模型

### 错误类型
1. **网络错误**：离线、超时、服务器异常
2. **认证错误**：登录失败、会话过期、账户问题
3. **权限错误**：健康数据权限、位置权限
4. **数据错误**：同步冲突、数据无效、版本冲突
5. **系统错误**：应用更新、设备兼容、存储空间

### 错误处理流程
```
异常发生 → 错误分类 → 消息转换 → 用户提示 → 重试策略
    │          │          │          │          │
    ▼          ▼          ▼          ▼          ▼
 技术异常   错误代码   友好消息   UI显示    自动重试
```

### 重试策略
- **智能重试**：根据错误类型自动设置重试延迟
- **指数退避**：网络错误采用指数退避算法
- **用户控制**：提供手动重试选项
- **最大限制**：防止无限重试消耗资源

---

## 🌐 网络异常处理

### 离线处理架构
**目的**：确保网络异常时的数据安全和功能可用性

**核心组件**：
- `NetworkStatusService` - 网络状态监听
- `OfflineDataQueueService` - 离线数据队列
- `DataConflictDetector` - 数据冲突检测
- `ConflictResolutionService` - 冲突解决服务

### 离线处理流程
```
网络断开 → 数据入队 → 本地存储 → 网络恢复 → 自动同步 → 冲突解决
    │          │          │          │          │          │
    ▼          ▼          ▼          ▼          ▼          ▼
 状态检测   队列管理   安全存储   状态监听   批量上传   数据合并
```

**[已知问题] 定时同步实现机制**：
- **EventTriggeredSyncService**：实际的2分钟定时器实现
- **TIMER_SYNC场景**：在HealthDataFlowCoordinator中仅为空实现
- **委托机制**：EventTriggeredSyncService通过`_delegateToHealthDataFlowService()`委托执行
- **[待实现] 轻量化同步**：TIMER_SYNC场景应实现轻量化的步骤3-4同步逻辑

### 冲突解决策略
1. **服务器优先**：默认策略，服务器数据为准
2. **时间戳优先**：使用最新时间戳的数据
3. **版本优先**：使用更高版本的数据
4. **数据合并**：智能合并本地和服务器数据
5. **手动审查**：复杂冲突需要用户确认

**数据保护机制**：
- **本地队列**：网络异常时数据安全存储
- **优先级管理**：重要数据优先同步
- **完整性验证**：确保数据传输完整性
- **自动重试**：网络恢复后自动处理队列

---

## 🔧 关键组件说明

### 1. HealthDataFlowCoordinator
**职责**：统一调度健康数据流程执行
- **场景管理**：
  - **LOGIN场景**：[已知问题] 仅执行步骤1-4，而非完整流程
  - **APP_RESTART场景**：[已知问题] 仅执行步骤1-4，与LOGIN场景实现相同
  - **APP_RESUME场景**：[已知问题] 声称有"智能检查"但实际与APP_RESTART相同
  - **TIMER_SYNC场景**：[已知问题] 仅有空实现，实际通过EventTriggeredSyncService委托执行
- **进度反馈**：实时更新执行进度和状态
- **异常处理**：统一的错误处理和重试机制

### 2. PhaseGateController (v14.1统一状态源)
**职责**：管理v14.1流程的状态和阶段控制，提供统一状态源
- **状态跟踪**：记录每个步骤的执行状态
- **阶段门控**：确保步骤按序执行
- **v14.1新增 - 权限状态存储**：存储步骤2的权限检查结果
- **状态一致性保障**：确保权限状态在所有组件间保持一致
- **跨界面状态传递**：负责在SplashScreen和MainLayoutScreen之间传递步骤1-4的完成状态
- **避免重复检查**：提供权限结果缓存，避免重复的原生权限查询

### 3. SyncProgressIndicator
**职责**：提供用户友好的进度反馈界面
- **实时进度**：显示当前执行步骤和进度百分比
- **状态反馈**：成功、失败、进行中状态显示
- **用户交互**：提供重试和取消操作选项

### 4. HealthAuthorizationDialogManager (v14.1权限参数支持)
**职责**：管理健康数据权限授权弹窗，确保权限状态显示准确性
- **v14.1新增 - 权限参数支持**：接收权限状态参数，避免重复检查
- **状态一致性**：确保弹窗显示的权限状态与实际权限状态一致
- **降级处理**：当权限参数不可用时，自动降级到重新检查模式
- **用户引导**：提供清晰的权限授权指导和说明
- **智能显示**：根据权限状态智能决定是否显示弹窗

### 5. ErrorMessageManager
**职责**：统一管理错误消息和用户提示
- **错误转换**：技术错误转换为用户友好消息
- **多语言支持**：支持中英文错误提示
- **智能建议**：提供具体的解决方案指导

---

## 📝 总结

SweatMint登录流程架构采用分层设计，v14.1版本重点优化了权限管理体系，确保了：

1. **可靠性**：完善的错误处理和重试机制
2. **用户体验**：友好的进度反馈和错误提示
3. **数据安全**：离线处理和冲突解决能力
4. **可维护性**：清晰的组件职责和接口设计
5. **扩展性**：模块化架构支持功能扩展
6. **v14.1新增 - 状态一致性**：权限状态在所有组件间保持完全一致

**v14.1权限管理优化成果**：
- ✅ **权限状态一致性**：修复了权限弹窗显示错误的问题
- ✅ **统一状态源**：PhaseGateController作为权限状态的唯一可信来源
- ✅ **避免重复检查**：权限结果一次检查，多处使用，提升性能
- ✅ **传参机制**：权限状态通过参数传递，避免状态不一致
- ✅ **降级处理**：完善的容错机制，确保系统稳定性

**架构现状**：
- **分离执行模式**：步骤1-4在SplashScreen，步骤5在MainLayoutScreen
- **权限管理优化**：v14.1版本已完成权限状态管理的重大优化
- **定时同步机制分散**：实际实现在EventTriggeredSyncService，TIMER_SYNC场景为空
- **智能检查未实现**：APP_RESUME场景的智能检查功能仅为占位符

**[待实现] 优化方向**：
1. **统一场景执行逻辑**：实现APP_RESUME的真正智能检查
2. **完善TIMER_SYNC场景**：实现轻量化的定时同步逻辑
3. **优化分离执行**：考虑是否需要统一为完整的1-5步骤流程
4. **增强状态管理**：改进跨界面的状态传递机制

该架构为SweatMint提供了基本的登录和健康数据同步功能，但仍有多个方面需要进一步完善和优化。
