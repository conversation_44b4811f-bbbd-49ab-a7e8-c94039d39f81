﻿SweatMint 会话与基线核心概念 v2.0 (最优混合模型)
文档版本: v2.0
核心理念: 关注点分离——将会话内增量的时间准确性与用户每日总量的完整性分离，通过不同机制分别保障，以实现数据既精确又无遗漏。
🎯 1. 核心概念与名词解释
1.1. 会话 (Session)
* 定义：一次连续的应用活跃使用周期。
* 开始 (Start)：当用户登录或冷启动App时，一个新会话立即开始。这是会话生命周期中唯一需要被精确记录的事件。
* 结束 (End)：一个逻辑上的概念，而非技术事件。
   * 主动结束：用户主动登出。
   * 被动结束：App被用户或系统关闭、或崩溃。此时，会话的生命周期被视为在最后一次成功同步数据的时间点自然结束。我们不再关心也不追认确切的关闭时间。
1.2. 基线 (Baseline)
* 定义：会话的“零点”，是计算会话内增量的起点。
* 作用：确保会话内产生的增量，只包含用户在使用App期间的真实活动。
* 计算方式：在会话开始的瞬间，通过 HKStatisticsQuery 获取从当天00:00到会话开始时间的健康数据累计值。
   * 基线 = HKStatisticsQuery(当天00:00, 会话开始时间)
1.3. 会话增量 (Session Increment)
* 定义：在一次单个会话中，用户产生的实际健康数据增量。
* 计算方式：在会话进行期间，通过周期性同步（例如每2分钟）获取的最新数据总量，减去本次会话的基线。
   * 会话增量 = 最新设备总量 - 本次会话基线
* 最终值：当会话结束后（无论是主动还是被动），其对每日总量的最终贡献，就是它生命周期内最后一次成功计算出的增量值。
1.4. 当日总增量 (Daily Total Increment)
* 定义：用户最终在App内看到的、当天累计的运动增量。
* 计算方式：将当天所有已结束会话的“会话增量”进行累加。
   * 当日总增量 = Σ (所有会话的会话增量)
* 数据完整性：通过下一个会话的基线来自动校准，确保完整无缺。
⚙️ 2. 方案详解：新模型的运作流程
本方案彻底摒弃了“追认关闭时间”和“会话状态缓存”的复杂逻辑，流程简化为：
步骤一：会话开始
1. 用户启动App。
2. 立即创建一个新会话。
3. 立即确定并记录本次会话的基线。
步骤二：会话进行中
1. App处于前台时，按固定周期（如2分钟）执行“健康数据同步”。
2. 每次同步都重新计算一次当前会话的增量，并将此增量值上报或在本地更新。
步骤三：会话结束（被动关闭场景）
1. 用户在任意时间点将App强制关闭。
2. 系统无需做任何处理。该会话的生命周期在逻辑上已经结束。
3. 它对“当日总增量”的最终贡献，就是步骤二中最后一次成功上报的那个增量值。
步骤四：数据完整性保障（下次启动）
1. 用户再次启动App。
2. 系统直接回到步骤一，开启一个全新的会话，并获取一个全新的基线。
3. 关键：在上一个会话被动结束后到本次会话开始前，用户产生的所有健康数据（例如，在您例子中8:04到9:00之间的数据），都会被自动包含在本次新会话的基线中。
4. 这意味着，这部分数据虽然不计入任何一次“会话增量”，但它作为后续计算的起点，确保了用户的每日总运动量是连续且完整的。
✨ 3. 流程示例（使用您的例子）
| 时间 | 事件 | 系统操作与数据状态 |
| 08:00:00 | App启动 | 会话1开始。


计算基线: HKStatisticsQuery(00:00, 08:00) 返回100。


会话1基线 = 100。 |
| 08:02:00 | 数据同步 | HKStatisticsQuery 返回总量120。


会话1增量 = 120 - 100 = 20。


当日总增量 = 20。 |
| 08:04:00 | 数据同步 | HKStatisticsQuery 返回总量150。


会话1增量 = 150 - 100 = 50。


当日总增量 = 50。 |
| 08:05:00 | App被强关 | 无需任何操作。会话1在逻辑上已结束。


其最终贡献被确定为 50。 |
| ... | 用户在App外活动 | ... |
| 09:00:00 | App再次启动 | 会话2开始。


计算基线: HKStatisticsQuery(00:00, 09:00) 返回800。


会话2基线 = 800。


此时，当日总增量依然是 50 (来自已结束的会话1)。 |
| 09:02:00 | 数据同步 | HKStatisticsQuery 返回总量830。


会话2增量 = 830 - 800 = 30。


当日总增量 = 50 (来自会话1) + 30 (来自会话2) = 80。 |
⚠️ 4. 关键注意点
1. 逻辑简化：不再需要任何用于“追认”会话结束的本地缓存（如"status": "ACTIVE"）。这极大地简化了客户端的逻辑和代码复杂度。
2. 关注点分离：“会话增量”负责回答“用户在用App时走了多少步？”，它的数据必须在时间上精确归因。“当日总增量”负责回答“用户今天总共走了多少步？”，它的数据必须完整。新方案完美地同时实现了这两个目标。
3. 无数据丢失：任何在App关闭期间产生的数据，都会成为下一次会话基线的一部分，从而被无缝地纳入到每日的整体运动数据流中，确保了数据的100%完整性。
4. 后端职责：后端的任务就是累加每次上报的、已确定的“会话增量”，从而得出“当日总增量”。
这个v2.0模型解决了原方案的数据准确性谬误，同时保持了数据的完整性，是目前最健壮、最精确、也更易于实现的解决方案。