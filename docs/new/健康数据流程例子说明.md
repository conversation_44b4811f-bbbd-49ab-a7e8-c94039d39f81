用户a只授权了步数，卡路里消耗、距离是没有授权的。健康系统是有2分钟的定时同步功能的
8:00 用户a，在新加坡时间8:00登陆app。
启动app后的步骤如下：
步骤1:检查认证状态。是否有掉，有掉就登出状态，没掉就进行下一步
步骤2:检查健康数据权限状态。本次用HKStatisticsQuery检查到步数已经授权，卡路里消耗、距离没有授权。
步骤3:检查跨天检查和基线重置。本次是当天的8:00，不需要跨天计算。检查到新的会话1开始，用HKStatisticsQuery请求0:00-8:00的步数，ios返回500，所以会话1步数的基线确定为500。
步骤4:健康数据进行同步。会话1内的当前步数健康数据值用HKStatisticsQuery请求0:00-8:00的步数健康数据总和，ios返回500，则会话1内实际运动增量为：500-500=0。
步数今日实际运动增量为：0
步骤5:首页数据ui加载。此时前端首页的Overview模块的步数应该显示0，距离是“--”，卡路里消耗是“--”。由于步骤2检查到距离、卡路里消耗是没有授权，弹出app的健康数据授权框！
8:02，由于用户a一直在app前端，触发2分钟定时同步。步骤如下：
步骤1:检查认证状态。是否有掉，有掉就登出状态，没掉就进行下一步
步骤2:检查健康数据权限状态。本次用HKStatisticsQuery检查到步数已经授权，卡路里消耗、距离没有授权。
步骤3:检查跨天检查和基线重置。本次是当天的8:02，不需要跨天计算。会话1未结束，不需要对基线进行重置。
步骤4:健康数据进行同步。会话1内的当前步数健康数据值用HKStatisticsQuery请求0:00-8:02的步数健康数据总和，ios返回550，则会话1内实际运动增量为：550-500=50。
步数今日实际运动增量为：50
步骤5:首页数据ui加载。此时前端首页的Overview模块的步数应该显示50，距离是“--”，卡路里消耗是“--”。由于步骤2检查到距离、卡路里消耗是没有授权，弹出app的健康数据授权框（如果健康数据授权框已经存在，则无需重复弹窗）！由于用户在前端首页，健康数据动量api有变动，触发事件驱动式前端静默刷新。

然后，用户a将app缩小到后台运行，在9:05分再次唤醒app，
9:05 此时应该是唤醒app后的步骤是：
步骤1:检查认证状态。是否有掉，有掉就登出状态，没掉就进行下一步
步骤2:检查健康数据权限状态。本次用HKStatisticsQuery检查到步数已经授权，卡路里消耗、距离没有授权。
步骤3:检查跨天检查和基线重置。本次是当天的9:05，不需要跨天计算。会话1未结束，健康数据授权没有变化，不需要对基线进行重置。
步骤4:健康数据进行同步。会话1内的当前步数健康数据值用HKStatisticsQuery请求0:00-9:05的步数健康数据总和，ios返回1020，则会话1内实际运动增量为：1020-500=520。
步数今日实际运动增量为：520
步骤5:首页数据ui加载。此时前端首页的Overview模块的步数应该显示520，距离是“--”，卡路里消耗是“--”。由于上一步检查到卡路里消耗、距离是没有授权，弹出app的健康数据授权框（如果授权框有在，就不需要弹出授权框）！

随后用户a 缩小了app到后端，并且手机健康数据开启了距离权限

9:10 用户a唤醒了app，按照唤醒app的步骤是：
步骤1:检查认证状态。是否有掉，有掉就登出状态，没掉就进行下一步
步骤2:检查健康数据权限状态。本次用HKStatisticsQuery检查到步数、距离已经授权，卡路里消耗没有授权。
步骤3:检查跨天检查和基线重置。本次是当天的9:10，不需要跨天计算。会话1未结束，步数依旧是授权状态，步数的基线不需要重置。上一步检查到新增距离授权，并且会话1内没有距离基线信息，用HKStatisticsQuery请求0:00-8:00的距离数，ios返回3.0，所以会话1距离的基线确定为3.0。
步骤4:健康数据进行同步。会话1内的当前步数健康数据值用HKStatisticsQuery请求0:00-9:10的步数健康数据总和，ios返回1020，则会话1内实际运动增量为：1020-500=520。
会话1内的当前距离健康数据值用HKStatisticsQuery请求0:00-9:10的距离健康数据总和，iso返回的是3.0。则会话1内距离实际运动增量为3.0-3.0=0.0
今日步数实际运动增量为：520
今日距离实际运动增量为：0.0
步骤5:首页数据ui加载。此时前端首页的Overview模块的步数应该显示520，距离是“0.0”，卡路里消耗是“--”。由于上一步检查到卡路里消耗、距离是没有授权，弹出app的健康数据授权框（如果授权框有在，就不需要弹出授权框）！

随后用户a关闭了app，本次会话结算，会话1实际产生的健康动量数据为：步数-520、距离-0.0、卡路里消耗-“--”。

用户a在当天的23:55分登陆，重启了新的会话2。

23:55 启动app后的步骤如下：
步骤1:检查认证状态。是否有掉，有掉就登出状态，没掉就进行下一步
步骤2:检查健康数据权限状态。本次用HKStatisticsQuery检查到步数、距离权限都已经授权，卡路里消耗没有授权。
步骤3:检查跨天检查和基线重置。本次是当天的23:55，不需要跨天计算。检查到新的会话2开始，用HKStatisticsQuery请求0:00-23:55的步数，ios返回2000，所以会话2步数的基线确定为2000。用HKStatisticsQuery请求0:00-23:55的距离，ios返回5.4公里，所以会话2距离的基线确定为5.4。
步骤4:健康数据进行同步。会话2内的当前步数健康数据值用HKStatisticsQuery请求0:00-23:55的步数健康数据总和，ios返回2000，则会话2内步数实际运动增量为：2000-2000=0。
会话2内的当前距离健康数据值用HKStatisticsQuery请求0:00-23:55的距离健康数据总和，ios返回3.4，则会话2内距离实际运动增量为：5.4-5.4=0.0。
步数今日实际运动增量为：520+0=520
距离今日实际运动增量为：0.0+0.0=0.0
步骤5:首页数据ui加载。此时前端首页的Overview模块的步数应该显示520，距离是0.0，卡路里消耗是“--”。由于上一步检查到卡路里消耗是没有授权，弹出app的健康数据授权框！

用户a在当天的23:56分缩小app，到后端，然后在新加坡时间第二天的0:10唤醒app
0:10 唤醒app后的步骤如下：
步骤1:检查认证状态。是否有掉，有掉就登出状态，没掉就进行下一步
步骤2:检查健康数据权限状态。本次用HKStatisticsQuery检查到步数、距离权限都已经授权，卡路里消耗没有授权。
步骤3:检查跨天检查和基线重置。本次由于是0:10检查到跨天，并且会话还是昨天的会话2，所以必须异步处理昨天会话2内未完成的数据和今日的基线重置。对步数、距离进行昨天0:00-23:59的HKStatisticsQuery请求，ios分别返回步数2050、距离5.6。昨日会话2内步数实际运动增量为：2050-2000=50。昨日会话2内距离实际运动增量为：5.6-5.4=0.2
所以更新：
昨日的步数实际运动增量为：520+50=570
昨日的距离实际运动增量为：0.0+0.2=0.2
更新清楚昨天的数据，检查每日任务是否完成。是否需要对昨日的每日任务状态进行更新
上面就是夸天昨日的数据处理与更新，下面就开始进行今日的基线的重置。对步数、距离进行新一天0:00-0:10的HKStatisticsQuery请求，ios分别返回步数10、距离0.1。确定新的一天的步数基线为10，距离的基线为0.1
步骤4:健康数据进行同步。新一天的会话1内的当前步数健康数据值用HKStatisticsQuery请求0:00-0:10的步数健康数据总和，ios返回10，则新一天会话1内步数实际运动增量为：10-10=0。新一天的会话1内的当前距离健康数据值用HKStatisticsQuery请求0:00-0:10的距离健康数据总和，ios返回0.1，则新一天会话1内距离实际运动增量为：0.1-0.1=0.0
步数新一天实际运动增量为：0
距离新一天实际运动增量为：0.0
步骤5:首页数据ui加载。此时前端首页的Overview模块显示的内容都应该刷新为新一天的数据，步数应该显示0，距离是0.0，卡路里消耗是“--”。由于上一步检查到卡路里消耗是没有授权，弹出app的健康数据授权框！

上面的例子很好的解释了包括：app启动流程、app唤醒流程、基线的处理、健康数据权限的检查、会话内的实际运动增量计算、当天的实际运动增量计算、app健康数据权限框的显示、跨天的计算。
注意点：
1:第一步都是进行认证状态的检查，根据双token的认证，如果没有认证状态失败，跳转到登陆页面，用户登陆后，再进行剩余4个步骤的运行。
2:权限的检查三种权限独立使用的HKStatisticsQuery，但是startDate到endDate，请求的是7天的数据总和，请求到数据error 参数为 nil并且result 参数是一个有效的 HKStatistics 对象，则表示用户对该健康数据权限授权成功。
2:基线的确定使用的是HKStatisticsQuery，请求startDate到endDate的健康数据总和，startDate固定新加坡当天的时间0:00，endDate是基线确定请求的时间！前提还是该健康数据权限有授权。
3:会话的时效要确定清楚！会话有效的情况下，基线是不能重置的。会话时效后必须重置基线。
4:健康数据同步的时候，针对的是有授权的健康数据，并且HKStatisticsQuery请求的是当天的startDate（新加坡实际0:00）到endDate（当天同步时间）的健康数据总和。
5:同一会话内的健康数据同步更新后，使用的是最新的会话实际动量数据。
6:健康数据同步后，如果用户还在首页，并且当天的健康数据动量有变动，首页必须事件驱动式前端静默刷新，文档在/Users/<USER>/Documents/worker/sweatmint/docs/更新后文档/WEBSOCKET_事件驱动前端静默刷新.md
7:启动步骤要做好合理的异步处理！减少启动的时间
8:最好是高效、统一的前后端的优化处理，减少代码冗余。
9:
- 前端上传累计总量 → 后端验证数据 → 计算增量 → 更新任务进度
- 增量计算公式: `delta = new_total - baseline`
- 所有验证和计算在后端完成，前端只负责数据采集
### **基线管理的本质**
- **基线 = 会话内运动增量的起点**，用来确定"0"的位置
- **会话内增量 = 当前设备累计总量 - 会话基线**
- **任务进度 = 所有会话内增量的累计**
- **当天运动增量 = 会话1增量 + 会话2增量 + 会话3增量...**

### **基线更新的前提条件**
1. **用户重启app并登录** + **健康数据授权检查通过**
2. **跨天时自动重置基线**（即使用户没有重启app）
3. **权限独立管理**：步数、距离、卡路里基线独立存储和计算

### **异常数据处理原则** 🔥
1. **先验证后计算**：前端数据传递到后端后，立即进行数据校验，只有正常数据才进行增量计算
2. **异常数据隔离**：异常数据不参与会话内增量计算，但会被标记记录
3. **后台可监控**：管理员可通过后台查看所有异常数据及其产生原因
4. **数据完整性**：异常数据处理不影响正常用户的使用体验


另外一个例子：
会话创建、基线的创建要理解清楚！！！同一个手机，例如只授权步数（各个健康数据都是独立的以这个为例子），当天8:00登陆app，此时我的步数显示的是120，
我登陆a账号，会话开始（8:00），创建基线（HKStatisticsQuery(0:00, 8:00) 为120），然后缩小app或者打开app反正就是没有关闭app，也就是会话继续，走了100步，到08:30，唤醒app健康数据同步，应该是HKStatisticsQuery(0:00, 8:30) 为220，后端计算此次会话的运动增量为220-120=100！然后我退出了a账号，标记会话结束！用户a在数据库中能查看到，此次会话8:00-8:30，步数增量是100，基线8:00创建为120
然后我登陆用户b，发现用户b登陆，此时为用户b创建会话，8:30，重置用户b基线（HKStatisticsQuery(0:00, 8:30)）为220 ，又运动了半小时，130步，健康数据同步，HKStatisticsQuery(0:00, 9:00)为350，后端计算用户b此次会话（8:30-9:00）步数的增量为：350-220=130！数据库能看到用户b，会话（8:30-9:00），会话持续（我未登出），基线8:30创建为220，步数增量为130。然后我关闭app，标记用户b此次会话结束。
然后我在9:30重新打开app，我的手机目前步数是500，打开app后，自动登陆用户b，那为用户b创建会话，9:30,重置基线（HKStatisticsQuery(0:00, 9:30) 为500），没有运动，我在9:31登出用户b。此次标记用户b会话结束！9:31又登陆用户a，为用户a创建会话9:31，为用户a重置基线（HKStatisticsQuery(0:00, 9:31) 为500），然后运动到9:40，健康数据同步，HKStatisticsQuery(0:00, 9:40)为560，后端计算用户a此次会话（9:31-9:40）步数的增量为：560-500=60，此次会话步数实际增量是60，那么今天用户a的实际步数增量为100+60=160！！！