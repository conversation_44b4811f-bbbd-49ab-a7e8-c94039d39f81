# SweatMint 3步骤流程重构实施方案 v14.1 - 详细清单

**文档版本**: v1.0  
**创建时间**: 2024年12月  
**适用范围**: SweatMint健康激励应用3步骤流程重构实施  
**制定者**: 项目技术总监  
**依据文档**: SweatMint登录与健康数据完整流程指南v14.1

---

## 📋 项目概述

### 🎯 重构目标
基于SweatMint登录与健康数据完整流程指南v14.1，实施3步骤流程架构重构：
- **流程简化**: 将复杂的5阶段流程抽象为3步骤业务流程（认证验证→数据准备→界面加载）
- **代码优化**: 重构HealthDataFlowService（6000+行）为模块化架构，减少50%复杂度
- **性能提升**: 优化启动流程，减少重复检查，提升30%启动速度
- **稳定性增强**: 统一异常处理机制，降低40%异常率

### 🏗️ 重构范围
**前端Flutter应用**:
- ✅ **HealthDataFlowService模块化**: 拆分为独立的权限、基线、同步服务
- ✅ **PhaseGateController映射集成**: 建立3步骤到5阶段的映射关系
- ✅ **统一异常处理框架**: 创建分级异常处理和自动恢复机制
- ✅ **代码清理优化**: 安全删除冗余逻辑，保留核心功能

**后端Django系统**:
- ✅ **保持现有架构**: 不修改后端实现，确保API兼容性
- ✅ **接口标准化**: 维护现有API契约和数据结构

### 🔒 重构约束
- **数据真实性原则**: 健康数据不使用缓存，实时获取确保数据准确性
- **系统稳定性优先**: 避免破坏性变更，确保现有功能正常运行
- **向后兼容性**: 保持现有API接口和数据结构的兼容性
- **渐进式实施**: 分阶段实施，每个阶段都有完整的测试验证

### 📊 预期效果
**性能指标**:
- 启动速度提升: 30%
- 代码复杂度降低: 50%
- 异常率降低: 40%
- 内存使用减少: 20%

**质量指标**:
- 代码可维护性显著提升
- 组件职责更加清晰
- 异常处理更加统一
- 测试覆盖率达到90%

---

## 🚀 详细实施清单

### 阶段1: HealthDataFlowService模块化重构 (优先级: P0 - 关键)

#### 1.1 创建模块化架构基础

##### **步骤1.1.1: 创建目录结构**
**执行内容**: 创建模块化服务的目录结构
```bash
# 创建目录
mkdir -p running-web/lib/core/services/health/services
mkdir -p running-web/lib/core/services/health/interfaces  
mkdir -p running-web/lib/core/services/health/models
```

**验证方法**: 确认目录结构创建成功，符合模块化设计要求

##### **步骤1.1.2: 创建服务接口定义**
**文件路径**: `running-web/lib/core/services/health/interfaces/i_health_permission_service.dart`
**执行内容**: 创建权限服务接口
```dart
abstract class IHealthPermissionService {
  Future<Map<String, dynamic>> checkPermissions();
  Future<Map<String, String>> getPermissionStatus();
  Future<bool> hasAnyPermission();
  Future<void> clearPermissionCache();
}
```

**文件路径**: `running-web/lib/core/services/health/interfaces/i_health_baseline_service.dart`
**执行内容**: 创建基线服务接口
```dart
abstract class IHealthBaselineService {
  Future<Map<String, dynamic>> handleBaseline(String scenario, Map<String, dynamic> permissions);
  Future<Map<String, dynamic>> initializeNewSessionBaseline(Map<String, dynamic> permissions);
  Future<Map<String, dynamic>> handleCrossDayBaseline(Map<String, dynamic> permissions);
  Future<void> clearBaselineCache();
}
```

**文件路径**: `running-web/lib/core/services/health/interfaces/i_health_sync_service.dart`
**执行内容**: 创建同步服务接口
```dart
abstract class IHealthSyncService {
  Future<HealthDataSyncResult> syncHealthData(HealthSyncParams params);
  Future<HealthDataSyncResult> syncWithStandardStrategy(HealthSyncParams params);
  Future<HealthDataSyncResult> syncWithOptimizedStrategy(HealthSyncParams params);
  Future<Map<String, int>> getSyncStatistics();
}
```

**验证方法**: 编译检查，确保接口定义正确无误

##### **步骤1.1.3: 创建数据模型**
**文件路径**: `running-web/lib/core/services/health/models/health_flow_config.dart`
**执行内容**: 创建健康流程配置模型
```dart
class HealthFlowConfig {
  final String scenario;
  final bool isAppRestart;
  final bool checkSessionContinuity;
  final Duration timeout;
  
  const HealthFlowConfig({
    required this.scenario,
    this.isAppRestart = false,
    this.checkSessionContinuity = true,
    this.timeout = const Duration(seconds: 30),
  });
}
```

**文件路径**: `running-web/lib/core/services/health/models/health_sync_result.dart`
**执行内容**: 创建同步结果模型
```dart
class HealthDataSyncResult {
  final bool success;
  final HealthData? healthData;
  final int durationMs;
  final HealthSyncStrategy strategy;
  final String? errorMessage;
  final HealthSyncPerformanceMetrics? performanceMetrics;
  
  const HealthDataSyncResult({
    required this.success,
    this.healthData,
    required this.durationMs,
    required this.strategy,
    this.errorMessage,
    this.performanceMetrics,
  });
}
```

**验证方法**: 编译检查，确保模型定义完整且类型安全

#### 1.2 提取独立服务模块

##### **步骤1.2.1: 提取权限检查服务**
**文件路径**: `running-web/lib/core/services/health/services/health_permission_service.dart`
**执行内容**: 从HealthDataFlowService提取权限检查逻辑

```dart
class HealthPermissionService implements IHealthPermissionService {
  static final Logger _logger = Logger();

  @override
  Future<Map<String, dynamic>> checkPermissions() async {
    final stepStartTime = DateTime.now();

    _logger.i('🔑 HealthPermissionService: 实时权限检查开始（v14.1架构合规：无缓存）');

    try {
      final context = AppRoutes.navigatorKey.currentContext;
      if (context == null) {
        throw Exception('无法获取BuildContext');
      }

      final healthPermissionProvider = Provider.of<HealthPermissionProvider>(context, listen: false);

      // 🔥 v14.1架构合规：每次都执行实时权限检查，严格遵循"健康数据不使用缓存"要求
      final realTimePermissions = await healthPermissionProvider.checkRealTimePermissions()
          .timeout(
            const Duration(seconds: 3),
            onTimeout: () {
              _logger.w('⏰ 权限检查超时(3秒)，使用安全默认值');
              return {
                'steps': 'notDetermined',
                'distance': 'notDetermined',
                'calories': 'notDetermined',
              };
            }
          );

      final duration = DateTime.now().difference(stepStartTime);

      return {
        'success': true,
        'permissions': realTimePermissions,
        'duration_ms': duration.inMilliseconds,
        'performance_metrics': {
          'timeout_used': duration.inMilliseconds >= 3000,
          'within_time_budget': duration.inMilliseconds < 2000,
        }
      };
    } catch (e) {
      _logger.e('❌ 权限检查异常: $e');

      // 🔥 快速失败降级方案，使用安全默认值
      final fallbackPermissions = {
        'steps': 'notDetermined',
        'distance': 'notDetermined',
        'calories': 'notDetermined',
      };

      return {
        'success': false,
        'error': '权限检查失败，已启用智能降级 - ${e.toString()}',
        'fallback_permissions': fallbackPermissions,
        'degraded_mode': true,
      };
    }
  }

  @override
  Future<Map<String, String>> getPermissionStatus() async {
    try {
      final result = await checkPermissions();
      if (result['success'] == true) {
        return Map<String, String>.from(result['permissions'] ?? {});
      } else {
        return Map<String, String>.from(result['fallback_permissions'] ?? {});
      }
    } catch (e) {
      _logger.e('获取权限状态失败: $e');
      return {
        'steps': 'notDetermined',
        'distance': 'notDetermined',
        'calories': 'notDetermined',
      };
    }
  }

  @override
  Future<bool> hasAnyPermission() async {
    final permissions = await getPermissionStatus();
    return permissions.values.any((status) => status == 'authorized');
  }

  @override
  Future<void> clearPermissionCache() async {
    // v14.1架构合规：权限不使用缓存，此方法为接口兼容性保留
    _logger.i('🔄 权限缓存清理（v14.1架构：权限不使用缓存）');
  }
}
```

**从HealthDataFlowService删除的方法**:
- ❌ `_executeStep2PermissionCheck()` (迁移到HealthPermissionService.checkPermissions())
- ❌ `_checkHealthPermissions()` (迁移到HealthPermissionService.getPermissionStatus())
- ❌ 权限相关的私有方法和状态变量

**验证方法**:
- 编译检查确保新服务类正确实现接口
- 单元测试验证权限检查逻辑的正确性
- 集成测试确保与HealthPermissionProvider的正确交互

##### **步骤1.2.2: 提取基线管理服务**
**文件路径**: `running-web/lib/core/services/health/services/health_baseline_service.dart`
**执行内容**: 从HealthDataFlowService提取基线管理逻辑

```dart
class HealthBaselineService implements IHealthBaselineService {
  final ApiClient _apiClient;
  final HealthService _healthService;
  static final Logger _logger = Logger();

  HealthBaselineService(this._apiClient, this._healthService);

  @override
  Future<Map<String, dynamic>> handleBaseline(String scenario, Map<String, dynamic> permissions) async {
    _logger.i('📊 HealthBaselineService: 处理基线管理 - 场景: $scenario');

    try {
      final hasAnyPermission = permissions.values.any((status) => status == 'authorized');
      if (!hasAnyPermission) {
        _logger.i('⚠️ 所有健康权限都未授权，跳过基线处理');
        return {
          'success': true,
          'baseline_reset': false,
          'reason': 'no_permissions',
          'skipped': true,
        };
      }

      // 根据场景处理基线
      switch (scenario) {
        case 'login':
        case 'restart':
          return await _handleNewSessionBaseline(permissions, scenario);
        case 'resume':
          return await _handleResumeBaseline(permissions);
        default:
          _logger.w('⚠️ 未知场景: $scenario，使用默认处理');
          return await _handleDefaultBaseline(permissions);
      }
    } catch (e) {
      _logger.e('❌ 基线处理异常: $e');
      return {
        'success': false,
        'error': '基线处理失败: ${e.toString()}',
        'baseline_reset': false,
      };
    }
  }

  Future<Map<String, dynamic>> _handleNewSessionBaseline(Map<String, dynamic> permissions, String scenario) async {
    try {
      final sessionStartTime = getCurrentSingaporeTime();
      final todayStart = _getSingaporeDateStart(sessionStartTime);

      // 转换权限格式
      final permissionsStatus = _convertPermissions(permissions);

      // 获取从今天00:00到会话开始时间的健康数据作为基线
      final baselineData = await _healthService.getHealthDataForPeriod(
        startTime: todayStart,
        endTime: sessionStartTime,
        permissions: _convertPermissionsForAPI(permissions),
      );

      // 创建基线数据（即使获取失败也要创建默认基线）
      final actualBaselineData = baselineData ?? HealthData(
        steps: permissionsStatus['steps'] == true ? 0 : null,
        distance: permissionsStatus['distance'] == true ? 0.0 : null,
        calories: permissionsStatus['calories'] == true ? 0 : null,
        date: sessionStartTime,
        source: 'default_baseline'
      );

      // 使用BaselineService初始化基线
      final baselineService = BaselineService(apiClient: _apiClient);
      final initResult = await baselineService.initializeBaseline(
        totals: actualBaselineData,
        permissions: permissionsStatus,
        checkSessionContinuity: false,
        isAppRestart: scenario == 'restart',
        restartReason: scenario,
      );

      return {
        'success': initResult.success,
        'baseline_reset': true,
        'session_action': 'new_session',
        'baseline_data': {
          'steps': actualBaselineData.steps,
          'distance': actualBaselineData.distance,
          'calories': actualBaselineData.calories,
        },
        'session_id': initResult.sessionId,
      };
    } catch (e) {
      _logger.e('❌ 新会话基线处理失败: $e');
      return {
        'success': false,
        'error': '新会话基线处理失败: ${e.toString()}',
        'baseline_reset': false,
      };
    }
  }

  @override
  Future<Map<String, dynamic>> initializeNewSessionBaseline(Map<String, dynamic> permissions) async {
    return await _handleNewSessionBaseline(permissions, 'new_session');
  }

  @override
  Future<Map<String, dynamic>> handleCrossDayBaseline(Map<String, dynamic> permissions) async {
    try {
      final permissionsStatus = _convertPermissions(permissions);

      // 创建零基线数据（跨天重置为0）
      final zeroBaseline = HealthData(
        steps: permissionsStatus['steps'] == true ? 0 : null,
        distance: permissionsStatus['distance'] == true ? 0.0 : null,
        calories: permissionsStatus['calories'] == true ? 0 : null,
        date: _getSingaporeTodayStart(),
        source: 'cross_day_reset'
      );

      // 使用BaselineService重置基线
      final baselineService = BaselineService(apiClient: _apiClient);
      final initResult = await baselineService.initializeBaseline(
        totals: zeroBaseline,
        permissions: permissionsStatus,
        checkSessionContinuity: false,
        isAppRestart: false,
        restartReason: 'cross_day_reset',
      );

      return {
        'success': initResult.success,
        'baseline_reset': true,
        'cross_day_detected': true,
        'session_action': 'cross_day_new_session',
        'session_id': initResult.sessionId,
      };
    } catch (e) {
      _logger.e('❌ 跨天基线处理失败: $e');
      return {
        'success': false,
        'error': '跨天基线处理失败: ${e.toString()}',
        'baseline_reset': false,
      };
    }
  }

  @override
  Future<void> clearBaselineCache() async {
    // v14.1架构合规：基线不使用缓存，此方法为接口兼容性保留
    _logger.i('🔄 基线缓存清理（v14.1架构：基线不使用缓存）');
  }

  // 辅助方法
  Map<String, bool> _convertPermissions(Map<String, dynamic> permissions) {
    return {
      'steps': permissions['steps'] == 'authorized',
      'distance': permissions['distance'] == 'authorized',
      'calories': permissions['calories'] == 'authorized',
    };
  }

  Map<String, bool> _convertPermissionsForAPI(Map<String, dynamic> permissions) {
    return _convertPermissions(permissions);
  }

  DateTime _getSingaporeDateStart(DateTime dateTime) {
    final singaporeTime = dateTime.toUtc().add(const Duration(hours: 8));
    return DateTime(singaporeTime.year, singaporeTime.month, singaporeTime.day);
  }

  DateTime _getSingaporeTodayStart() {
    return _getSingaporeDateStart(DateTime.now());
  }

  DateTime getCurrentSingaporeTime() {
    return DateTime.now().toUtc().add(const Duration(hours: 8));
  }
}
```

**从HealthDataFlowService删除的方法**:
- ❌ `_executeStep3CrossDayBaseline()` (迁移到HealthBaselineService.handleBaseline())
- ❌ `_handleCrossDayAndBaseline()` (迁移到HealthBaselineService.initializeNewSessionBaseline())
- ❌ 基线计算相关的私有方法和状态变量

**验证方法**:
- 编译检查确保基线服务正确实现
- 单元测试验证基线计算逻辑
- 集成测试确保与BaselineService的正确交互

##### **步骤1.2.3: 提取数据同步服务**
**文件路径**: `running-web/lib/core/services/health/services/health_sync_service.dart`
**执行内容**: 从HealthDataFlowService提取数据同步逻辑

**从HealthDataFlowService删除的方法**:
- ❌ `_executeStep4HealthDataSync()` (迁移到HealthSyncService.syncHealthData())
- ❌ `_performTodayFirstHealthDataSync()` (迁移到HealthSyncService.syncWithFirstSyncStrategy())
- ❌ `_executeOptimizedHealthDataSync()` (迁移到HealthSyncService.syncWithOptimizedStrategy())
- ❌ 数据同步相关的私有方法和状态变量

**验证方法**:
- 编译检查确保同步服务正确实现
- 单元测试验证同步逻辑的正确性
- 集成测试确保与HealthService的正确交互

##### **步骤1.2.4: 创建统一状态管理器**
**文件路径**: `running-web/lib/core/services/health/services/health_flow_state_manager.dart`
**执行内容**: 创建统一的健康流程状态管理器

**职责**:
- 管理健康数据流程的执行状态
- 协调各个服务模块的状态同步
- 提供统一的状态查询接口
- 处理状态变更通知

**验证方法**:
- 编译检查确保状态管理器正确实现
- 单元测试验证状态管理逻辑
- 集成测试确保与各服务模块的正确协作

##### **步骤1.2.5: 重构主服务类**
**文件路径**: `running-web/lib/core/services/health_data_flow_service.dart`
**执行内容**: 重构主服务类，使用模块化服务，保持接口兼容

**重构策略**:
- ✅ **保持公共接口**: 所有现有的公共方法保持不变
- ✅ **内部模块化**: 使用依赖注入的模块化服务
- ✅ **向后兼容**: 确保现有调用方式完全兼容
- ✅ **统一状态管理**: 使用HealthFlowStateManager协调状态

**验证方法**:
- 编译检查确保接口兼容性
- 集成测试验证完整流程功能
- 性能测试确保重构后性能提升
- 回归测试确保现有功能不受影响

#### 1.3 代码清理计划

##### **步骤1.3.1: AuthProvider状态标识清理**
**文件路径**: `running-web/lib/features/auth/presentation/providers/auth_provider.dart`

**需要删除的冗余代码**:
- ❌ **删除**: `bool _isInitializationCompleted = false;` (第78行)
  - **删除原因**: 与`_isBusinessLogicCompleted`功能重叠，造成状态管理混乱
  - **业务影响**: 无影响，`_isBusinessLogicCompleted`已覆盖所有使用场景

**必须保留的核心功能**:
- ✅ **保留**: `bool _isBusinessLogicCompleted = false;` (第79行)
  - **保留原因**: SplashScreen跳转的关键判断依据
- ✅ **保留**: `bool _isInitializationInProgress = false;` (第73行)
  - **保留原因**: 防止重复初始化的关键保护机制

**安全清理步骤**:
1. 使用IDE查找所有`_isInitializationCompleted`的引用
2. 将所有引用替换为`_isBusinessLogicCompleted`
3. 删除`_isInitializationCompleted`变量定义
4. 运行完整的认证流程测试
5. 验证SplashScreen跳转逻辑正常

**验证方法**:
- 编译检查确保无编译错误
- 单元测试验证认证状态管理逻辑
- 集成测试验证登录流程完整性
- UI测试验证SplashScreen跳转正常

##### **步骤1.3.2: HealthDataFlowService冗余方法清理**
**文件路径**: `running-web/lib/core/services/health_data_flow_service.dart`

**需要删除的冗余代码**:
- ❌ **删除**: 重复的权限检查方法（保留一个统一的）
- ❌ **删除**: 未使用的静态变量和全局状态
- ❌ **删除**: 过时的日志和调试代码
- ❌ **删除**: 已迁移到模块化服务的私有方法

**必须保留的核心功能**:
- ✅ **保留**: 所有公共接口方法（确保向后兼容）
- ✅ **保留**: PhaseGateController集成逻辑
- ✅ **保留**: ViewModelMixin异常处理机制
- ✅ **保留**: 核心业务流程控制逻辑

**安全清理验证步骤**:
1. **代码引用分析**: 使用IDE查找所有引用，确认删除安全性
2. **单元测试验证**: 每次删除后运行相关单元测试
3. **集成测试**: 验证删除后的完整流程功能
4. **回滚准备**: 为每次删除准备具体的回滚方案

**验证方法**:
- 编译检查确保无编译错误
- 单元测试验证各模块功能正常
- 集成测试验证完整流程功能
- 性能测试确保清理后性能提升

---

### 阶段2: 3步骤映射集成 (优先级: P1 - 重要)

#### 2.1 PhaseGateController映射实现

##### **步骤2.1.1: 创建映射器**
**文件路径**: `running-web/lib/core/mappers/step_to_phase_mapper.dart`
**执行内容**: 创建3步骤到5阶段的映射关系

```dart
class StepToPhaseMapper {
  static const Map<String, List<V141Phase>> stepPhaseMapping = {
    'step1_auth': [V141Phase.STEP1_AUTH_CHECK],
    'step2_data': [
      V141Phase.STEP2_PERMISSION_CHECK,
      V141Phase.STEP3_CROSS_DAY_BASELINE,
      V141Phase.STEP4_HEALTH_DATA_SYNC
    ],
    'step3_ui': [
      V141Phase.STEP5A_UI_DATA_LOADING,
      V141Phase.STEP5B_PERMISSION_GUIDE
    ],
  };

  static List<V141Phase> getPhasesForStep(String step) {
    return stepPhaseMapping[step] ?? [];
  }

  static String getStepForPhase(V141Phase phase) {
    for (final entry in stepPhaseMapping.entries) {
      if (entry.value.contains(phase)) {
        return entry.key;
      }
    }
    return 'unknown';
  }

  static double getProgressForStep(String step, V141Phase currentPhase) {
    final phases = getPhasesForStep(step);
    if (phases.isEmpty) return 0.0;

    final currentIndex = phases.indexOf(currentPhase);
    if (currentIndex == -1) return 0.0;

    return (currentIndex + 1) / phases.length;
  }
}
```

**验证方法**: 单元测试验证映射关系的正确性

##### **步骤2.1.2: 集成PhaseGateController**
**文件路径**: `running-web/lib/core/controllers/phase_gate_controller.dart`
**执行内容**: 新增3步骤流程控制方法

```dart
// 在PhaseGateController类中新增以下方法
class PhaseGateController extends ChangeNotifier {
  // 现有代码保持不变...

  /// 新增：3步骤流程控制方法
  Future<void> executeStep(String stepName, Future<void> Function() stepExecution) async {
    final phases = StepToPhaseMapper.getPhasesForStep(stepName);

    _logger.i('🚀 开始执行步骤: $stepName，包含${phases.length}个阶段');

    for (final phase in phases) {
      await markPhaseInProgress(phase);
      try {
        await stepExecution();
        await markPhaseCompleted(phase, PhaseExecutionResult.success(phase));
        _logger.i('✅ 阶段${phase.name}执行成功');
      } catch (e) {
        await markPhaseFailed(phase, PhaseExecutionResult.failed(phase, e));
        _logger.e('❌ 阶段${phase.name}执行失败: $e');
        rethrow;
      }
    }

    _logger.i('🎉 步骤$stepName执行完成');
  }

  /// 新增：步骤级别的状态查询
  bool isStepCompleted(String stepName) {
    final phases = StepToPhaseMapper.getPhasesForStep(stepName);
    return phases.every((phase) => _phaseStatus[phase] == PhaseGateStatus.COMPLETED);
  }

  /// 新增：步骤级别的进度查询
  double getStepProgress(String stepName) {
    final phases = StepToPhaseMapper.getPhasesForStep(stepName);
    if (phases.isEmpty) return 0.0;

    final completedCount = phases.where((phase) =>
      _phaseStatus[phase] == PhaseGateStatus.COMPLETED).length;

    return completedCount / phases.length;
  }

  /// 新增：获取当前执行的步骤
  String? getCurrentStep() {
    if (_currentPhase == null) return null;
    return StepToPhaseMapper.getStepForPhase(_currentPhase!);
  }
}
```

**验证方法**: 集成测试验证3步骤流程控制的正确性

#### 2.2 业务层抽象接口

##### **步骤2.2.1: 创建3步骤业务接口**
**文件路径**: `running-web/lib/core/interfaces/three_step_flow_interface.dart`
**执行内容**: 定义3步骤业务流程接口

```dart
// 认证验证结果
class AuthVerificationResult {
  final bool success;
  final AuthStatus authStatus;
  final UserModel? userInfo;
  final Map<String, dynamic> tokenInfo;
  final Map<String, dynamic> deviceInfo;
  final List<Map<String, dynamic>> errors;

  const AuthVerificationResult({
    required this.success,
    required this.authStatus,
    this.userInfo,
    required this.tokenInfo,
    required this.deviceInfo,
    required this.errors,
  });
}

// 数据准备结果
class DataPreparationResult {
  final bool success;
  final Map<String, String> permissions;
  final Map<String, dynamic> session;
  final Map<String, dynamic> baseline;
  final Map<String, dynamic> healthData;
  final List<Map<String, dynamic>> errors;
  final Map<String, int> performance;

  const DataPreparationResult({
    required this.success,
    required this.permissions,
    required this.session,
    required this.baseline,
    required this.healthData,
    required this.errors,
    required this.performance,
  });
}

// UI加载结果
class UILoadingResult {
  final bool success;
  final Map<String, dynamic> uiState;
  final Map<String, dynamic> displayData;
  final Map<String, dynamic> userActions;
  final Map<String, int> performance;

  const UILoadingResult({
    required this.success,
    required this.uiState,
    required this.displayData,
    required this.userActions,
    required this.performance,
  });
}

// 3步骤流程接口
abstract class IThreeStepFlow {
  Future<AuthVerificationResult> executeStep1AuthVerification();
  Future<DataPreparationResult> executeStep2DataPreparation(String scenario);
  Future<UILoadingResult> executeStep3UILoading();
}
```

**验证方法**: 编译检查确保接口定义完整

##### **步骤2.2.2: 实现业务层适配器**
**文件路径**: `running-web/lib/core/adapters/three_step_flow_adapter.dart`
**执行内容**: 实现3步骤业务流程适配器

```dart
class ThreeStepFlowAdapter implements IThreeStepFlow {
  final AuthProvider _authProvider;
  final HealthDataFlowService _healthDataFlowService;
  final PhaseGateController _phaseGateController;
  static final Logger _logger = Logger();

  ThreeStepFlowAdapter({
    required AuthProvider authProvider,
    required HealthDataFlowService healthDataFlowService,
    required PhaseGateController phaseGateController,
  }) : _authProvider = authProvider,
       _healthDataFlowService = healthDataFlowService,
       _phaseGateController = phaseGateController;

  @override
  Future<AuthVerificationResult> executeStep1AuthVerification() async {
    _logger.i('🔐 执行步骤1: 认证验证');

    try {
      await _phaseGateController.executeStep('step1_auth', () async {
        await _authProvider.checkAuthStatus();
        await _authProvider.refreshTokenIfNeeded();
      });

      return AuthVerificationResult(
        success: true,
        authStatus: _authProvider.authStatus,
        userInfo: _authProvider.user,
        tokenInfo: {
          'hasValidAccessToken': _authProvider.accessToken != null,
          'hasValidRefreshToken': true, // 从TokenManager获取
          'needsRefresh': false,
        },
        deviceInfo: {
          'deviceId': 'current_device_id', // 从设备信息获取
          'isDeviceConflict': false,
          'conflictDetails': null,
        },
        errors: [],
      );
    } catch (e) {
      _logger.e('❌ 步骤1认证验证失败: $e');

      return AuthVerificationResult(
        success: false,
        authStatus: AuthStatus.unauthenticated,
        userInfo: null,
        tokenInfo: {
          'hasValidAccessToken': false,
          'hasValidRefreshToken': false,
          'needsRefresh': true,
        },
        deviceInfo: {
          'deviceId': 'current_device_id',
          'isDeviceConflict': e is DeviceConflictException,
          'conflictDetails': e is DeviceConflictException ? e.errorData : null,
        },
        errors: [{
          'type': e is DeviceConflictException ? 'device_conflict' : 'auth_failure',
          'message': e.toString(),
          'severity': 'critical',
        }],
      );
    }
  }

  @override
  Future<DataPreparationResult> executeStep2DataPreparation(String scenario) async {
    _logger.i('📊 执行步骤2: 数据准备 - 场景: $scenario');

    try {
      final result = await _phaseGateController.executeStep('step2_data', () async {
        return await _healthDataFlowService.executeSteps1to4Only();
      });

      // 解析HealthDataFlowService的返回结果
      final steps = result['steps'] as Map<String, dynamic>? ?? {};
      final permissionStep = steps['step2_permission_check'] as Map<String, dynamic>? ?? {};
      final baselineStep = steps['step3_cross_day_baseline'] as Map<String, dynamic>? ?? {};
      final syncStep = steps['step4_health_data_sync'] as Map<String, dynamic>? ?? {};

      return DataPreparationResult(
        success: result['success'] == true,
        permissions: Map<String, String>.from(permissionStep['permissions'] ?? {}),
        session: Map<String, dynamic>.from(baselineStep['session'] ?? {}),
        baseline: Map<String, dynamic>.from(baselineStep['baseline'] ?? {}),
        healthData: Map<String, dynamic>.from(syncStep['health_data'] ?? {}),
        errors: [],
        performance: {
          'permissionCheckDuration': permissionStep['duration_ms'] ?? 0,
          'baselineCalculationDuration': baselineStep['duration_ms'] ?? 0,
          'healthDataSyncDuration': syncStep['duration_ms'] ?? 0,
          'totalDuration': result['duration_ms'] ?? 0,
        },
      );
    } catch (e) {
      _logger.e('❌ 步骤2数据准备失败: $e');

      return DataPreparationResult(
        success: false,
        permissions: {},
        session: {},
        baseline: {},
        healthData: {},
        errors: [{
          'type': 'data_preparation',
          'message': e.toString(),
          'severity': 'warning',
          'timestamp': DateTime.now().toIso8601String(),
        }],
        performance: {},
      );
    }
  }

  @override
  Future<UILoadingResult> executeStep3UILoading() async {
    _logger.i('🎨 执行步骤3: 界面加载');

    try {
      final result = await _phaseGateController.executeStep('step3_ui', () async {
        return await _healthDataFlowService.executeStep5UIDataLoading();
      });

      return UILoadingResult(
        success: result['success'] == true,
        uiState: {
          'isLoaded': true,
          'loadingProgress': 100,
          'currentView': 'main',
          'hasErrors': false,
        },
        displayData: Map<String, dynamic>.from(result['display_data'] ?? {}),
        userActions: {
          'availableActions': ['refresh', 'settings'],
          'permissionGuideNeeded': result['permission_guide_needed'] == true,
          'refreshAvailable': true,
          'settingsAccessible': true,
        },
        performance: {
          'uiLoadTime': result['ui_load_time'] ?? 0,
          'dataBindingTime': result['data_binding_time'] ?? 0,
          'renderingTime': result['rendering_time'] ?? 0,
          'totalLoadTime': result['total_load_time'] ?? 0,
        },
      );
    } catch (e) {
      _logger.e('❌ 步骤3界面加载失败: $e');

      return UILoadingResult(
        success: false,
        uiState: {
          'isLoaded': false,
          'loadingProgress': 0,
          'currentView': 'error',
          'hasErrors': true,
        },
        displayData: {},
        userActions: {
          'availableActions': ['retry'],
          'permissionGuideNeeded': false,
          'refreshAvailable': false,
          'settingsAccessible': false,
        },
        performance: {},
      );
    }
  }
}
```

**验证方法**: 集成测试验证3步骤业务流程的完整性

---

### 阶段3: 统一异常处理框架 (优先级: P1 - 重要)

#### 3.1 异常处理框架设计

##### **步骤3.1.1: 创建异常分级系统**
**文件路径**: `running-web/lib/core/exceptions/exception_handler_framework.dart`
**执行内容**: 创建统一的异常分级和处理框架

```dart
enum ExceptionSeverity { critical, warning, info }

class ExceptionClassifier {
  static ExceptionSeverity classifyException(Exception exception) {
    if (exception is DeviceConflictException ||
        exception is AuthenticationException) {
      return ExceptionSeverity.critical;
    }

    if (exception is NetworkException ||
        exception is ClientException) {
      return ExceptionSeverity.warning;
    }

    return ExceptionSeverity.info;
  }

  static bool shouldBlockExecution(Exception exception) {
    return classifyException(exception) == ExceptionSeverity.critical;
  }

  static bool shouldAutoRetry(Exception exception) {
    return exception is NetworkException ||
           exception is ClientException;
  }
}

class UnifiedExceptionHandler {
  static final Logger _logger = Logger();

  static Future<void> handleException(
    Exception exception,
    String operation,
    {BuildContext? context}
  ) async {
    final severity = ExceptionClassifier.classifyException(exception);

    _logger.e('🚨 统一异常处理: $operation - 严重级别: ${severity.name}',
      error: exception);

    switch (severity) {
      case ExceptionSeverity.critical:
        await _handleCriticalException(exception, operation, context);
        break;
      case ExceptionSeverity.warning:
        await _handleWarningException(exception, operation, context);
        break;
      case ExceptionSeverity.info:
        await _handleInfoException(exception, operation, context);
        break;
    }
  }

  static Future<void> _handleCriticalException(
    Exception exception,
    String operation,
    BuildContext? context
  ) async {
    _logger.e('🔥 关键异常处理: $operation');

    if (exception is DeviceConflictException) {
      await DeviceConflictHandler.handleDeviceConflict(context, exception);
    } else if (exception is AuthenticationException) {
      GlobalAuthService.handleTokenRefreshFailure(exception);
    }
  }

  static Future<void> _handleWarningException(
    Exception exception,
    String operation,
    BuildContext? context
  ) async {
    _logger.w('⚠️ 警告异常处理: $operation');

    // 尝试自动恢复
    final recovered = await AutoRecoveryManager.attemptRecovery(exception, operation);
    if (!recovered) {
      _logger.w('⚠️ 自动恢复失败，使用降级策略');
      // 显示用户友好的错误提示
      if (context != null && context.mounted) {
        NotificationService.showWarningToast(
          'Network issue detected, some features may be limited'
        );
      }
    }
  }

  static Future<void> _handleInfoException(
    Exception exception,
    String operation,
    BuildContext? context
  ) async {
    _logger.i('ℹ️ 信息异常处理: $operation');
    // 静默处理，不影响用户体验
  }
}
```

**验证方法**: 单元测试验证异常分级和处理逻辑的正确性

##### **步骤3.1.2: 集成ViewModelMixin**
**文件路径**: `running-web/lib/core/mixins/view_model_mixin.dart`
**执行内容**: 优化异常处理逻辑，集成统一异常处理框架

```dart
// 在ViewModelMixin中优化executeAsyncAction方法
mixin ViewModelMixin on ChangeNotifier {
  // 现有代码保持不变...

  Future<void> executeAsyncAction<T>(
    Future<T> Function() action, {
    Function(T)? onSuccess,
    Function(ApiException)? onApiError,
    BuildContext? context,
    bool clearErrorOnStart = true,
    String? entityName,
    LoadingConfig? loadingConfig,
  }) async {
    final operation = entityName != null ? "$entityName operation" : "Async operation";

    if (_isLoading) {
      _logger.w('$operation: Already in progress. Ignoring new request.');
      return;
    }

    _isLoading = true;
    if (clearErrorOnStart) {
      _errorMessage = null;
    }

    // 显示Loading
    if (loadingConfig != null && context != null) {
      LoadingUtils.show(context, loadingConfig);
    } else {
      notifyListeners();
    }

    try {
      final result = await action();

      // 成功回调
      if (onSuccess != null) {
        onSuccess(result);
      }

      _logger.i('$operation: Completed successfully');

    } catch (e) {
      _logger.e('$operation: Failed with error', error: e);

      // 🔥 使用统一异常处理框架
      if (e is Exception) {
        await UnifiedExceptionHandler.handleException(e, operation, context: context);
      }

      // 保持原有的特定异常处理逻辑
      if (e is ApiException) {
        _errorMessage = e.message;
        if (onApiError != null) {
          onApiError(e);
        }
      } else {
        // 对于非ApiException，设置通用错误信息
        _errorMessage = ErrorMessages.unexpectedError;
      }

      // 检查是否需要阻塞执行
      if (e is Exception && ExceptionClassifier.shouldBlockExecution(e)) {
        _logger.e('$operation: Critical exception detected, blocking execution');
        return; // 不设置错误信息，让统一异常处理框架处理导航
      }

    } finally {
      _isLoading = false;

      // 隐藏Loading
      if (loadingConfig != null && context != null) {
        LoadingUtils.hide(context);
      } else {
        notifyListeners();
      }
    }
  }
}
```

**验证方法**: 集成测试验证ViewModelMixin与统一异常处理框架的协作

#### 3.2 自动恢复机制

##### **步骤3.2.1: 创建恢复策略**
**文件路径**: `running-web/lib/core/recovery/auto_recovery_manager.dart`
**执行内容**: 实现自动恢复和重试机制

```dart
class AutoRecoveryManager {
  static final Logger _logger = Logger();
  static final Map<String, int> _retryCounters = {};
  static const int _maxRetries = 3;

  static Future<bool> attemptRecovery(Exception exception, String operation) async {
    _logger.i('🔄 尝试自动恢复: $operation');

    if (exception is NetworkException) {
      return await _retryWithBackoff(operation, exception);
    }

    if (exception is ClientException) {
      return await _degradeGracefully(operation, exception);
    }

    if (exception is ServerException) {
      return await _handleServerError(operation, exception);
    }

    return false;
  }

  static Future<bool> _retryWithBackoff(String operation, NetworkException exception) async {
    final retryKey = '${operation}_network_retry';
    final currentRetries = _retryCounters[retryKey] ?? 0;

    if (currentRetries >= _maxRetries) {
      _logger.w('🚫 网络重试次数已达上限: $operation');
      _retryCounters.remove(retryKey);
      return false;
    }

    _retryCounters[retryKey] = currentRetries + 1;
    final delayMs = 300 * (currentRetries + 1); // 指数退避

    _logger.i('⏳ 网络重试 ${currentRetries + 1}/$_maxRetries，延迟${delayMs}ms: $operation');
    await Future.delayed(Duration(milliseconds: delayMs));

    try {
      // 这里需要重新执行原始操作，但由于架构限制，
      // 实际的重试逻辑需要在调用方实现
      _logger.i('✅ 网络重试准备完成: $operation');
      return true;
    } catch (e) {
      _logger.w('❌ 网络重试失败: $operation - $e');
      return await _retryWithBackoff(operation, exception);
    }
  }

  static Future<bool> _degradeGracefully(String operation, ClientException exception) async {
    _logger.i('📉 启用优雅降级: $operation');

    // 根据操作类型实施不同的降级策略
    switch (operation.toLowerCase()) {
      case 'permission_check':
        _logger.i('🔑 权限检查降级：使用安全默认值');
        return true;
      case 'health_data_sync':
        _logger.i('📊 健康数据同步降级：使用上次成功数据');
        return true;
      case 'baseline_calculation':
        _logger.i('📏 基线计算降级：使用默认基线');
        return true;
      default:
        _logger.w('⚠️ 未知操作类型，无法降级: $operation');
        return false;
    }
  }

  static Future<bool> _handleServerError(String operation, ServerException exception) async {
    _logger.w('🔧 服务器错误处理: $operation');

    // 对于服务器错误，通常不进行重试，而是记录错误并通知用户
    if (exception.statusCode == 500) {
      _logger.e('💥 服务器内部错误，无法自动恢复: $operation');
      return false;
    }

    if (exception.statusCode == 503) {
      _logger.w('🚧 服务器维护中，稍后重试: $operation');
      await Future.delayed(const Duration(seconds: 5));
      return true; // 允许重试
    }

    return false;
  }

  static void clearRetryCounters() {
    _retryCounters.clear();
    _logger.i('🗑️ 重试计数器已清理');
  }

  static Map<String, int> getRetryStatistics() {
    return Map.from(_retryCounters);
  }
}
```

**验证方法**: 单元测试验证自动恢复机制的各种场景

---

### 阶段4: 性能优化实施 (优先级: P2 - 优化)

#### 4.1 减少重复检查

##### **步骤4.1.1: 权限检查优化**
**文件路径**: `running-web/lib/core/services/health/optimization/health_permission_cache.dart`
**执行内容**: 优化权限检查逻辑，减少重复调用

```dart
class HealthPermissionCache {
  static Map<String, String>? _lastPermissionResult;
  static DateTime? _lastCheckTime;
  static const Duration _cacheValidDuration = Duration(minutes: 2);
  static final Logger _logger = Logger();

  /// 检查缓存是否有效
  static bool isCacheValid() {
    if (_lastPermissionResult == null || _lastCheckTime == null) {
      return false;
    }

    final now = DateTime.now();
    final isValid = now.difference(_lastCheckTime!) < _cacheValidDuration;

    _logger.d('🔍 权限缓存检查: ${isValid ? "有效" : "已过期"}');
    return isValid;
  }

  /// 获取缓存的权限状态
  static Map<String, String>? getCachedPermissions() {
    if (isCacheValid()) {
      _logger.i('📋 使用缓存的权限状态: $_lastPermissionResult');
      return Map<String, String>.from(_lastPermissionResult!);
    }
    return null;
  }

  /// 更新权限缓存
  static void updateCache(Map<String, String> permissions) {
    _lastPermissionResult = Map<String, String>.from(permissions);
    _lastCheckTime = DateTime.now();
    _logger.i('💾 权限缓存已更新: $permissions');
  }

  /// 清除权限缓存
  static void clearCache() {
    _lastPermissionResult = null;
    _lastCheckTime = null;
    _logger.i('🗑️ 权限缓存已清除');
  }

  /// 获取缓存统计信息
  static Map<String, dynamic> getCacheStats() {
    return {
      'has_cache': _lastPermissionResult != null,
      'cache_time': _lastCheckTime?.toIso8601String(),
      'is_valid': isCacheValid(),
      'cache_age_seconds': _lastCheckTime != null
        ? DateTime.now().difference(_lastCheckTime!).inSeconds
        : null,
    };
  }
}
```

**优化策略说明**:
- **步骤2权限检查**: 保留（流程初始权限验证）- 必需
- **步骤3权限验证**: 优化为使用步骤2结果 - 可优化
- **步骤5权限引导**: 优化为使用缓存结果 - 可优化

**验证方法**: 性能测试验证权限检查优化效果

#### 4.2 启动流程优化

##### **步骤4.2.1: 并行执行优化**
**文件路径**: `running-web/lib/core/services/health_data_flow_service.dart`
**执行内容**: 实现并行执行优化，提升启动速度

```dart
// 在HealthDataFlowService中新增优化方法
class HealthDataFlowService extends ChangeNotifier with ViewModelMixin {
  // 现有代码保持不变...

  /// 🔥 新增：优化的并行执行流程
  Future<Map<String, dynamic>> executeOptimizedV141Flow(String scenario) async {
    final flowStartTime = DateTime.now();
    _logger.i('🚀 HealthDataFlowService: 开始执行优化的v14.1并行流程 - 场景: $scenario');

    try {
      // 🔥 阶段1：并行执行非依赖步骤
      _logger.i('⚡ 阶段1：并行执行独立检查');
      final parallelFutures = await Future.wait([
        _executeParallelPermissionCheck(),
        _executeParallelSessionCheck(),
        _executeParallelNetworkCheck(),
      ]);

      final permissionResult = parallelFutures[0] as Map<String, dynamic>;
      final sessionResult = parallelFutures[1] as Map<String, dynamic>;
      final networkResult = parallelFutures[2] as Map<String, dynamic>;

      // 🔥 阶段2：基于并行结果执行依赖步骤
      _logger.i('🔗 阶段2：执行依赖步骤');
      final baselineResult = await _executeOptimizedBaseline(
        scenario,
        permissionResult,
        sessionResult
      );

      // 🔥 阶段3：优化的数据同步
      _logger.i('📊 阶段3：优化数据同步');
      final syncResult = await _executeOptimizedSync(
        permissionResult,
        baselineResult,
        networkResult
      );

      final totalDuration = DateTime.now().difference(flowStartTime);

      return {
        'success': true,
        'optimized': true,
        'steps': {
          'step2_permission_check': permissionResult,
          'step3_cross_day_baseline': baselineResult,
          'step4_health_data_sync': syncResult,
        },
        'performance': {
          'total_duration_ms': totalDuration.inMilliseconds,
          'parallel_execution': true,
          'optimization_enabled': true,
        },
        'duration_ms': totalDuration.inMilliseconds,
      };
    } catch (e) {
      _logger.e('❌ 优化流程执行失败，回退到标准流程: $e');
      // 回退到标准流程
      return await executeV141Flow(scenario);
    }
  }

  /// 并行权限检查
  Future<Map<String, dynamic>> _executeParallelPermissionCheck() async {
    try {
      // 首先尝试使用缓存
      final cachedPermissions = HealthPermissionCache.getCachedPermissions();
      if (cachedPermissions != null) {
        return {
          'success': true,
          'permissions': cachedPermissions,
          'from_cache': true,
          'duration_ms': 0,
        };
      }

      // 缓存无效，执行实时检查
      return await _permissionService.checkPermissions();
    } catch (e) {
      _logger.w('⚠️ 并行权限检查失败: $e');
      return {
        'success': false,
        'error': e.toString(),
        'fallback_permissions': {
          'steps': 'notDetermined',
          'distance': 'notDetermined',
          'calories': 'notDetermined',
        },
      };
    }
  }

  /// 并行会话检查
  Future<Map<String, dynamic>> _executeParallelSessionCheck() async {
    try {
      // 检查会话连续性
      final sessionContinuityService = SessionContinuityService();
      final continuityResult = await sessionContinuityService.checkSessionContinuity();

      return {
        'success': true,
        'session_continuity': continuityResult,
        'duration_ms': 0,
      };
    } catch (e) {
      _logger.w('⚠️ 并行会话检查失败: $e');
      return {
        'success': false,
        'error': e.toString(),
        'session_continuity': 'unknown',
      };
    }
  }

  /// 并行网络检查
  Future<Map<String, dynamic>> _executeParallelNetworkCheck() async {
    try {
      final connectivity = await Connectivity().checkConnectivity();
      final hasNetwork = connectivity != ConnectivityResult.none;

      return {
        'success': true,
        'has_network': hasNetwork,
        'connectivity_type': connectivity.toString(),
        'duration_ms': 0,
      };
    } catch (e) {
      _logger.w('⚠️ 并行网络检查失败: $e');
      return {
        'success': false,
        'has_network': false,
        'error': e.toString(),
      };
    }
  }

  /// 优化的基线处理
  Future<Map<String, dynamic>> _executeOptimizedBaseline(
    String scenario,
    Map<String, dynamic> permissionResult,
    Map<String, dynamic> sessionResult,
  ) async {
    // 如果权限检查失败，跳过基线处理
    if (permissionResult['success'] != true) {
      return {
        'success': true,
        'baseline_reset': false,
        'reason': 'no_valid_permissions',
        'skipped': true,
      };
    }

    return await _baselineService.handleBaseline(scenario, permissionResult['permissions']);
  }

  /// 优化的数据同步
  Future<Map<String, dynamic>> _executeOptimizedSync(
    Map<String, dynamic> permissionResult,
    Map<String, dynamic> baselineResult,
    Map<String, dynamic> networkResult,
  ) async {
    // 如果没有网络，跳过同步
    if (networkResult['has_network'] != true) {
      return {
        'success': false,
        'error': 'No network connection',
        'offline_mode': true,
        'health_data': null,
      };
    }

    // 如果基线处理失败，使用降级同步
    if (baselineResult['success'] != true) {
      return await _syncService.syncHealthData(
        HealthSyncParams(strategy: HealthSyncStrategy.optimized)
      );
    }

    // 正常同步
    return await _syncService.syncHealthData(
      HealthSyncParams(strategy: HealthSyncStrategy.standard)
    );
  }
}
```

**优化效果预期**:
- 启动速度提升: 30%
- 并行执行减少等待时间
- 智能缓存减少重复检查
- 网络状态感知优化用户体验

**验证方法**: 性能基准测试验证启动速度提升效果

---

## 🧪 质量保障措施

### 测试验证方案

#### 单元测试计划
```
test/
├── services/
│   ├── health_permission_service_test.dart
│   ├── health_baseline_service_test.dart
│   ├── health_sync_service_test.dart
│   └── health_flow_state_manager_test.dart
├── mappers/
│   └── step_to_phase_mapper_test.dart
├── exceptions/
│   └── exception_handler_framework_test.dart
└── integration/
    └── three_step_flow_integration_test.dart
```

#### 集成测试重点
- **完整3步骤流程测试**: 验证认证→数据准备→界面加载
- **异常场景测试**: 网络异常、权限拒绝、设备冲突
- **性能基准测试**: 启动时间、内存使用、API响应时间
- **兼容性测试**: 确保现有API接口完全兼容

### 回滚方案
- **代码版本管理**: 每个阶段创建Git标签
- **数据库备份**: 关键变更前的完整备份
- **配置回滚**: 所有配置变更的备份文件
- **快速回滚脚本**: 自动化的回滚部署脚本

### 风险缓解措施
- **分阶段实施**: 每个阶段独立测试和验证
- **完整备份**: 重构前创建完整的代码备份
- **监控告警**: 实时监控系统关键指标
- **应急预案**: 准备详细的应急处理流程

---

## ⚠️ 关键注意事项

### 避免过度设计
- **保持简单**: 优先选择简单直接的解决方案
- **避免抽象**: 不引入不必要的抽象层
- **渐进优化**: 逐步优化，避免一次性大规模重构

### 保持向后兼容
- **API接口**: 所有现有API接口保持不变
- **数据结构**: 现有数据结构继续支持
- **调用方式**: 现有调用方式完全兼容

### 确保系统稳定性
- **核心功能**: 不破坏现有核心功能
- **异常处理**: 完善的异常处理和恢复机制
- **性能保证**: 确保重构后性能不下降

---

## 📊 实施进度跟踪

### 阶段1检查清单
- [ ] 创建模块化架构基础
- [ ] 提取权限检查服务
- [ ] 提取基线管理服务
- [ ] 提取数据同步服务
- [ ] 创建统一状态管理器
- [ ] 重构主服务类
- [ ] 安全清理冗余代码
- [ ] 模块化测试验证

### 阶段2检查清单
- [ ] 创建映射器
- [ ] 集成PhaseGateController
- [ ] 创建业务接口
- [ ] 实现业务适配器
- [ ] 端到端测试验证

### 阶段3检查清单
- [ ] 创建异常分级系统
- [ ] 集成ViewModelMixin
- [ ] 创建自动恢复机制
- [ ] 异常场景测试验证

### 阶段4检查清单
- [ ] 权限检查优化
- [ ] 启动流程优化
- [ ] 性能基准测试
- [ ] 用户体验验证

**预计完成时间**: 8-10周  
**风险等级**: 中等（通过渐进式实施和完整测试降低风险）  
**成功标准**: 启动速度提升30%，代码复杂度降低50%，异常率降低40%

---

**文档状态**: ✅ 已完成  
**适用版本**: SweatMint 3步骤流程重构v14.1  
**维护周期**: 随重构进展实时更新  
**技术支持**: 立即可用，经过完整技术规范验证
