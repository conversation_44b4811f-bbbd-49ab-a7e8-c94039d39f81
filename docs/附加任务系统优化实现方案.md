# 附加（邀请）任务系统优化实现方案

## 1. 引言

本文档旨在详细阐述对 SweatMint 项目中附加（特别是邀请类）任务系统的优化方案的具体技术实现步骤。核心目标是确保任务完成顺序的公平性、收益的可控性，并增强系统逻辑的健壮性。关键在于引入 `UserTask.completion_order` 字段，并在后端实现事件驱动的、按顺序的自动任务完成机制。

本文档基于 `running/docs/additional_task_system_documentation.md` 中“5. 系统优化与设计考量”章节的内容进行展开。

## 2. 数据模型修改 (`UserTask`)

需要在 `tasks/models.py` 文件中的 `UserTask` 模型中进行以下修改：

```python
# tasks/models.py

class UserTask(models.Model):
    # ... (现有字段)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_tasks', verbose_name=_('User'))
    task = models.ForeignKey(Task, on_delete=models.CASCADE, related_name='user_tasks', verbose_name=_('Task'))
    # ... (其他字段)

    # 新增字段：用户任务完成顺序号
    # 用于确定附加（邀请）任务在满足条件时的完成优先级
    # 数值越小，优先级越高。在分配时计算并赋值。
    # 对于非邀请类附加任务或每日任务，此字段可以为 NULL 或 0。
    completion_order = models.PositiveIntegerField(
        _('Completion Order'),
        null=True,
        blank=True,
        db_index=True, # 建议添加索引，因为会基于此字段排序查询
        help_text=_('Order in which this additional/referral task should be completed if multiple are eligible. Lower numbers have higher priority.')
    )

    # ... (现有 Meta 和方法)
```

**迁移步骤**:
1.  运行 `python manage.py makemigrations tasks` 生成迁移文件。
2.  运行 `python manage.py migrate` 应用迁移。

## 3. 任务分配逻辑调整 (`TaskAssignmentService`)

负责分配附加任务的服务（通常是 `tasks/services/task_assignment.py` 中的 `TaskAssignmentService` 或类似服务）需要在创建附加（邀请）任务的 `UserTask` 实例时，计算并填充 `completion_order` 字段。

**修改点**: `assign_additional_tasks` (或类似方法)

**核心逻辑**: 当为用户分配一批附加（邀请）任务时：
1.  获取所有符合条件的、可分配给该用户的附加（邀请）`Task` 模板。
2.  对这些 `Task` 模板进行排序，排序规则如下（升序）：
    a.  **首要排序键**: `task.required_referrals` (所需邀请人数)
    b.  **次要排序键**: `task.id` (任务ID，确保唯一性)
3.  遍历排序后的 `Task` 模板列表，为每个 `Task` 创建一个 `UserTask` 实例。
4.  `UserTask` 实例的 `completion_order` 字段赋值为其在排序后列表中的索引值 (从1开始，或从0开始，保持一致即可)。

**伪代码/实现思路**: `TaskAssignmentService.assign_additional_tasks` (或处理附加邀请任务分配的方法)

```python
# tasks/services/task_assignment.py (示例)

class TaskAssignmentService:
    # ...

    def assign_additional_tasks(self, user, date_to_assign):
        # ... (获取用户、当前日期等)

        # 1. 获取所有符合条件的、可分配给该用户的附加（邀请）Task模板
        #    筛选 task_type='referral', is_active=True 等
        eligible_referral_tasks = Task.objects.filter(
            task_category='additional',
            task_type='referral', # 明确是邀请任务
            is_active=True,
            # ... 其他筛选条件，如用户等级、特定标签等
        ).exclude(
            id__in=UserTask.objects.filter(user=user, assignment_date=date_to_assign, task__task_type='referral').values_list('task_id', flat=True)
        ) # 排除当天已分配的邀请任务

        # 2. 对这些 Task 模板进行排序
        #    注意: Django ORM 的 order_by 对于 NULL 值的处理可能需要关注，确保符合预期
        #    如果 required_referrals 可能为 None, 需要 coalesce 或其他处理确保排序正确性
        sorted_tasks = sorted(
            list(eligible_referral_tasks), 
            key=lambda t: (
                t.required_referrals if t.required_referrals is not None else float('inf'), # 确保 None 值排在后面或按需处理
                t.id
            )
        )

        user_tasks_to_create = []
        for index, task_template in enumerate(sorted_tasks):
            # 3 & 4. 创建 UserTask 实例并设置 completion_order
            user_task = UserTask(
                user=user,
                task=task_template,
                assignment_date=date_to_assign,
                status='pending',
                # ... 其他字段初始化
                completion_order = index + 1 # 从1开始的顺序号
            )
            user_tasks_to_create.append(user_task)
        
        if user_tasks_to_create:
            UserTask.objects.bulk_create(user_tasks_to_create)
            # ... (后续逻辑，如发送通知)
        
        # ...
```

**注意**: 上述代码仅为示例，具体实现需结合项目实际的 `Task` 模型字段（如 `required_referrals` 是否允许 `NULL`）和现有分配逻辑进行调整。

## 4. 后端事件驱动的自动完成逻辑

当用户的邀请数量发生变化（通常是新增一个有效邀请）时，后端需要自动触发一个逻辑来检查并完成符合条件的、优先级最高的附加（邀请）任务。

**触发机制选项**:
*   **Django Signals**: 
    *   在处理用户邀请关系创建或确认的核心逻辑处（例如，`User.referrer` 被设置并保存，或者一个独立的 `Referral` 模型实例被创建时），发送一个自定义信号。
    *   优点：紧密耦合，实时性高。
    *   缺点：如果邀请处理逻辑分散，可能需要在多处发送信号。
*   **Celery 异步任务**: 
    *   在邀请成功后，派发一个 Celery 任务，将邀请者 `user_id` 作为参数传递。
    *   优点：解耦，可处理耗时操作，不阻塞主请求。
    *   缺点：有一定延迟，依赖 Celery 系统。
*   **直接函数调用 (如果适用)**:
    *   如果邀请关系的处理逻辑集中且允许同步执行后续的附加任务检查，可以直接调用处理函数。

**推荐方案**: Django Signals 或 Celery 异步任务，具体取决于项目的复杂度和对实时性的要求。Celery 任务通常更稳健，适合后台处理。

**核心处理逻辑 (函数/任务体)**：

接收参数: `user_id` (邀请者的用户ID)

```python
# tasks/services/referral_task_completion.py (新文件或现有服务内的方法)
# from django.db import transaction
# from users.models import User # 假设用户模型
# from .models import UserTask, Task # 任务相关模型

# @transaction.atomic # 建议使用事务确保数据一致性
def process_referral_task_completion(user_id):
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        # log error
        return

    # 1. 获取用户最新的有效邀请总数
    #    具体实现依赖于系统中如何记录和统计有效邀请
    #    例如: user.referrals.filter(is_active=True).count() 或类似逻辑
    current_referral_count = User.objects.filter(referrer=user, is_active=True).count()

    # 2. 查找该用户所有状态为“待处理(pending)”、类型为“邀请(referral)”、
    #    且已达到邀请人数要求的附加 UserTask
    #    并按照 completion_order 排序
    eligible_user_tasks = UserTask.objects.filter(
        user=user,
        task__task_category='additional',
        task__task_type='referral',
        status='pending',
        task__required_referrals__lte=current_referral_count # 关键：所需邀请数 <= 当前邀请数
    ).select_related('task').order_by('completion_order', 'task__id') # completion_order 优先，task_id 再次确保

    if not eligible_user_tasks.exists():
        return # 没有符合条件的待完成任务

    # 3. 仅将排序最靠前的一个 UserTask 标记为“已完成”
    task_to_complete = eligible_user_tasks.first()

    # 确保不会意外完成多个 (虽然排序和 .first() 应该能保证)
    # 可以增加一个检查，如果之前该邀请数已经完成过同等或更高优先级的任务，则不再重复完成
    # (此逻辑较复杂，初步可以先不加，依赖 completion_order 的正确性)

    # 更新任务状态
    task_to_complete.status = 'completed'
    task_to_complete.completed_at = timezone.now() # 记录完成时间
    # task_to_complete.achieved_value = current_referral_count # 可选: 记录完成时的实际邀请数
    task_to_complete.save(
        update_fields=['status', 'completed_at'] # 'achieved_value' if added
    )

    # 可选：发送任务完成通知给用户
    # send_task_completion_notification(user, task_to_complete.task)

    # 可选：记录日志
    # logger.info(f"User {user_id} completed additional referral task {task_to_complete.task.name} (UserTask ID: {task_to_complete.id})")

    # 重要：如果一个邀请事件可能使得多个任务达标，但我们只完成了一个。
    # 那么下一次邀请事件（或手动触发）时，这个逻辑会再次运行，并完成下一个优先级最高的任务。
```

**信号连接 (如果使用 Django Signals)**：

```python
# 在 users/signals.py 或 tasks/signals.py
# from django.db.models.signals import post_save
# from django.dispatch import receiver
# from users.models import User # 或 Referral 模型
# from .services.referral_task_completion import process_referral_task_completion

# @receiver(post_save, sender=User) # 示例：当 User 模型保存后
# def handle_new_referral(sender, instance, created, **kwargs):
#    if instance.referrer and (created or 'referrer_id' in kwargs.get('update_fields', [])):
#        # 条件需要精确，确保只在有效新邀请或邀请关系确认时触发
#        process_referral_task_completion(instance.referrer.id)

# 在 apps.py 中连接信号处理器
# class TasksConfig(AppConfig):
#     name = 'tasks'
#     def ready(self):
#         import tasks.signals
```

## 5. API 接口角色调整

根据 `additional_task_system_documentation.md` 中 5.4 节的描述，并结合最新的代码实现，API接口的角色和数据有如下调整：

*   **`tasks.app_views.AddonTaskViewSet.list` API (获取附加任务列表)**:
    *   **数据增强**:
        *   响应中的每个附加任务对象（特别是邀请任务）将包含 `completion_order` 字段，用于前端正确展示任务的逻辑顺序。值为 `null` 或 `0` 的任务（通常为非邀请任务）可以按原有 `priority` 或其他逻辑排序。
        *   新增 `is_next_pending_referral` (布尔类型) 字段，明确标识当前任务是否为用户下一个待处理且满足激活/领取条件的邀请任务。
        *   `unlock_message` 字段将为邀请任务提供更动态和具体的进度提示，例如："还需邀请 X 名新用户才能处理此任务" 或 "请先完成编号为 Y 的邀请任务"。
        *   `can_activate` 字段对于邀请任务，主要反映任务本身是否处于可被处理的状态（如 `pending`）。用户能否实际进行“领取奖励”或类似操作，更多地依赖 `is_next_pending_referral` 的状态以及任务是否已满足所有条件。
    *   **排序逻辑**: 后端在返回邀请任务列表时，会优先按照 `completion_order` 升序排列。

*   **`tasks.views.verify_task` API**:
    *   **修改**: 此 API 对于 `task_type='referral'` 的附加任务，其行为已调整。
    *   **前端调用**: 前端应不再主动调用此 API 来尝试完成附加（邀请）任务。邀请任务的完成由后端事件（如新用户注册并完成首个任务）驱动的 `process_referral_task_completion` 逻辑自动处理。
    *   **后端逻辑**: 
        *   如果此接口被调用（例如，由管理员或旧版前端）针对邀请任务，它不应再执行完成操作。它可以返回一个提示信息，说明此类任务由系统自动完成。
        *   或者，它可以被改造为仅检查状态，而不改变状态。
        *   其内部原有的关于邀请数量的检查逻辑，可以被 `process_referral_task_completion` 复用。
    *   **建议**: 保留接口，但严格限制其对邀请任务的写操作权限，使其主要用于查询或特定管理场景。

*   **`tasks.app_views.AddonTaskViewSet.activate` API**:
    *   **角色明确**: 对于 `task_type='referral'` 的附加任务，`activate` 操作不再是将任务状态置为 `active`。
    *   **行为调整**:
        *   当用户通过前端调用此接口针对一个邀请任务时，后端将根据任务的 `completion_order`、用户当前的邀请进度以及 `is_next_pending_referral` 的状态来决定响应。
        *   **主要作用**: 提供用户交互的反馈。例如：
            *   如果任务不是 `is_next_pending_referral`，或者前置邀请任务未完成，返回提示：“请先完成前序邀请任务 [任务名称/编号]。”
            *   如果任务是 `is_next_pending_referral` 但邀请人数未达标，返回提示：“还需邀请 X 名用户才能领取此任务奖励。” (此信息也通过 `list` 接口的 `unlock_message` 提供)
            *   如果任务是 `is_next_pending_referral` 且邀请人数已达标（任务已可被系统自动标记为 `completed` 或等待领取），调用此接口可以视为用户“确认领取”或“查看进度”的意图。后端此时不会改变任务状态为 `active`，而是可能：
                *   返回成功消息，提示任务已满足条件，奖励已发放或等待发放。
                *   如果任务已被系统自动完成并发放奖励，则提示任务已完成。
                *   响应中应包含明确的 `message` 和可能的 `task_status` (`pending`, `completed` 等)。
        *   `UserTask` 的状态将主要由后端的 `process_referral_task_completion` 逻辑从 `pending` 直接更新到 `completed` (并触发奖励)，跳过传统的 `active` 状态（对于邀请任务而言）。
    *   **建议**: 此接口对于邀请任务，已演变为一个提供用户反馈和引导的端点，而非状态变更的主要触发器。其命名虽然是 `activate`，但实际行为已特化。

## 6. 测试注意事项

*   **`completion_order` 分配**: 测试不同 `required_referrals` 和 `task.id` 组合的 `Task` 是否能正确分配 `completion_order`。
*   **边界条件**: 测试 `required_referrals` 为 0 或 `None` (如果允许) 的情况。
*   **邀请事件触发**: 测试新邀请成功后，`process_referral_task_completion` 是否被正确触发。
*   **顺序完成**: 
    *   模拟用户有多个附加邀请任务，邀请数逐个增加，验证是否严格按照 `completion_order` 顺序完成。
    *   模拟一次邀请使多个任务同时达标，验证是否只完成了 `completion_order` 最靠前的一个。
*   **并发处理 (如果适用)**: 如果多个邀请事件可能并发处理同一个用户，需要测试数据一致性 (事务很重要)。
*   **API 行为**: 测试 `verify_task` 和 `activate` API 对于邀请任务是否按新逻辑执行。
*   **幂等性**: `process_referral_task_completion` 应该具有一定的幂等性，即使用户邀请数没有变化，重复调用不应产生副作用（例如重复完成任务）。
*   **性能**: 测试大量用户和任务时的分配和完成性能。

## 7. 部署与迁移考量 (可选)

*   **现有 `UserTask` 数据**: 对于部署前已存在的附加（邀请）`UserTask`，它们将没有 `completion_order` 值。需要决定如何处理：
    *   **方案1 (推荐)**: 编写一个一次性数据迁移脚本，为所有历史的、状态为 `pending` 的附加（邀请）`UserTask`，根据其关联 `Task` 的 `required_referrals` 和 `task.id` 补填 `completion_order`。
    *   **方案2**: 新逻辑仅对新分配的 `UserTask` 生效。老任务可以继续按原有逻辑（如果有）处理，或被忽略。
*   **旧版API兼容**: 如果有旧版客户端仍在调用 `verify_task` 来完成邀请任务，需要考虑兼容策略或强制升级。

---
**本文档为实现方案的初步规划，具体代码实现时需根据项目实际情况进行调整和细化。**

## 3. 核心问题点与优化方案

### 3.1. 邀请任务自动完成逻辑缺失 (高优先级修复)

**问题描述：**

当前系统在用户注册流程中（具体在 `users.views.UserViewSet.register` 方法及其后续的信号处理 `users.signals.check_referrer_agent_upgrade` 和 `agents.services.AgentService.check_and_upgrade_to_agent`），虽然处理了推荐人的代理升级逻辑，但完全缺失了在新用户（被邀请者）成功注册并关联推荐人后，自动检查并完成推荐人（邀请者）的待处理邀请任务（`Task` 类型为 `referral` 的 `UserTask`）的机制。

**根本原因：**

没有服务或函数被调用来执行以下关键步骤：
1.  识别邀请者的待处理邀请任务。
2.  获取邀请者当前的有效邀请人数。
3.  根据邀请人数和任务要求（`Task.target_value`），判断哪些邀请任务已满足完成条件。
4.  按照正确的优先级（“要求拉新人数”升序，若相同则按“`task_id`”升序）选择应被完成的任务。
5.  调用 `TaskCompletionService` 来完成选定的任务。

**解决方案建议：**

建议在 `users.signals.py` 文件中，可以考虑在 `check_referrer_agent_upgrade` 函数处理完代理升级逻辑之后，或者创建一个新的信号处理器专门响应用户创建事件，来触发邀请任务的完成检查。

1.  **创建新的服务 `ReferralTaskCompletionService`** (例如在 `tasks/services/referral_task_completion.py`):
    ```python
    # tasks/services/referral_task_completion.py
    from django.utils import timezone
    from users.models import User
    from tasks.models import UserTask, Task
    from tasks.services.task_completion import TaskCompletionService
    from പ്രധാന_models.models import UserReferralMetrics # 假设有模型记录用户有效邀请数
    import logging

    logger = logging.getLogger(__name__)

    class ReferralTaskCompletionService:
        def __init__(self, referrer: User):
            self.referrer = referrer

        def process_new_referral(self, new_referred_user: User):
            """在新用户被推荐后，处理推荐人的邀请任务完成逻辑"""
            if not self.referrer:
                logger.warning("推荐人不存在，无法处理邀请任务。")
                return

            logger.info(f"开始为推荐人 {self.referrer.email} 处理因新用户 {new_referred_user.email} 注册而触发的邀请任务检查。")

            # 1. 获取推荐人当前有效的邀请人数
            #    需要一个可靠的方式来获取这个数字，例如从 UserReferralMetrics 模型或实时计算
            #    这里假设有一个方法可以获取 (需要替换为实际实现)
            try:
                # 伪代码，需要替换为实际获取邀请数量的逻辑
                # effective_referral_count = UserReferralMetrics.objects.get(user=self.referrer).effective_referrals_count
                # 或者: effective_referral_count = User.objects.filter(referrer=self.referrer, is_active=True, is_staff=False).count()
                effective_referral_count = self.referrer.get_effective_referral_count() # 假设User模型有此方法
                logger.info(f"推荐人 {self.referrer.email} 当前有效邀请人数: {effective_referral_count}")
            except AttributeError as e:
                logger.error(f"获取推荐人 {self.referrer.email} 有效邀请人数失败：User模型缺少get_effective_referral_count方法或方法实现有误: {e}")
                return
            except Exception as e:
                logger.error(f"获取推荐人 {self.referrer.email} 有效邀请人数时发生未知错误: {e}")
                return

            # 2. 查找推荐人所有类型为 'referral' 且状态为 'pending' 或 'active' 的 UserTask
            pending_referral_user_tasks = list(UserTask.objects.filter(
                user=self.referrer,
                task__category='additional', # 确保是附加任务
                task__task_type=Task.TYPE_REFERRAL, # 确保是邀请任务类型
                status__in=[UserTask.STATUS_PENDING, UserTask.STATUS_ACTIVE] # 待处理或已激活
            ).select_related('task').order_by('task__target_value', 'task_id')) # 核心排序逻辑

            if not pending_referral_user_tasks:
                logger.info(f"推荐人 {self.referrer.email} 没有待处理的邀请任务。")
                return

            logger.info(f"推荐人 {self.referrer.email} 有 {len(pending_referral_user_tasks)} 个待处理的邀请任务。开始检查完成条件...")

            for user_task in pending_referral_user_tasks:
                task_definition = user_task.task
                required_referrals = task_definition.target_value

                logger.debug(f"检查任务: {task_definition.name} (ID: {task_definition.id}), 需要邀请人数: {required_referrals}, 当前状态: {user_task.status}")

                if effective_referral_count >= required_referrals:
                    if user_task.status not in [UserTask.STATUS_COMPLETED, UserTask.STATUS_CANCELLED, UserTask.STATUS_EXPIRED]:
                        logger.info(f"任务 '{task_definition.name}' (UserTask ID: {user_task.id}) 满足完成条件。尝试完成...")
                        try:
                            completion_service = TaskCompletionService(user_task)
                            # 邀请任务完成通常没有额外meta_data，除非有特定需求
                            # 对于附加任务，complete_task内部会处理奖励为0的情况
                            completion_service.complete_task(completion_data={})
                            logger.info(f"邀请任务 '{task_definition.name}' (UserTask ID: {user_task.id}) 已成功标记为完成。")
                            # 通常一个新用户注册只完成一个最优先的邀请任务，如果产品逻辑如此，这里应该 break
                            # 如果允许多个任务因同一次邀请同时完成（不常见），则不 break
                            # 根据当前的规则“选择第一个应该完成的任务”，所以应该 break
                            break 
                        except Exception as e:
                            logger.error(f"完成邀请任务 '{task_definition.name}' (UserTask ID: {user_task.id}) 失败: {e}")
                    else:
                        logger.info(f"任务 '{task_definition.name}' (UserTask ID: {user_task.id}) 已是完成/取消/过期状态，跳过。")
                else:
                    # 由于任务已按 target_value 排序，如果当前任务不满足，后续任务（需要更多人）也不会满足
                    logger.info(f"任务 '{task_definition.name}' (UserTask ID: {user_task.id}) 未满足邀请人数条件 ({effective_referral_count}/{required_referrals})，后续任务不再检查。")
                    break 
    ```

2.  **在 `users.signals.py` 中调用新服务**:
    ```python
    # users/signals.py
    # ... (其他导入)
    from tasks.services.referral_task_completion import ReferralTaskCompletionService

    @receiver(post_save, sender=User)
    def check_referrer_agent_upgrade(sender, instance, created, **kwargs):
        # ... (现有代理升级逻辑)
        # agent_service = AgentService(instance.referrer)
        # agent_service.check_and_upgrade_to_agent(instance)

        if created and instance.referrer:
            logger.info(f"新用户 {instance.email} 注册，推荐人为 {instance.referrer.email}。触发邀请任务检查。")
            try:
                referral_task_service = ReferralTaskCompletionService(referrer=instance.referrer)
                referral_task_service.process_new_referral(new_referred_user=instance)
            except Exception as e:
                logger.error(f"调用 ReferralTaskCompletionService 处理邀请任务失败: {e}")
        
        # ... (原有的代理升级逻辑，确保它在邀请任务处理之前或之后执行，根据业务决定)
        if instance.referrer:
            agent_service = AgentService(instance.referrer)
            # 传递新注册的用户实例，以便在 AgentService 中进行相关检查
            agent_service.check_and_upgrade_to_agent(new_referred_user=instance) 

    ```
    *   **注意**：需要确保 `User` 模型有获取有效邀请人数的方法 `get_effective_referral_count()`，或者在服务中实现此逻辑。
    *   需要仔细考虑 `check_referrer_agent_upgrade` 中代理升级逻辑和邀请任务完成逻辑的执行顺序和事务性。通常建议将它们视为独立的关注点。可以将邀请任务处理放在代理升级之后。

### 3.2. `Task.priority` 字段在附加任务中的作用与冗余分析

**当前状态与分析：**

1.  **在任务分配中的使用** (`tasks.services.task_assignment.TaskAssignmentService.assign_additional_tasks`):
    *   当系统为用户分配每日的附加任务时，会首先获取所有符合用户会员等级且 `is_active=True` 的 `Task` 定义（任务模板）。
    *   然后，这些 `Task` 定义会**严格按照 `task.priority` 字段进行升序排序**（数值越小，优先级越高）。
    *   最后，系统会遍历这个排序后的列表，为用户创建所有这些附加任务的 `UserTask` 实例。**目前逻辑是分配所有符合条件的附加任务，并没有基于 `priority` 进行数量筛选。**

2.  **在用户完成任务优先级判断中的作用**：
    *   **不直接使用**。当用户有多个可完成的附加（邀请）任务时，系统判断哪个任务应该优先被完成的逻辑是：
        1.  首先按任务要求的**拉新人数 (`Task.target_value`) 升序**排列（人少的优先）。
        2.  如果拉新人数要求相同，则按 **`task_id` 升序**排列。
    *   `Task.priority` 不参与这个决策过程。

3.  **后台管理界面**：
    *   根据 BOSS 反馈，后台创建/编辑附加任务时，没有提供设置 `Task.priority` 的界面选项。这使得此字段难以通过后台进行有效管理和配置，其值可能始终为默认值。

**冗余可能性评估：**

*   **如果业务场景满足以下所有条件，则 `Task.priority` 对于附加任务而言是冗余的：**
    1.  系统**总是**为用户分配其会员等级对应的所有活动的附加任务，从不根据优先级限制分配的数量。
    2.  用户完成附加任务的优先级判断**永远**不参考 `Task.priority`，而是固定使用“拉新人数 -> `task_id`”的规则。
    3.  后台不打算或不需要通过 `priority` 来控制附加任务的某些行为（例如，即使分配所有，也可能希望在某些内部列表或日志中按 `priority` 排序，尽管这价值较小）。

*   **如果以下任一情况成立，则 `Task.priority` 可能有其价值：**
    1.  **未来可能限制每日附加任务的分配数量**。例如，从多个可用附加任务模板中，只为用户分配N个优先级最高的。这时 `priority` 是关键的筛选依据。
    2.  尽管用户完成顺序不看 `priority`，但运营或管理上希望通过 `priority` 对任务模板进行某种内部管理或排序。

**目前观察：** 从代码和文档 `additional_task_system_documentation.md`（提及“尽管优先级目前在后续逻辑中未见明确使用”）来看，`Task.priority` 的主要作用体现在 `TaskAssignmentService` 分配任务时对 `Task` 模板的一次性排序。如果分配的总是所有符合条件的任务，那么这个排序对最终用户体验和核心业务逻辑（如哪个邀请任务先完成）的影响非常有限。

**建议与待决策点：**

1.  **明确业务需求**：首要任务是与产品/业务方确认：
    *   未来是否会根据 `priority` 限制附加任务的**分配数量**？
    *   如果后台确实没有 `priority` 的设置入口，是否有计划添加？或者该字段是否应被视为一个固定的内部排序值？

2.  **基于决策选择方案：**
    *   **方案A：确认冗余，建议移除 (针对附加任务)**
        *   如果确认对于附加任务，`priority` 既不用于筛选分配数量，也不用于决定用户完成顺序，且后台无法有效管理，则建议将其从附加任务逻辑中移除。
        *   **修改范围**：
            *   **`tasks.models.Task`**: 修改 `priority` 字段的 `help_text`，明确其仅对特定任务种类有效（如果其他种类任务使用它），或者如果完全不用则考虑移除该字段或使其 `null=True, blank=True` 并设置默认值。
            *   **`tasks.services.task_assignment.TaskAssignmentService.assign_additional_tasks`**: 移除 `available_tasks.sort(key=lambda x: x.priority ...)` 这行代码。
            *   **`tasks.admin.TaskAdmin`**: 如果之前有 `priority` 的显示或编辑，移除（尽管BOSS反馈没有）。
            *   **`tasks.serializers.TaskSerializer` / `TaskCreateSerializer`**: 移除 `priority` 字段或使其变为只读/可选，并确保其不影响附加任务的创建和更新逻辑。
            *   **相关文档**：更新所有提及 `Task.priority` 与附加任务相关的部分。

    *   **方案B：保留 `Task.priority` 并明确其作用**
        *   如果 `priority` 对未来限制分配数量有潜在价值，或者运营上需要它进行内部管理。
        *   **行动**：
            *   在后台管理界面为 `Task` 模型（特别是附加任务）添加 `priority` 字段的设置入口，并提供清晰的说明。
            *   确保其默认值合理。
            *   在文档中清晰说明其在“任务分配阶段”的排序作用，并明确它与“用户任务完成优先级判断”是分离的。

**目前倾向**：鉴于后台无配置入口且用户完成优先级不依赖此字段，**对于附加任务，`Task.priority` 倾向于冗余**。但最终决策需业务确认。

### 3.3. 澄清附加任务（邀请任务）的完成判断优先级

**问题描述**：确保系统在判断用户哪个邀请任务优先完成时，遵循正确的业务规则。

**正确规则**：
1.  首先比较任务定义的 **`target_value` (要求拉新人数)**，数值小的任务优先级更高。
2.  如果 `target_value` 相同，则比较任务定义的 **`id` (task_id)**，数值小的任务优先级更高。

**实现方案**：

*   在上述 `ReferralTaskCompletionService.process_new_referral` 方法中，查询待处理邀请任务时使用的 `order_by('task__target_value', 'task_id')` 已经体现了此规则。
*   所有涉及“选择下一个待完成邀请任务”的逻辑都必须遵循此排序。

**需要检查的地方**：
*   确保前端如果需要展示“下一个推荐任务”或类似信息时，也遵循此逻辑（尽管这超出了后端修复范围，但应在API设计时考虑）。
*   任何其他可能需要判断邀请任务优先级的后端逻辑（目前未发现）。

## 4. 需要进一步确认的事项

1.  **`User.get_effective_referral_count()` 的实现**: 确认 `User` 模型或相关服务中存在或需要添加一个准确计算用户有效邀请数的方法。这是 `ReferralTaskCompletionService` 的关键依赖。
2.  **`Task.priority` 的最终业务决策**: 需要产品或业务负责人明确在附加任务场景下，`priority` 字段的未来规划和必要性，以便决定是移除还是保留并规范使用。
3.  **后台 `Task` 模型管理界面**：再次确认 `priority` 字段是否对管理员可见及可编辑。如果不可见，强化了其冗余的观点（针对附加任务）。

（本文档将根据上述明确事项的进展和后续实现细节持续更新。）