2025-07-19 10:10:40.077176+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:40.077314+0800 Runner[906:265995] flutter: │ #0   CommonAppBar.build.<anonymous closure> (package:sweatmint/presentation/widgets/common/common_app_bar.dart:49:21)
2025-07-19 10:10:40.077365+0800 Runner[906:265995] flutter: │ #1   _InkResponseState.handleTap (package:flutter/src/material/ink_well.dart:1203:21)
2025-07-19 10:10:40.077412+0800 Runner[906:265995] flutter: ├
2025-07-19 10:10:40.077455+0800 Runner[906:265995] flutter: │ 🐛 Menu icon tapped - opening drawer
2025-07-19 10:10:40.077506+0800 Runner[906:265995] flutter: └
2025-07-19 10:10:41.352758+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.353035+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:599:13)<…>
2025-07-19 10:10:41.353137+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   CommonDrawer.build.<anonymous closure> (package:sweatmint/presentation/widgets/common/common_drawer.dart:178:70)<…>
2025-07-19 10:10:41.353244+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.353414+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔐 AuthProvider: Starting unified device session management model logout flow...<…>
2025-07-19 10:10:41.353520+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.355119+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.355280+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   CommonDrawer.build.<anonymous closure> (package:sweatmint/presentation/widgets/common/common_drawer.dart:179:27)<…>
2025-07-19 10:10:41.355416+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _InkResponseState.handleTap (package:flutter/src/material/ink_well.dart:1203:21)<…>
2025-07-19 10:10:41.355534+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.355630+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 Logout initiated<…>
2025-07-19 10:10:41.355742+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.355630+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 Logout initiated<…>
2025-07-19 10:10:41.355742+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:10:41.376446+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.376571+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:621:21)<…>
2025-07-19 10:10:41.376626+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.376679+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.376722+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🌐 AuthProvider: Calling unified device session management model backend logout API...<…>
2025-07-19 10:10:41.376784+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.377456+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.377514+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthServiceImpl.logout (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:127:15)<…>
2025-07-19 10:10:41.377555+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:622:31)<…>
2025-07-19 10:10:41.377601+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.378063+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔐 AuthService: 开始执行统一设备会话管理模型的登出流程<…>
2025-07-19 10:10:41.378108+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.378273+0800 Runner[906:265995] flutter: \^[[38;5;208m┌<…>
2025-07-19 10:10:41.378320+0800 Runner[906:265995] flutter: \^[[38;5;208m│ #0   AuthServiceImpl.logout (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:132:17)<…>
2025-07-19 10:10:41.378363+0800 Runner[906:265995] flutter: \^[[38;5;208m│ #1   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:622:31)<…>
2025-07-19 10:10:41.378407+0800 Runner[906:265995] flutter: \^[[38;5;208m├<…>
2025-07-19 10:10:41.378567+0800 Runner[906:265995] flutter: \^[[38;5;208m│ ⚠️ ⚠️ AuthService: 登出时未提供健康数据<…>
2025-07-19 10:10:41.378696+0800 Runner[906:265995] flutter: \^[[38;5;208m└<…>
2025-07-19 10:10:41.379584+0800 Runner[906:265995] flutter: \^[[38;5;208m┌<…>
2025-07-19 10:10:41.379664+0800 Runner[906:265995] flutter: \^[[38;5;208m│ #0   AuthRepositoryImpl.logout (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:199:15)<…>
2025-07-19 10:10:41.379712+0800 Runner[906:265995] flutter: \^[[38;5;208m│ #1   AuthServiceImpl.logout (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:135:29)<…>
2025-07-19 10:10:41.379758+0800 Runner[906:265995] flutter: \^[[38;5;208m├<…>
2025-07-19 10:10:41.379799+0800 Runner[906:265995] flutter: \^[[38;5;208m│ ⚠️ ⚠️ AuthRepository: DataSource未完全初始化，执行降级登出处理<…>
2025-07-19 10:10:41.379842+0800 Runner[906:265995] flutter: \^[[38;5;208m└行降级登出处理<…>
2025-07-19 10:10:41.379842+0800 Runner[906:265995] flutter: \^[[38;5;208m└\342<…>
2025-07-19 10:10:41.380002+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.380210+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthRepositoryImpl.logout (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:200:15)<…>
2025-07-19 10:10:41.380251+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthServiceImpl.logout (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:135:29)<…>
2025-07-19 10:10:41.380292+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.380333+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔄 AuthRepository: 基础模式下的登出 - 跳过后端API调用，仅执行本地清理<…>
2025-07-19 10:10:41.380610+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.380793+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.380840+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthRepositoryImpl.logout (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:203:15)<…>
2025-07-19 10:10:41.380969+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthServiceImpl.logout (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:135:29)<…>
2025-07-19 10:10:41.381084+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.381517+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthRepository: 降级登出处理完成（本地清理模式）<…>
2025-07-19 10:10:41.381564+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.382178+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.382178+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:10:41.382178+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-19 10:10:41.382245+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthServiceImpl.logout (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:140:15)<…>
2025-07-19 10:10:41.382284+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.382343+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.382384+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthService: 统一设备会话管理模型登出流程完成<…>
2025-07-19 10:10:41.382427+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.382384+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthService: 统一设备会话管理模型登出流程完成<…>
2025-07-19 10:10:41.382427+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342<…>
2025-07-19 10:10:41.382384+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthService: 统一设备会话管理模型登出流程完成<…>
2025-07-19 10:10:41.382427+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\342\224<…>
2025-07-19 10:10:41.382577+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.382384+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthService: 统一设备会话管理模型登出流程完成<…>
2025-07-19 10:10:41.382427+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\342\224<…>
2025-07-19 10:10:41.382577+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:10:41.382796+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthServiceImpl.logout (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:141:15)<…>
2025-07-19 10:10:41.382839+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.382971+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.383188+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔒 已完成完整的会话终止流程: 后端API调用、token黑名单、健康数据快照、会话状态更新、审计日志<…>
2025-07-19 10:10:41.383400+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.383702+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.383702+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:10:41.383752+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:626:21)<…>
2025-07-19 10:10:41.383892+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.384064+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\224<…>
2025-07-19 10:10:41.384240+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: Unified device session management model backend logout API call successful<…>
2025-07-19 10:10:41.384376+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.384707+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.384805+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:627:21)<…>
2025-07-19 10:10:41.384942+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.385123+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.385274+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔒 Backend completed: token blacklist, health data snapshot, session state update, audit log recording<…>
2025-07-19 10:10:41.385430+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.385781+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.385781+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:10:41.385781+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-19 10:10:41.385843+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:643:15)<…>
2025-07-19 10:10:41.385884+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.385983+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.386091+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔒 AuthProvider: User state has been reset to unauthenticated<…>
2025-07-19 10:10:41.386244+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.387022+0800 Runner[906:265995] flutter: ┌<…>
2025-07-19 10:10:41.387022+0800 Runner[906:265995] flutter: ┌\342<…>
2025-07-19 10:10:41.387022+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:10:41.387084+0800 Runner[906:265995] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:243:19)
2025-07-19 10:10:41.387124+0800 Runner[906:265995] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-19 10:10:41.387341+0800 Runner[906:265995] flutter: ├
2025-07-19 10:10:41.387415+0800 Runner[906:265995] flutter: │ 🐛 Redirect: Unauthenticated user accessing protected route (/home). Redirecting to login.
2025-07-19 10:10:41.387460+0800 Runner[906:265995] flutter: └
2025-07-19 10:10:41.391475+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.391552+0800 Runner[906:265995] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:239:21)
2025-07-19 10:10:41.391596+0800 Runner[906:265995] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-19 10:10:41.391638+0800 Runner[906:265995] flutter: ├
2025-07-19 10:10:41.391475+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.391552+0800 Runner[906:265995] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:239:21)
2025-07-19 10:10:41.391596+0800 Runner[906:265995] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-19 10:10:41.391638+0800 Runner[906:265995] flutter: ├\342
2025-07-19 10:10:41.391475+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.391552+0800 Runner[906:265995] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:239:21)
2025-07-19 10:10:41.391596+0800 Runner[906:265995] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-19 10:10:41.391638+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:10:41.391679+0800 Runner[906:265995] flutter: │ 🐛 Redirect: Unauthenticated user accessing public route (/login). Allowed.
2025-07-19 10:10:41.391719+0800 Runner[906:265995] flutter: └
2025-07-19 10:10:41.391475+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.391552+0800 Runner[906:265995] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:239:21)
2025-07-19 10:10:41.391596+0800 Runner[906:265995] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-19 10:10:41.391638+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:10:41.391679+0800 Runner[906:265995] flutter: │ 🐛 Redirect: Unauthenticated user accessing public route (/login). Allowed.
2025-07-19 10:10:41.391719+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:10:41.392466+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.392526+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   TokenManager._stopBackgroundRefresh (package:sweatmint/core/services/token_manager.dart:362:12)<…>
2025-07-19 10:10:41.392571+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   TokenManager.clearAllTokens (package:sweatmint/core/services/token_manager.dart:368:7)<…>
2025-07-19 10:10:41.392615+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\224<…>
2025-07-19 10:10:41.392656+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:10:41.392 (+0:00:19.710688)<…>
2025-07-19 10:10:41.392701+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.392754+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 TokenManager: Background refresh timer stopped.<…>
2025-07-19 10:10:41.392797+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.394378+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.394378+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:10:41.394439+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   MyApp.build.<anonymous closure> (package:sweatmint/main.dart:308:17)<…>
2025-07-19 10:10:41.394470+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   Consumer.buildWithChild (package:provider/src/consumer.dart:179:19)<…>
2025-07-19 10:10:41.394500+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.394533+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 阶段1：显示基础MaterialApp界面 - 认证状态: AuthStatus.unauthenticated<…>
2025-07-19 10:10:41.394563+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.400782+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.400903+0800 Runner[906:265995] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:211:19)
2025-07-19 10:10:41.400949+0800 Runner[906:265995] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-19 10:10:41.400983+0800 Runner[906:265995] flutter: ├
2025-07-19 10:10:41.401018+0800 Runner[906:265995] flutter: │ 🐛 Redirect: At splash screen with unauthenticated user (after logout), allowing navigation to login.
2025-07-19 10:10:41.401070+0800 Runner[906:265995] flutter: └
2025-07-19 10:10:41.408794+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.408918+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState.initState (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:32:13)<…>
2025-07-19 10:10:41.408956+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5842:55)<…>
2025-07-19 10:10:41.408999+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.409030+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🚀 SplashScreen: 开屏页面初始化<…>
2025-07-19 10:10:41.409068+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.411497+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.411535+0800 Runner[906:265995] flutter: │ #0   _SplashScreenState.build (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:394:13)
2025-07-19 10:10:41.411563+0800 Runner[906:265995] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-19 10:10:41.411589+0800 Runner[906:265995] flutter: ├
2025-07-19 10:10:41.411617+0800 Runner[906:265995] flutter: │ 🐛 🎨 SplashScreen: 构建UI
2025-07-19 10:10:41.411644+0800 Runner[906:265995] flutter: └
2025-07-19 10:10:41.414090+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.414156+0800 Runner[906:265995] flutter: │ #0   _SplashScreenState.build.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:408:17)
2025-07-19 10:10:41.414185+0800 Runner[906:265995] flutter: │ #1   _Selector0State.buildWithChild.<anonymous closure> (package:provider/src/selector.dart:86:45)
2025-07-19 10:10:41.414215+0800 Runner[906:265995] flutter: ├
2025-07-19 10:10:41.414245+0800 Runner[906:265995] flutter: │ 🐛 🔄 SplashScreen: Provider状态更新 - AuthStatus.unauthenticated, Stage: LoginStage.stage3Complete, 业务逻辑完成: true
2025-07-19 10:10:41.414274+0800 Runner[906:265995] flutter: └
2025-07-19 10:10:41.416532+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.416569+0800 Runner[906:265995] flutter: \^[[38;5;12m<…>
2025-07-19 10:10:41.416569+0800 Runner[906:265995] flutter: \^[[38;5;12m\342│ #0   _SplashScreenState.build.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:417:19)<…>
2025-07-19 10:10:41.416600+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _Selector0State.buildWithChild.<anonymous closure> (package:provider/src/selector.dart:86:45)<…>
2025-07-19 10:10:41.416630+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.416879+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ SplashScreen: 真正的业务逻辑初始化完成 - AuthStatus.unauthenticated<…>
2025-07-19 10:10:41.416912+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:10:41.437865+0800 Runner[906:265995] flutter: \^[[38;5;208m┌<…>
2025-07-19 10:10:41.437956+0800 Runner[906:265995] flutter: \^[[38;5;208m│ #0   _SplashScreenState._startStage2Loading (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:260:15)<…>
2025-07-19 10:10:41.437994+0800 Runner[906:265995] flutter: \^[[38;5;208m│ #1   _SplashScreenState.initState.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:36:7)<…>
2025-07-19 10:10:41.438027+0800 Runner[906:265995] flutter: \^[[38;5;208m├<…>
2025-07-19 10:10:41.438107+0800 Runner[906:265995] flutter: \^[[38;5;208m│ ⚠️ ⚠️ 业务逻辑已完成，跳过阶段2重复执行<…>
2025-07-19 10:10:41.438141+0800 Runner[906:265995] flutter: \^[[38;5;208m└<…>
2025-07-19 10:10:41.438276+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.438312+0800 Runner[906:265995] flutter: │ #0   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:70:13)
2025-07-19 10:10:41.438341+0800 Runner[906:265995] flutter: │ #1   _SplashScreenState.build.<anonymous closure>.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:421:13)
2025-07-19 10:10:41.438369+0800 Runner[906:265995] flutter: ├
2025-07-19 10:10:41.438437+0800 Runner[906:265995] flutter: │ 🐛 🔍 SplashScreen: 检查导航条件
2025-07-19 10:10:41.438468+0800 Runner[906:265995] flutter: └
2025-07-19 10:10:41.438565+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.438599+0800 Runner[906:265995] flutter: │ #0   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:71:13)
2025-07-19 10:10:41.438628+0800 Runner[906:265995] flutter: │ #1   _SplashScreenState.build.<anonymous closure>.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:421:13)
2025-07-19 10:10:41.438656+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:10:41.438842+0800 Runner[906:265995] flutter: │ 🐛    - 最小时间完成: false
2025-07-19 10:10:41.438873+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:10:41.438842+0800 Runner[906:265995] flutter: │ 🐛    - 最小时间完成: false
2025-07-19 10:10:41.438873+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:10:41.438973+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.439004+0800 Runner[906:265995] flutter: │ #0   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:72:13)
2025-07-19 10:10:41.439033+0800 Runner[906:265995] flutter: │ #1   _SplashScreenState.build.<anonymous closure>.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:421:13)
2025-07-19 10:10:41.439061+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:10:41.439391+0800 Runner[906:265995] flutter: │ 🐛    - 认证状态: AuthStatus.unauthenticated
2025-07-19 10:10:41.439426+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:10:41.439391+0800 Runner[906:265995] flutter: │ 🐛    - 认证状态: AuthStatus.unauthenticated
2025-07-19 10:10:41.439426+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:10:41.439532+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.439564+0800 Runner[906:265995] flutter: │ #0   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:73:13)
2025-07-19 10:10:41.439591+0800 Runner[906:265995] flutter: │ #1   _SplashScreenState.build.<anonymous closure>.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:421:13)
2025-07-19 10:10:41.439617+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:10:41.439804+0800 Runner[906:265995] flutter: │ 🐛    - 已导航: false
2025-07-19 10:10:41.439835+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:10:41.439804+0800 Runner[906:265995] flutter: │ 🐛    - 已导航: false
2025-07-19 10:10:41.439835+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:10:41.439954+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.439986+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:77:15)<…>
2025-07-19 10:10:41.440156+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _SplashScreenState.build.<anonymous closure>.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:421:13)<…>
2025-07-19 10:10:41.440195+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.440227+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🚀 SplashScreen: 检测到未认证状态（<…>
2025-07-19 10:10:41.440227+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🚀 SplashScreen: 检测到未认证状态（\347登出后），立即导航到登录页<…>
2025-07-19 10:10:41.440256+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.440227+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🚀 SplashScreen: 检测到未认证状态（\347登出后），立即导航到登录页<…>
2025-07-19 10:10:41.440256+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342<…>
2025-07-19 10:10:41.440542+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.440582+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._navigateToLogin (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:148:13)<…>
2025-07-19 10:10:41.440614+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:78:7)<…>
2025-07-19 10:10:41.440646+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.440671+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔑 SplashScreen: 直接导航到登录页<…>
2025-07-19 10:10:41.440698+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.445974+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.445974+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:10:41.446035+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   TokenManager.clearAllTokens (package:sweatmint/core/services/token_manager.dart:378:14)<…>
2025-07-19 10:10:41.446064+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.446094+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.446122+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:10:41.445 (+0:00:19.764133)<…>
2025-07-19 10:10:41.446149+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.446177+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 TokenManager: All tokens have been cleared from secure storage.<…>
2025-07-19 10:10:41.446205+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.446309+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.446339+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:649:17)<…>
2025-07-19 10:10:41.446366+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.446393+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.446504+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔑 AuthProvider: Tokens in TokenManager have been cleared<…>
2025-07-19 10:10:41.446536+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.446632+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.446632+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:10:41.446667+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:657:17)<…>
2025-07-19 10:10:41.446697+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>

2025-07-19 10:10:41.446725+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.446752+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🧹 AuthProvider: Application-level user caches cleared<…>
2025-07-19 10:10:41.446780+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.446858+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.446889+0800 Runner[906:265995] flutter: \^[[38;5;12m<…>
2025-07-19 10:10:41.446889+0800 Runner[906:265995] flutter: \^[[38;5;12m\342\224│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:665:17)<…>
2025-07-19 10:10:41.446913+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.446939+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.446964+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔄 AuthProvider: 健康权限管理器状态已重置<…>
2025-07-19 10:10:41.446992+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.447066+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.447103+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:672:17)<…>
2025-07-19 10:10:41.447126+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.447152+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.447178+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔚 AuthProvider: 调用GlobalAppLifecycleManager进行会话结束标记<…>
2025-07-19 10:10:41.447205+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.449441+0800 Runner[906:265995] flutter: ┌<…>
2025-07-19 10:10:41.449441+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:10:41.449492+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-19 10:10:41.449521+0800 Runner[906:265995] flutter: │ #1   LogInterceptor.onRequest (package:dio/src/interceptors/log.dart:68:13)
2025-07-19 10:10:41.449551+0800 Runner[906:265995] flutter: ├
2025-07-19 10:10:41.449633+0800 Runner[906:265995] flutter: │ 🐛 ApiClient: *** Request ***
2025-07-19 10:10:41.449663+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:10:41.450110+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:10:41.450110+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:10:41.450144+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-19 10:10:41.451395+0800 Runner[906:265995] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-07-19 10:10:41.451436+0800 Runner[906:265995] flutter: ├
2025-07-19 10:10:41.451461+0800 Runner[906:265995] flutter: │ 🐛 ApiClient: uri: /api/app/v1/health/session/end/
2025-07-19 10:10:41.451488+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:10:41.452120+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:10:41.452120+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:10:41.452157+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-19 10:10:41.452181+0800 Runner[906:265995] flutter: │ #1   LogInterceptor.onRequest (package:dio/src/interceptors/log.dart:94:13)
2025-07-19 10:10:41.452207+0800 Runner[906:265995] flutter: ├
2025-07-19 10:10:41.452516+0800 Runner[906:265995] flutter: │ 🐛 ApiClient:
2025-07-19 10:10:41.452544+0800 Runner[906:265995] flutter: └
2025-07-19 10:10:41.454344+0800 Runner[906:265995] flutter: \^[[38;5;208m┌
2025-07-19 10:10:41.454344+0800 Runner[906:265995] flutter: \^[[38;5;208m┌\342<…>
2025-07-19 10:10:41.454480+0800 Runner[906:265995] flutter: \^[[38;5;208m│ #0   TokenManager.getAccessToken (package:sweatmint/core/services/token_manager.dart:121:14)<…>
2025-07-19 10:10:41.454512+0800 Runner[906:265995] flutter: \^[[38;5;208m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.454561+0800 Runner[906:265995] flutter: \^[[38;5;208m├
2025-07-19 10:10:41.454344+0800 Runner[906:265995] flutter: \^[[38;5;208m┌\342<…>
2025-07-19 10:10:41.454480+0800 Runner[906:265995] flutter: \^[[38;5;208m│ #0   TokenManager.getAccessToken (package:sweatmint/core/services/token_manager.dart:121:14)<…>
2025-07-19 10:10:41.454512+0800 Runner[906:265995] flutter: \^[[38;5;208m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.454561+0800 Runner[906:265995] flutter: \^[[38;5;208m├\342\224<…>
2025-07-19 10:10:41.454593+0800 Runner[906:265995] flutter: \^[[38;5;208m│ 10:10:41.454 (+0:00:19.772539)<…>
2025-07-19 10:10:41.454636+0800 Runner[906:265995] flutter: \^[[38;5;208m├<…>
2025-07-19 10:10:41.454968+0800 Runner[906:265995] flutter: \^[[38;5;208m│ ⚠️ TokenManager: No access token found.<…>
2025-07-19 10:10:41.455042+0800 Runner[906:265995] flutter: \^[[38;5;208m└<…>
2025-07-19 10:10:41.455247+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.455286+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:80:23)
2025-07-19 10:10:41.455315+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:10:41.455344+0800 Runner[906:265995] flutter: ├
2025-07-19 10:10:41.455374+0800 Runner[906:265995] flutter: │ 🐛 ApiClient: Using TokenManager.getAccessToken() for API request
2025-07-19 10:10:41.455402+0800 Runner[906:265995] flutter: └
2025-07-19 10:10:41.455574+0800 Runner[906:265995] flutter: \^[[38;5;208m┌<…>
2025-07-19 10:10:41.455605+0800 Runner[906:265995] flutter: \^[[38;5;208m│ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:87:23)<…>
2025-07-19 10:10:41.456077+0800 Runner[906:265995] flutter: \^[[38;5;208m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.456114+0800 Runner[906:265995] flutter: \^[[38;5;208m├<…>
2025-07-19 10:10:41.456147+0800 Runner[906:265995] flutter: \^[[38;5;208m│ ⚠️ ApiClient: No access token available for /api/app/v1/health/session/end/<…>
2025-07-19 10:10:41.456179+0800 Runner[906:265995] flutter: \^[[38;5;208m└<…>
2025-07-19 10:10:41.456784+0800 Runner[906:265995] flutter: \^[[38;5;196m┌<…>
2025-07-19 10:10:41.456822+0800 Runner[906:265995] flutter: \^[[38;5;196m│ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:111:25)<…>
2025-07-19 10:10:41.456849+0800 Runner[906:265995] flutter: \^[[38;5;196m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.456877+0800 Runner[906:265995] flutter: \^[[38;5;196m├<…>
2025-07-19 10:10:41.456902+0800 Runner[906:265995] flutter: \^[[38;5;196m│ ⛔ ApiClient: Missing token for authenticated endpoint: /api/app/v1/health/session/end/<…>
2025-07-19 10:10:41.456931+0800 Runner[906:265995] flutter: \^[[38;5;196m└<…>
2025-07-19 10:10:41.458802+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.458858+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-19 10:10:41.458885+0800 Runner[906:265995] flutter: │ #1   LogInterceptor.onError (package:dio/src/interceptors/log.dart:109:15)
2025-07-19 10:10:41.458915+0800 Runner[906:265995] flutter: ├
2025-07-19 10:10:41.458941+0800 Runner[906:265995] flutter: │ 🐛 ApiClient: *** DioException ***:
2025-07-19 10:10:41.458967+0800 Runner[906:265995] flutter: └
2025-07-19 10:10:41.459174+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.459174+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:10:41.459174+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:10:41.459217+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-19 10:10:41.459245+0800 Runner[906:265995] flutter: │ #1   LogInterceptor.onError (package:dio/src/interceptors/log.dart:110:15)
2025-07-19 10:10:41.459271+0800 Runner[906:265995] flutter: ├
2025-07-19 10:10:41.459296+0800 Runner[906:265995] flutter: │ 🐛 ApiClient: uri: /api/app/v1/health/session/end/
2025-07-19 10:10:41.459323+0800 Runner[906:265995] flutter: └
2025-07-19 10:10:41.459296+0800 Runner[906:265995] flutter: │ 🐛 ApiClient: uri: /api/app/v1/health/session/end/
2025-07-19 10:10:41.459323+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:10:41.459932+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.459296+0800 Runner[906:265995] flutter: │ 🐛 ApiClient: uri: /api/app/v1/health/session/end/
2025-07-19 10:10:41.459323+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:10:41.459932+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:10:41.459970+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-19 10:10:41.460000+0800 Runner[906:265995] flutter: │ #1   LogInterceptor.onError (package:dio/src/interceptors/log.dart:111:15)
2025-07-19 10:10:41.460029+0800 Runner[906:265995] flutter: ├
2025-07-19 10:10:41.460053+0800 Runner[906:265995] flutter: │ 
2025-07-19 10:10:41.460053+0800 Runner[906:265995] flutter: │ \360\237🐛 ApiClient: DioException [request cancelled]: 用户未登录，请先登录
2025-07-19 10:10:41.460081+0800 Runner[906:265995] flutter: └
2025-07-19 10:10:41.460053+0800 Runner[906:265995] flutter: │ \360\237🐛 ApiClient: DioException [request cancelled]: 用户未登录，请先登录
2025-07-19 10:10:41.460081+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:10:41.460217+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.460248+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-19 10:10:41.460276+0800 Runner[906:265995] flutter: │ #1   LogInterceptor.onError (package:dio/src/interceptors/log.dart:115:15)
2025-07-19 10:10:41.460311+0800 Runner[906:265995] flutter: ├
2025-07-19 10:10:41.460335+0800 Runner[906:265995] flutter: │ 🐛 ApiClient:
2025-07-19 10:10:41.460363+0800 Runner[906:265995] flutter: └
2025-07-19 10:10:41.461136+0800 Runner[906:265995] flutter: \^[[38;5;196m┌
2025-07-19 10:10:41.461136+0800 Runner[906:265995] flutter: \^[[38;5;196m┌\342\224<…>
2025-07-19 10:10:41.461176+0800 Runner[906:265995] flutter: \^[[38;5;196m│ #0   DioMixin.request (package:dio/src/dio_mixin.dart:364:36)<…>
2025-07-19 10:10:41.461204+0800 Runner[906:265995] flutter: \^[[38;5;196m│ #1   DioMixin.post (package:dio/src/dio_mixin.dart:108:12)<…>
2025-07-19 10:10:41.461233+0800 Runner[906:265995] flutter: \^[[38;5;196m├<…>
2025-07-19 10:10:41.461258+0800 Runner[906:265995] flutter: \^[[38;5;196m│ ⛔ ApiClient Error: /api/app/v1/health/session/end/ - null<…>
2025-07-19 10:10:41.461284+0800 Runner[906:265995] flutter: \^[[38;5;196m└<…>
2025-07-19 10:10:41.462362+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.462404+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:681:17)<…>
2025-07-19 10:10:41.462432+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.462463+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.462489+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 会话结束标记成功，logout_time已记录<…>
2025-07-19 10:10:41.462515+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.462745+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.462745+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:10:41.462779+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._resetInitializationState (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:476:13)<…>
2025-07-19 10:10:41.462805+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:687:7)<…>
2025-07-19 10:10:41.462834+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.462925+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔧 AuthProvider: 完整初始化状态已重置，包括网络组件<…>
2025-07-19 10:10:41.462958+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.463054+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.463054+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:10:41.463084+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._resetInitializationState (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:477:13)<…>
2025-07-19 10:10:41.463114+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:687:7)<…>
2025-07-19 10:10:41.463143+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.463169+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🎯 AuthProvider: 下次登录将重新执行完整的初始化流程<…>
2025-07-19 10:10:41.463198+0800 Runner[906:265995] flutter: \^[[38;5;12m└行完整的初始化流程<…>
2025-07-19 10:10:41.463198+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342<…>
2025-07-19 10:10:41.463288+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.463320+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.logout (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:689:15)<…>
2025-07-19 10:10:41.463344+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:10:41.463371+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.463399+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: Logout process completed successfully<…>
2025-07-19 10:10:41.463425+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.476778+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:10:41.476866+0800 Runner[906:265995] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:239:21)
2025-07-19 10:10:41.476902+0800 Runner[906:265995] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-19 10:10:41.476938+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:10:41.476964+0800 Runner[906:265995] flutter: │ 🐛 Redirect: Unauthenticated user accessing public route (/login). Allowed.
2025-07-19 10:10:41.476990+0800 Runner[906:265995] flutter: └
2025-07-19 10:10:41.477284+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.477320+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._navigateToLogin.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:153:17)<…>
2025-07-19 10:10:41.477354+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)<…>
2025-07-19 10:10:41.477387+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.477779+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ SplashScreen: 成功导航到登录页<…>
2025-07-19 10:10:41.477810+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.477918+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.478045+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._navigateToLogin.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:157:17)<…>
2025-07-19 10:10:41.478133+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)<…>
2025-07-19 10:10:41.478291+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.478450+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🎯 SplashScreen: 登录页导航状态已更新<…>
2025-07-19 10:10:41.478565+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:10:41.807601+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:10:41.807703+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _MainLayoutScreenState.dispose (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:365:13)<…>
2025-07-19 10:10:41.807738+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   StatefulElement.unmount (package:flutter/src/widgets/framework.dart:5922:11)<…>
2025-07-19 10:10:41.807770+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:10:41.807811+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🧹 MainLayoutScreen: 阶段门监听器已清理<…>
2025-07-19 10:10:41.807841+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:18.822511+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:18.822638+0800 Runner[906:265995] flutter: │ #0   DeviceIdManager.getDeviceId (package:sweatmint/core/services/device_id_manager.dart:37:17)
2025-07-19 10:11:18.822706+0800 Runner[906:265995] flutter: │ #1   LoginProvider._getDeviceId (package:sweatmint/features/auth/presentation/providers/login_provider.dart:49:34)
2025-07-19 10:11:18.822757+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:18.822798+0800 Runner[906:265995] flutter: │ 🐛 DeviceIdManager: Using cached device ID: FCD1C9A5...
2025-07-19 10:11:18.822844+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:18.824004+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:18.824075+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthServiceImpl.login (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:69:15)<…>
2025-07-19 10:11:18.824122+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   LoginProvider.login (package:sweatmint/features/auth/presentation/providers/login_provider.dart:86:43)<…>
2025-07-19 10:11:18.824165+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:18.826176+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 AuthService: 开始用户登录流程 - <EMAIL><…>
2025-07-19 10:11:18.826229+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:18.826795+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:18.826795+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:18.826848+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthRepositoryImpl.login (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:93:13)<…>
2025-07-19 10:11:18.826888+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthServiceImpl.login (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:71:44)<…>
2025-07-19 10:11:18.826929+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\224<…>
2025-07-19 10:11:18.827049+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 AuthRepository: 用户登录请求 - <EMAIL><…>
2025-07-19 10:11:18.827093+0800 Runner[906:265995] flutter: \^[[38;5;12m\342\224<…>
2025-07-19 10:11:18.827049+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 AuthRepository: 用户登录请求 - <EMAIL><…>
2025-07-19 10:11:18.827093+0800 Runner[906:265995] flutter: \^[[38;5;12m\342\224└<…>
2025-07-19 10:11:18.827246+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>

2025-07-19 10:11:18.827347+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthRepositoryImpl.login (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:98:17)<…>
2025-07-19 10:11:18.827388+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthServiceImpl.login (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:71:44)<…>
2025-07-19 10:11:18.827490+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\224<…>
2025-07-19 10:11:18.827652+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔄 基础模式下登录，创建临时ApiClient和DataSource<…>
2025-07-19 10:11:18.827814+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:18.827652+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔄 基础模式下登录，创建临时ApiClient和DataSource<…>
2025-07-19 10:11:18.827814+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:18.829279+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:18.827652+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔄 基础模式下登录，创建临时ApiClient和DataSource<…>
2025-07-19 10:11:18.827814+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:18.829279+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:18.829344+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthRemoteDataSourceImpl.login (package:sweatmint/features/auth/data/datasources/auth_remote_datasource_impl.dart:90:15)<…>
2025-07-19 10:11:18.829389+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthRepositoryImpl.login (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:119:45)<…>
2025-07-19 10:11:18.829432+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:18.829481+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 AuthDataSource: 开始登录流程 - <EMAIL><…>
2025-07-19 10:11:18.829531+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>

2025-07-19 10:11:18.829969+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:18.830028+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthRemoteDataSourceImpl.login (package:sweatmint/features/auth/data/datasources/auth_remote_datasource_impl.dart:100:15)<…>
2025-07-19 10:11:18.830068+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthRepositoryImpl.login (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:119:45)<…>
2025-07-19 10:11:18.830111+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:18.830152+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 AuthDataSource: 发送登录请求 - <EMAIL> with device FCD1C9A5...<…>
2025-07-19 10:11:18.830192+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:18.832216+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:18.832298+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-19 10:11:18.832336+0800 Runner[906:265995] flutter: │ #1   LogInterceptor.onRequest (package:dio/src/interceptors/log.dart:68:13)
2025-07-19 10:11:18.832387+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:18.832430+0800 Runner[906:265995] flutter: │ 🐛 ApiClient: *** Request ***
2025-07-19 10:11:18.832474+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:18.833189+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:18.833249+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-19 10:11:18.833286+0800 Runner[906:265995] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-07-19 10:11:18.833329+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:18.833784+0800 Runner[906:265995] flutter: │ 🐛 ApiClient: uri: http://192.168.100.232:8000/api/app/v1/authentication/login/
2025-07-19 10:11:18.833829+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:18.833996+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:18.834619+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-19 10:11:18.834669+0800 Runner[906:265995] flutter: │ #1   LogInterceptor.onRequest (package:dio/src/interceptors/log.dart:94:13)
2025-07-19 10:11:18.834715+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:18.834753+0800 Runner[906:265995] flutter: │ 🐛 ApiClient:
2025-07-19 10:11:18.834793+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:18.834753+0800 Runner[906:265995] flutter: │ 🐛 ApiClient:
2025-07-19 10:11:18.834793+0800 Runner[906:265995] flutter: └\342
2025-07-19 10:11:19.128416+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.128555+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-19 10:11:19.128636+0800 Runner[906:265995] flutter: │ #1   LogInterceptor.onResponse (package:dio/src/interceptors/log.dart:101:13)
2025-07-19 10:11:19.128700+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.128748+0800 Runner[906:265995] flutter: │ 🐛 ApiClient: *** Response ***
2025-07-19 10:11:19.128790+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.129712+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.129795+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-19 10:11:19.129839+0800 Runner[906:265995] flutter: │ #1   LogInterceptor._printKV (package:dio/src/interceptors/log.dart:140:13)
2025-07-19 10:11:19.129882+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.130264+0800 Runner[906:265995] flutter: │ 🐛 ApiClient: uri: http://192.168.100.232:8000/api/app/v1/authentication/login/
2025-07-19 10:11:19.130307+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.130494+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.130494+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.130541+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-19 10:11:19.130578+0800 Runner[906:265995] flutter: │ #1   LogInterceptor._printResponse (package:dio/src/interceptors/log.dart:136:13)
2025-07-19 10:11:19.130620+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.130494+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.130541+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-19 10:11:19.130578+0800 Runner[906:265995] flutter: │ #1   LogInterceptor._printResponse (package:dio/src/interceptors/log.dart:136:13)
2025-07-19 10:11:19.130620+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.131387+0800 Runner[906:265995] flutter: │ 🐛 ApiClient:
2025-07-19 10:11:19.131434+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.130494+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.130541+0800 Runner[906:265995] flutter: │ #0   ApiClient._setupInterceptors.<anonymous closure> (package:sweatmint/core/network/api_client.dart:49:19)
2025-07-19 10:11:19.130578+0800 Runner[906:265995] flutter: │ #1   LogInterceptor._printResponse (package:dio/src/interceptors/log.dart:136:13)
2025-07-19 10:11:19.130620+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.131387+0800 Runner[906:265995] flutter: │ 🐛 ApiClient:
2025-07-19 10:11:19.131434+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.132363+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.132429+0800 Runner[906:265995] flutter: \^[[38;5;12m<…>
2025-07-19 10:11:19.132429+0800 Runner[906:265995] flutter: \^[[38;5;12m\342│ #0   AuthRemoteDataSourceImpl.login (package:sweatmint/features/auth/data/datasources/auth_remote_datasource_impl.dart:109:17)<…>
2025-07-19 10:11:19.132470+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.132514+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.133040+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthDataSource: 登录API调用成功 - <EMAIL><…>
2025-07-19 10:11:19.133089+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.135186+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.135271+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthRepositoryImpl.login (package:sweatmint/features/auth/data/repositories/auth_repository_impl.dart:125:17)<…>
2025-07-19 10:11:19.135314+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.135363+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.135409+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 基础模式登录成功<…>
2025-07-19 10:11:19.135454+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.135618+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.135682+0800 Runner[906:265995] flutter: \^[[38;5;12m<…>
2025-07-19 10:11:19.135682+0800 Runner[906:265995] flutter: \^[[38;5;12m\342│ #0   AuthServiceImpl.login (package:sweatmint/features/auth/domain/services/auth_service_impl.dart:77:15)<…>
2025-07-19 10:11:19.135825+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.135980+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.137056+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthService: 用户登录成功 - <EMAIL><…>
2025-07-19 10:11:19.137222+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.137818+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.137986+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   LoginProvider.login (package:sweatmint/features/auth/presentation/providers/login_provider.dart:91:15)<…>
2025-07-19 10:11:19.138216+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.138359+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.138544+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 Login successful! Storing tokens and updating auth status...<…>
2025-07-19 10:11:19.138763+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.141873+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.141948+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:495:13)<…>
2025-07-19 10:11:19.141993+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   LoginProvider.login (package:sweatmint/features/auth/presentation/providers/login_provider.dart:95:28)<…>
2025-07-19 10:11:19.142033+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.142070+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔐 登录成功处理开始 - Processing successful login...<…>
2025-07-19 10:11:19.142106+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.142291+0800 Runner[906:265995] flutter: \^[[38;5;196m┌<…>
2025-07-19 10:11:19.142291+0800 Runner[906:265995] flutter: \^[[38;5;196m┌\342\224<…>
2025-07-19 10:11:19.142329+0800 Runner[906:265995] flutter: \^[[38;5;196m│ #0   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:499:16)<…>
2025-07-19 10:11:19.142398+0800 Runner[906:265995] flutter: \^[[38;5;196m│ #1   LoginProvider.login (package:sweatmint/features/auth/presentation/providers/login_provider.dart:95:28)<…>
2025-07-19 10:11:19.142556+0800 Runner[906:265995] flutter: \^[[38;5;196m├<…>
2025-07-19 10:11:19.142664+0800 Runner[906:265995] flutter: \^[[38;5;196m│ ⛔ AuthProvider: Could not get UserService during loginSuccess.<…>
2025-07-19 10:11:19.142870+0800 Runner[906:265995] flutter: \^[[38;5;196m└<…>
2025-07-19 10:11:19.143168+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.143207+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:503:15)<…>
2025-07-19 10:11:19.143351+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   LoginProvider.login (package:sweatmint/features/auth/presentation/providers/login_provider.dart:95:28)<…>
2025-07-19 10:11:19.143494+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.143647+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 AuthProvider: Starting to process successful login...<…>
2025-07-19 10:11:19.143802+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.168360+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.168438+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   TokenManager.setTokens (package:sweatmint/core/services/token_manager.dart:92:14)<…>
2025-07-19 10:11:19.168475+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.168505+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.168534+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:11:19.168 (+0:00:57.486440)<…>
2025-07-19 10:11:19.168566+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.168595+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 TokenManager: Dual-Tokens saved successfully. Access token expires at: **********<…>
2025-07-19 10:11:19.168623+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.169157+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.169209+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   TokenManager._startBackgroundRefresh (package:sweatmint/core/services/token_manager.dart:355:12)<…>
2025-07-19 10:11:19.169241+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   TokenManager.setTokens (package:sweatmint/core/services/token_manager.dart:99:7)<…>
2025-07-19 10:11:19.169269+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.169618+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:11:19.169 (+0:00:57.487426)<…>
2025-07-19 10:11:19.169652+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.169677+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 TokenManager: Background refresh timer started.<…>
2025-07-19 10:11:19.169705+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.171340+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.171340+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.171374+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:516:15)<…>
2025-07-19 10:11:19.171403+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.171430+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.172193+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 新Token保存成功 - 登录状态确认<…>
2025-07-19 10:11:19.172229+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.172409+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>

2025-07-19 10:11:19.172684+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:522:15)<…>
2025-07-19 10:11:19.172723+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.172752+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.172784+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 用户状态设置为已认证<…>
2025-07-19 10:11:19.172811+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:19.175390+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.175390+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.175439+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:526:15)<…>
2025-07-19 10:11:19.175468+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.175495+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.176026+0800 Runner[906:265995] flutter: \^[[38;5;12m<…>
2025-07-19 10:11:19.176026+0800 Runner[906:265995] flutter: \^[[38;5;12m\342│ 💡 ✅ AuthProvider: UI状态更新通知已发送<…>
2025-07-19 10:11:19.176057+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.176026+0800 Runner[906:265995] flutter: \^[[38;5;12m\342│ 💡 ✅ AuthProvider: UI状态更新通知已发送<…>
2025-07-19 10:11:19.176057+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:19.176150+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.176185+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:530:17)<…>
2025-07-19 10:11:19.176212+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.176247+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.176273+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🚀 AuthProvider: 开<…>
2025-07-19 10:11:19.176273+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🚀 AuthProvider: 开\345始初始化完整服务，解决登出UnimplementedError问题<…>
2025-07-19 10:11:19.176300+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.176273+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🚀 AuthProvider: 开\345始初始化完整服务，解决登出UnimplementedError问题<…>
2025-07-19 10:11:19.176300+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342<…>
2025-07-19 10:11:19.176918+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.176956+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeCompleteServices (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:351:15)<…>
2025-07-19 10:11:19.176989+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:533:15)<…>
2025-07-19 10:11:19.177017+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>

2025-07-19 10:11:19.177044+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🚀 AuthProvider: 开始初始化完整服务<…>
2025-07-19 10:11:19.177070+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:19.177166+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.177166+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.177199+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeCompleteServices (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:355:17)<…>
2025-07-19 10:11:19.177225+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:533:15)<…>
2025-07-19 10:11:19.177252+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.177521+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔧 AuthProvider: 初始化ApiClient（无Context模式）<…>
2025-07-19 10:11:19.177555+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.177823+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.177823+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:11:19.177823+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-19 10:11:19.177857+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeApiClientWithoutContext (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:336:17)<…>
2025-07-19 10:11:19.177885+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider._initializeCompleteServices (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:356:15)<…>
2025-07-19 10:11:19.177914+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.178013+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🌐 初始化ApiClient（无Context模式）<…>
2025-07-19 10:11:19.178041+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.178348+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.178383+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeApiClientWithoutContext (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:340:17)<…>
2025-07-19 10:11:19.178415+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider._initializeCompleteServices (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:356:15)<…>
2025-07-19 10:11:19.178442+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.178472+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ ApiClient初始化完成（无Context模式）<…>
2025-07-19 10:11:19.178499+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.178707+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.178741+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeCompleteServices (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:361:17)<…>
2025-07-19 10:11:19.178771+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.178800+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.178825+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔧 AuthProvider: 切换AuthRepository到完整模式<…>
2025-07-19 10:11:19.178852+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.179145+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.179181+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeCompleteServices (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:369:17)<…>
2025-07-19 10:11:19.179209+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.179237+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.179263+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: AuthRepository已切换到完整模式<…>
2025-07-19 10:11:19.179289+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.179565+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.179565+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:11:19.179601+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeGlobalLifecycleManager (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:323:17)<…>
2025-07-19 10:11:19.179631+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.179660+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.179685+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 全局应用生命周期管理器初始化完成<…>
2025-07-19 10:11:19.179712+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.179817+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.179849+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeCompleteServices (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:375:15)<…>
2025-07-19 10:11:19.179873+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.179899+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.179926+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 完整服务初始化完成，登出功能现在可用<…>
2025-07-19 10:11:19.179954+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.180225+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.180262+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:537:17)<…>
2025-07-19 10:11:19.180328+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.180435+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.180547+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: v14.1架构合规 - 认证完成，健康数据流程由独立组件管理<…>
2025-07-19 10:11:19.180735+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.180940+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.180940+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:11:19.180974+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:538:17)<…>
2025-07-19 10:11:19.180997+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.181024+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.181049+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🎯 AuthProvider: 等待SplashScreen完成导航，MainLayoutScreen将执行步骤5<…>
2025-07-19 10:11:19.181128+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.181310+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.181310+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:11:19.181342+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:542:17)<…>
2025-07-19 10:11:19.181369+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.181395+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.181423+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔄 AuthProvider: 重置MainLayoutScreen步骤5状态，确保权限弹窗能够显示<…>
2025-07-19 10:11:19.181451+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.181586+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.181586+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.181745+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:548:17)<…>
2025-07-19 10:11:19.181908+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.182046+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.182190+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔄 AuthProvider: MainLayoutScreen步骤5状态重置（暂时跳过实际调用）<…>
2025-07-19 10:11:19.182336+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.182503+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.182503+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:11:19.182503+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-19 10:11:19.182536+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:550:17)<…>
2025-07-19 10:11:19.182561+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.182589+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.182619+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: MainLayoutScreen步骤5状态重置完成<…>
2025-07-19 10:11:19.182698+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:19.182889+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.182889+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.182922+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:556:17)<…>
2025-07-19 10:11:19.182947+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.182974+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.183047+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 完整服务初始化和登录后处理已安排<…>
2025-07-19 10:11:19.183203+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.183406+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.183406+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.183439+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.loginSuccess (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:564:15)<…>
2025-07-19 10:11:19.183462+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.183490+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.183517+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 阶段4：登录认证处理完成 - Login success processing completed<…>
2025-07-19 10:11:19.183552+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.185019+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.185019+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.185080+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   MyApp.build.<anonymous closure> (package:sweatmint/main.dart:308:17)<…>
2025-07-19 10:11:19.185110+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   Consumer.buildWithChild (package:provider/src/consumer.dart:179:19)<…>
2025-07-19 10:11:19.185137+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.185307+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 阶段1：显示基础MaterialApp界面 - 认证状态: AuthStatus.authenticated<…>
2025-07-19 10:11:19.185339+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.190274+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.190361+0800 Runner[906:265995] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:198:19)
2025-07-19 10:11:19.190390+0800 Runner[906:265995] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-19 10:11:19.190419+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.190447+0800 Runner[906:265995] flutter: │ 🐛 Redirect: At splash screen with authenticated user, allowing navigation to home.
2025-07-19 10:11:19.190475+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.197088+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.197210+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState.initState (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:32:13)<…>
2025-07-19 10:11:19.197242+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5842:55)<…>
2025-07-19 10:11:19.197274+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.197301+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🚀 SplashScreen: 开屏页面初始化<…>
2025-07-19 10:11:19.197330+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.199600+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.199641+0800 Runner[906:265995] flutter: │ #0   _SplashScreenState.build (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:394:13)
2025-07-19 10:11:19.199671+0800 Runner[906:265995] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-19 10:11:19.199698+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.199724+0800 Runner[906:265995] flutter: │ 🐛 🎨 SplashScreen: 构建UI
2025-07-19 10:11:19.199751+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.202187+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.202187+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.202187+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:11:19.202234+0800 Runner[906:265995] flutter: │ #0   _SplashScreenState.build.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:408:17)
2025-07-19 10:11:19.202262+0800 Runner[906:265995] flutter: │ #1   _Selector0State.buildWithChild.<anonymous closure> (package:provider/src/selector.dart:86:45)
2025-07-19 10:11:19.202300+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.202328+0800 Runner[906:265995] flutter: │ 🐛 🔄 SplashScreen: Provider状态更新 - AuthStatus.authenticated, Stage: LoginStage.stage1Startup, 业务逻辑完成: false
2025-07-19 10:11:19.202354+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.235729+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.235818+0800 Runner[906:265995] flutter: \^[[38;5;12m<…>
2025-07-19 10:11:19.235818+0800 Runner[906:265995] flutter: \^[[38;5;12m\342│ #0   _SplashScreenState._startStage2Loading (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:265:13)<…>
2025-07-19 10:11:19.235857+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _SplashScreenState.initState.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:36:7)<…>
2025-07-19 10:11:19.235890+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.235921+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔄 SplashScreen: 阶段2开始 - Loading数据准备（v14.1卡死修复版）<…>
2025-07-19 10:11:19.235951+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.236081+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.236081+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.236114+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:300:13)<…>
2025-07-19 10:11:19.236140+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _SplashScreenState._startStage2Loading (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:269:29)<…>
2025-07-19 10:11:19.236175+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.236214+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ⚡ SplashScreen: 开始执行阶段2与健康数据流集成<…>
2025-07-19 10:11:19.236245+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.236367+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.236396+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:307:15)<…>
2025-07-19 10:11:19.236422+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _SplashScreenState._startStage2Loading (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:269:29)<…>
2025-07-19 10:11:19.236450+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.236636+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🚀 SplashScreen: 启动AuthProvider认证检查<…>
2025-07-19 10:11:19.236665+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:19.236774+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.236774+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.236808+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:139:13)<…>
2025-07-19 10:11:19.236837+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:309:28)<…>
2025-07-19 10:11:19.236865+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.236900+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🚀 AuthProvider: 开始执行阶段2: Loading数据准备阶段<…>
2025-07-19 10:11:19.236929+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.237305+0800 Runner[906:265995] flutter: ┌<…>
2025-07-19 10:11:19.237305+0800 Runner[906:265995] flutter: ┌\342<…>
2025-07-19 10:11:19.237305+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:11:19.237342+0800 Runner[906:265995] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:198:19)
2025-07-19 10:11:19.237371+0800 Runner[906:265995] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-19 10:11:19.237398+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.237425+0800 Runner[906:265995] flutter: │ 🐛 Redirect: At splash screen with authenticated user, allowing navigation to home.
2025-07-19 10:11:19.237451+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.237666+0800 Runner[906:265995] flutter: \^[[38;5;12m┌
2025-07-19 10:11:19.237666+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342
2025-07-19 10:11:19.237666+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-19 10:11:19.237702+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:149:15)<…>
2025-07-19 10:11:19.237734+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:309:28)<…>
2025-07-19 10:11:19.237761+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.237787+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 📍 开始阶段2.1: 认证状态检查 (200-800ms)<…>
2025-07-19 10:11:19.237820+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.237787+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 📍 开始阶段2.1: 认证状态检查 (200-800ms)<…>
2025-07-19 10:11:19.237820+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342<…>
2025-07-19 10:11:19.237930+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.237787+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 📍 开始阶段2.1: 认证状态检查 (200-800ms)<…>
2025-07-19 10:11:19.237820+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342<…>
2025-07-19 10:11:19.237930+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.237963+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:213:13)<…>
2025-07-19 10:11:19.237996+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:150:13)<…>
2025-07-19 10:11:19.238023+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.238050+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔐 开始阶段2.1认证状态检查 - 1752891079237<…>
2025-07-19 10:11:19.238077+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.238183+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.238183+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:11:19.238265+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:217:15)<…>
2025-07-19 10:11:19.238301+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:150:13)<…>
2025-07-19 10:11:19.238334+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.238364+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 📋 步骤1: 初始化 ApiClient / TokenManager<…>
2025-07-19 10:11:19.238393+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.238546+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.238583+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeNetworkComponents (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:305:15)<…>
2025-07-19 10:11:19.238613+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:219:15)<…>
2025-07-19 10:11:19.238641+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.238667+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 网络组件初始化完成<…>
2025-07-19 10:11:19.238694+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.239641+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.239641+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.239694+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:223:15)<…>
2025-07-19 10:11:19.239723+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.239752+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\224<…>
2025-07-19 10:11:19.239813+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 📋 步骤1.1: 初始化全局应用生命周期管理器<…>
2025-07-19 10:11:19.239844+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:19.239813+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 📋 步骤1.1: 初始化全局应用生命周期管理器<…>
2025-07-19 10:11:19.239844+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342<…>
2025-07-19 10:11:19.239971+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.239813+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 📋 步骤1.1: 初始化全局应用生命周期管理器<…>
2025-07-19 10:11:19.239844+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342<…>
2025-07-19 10:11:19.239971+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:11:19.240015+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeGlobalLifecycleManager (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:323:17)<…>
2025-07-19 10:11:19.240044+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.240073+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.240202+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 全局应用生命周期管理器初始化完成<…>
2025-07-19 10:11:19.240236+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.240344+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.240379+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:229:15)<…>
2025-07-19 10:11:19.240408+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.240435+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.240461+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 📋 步骤2: 读取本地Token<…>
2025-07-19 10:11:19.240487+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.243676+0800 Runner[906:265995] flutter: ┌<…>
2025-07-19 10:11:19.243676+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:11:19.243745+0800 Runner[906:265995] flutter: │ #0   TokenManager.getAccessToken (package:sweatmint/core/services/token_manager.dart:130:12)
2025-07-19 10:11:19.243789+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.243817+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.243842+0800 Runner[906:265995] flutter: │ 10:11:19.243 (+0:00:57.561773)
2025-07-19 10:11:19.243870+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.243897+0800 Runner[906:265995] flutter: │ 🐛 TokenManager: Returning existing valid token.
2025-07-19 10:11:19.243948+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.245668+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.245716+0800 Runner[906:265995] flutter: │ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:233:15)
2025-07-19 10:11:19.245747+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.245777+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.245853+0800 Runner[906:265995] flutter: │ 🐛 🔍 Token状态: AccessToken=存在, RefreshToken=存在
2025-07-19 10:11:19.245882+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.245989+0800 Runner[906:265995] flutter: \^[[38;5;12m┌
2025-07-19 10:11:19.245989+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342
2025-07-19 10:11:19.245989+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-19 10:11:19.246024+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:237:17)<…>
2025-07-19 10:11:19.246054+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.246091+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.246116+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 步骤3: 发现Access Token，验证有效性<…>
2025-07-19 10:11:19.246144+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.246116+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 步骤3: 发现Access Token，验证有效性<…>
2025-07-19 10:11:19.246144+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:19.246480+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.246517+0800 Runner[906:265995] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:198:19)
2025-07-19 10:11:19.246545+0800 Runner[906:265995] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-19 10:11:19.246571+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.246599+0800 Runner[906:265995] flutter: │ 🐛 Redirect: At splash screen with authenticated user, allowing navigation to home.
2025-07-19 10:11:19.246625+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.246837+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.246873+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:243:17)<…>
2025-07-19 10:11:19.246898+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.246928+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.246956+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 认证状态已更新为authenticated，UI通知已发送<…>
2025-07-19 10:11:19.246990+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.247086+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.247115+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:246:17)<…>
2025-07-19 10:11:19.247139+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.247167+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.247193+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 自动登录成功，健康数据流程由HealthDataFlowService独立管理<…>
2025-07-19 10:11:19.247220+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:19.247325+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.247325+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.247359+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage21AuthCheck (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:294:13)<…>
2025-07-19 10:11:19.247384+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.247413+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.247441+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 阶段2.1完成，耗时: 9ms，认证状态: AuthStatus.authenticated<…>
2025-07-19 10:11:19.247469+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.247569+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.247601+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:170:15)<…>
2025-07-19 10:11:19.247626+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.247652+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.247679+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 📍 阶段2.2: v14.1架构合规 - AuthProvider跳过健康数据流程处理<…>
2025-07-19 10:11:19.247706+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.247795+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.247795+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:11:19.247795+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-19 10:11:19.247826+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:173:15)<…>
2025-07-19 10:11:19.247851+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.247878+0800 Runner[906:265995] flutter: \^[[38;5;12m├<<\342…>
2025-07-19 10:11:19.247905+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 📍 开始阶段2.3: 业务数据同步 (1.5s-2.5s)<…>
2025-07-19 10:11:19.247931+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.248027+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.248027+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:11:19.248064+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:388:13)<…>
2025-07-19 10:11:19.248094+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:174:13)<…>
2025-07-19 10:11:19.248125+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\224<…>
2025-07-19 10:11:19.248149+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔄 开始阶段2.3: 业务数据同步<…>
2025-07-19 10:11:19.248176+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.248275+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.248306+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._preloadHomePageData (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:412:15)<…>
2025-07-19 10:11:19.248333+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:393:9)<…>
2025-07-19 10:11:19.248360+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.248385+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🏠 开始预加载首页数据<…>
2025-07-19 10:11:19.248411+0800 Runner[906:265995] flutter: \^[[38;5;12m└预加载首页数据<…>
2025-07-19 10:11:19.248411+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342<…>
2025-07-19 10:11:19.248581+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.248618+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   ViewModelMixin.executeAsyncAction (package:sweatmint/core/mixins/view_model_mixin.dart:77:13)<…>
2025-07-19 10:11:19.248650+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   HomeProvider.loadHomeData (package:sweatmint/features/home/<USER>/providers/home_provider.dart:274:11)<…>
2025-07-19 10:11:19.248680+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.248746+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 HomeDashboardData operation: Starting...<…>
2025-07-19 10:11:19.248774+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.248920+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.248954+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:276:16)<…>
2025-07-19 10:11:19.248986+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   ViewModelMixin.executeAsyncAction (package:sweatmint/core/mixins/view_model_mixin.dart:80:36)<…>
2025-07-19 10:11:19.249015+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.249039+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:11:19.248 (+0:00:57.567186)<…>
2025-07-19 10:11:19.249066+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.249092+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 开始加载首页聚合数据 (forceRefresh: false)<…>
2025-07-19 10:11:19.249118+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:19.249289+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.249289+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.249333+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:21:12)<…>
2025-07-19 10:11:19.249362+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:289:65)<…>
2025-07-19 10:11:19.249390+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.249416+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:11:19.249 (+0:00:57.567567)<…>
2025-07-19 10:11:19.249443+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.249470+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 DashboardRemoteDataSource: 开始获取首页聚合数据 (forceRefresh: false)<…>
2025-07-19 10:11:19.249498+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.249627+0800 Runner[906:265995] flutter: ┌<…>
2025-07-19 10:11:19.249627+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.249659+0800 Runner[906:265995] flutter: │ #0   CacheManager.get (package:sweatmint/core/data/cache_manager.dart:93:14)
2025-07-19 10:11:19.249687+0800 Runner[906:265995] flutter: │ #1   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:38:47)
2025-07-19 10:11:19.249717+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.249820+0800 Runner[906:265995] flutter: │ 10:11:19.249 (+0:00:57.567907)
2025-07-19 10:11:19.249854+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.249887+0800 Runner[906:265995] flutter: │ 🐛 CacheManager.get: 获取缓存 dashboard_home
2025-07-19 10:11:19.249915+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.250073+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.250108+0800 Runner[906:265995] flutter: │ #0   CacheManager.get (package:sweatmint/core/data/cache_manager.dart:98:16)
2025-07-19 10:11:19.250138+0800 Runner[906:265995] flutter: │ #1   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:38:47)
2025-07-19 10:11:19.250165+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.250189+0800 Runner[906:265995] flutter: │ 10:11:19.249 (+0:00:57.568342)
2025-07-19 10:11:19.250221+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.250246+0800 Runner[906:265995] flutter: │ 🐛 CacheManager.get: 内存缓存命中 dashboard_home
2025-07-19 10:11:19.250273+0800 Runner[906:265995] flutter: └中 dashboard_home
2025-07-19 10:11:19.250273+0800 Runner[906:265995] flutter: └\342
2025-07-19 10:11:19.250476+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.250623+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeVipService (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:440:15)<…>
2025-07-19 10:11:19.250727+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:394:9)<…>
2025-07-19 10:11:19.250923+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.251060+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 💎 初始化VIP服务<…>
2025-07-19 10:11:19.251179+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.251498+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.251585+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeVipService (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:442:15)<…>
2025-07-19 10:11:19.251780+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:394:9)<…>
2025-07-19 10:11:19.252395+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.252537+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ VIP服务初始化完成<…>
2025-07-19 10:11:19.252640+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.252822+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.252853+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeEventSyncService (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:451:15)<…>
2025-07-19 10:11:19.255031+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:395:9)<…>
2025-07-19 10:11:19.255066+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.255093+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔄 初始化事件同步服务<…>
2025-07-19 10:11:19.255124+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.255248+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.255489+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._initializeEventSyncService (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:453:15)<…>
2025-07-19 10:11:19.255527+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:395:9)<…>
2025-07-19 10:11:19.255557+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.255584+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 事件同步服务初始化完成<…>
2025-07-19 10:11:19.255618+0800 Runner[906:265995] flutter: \^[[38;5;12m└…>
2025-07-19 10:11:19.255618+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:19.256498+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.256537+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   DashboardRemoteDataSourceImpl.getHomeDashboardData (package:sweatmint/features/dashboard/data/datasources/dashboard_remote_data_source.dart:40:18)<…>
2025-07-19 10:11:19.256574+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.256603+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.256628+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:11:19.256 (+0:00:57.574415)<…>
2025-07-19 10:11:19.256654+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.256721+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 DashboardRemoteDataSource: 缓存命中，返回缓存数据<…>
2025-07-19 10:11:19.256755+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.257888+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.257926+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:294:18)
2025-07-19 10:11:19.257952+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.257980+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.258113+0800 Runner[906:265995] flutter: │ 10:11:19.257 (+0:00:57.576148)
2025-07-19 10:11:19.258147+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.258173+0800 Runner[906:265995] flutter: │ 🐛 ----- 首页原始DTO数据开始 -----
2025-07-19 10:11:19.258199+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.258387+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.258433+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:295:18)
2025-07-19 10:11:19.258459+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.258484+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.258508+0800 Runner[906:265995] flutter: │ 10:11:19.258 (+0:00:57.576657)
2025-07-19 10:11:19.258535+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.258576+0800 Runner[906:265995] flutter: │ 🐛 DashboardDto.todaySummaryJson = {date: 2025-07-19, total_tasks: 4, completed_tasks: 0, completion_percentage: 0, earned_today: {swmt: 0.00, exp: 0}, current_streak: 0, health_data: {steps: 0, distance: 0.0, calories: 0}, next_tasks: [{id: 116, name: 距离02-L3-6.5, type: distance, icon: null, total_swmt: 182.00, total_exp: 390}, {id: 113, name: 步数02-L3-5000步-权重2, type: steps, icon: null, total_swmt: 1143.80, total_exp: 481}, {id: 119, name: 广告02-L3-100权重-40秒, type: ad, icon: null, total_swmt: 159.60, total_exp: 1423}], all_tasks_completed: false}
2025-07-19 10:11:19.258606+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.258741+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.258775+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:300:20)
2025-07-19 10:11:19.258803+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.258829+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.258853+0800 Runner[906:265995] flutter: │ 10:11:19.258 (+0:00:57.577023)
2025-07-19 10:11:19.258879+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.258904+0800 Runner[906:265995] flutter: │ 🐛 原始API返回 earned_today = {swmt: 0.00, exp: 0}
2025-07-19 10:11:19.258932+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.259065+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.259065+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.259100+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:306:22)
2025-07-19 10:11:19.259129+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.259155+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.259181+0800 Runner[906:265995] flutter: │ 10:11:19.258 (+0:00:57.577349)
2025-07-19 10:11:19.259207+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.259232+0800 Runner[906:265995] flutter: │ 🐛 原始API返回 earned_today.swmt = 0.00 (类型: String)
2025-07-19 10:11:19.259257+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.259232+0800 Runner[906:265995] flutter: │ 🐛 原始API返回 earned_today.swmt = 0.00 (类型: String)
2025-07-19 10:11:19.259257+0800 Runner[906:265995] flutter: └\342
2025-07-19 10:11:19.259232+0800 Runner[906:265995] flutter: │ 🐛 原始API返回 earned_today.swmt = 0.00 (类型: String)
2025-07-19 10:11:19.259257+0800 Runner[906:265995] flutter: └\342\342\224
2025-07-19 10:11:19.259386+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.259232+0800 Runner[906:265995] flutter: │ 🐛 原始API返回 earned_today.swmt = 0.00 (类型: String)
2025-07-19 10:11:19.259257+0800 Runner[906:265995] flutter: └\342\342\224
2025-07-19 10:11:19.259386+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.259232+0800 Runner[906:265995] flutter: │ 🐛 原始API返回 earned_today.swmt = 0.00 (类型: String)
2025-07-19 10:11:19.259257+0800 Runner[906:265995] flutter: └\342\342\224
2025-07-19 10:11:19.259386+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:11:19.259417+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:307:22)
2025-07-19 10:11:19.259439+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.259465+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.259489+0800 Runner[906:265995] flutter: │ 10:11:19.259 (+0:00:57.577669)
2025-07-19 10:11:19.259515+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.259541+0800 Runner[906:265995] flutter: │ 🐛 原始API返回 earned_today.exp = 0 (类型: int)
2025-07-19 10:11:19.259569+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.259691+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.259733+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:314:18)
2025-07-19 10:11:19.259759+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.259785+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.259810+0800 Runner[906:265995] flutter: │ 10:11:19.259 (+0:00:57.577974)
2025-07-19 10:11:19.259836+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.259860+0800 Runner[906:265995] flutter: │ 🐛 ----- 首页原始DTO数据结束 -----
2025-07-19 10:11:19.259885+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.260018+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.260052+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:192:12)<…>
2025-07-19 10:11:19.260082+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)<…>
2025-07-19 10:11:19.260111+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.260137+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:11:19.259 (+0:00:57.578292)<…>
2025-07-19 10:11:19.260163+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.260439+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 HomeDashboardData.fromDto - 开始解析聚合数据<…>
2025-07-19 10:11:19.260475+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.260612+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.260645+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:202:14)
2025-07-19 10:11:19.260674+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.260701+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.260726+0800 Runner[906:265995] flutter: │ 10:11:19.260 (+0:00:57.578888)
2025-07-19 10:11:19.260752+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.260778+0800 Runner[906:265995] flutter: │ 🐛 ===== Dashboard API 响应数据 =====
2025-07-19 10:11:19.260804+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.260960+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.260996+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:203:14)
2025-07-19 10:11:19.261024+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.261052+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.261075+0800 Runner[906:265995] flutter: │ 10:11:19.260 (+0:00:57.579236)
2025-07-19 10:11:19.261101+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.261179+0800 Runner[906:265995] flutter: │ 🐛 UserProfile数据: {user_id: S0401025, email: <EMAIL>, username: test_user2, avatar: avatar_14.png, member_level: {id: 20, name: Level 3, level: 3, min_exp: 10000, max_exp: 99999, daily_task_count: 4, extra_task_count: 2, benefits: Complete 4 daily tasks, activate 2 extra tasks}, exp: 11586, swmt_balance: 10995.2, usdt_balance: 0.********, total_commission: 0.0, is_agent: false, is_active_member: false, referral_code: R8728568, referral_count: 0, account_status: Active, can_withdraw_status: Withdrawal allowed, agent_id: null}
2025-07-19 10:11:19.261209+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.261387+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.261387+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.261387+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:11:19.261421+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:204:14)
2025-07-19 10:11:19.261449+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.261475+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.261501+0800 Runner[906:265995] flutter: │ 10:11:19.261 (+0:00:57.579660)
2025-07-19 10:11:19.261528+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.261575+0800 Runner[906:265995] flutter: │ 🐛 VipStatus数据: {has_vip: true, vip_info: {level_id: 15, name: VIP 2, level: 2, upgrade_time: 2025-05-31T16:50:24.485657Z, refund_active: false, refund_required_days: 26, completed_days: 0, refund_progress: 0, refund_status_label: 未开始, active_plans_count: 0, vip_level: {name: VIP 2, level: 2, swmt_bonus_rate: 1.4, exp_bonus_rate: 1.5, upgrade_fee: 300.0, refund_enabled: true, refund_days: 26}}, features: [{name: 任务加成, description: 所有任务SWMT奖励增加40%, bonus_rate: 0.4, is_active: true}, {name: 经验加成, description: 获得经验值增加50%, bonus_rate: 0.5, is_active: true}, {name: 兑换手续费, description: 享受商品兑换手续费减免, physical_rate: 0.05, virtual_rate: 0.05, is_active: true}, {name: 专属任务, description: 解锁VIP专属任务, special_tasks: true, is_active: true}], availableLevel: {id: 16, name: VIP 3, level: 3, price: {amount: 500.00, currency: USDT, refund_days: 28}, swmt_bonus_rate: 1.6, exp_bonus_rate: 1.7}, additional_stats: {additional_swmt_earned: 2657.92, additional_exp_earned: 2725, actual_refund_amount: 0.0, description: {swmt: 通过VIP加成功能获得的总额外SWMT, exp: 通过VIP加成功能获得的总额外XP, refund: VIP返还计划实际返还的USDT金额}}}
2025-07-19 10:11:19.261811+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.261972+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:11:19.261972+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.262007+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:205:14)
2025-07-19 10:11:19.262041+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.262068+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.262092+0800 Runner[906:265995] flutter: │ 10:11:19.261 (+0:00:57.580242)
2025-07-19 10:11:19.262118+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.262092+0800 Runner[906:265995] flutter: │ 10:11:19.261 (+0:00:57.580242)
2025-07-19 10:11:19.262118+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:11:19.262092+0800 Runner[906:265995] flutter: │ 10:11:19.261 (+0:00:57.580242)
2025-07-19 10:11:19.262118+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:11:19.262155+0800 Runner[906:265995] flutter: │ 🐛 TodaySummary数据: {date: 2025-07-19, total_tasks: 4, completed_tasks: 0, completion_percentage: 0, earned_today: {swmt: 0.00, exp: 0}, current_streak: 0, health_data: {steps: 0, distance: 0.0, calories: 0}, next_tasks: [{id: 116, name: 距离02-L3-6.5, type: distance, icon: null, total_swmt: 182.00, total_exp: 390}, {id: 113, name: 步数02-L3-5000步-权重2, type: steps, icon: null, total_swmt: 1143.80, total_exp: 481}, {id: 119, name: 广告02-L3-100权重-40秒, type: ad, icon: null, total_swmt: 159.60, total_exp: 1423}], all_tasks_completed: false}
2025-07-19 10:11:19.262246+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.262487+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:11:19.262487+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.262520+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:206:14)
2025-07-19 10:11:19.262550+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.262575+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.262599+0800 Runner[906:265995] flutter: │ 10:11:19.262 (+0:00:57.580755)
2025-07-19 10:11:19.262624+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.262810+0800 Runner[906:265995] flutter: │ 🐛 DailyTasks数据: {total_tasks: 4, completed_tasks: 0, completion_percentage: 0, user_level: {name: Level 3, level: 3}, user_vip: {has_vip: true, name: VIP 2, level: 2}, vip_refund: null, tasks: [{id: 116, name: 距离02-L3-6.5, type: distance, icon: distance_icon.png, description: <p>距离02-L3-6.5</p>, rewards: {base: {swmt: 130.00, exp: 260}, vip_bonus: {swmt: 52.00, exp: 130}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 182.00, exp: 390}}, requirements: {steps_required: null, distance_required: 6.5, ad_duration: null, verification_type: health_app}, progress: {current_steps: null, current_distance: null, percentage: 0, last_updated: 2025-07-19 00:17:35}, status: pending, unlock_time: 2025-07-19T00:00:00, expire_time: 2025-07-19T23:59:59.999999, can_complete: false, completed_at: null}, {id: 113, name: 步数02-L3-5000步-权重2, type: steps, icon: steps_icon.png, description: <p>步数02-L3-5000步-权重2</p>, rewards: {base: {swmt: 817.00, exp: 321}, vip_bonus: {swmt: 326.80, exp: 160}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 1143.80, exp: 481}}, requirements: {steps_required: 5000, distance_required: null, ad_duration: null, verification_type: health_app}, progress: {current_steps: 0, current_distance: null, percentage: 0, last_updated: 2025-07-19 00:17:35}, status: pending, unlock_time: 2025-07-19T00:00:00, expire_time: 2025-07-19T23:59:59.999999, can_complete: false, completed_at: null}, {id: 119, name: 广告02-L3-100权重-40秒, type: ad, icon: ad_icon.png, description: <p>广告02-L3-100权重-40秒</p>, rewards: {base: {swmt: 114.00, exp: 949}, vip_bonus: {swmt: 45.60, exp: 474}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 159.60, exp: 1423}}, requirements: {steps_required: null, distance_required: null, ad_duration: 40, verification_type: system}, progress: {current_steps: null, current_distance: null, percentage: 0, last_updated: 2025-07-19 00:17:35}, status: pending, unlock_time: 2025-07-19T00:00:00, expire_time: 2025-07-19T23:59:59.999999, can_complete: true, completed_at: null}, {id: 118, name: 广告01-L3-100权重-30秒, type: ad, icon: ad_icon.png, description: <p>广告01-L3-100权重</p>, rewards: {base: {swmt: 116.00, exp: 201}, vip_bonus: {swmt: 46.40, exp: 100}, addon_bonus: {swmt: 0.00, exp: 0}, total: {swmt: 162.40, exp: 301}}, requirements: {steps_required: null, distance_required: null, ad_duration: 30, verification_type: system}, progress: {current_steps: null, current_distance: null, percentage: 0, last_updated: 2025-07-19 00:17:35}, status: pending, unlock_time: 2025-07-19T00:00:00, expire_time: 2025-07-19T23:59:59.999999, can_complete: true, completed_at: null}]}
2025-07-19 10:11:19.263255+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.263461+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.263495+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:207:14)
2025-07-19 10:11:19.263522+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.263549+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.263495+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:207:14)
2025-07-19 10:11:19.263522+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.263549+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.263574+0800 Runner[906:265995] flutter: │ 10:11:19.263 (+0:00:57.581711)
2025-07-19 10:11:19.263600+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.263645+0800 Runner[906:265995] flutter: │ 🐛 AddonTasks数据: {active_addon_tasks: 0, completed_addon_tasks: 0, max_addon_tasks: 2, available_slots: 2, current_member_level: {name: Level 3, level: 20}, addon_tasks: [{id: 142, name: 拉1人-L123, icon: share_icon.png, description: <p>拉1人-L123</p>, referral_required: 1, swmt_bonus_rate: 1.3, exp_bonus_rate: 1.2, progress: {current_referrals: 0, required_referrals: 1, percentage: 0, status: pending}, expire_time: null, unlock_requirements: Refer 1 new user(s) to unlock this task., can_activate: true, unlock_message: 需要再邀请 1 位新用户才能完成此任务 (当前: 0/1), status: pending, completion_order: 1, is_next_pending_referral: false}, {id: 139, name: 拉2人-L123, icon: share_icon.png, description: <p>拉2人-L123</p>, referral_required: 2, swmt_bonus_rate: 1.4, exp_bonus_rate: 1.22, progress: {current_referrals: 0, required_referrals: 2, percentage: 0, status: pending}, expire_time: null, unlock_requirements: Refer 2 new user(s) to unlock this task., can_activate: true, unlock_message: 需要再邀请 2 位新用户才能完成此任务 (当前: 0/2), status: pending, completion_order: 2, is_next_pending_referral: false}]}
2025-07-19 10:11:19.263832+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.263981+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.263981+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.263981+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:11:19.264010+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:208:14)
2025-07-19 10:11:19.264035+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.264060+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.263981+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:11:19.264010+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:208:14)
2025-07-19 10:11:19.264035+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.264060+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.264084+0800 Runner[906:265995] flutter: │ 10:11:19.263 (+0:00:57.582241)
2025-07-19 10:11:19.264115+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.263981+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:11:19.264010+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:208:14)
2025-07-19 10:11:19.264035+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.264060+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.264084+0800 Runner[906:265995] flutter: │ 10:11:19.263 (+0:00:57.582241)
2025-07-19 10:11:19.264115+0800 Runner[906:265995] flutter: ├\342
2025-07-19 10:11:19.263981+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:11:19.264010+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:208:14)
2025-07-19 10:11:19.264035+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.264060+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.264084+0800 Runner[906:265995] flutter: │ 10:11:19.263 (+0:00:57.582241)
2025-07-19 10:11:19.264115+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:11:19.264202+0800 Runner[906:265995] flutter: │ 🐛 ================================
2025-07-19 10:11:19.264336+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.264628+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.264661+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:215:18)
2025-07-19 10:11:19.264686+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.264713+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.264830+0800 Runner[906:265995] flutter: │ 10:11:19.264 (+0:00:57.582899)
2025-07-19 10:11:19.264934+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.264830+0800 Runner[906:265995] flutter: │ 10:11:19.264 (+0:00:57.582899)
2025-07-19 10:11:19.264934+0800 Runner[906:265995] flutter: ├\342
2025-07-19 10:11:19.265065+0800 Runner[906:265995] flutter: │ 🐛 已解析userProfile，username: test_user2
2025-07-19 10:11:19.265206+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.265508+0800 Runner[906:265995] flutter: 🔍 VipStatusDataDto解析additionalStats:
2025-07-19 10:11:19.265561+0800 Runner[906:265995] flutter:   json.keys: [has_vip, vip_info, features, availableLevel, additional_stats]
2025-07-19 10:11:19.265597+0800 Runner[906:265995] flutter:   additionalStats字段存在(驼峰): false
2025-07-19 10:11:19.265703+0800 Runner[906:265995] flutter:   additional_stats字段存在(下划线): true
2025-07-19 10:11:19.265812+0800 Runner[906:265995] flutter:   解析后的additionalStats != null: true
2025-07-19 10:11:19.265981+0800 Runner[906:265995] flutter:   additionalStats类型: _Map<String, dynamic>
2025-07-19 10:11:19.266104+0800 Runner[906:265995] flutter:   additionalStats内容: {additional_swmt_earned: 2657.92, additional_exp_earned: 2725, actual_refund_amount: 0.0, description: {swmt: 通过VIP加成功能获得的总额外SWMT, exp: 通过VIP加成功能获得的总额外XP, refund: VIP返还计划实际返还的USDT金额}}
2025-07-19 10:11:19.266241+0800 Runner[906:265995] flutter:   ✅ AdditionalStatsDto解析成功: SWMT=2657.92, XP=2725.0
2025-07-19 10:11:19.266626+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:11:19.266660+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:230:18)
2025-07-19 10:11:19.266766+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.266892+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.267017+0800 Runner[906:265995] flutter: │ 10:11:19.266 (+0:00:57.584877)
2025-07-19 10:11:19.267136+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.267269+0800 Runner[906:265995] flutter: │ 🐛 已解析vipStatus，hasVip: true
2025-07-19 10:11:19.267380+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.267628+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.267662+0800 Runner[906:265995] flutter: │ #0   new TodaySummaryDataDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:166:12)
2025-07-19 10:11:19.267758+0800 Runner[906:265995] flutter: │ #1   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:242:59)
2025-07-19 10:11:19.267884+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.267992+0800 Runner[906:265995] flutter: │ 10:11:19.267 (+0:00:57.585894)
2025-07-19 10:11:19.268092+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.268188+0800 Runner[906:265995] flutter: │ 🐛 TodaySummaryDataDto.fromJson: received json for earned_today: {swmt: 0.00, exp: 0}
2025-07-19 10:11:19.268314+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.268639+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.268719+0800 Runner[906:265995] flutter: │ #0   new EarnedTodayDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:34:12)
2025-07-19 10:11:19.268833+0800 Runner[906:265995] flutter: │ #1   new TodaySummaryDataDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:178:28)
2025-07-19 10:11:19.268949+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.269052+0800 Runner[906:265995] flutter: │ 10:11:19.268 (+0:00:57.586868)
2025-07-19 10:11:19.269209+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.269052+0800 Runner[906:265995] flutter: │ 10:11:19.268 (+0:00:57.586868)
2025-07-19 10:11:19.269209+0800 Runner[906:265995] flutter: ├\342
2025-07-19 10:11:19.269052+0800 Runner[906:265995] flutter: │ 10:11:19.268 (+0:00:57.586868)
2025-07-19 10:11:19.269209+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:11:19.269310+0800 Runner[906:265995] flutter: │ 🐛 EarnedTodayDto.fromJson - 原始数据: {swmt: 0.00, exp: 0}
2025-07-19 10:11:19.269414+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.269052+0800 Runner[906:265995] flutter: │ 10:11:19.268 (+0:00:57.586868)
2025-07-19 10:11:19.269209+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:11:19.269310+0800 Runner[906:265995] flutter: │ 🐛 EarnedTodayDto.fromJson - 原始数据: {swmt: 0.00, exp: 0}
2025-07-19 10:11:19.269414+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.269683+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.269714+0800 Runner[906:265995] flutter: │ #0   new EarnedTodayDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:35:12)
2025-07-19 10:11:19.269758+0800 Runner[906:265995] flutter: │ #1   new TodaySummaryDataDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:178:28)
2025-07-19 10:11:19.269920+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.270028+0800 Runner[906:265995] flutter: │ 10:11:19.269 (+0:00:57.587923)
2025-07-19 10:11:19.270126+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.270280+0800 Runner[906:265995] flutter: │ 🐛 EarnedTodayDto.fromJson - swmt类型: String, 值: 0.00
2025-07-19 10:11:19.270415+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.270692+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.270726+0800 Runner[906:265995] flutter: │ #0   new EarnedTodayDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:36:12)
2025-07-19 10:11:19.270751+0800 Runner[906:265995] flutter: │ #1   new TodaySummaryDataDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:178:28)
2025-07-19 10:11:19.270867+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.271405+0800 Runner[906:265995] flutter: │ 10:11:19.270 (+0:00:57.588949)
2025-07-19 10:11:19.271432+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.271465+0800 Runner[906:265995] flutter: │ 🐛 EarnedTodayDto.fromJson - exp类型: int, 值: 0
2025-07-19 10:11:19.271495+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.271698+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.271748+0800 Runner[906:265995] flutter: │ #0   new EarnedTodayDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:69:12)
2025-07-19 10:11:19.271775+0800 Runner[906:265995] flutter: │ #1   new TodaySummaryDataDto.fromJson (package:sweatmint/features/tasks/data/dto/today_summary_dto.dart:178:28)
2025-07-19 10:11:19.271801+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.272052+0800 Runner[906:265995] flutter: │ 10:11:19.271 (+0:00:57.589959)
2025-07-19 10:11:19.272103+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.272132+0800 Runner[906:265995] flutter: │ 🐛 EarnedTodayDto.fromJson - 解析后: swmt=0.00, exp=0
2025-07-19 10:11:19.272209+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.272424+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.272424+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.272424+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:11:19.272459+0800 Runner[906:265995] flutter: │ #0   new TodaySummary.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:135:12)
2025-07-19 10:11:19.272594+0800 Runner[906:265995] flutter: │ #1   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:248:39)
2025-07-19 10:11:19.272624+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.272723+0800 Runner[906:265995] flutter: │ 10:11:19.272 (+0:00:57.590660)
2025-07-19 10:11:19.272867+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.272723+0800 Runner[906:265995] flutter: │ 10:11:19.272 (+0:00:57.590660)
2025-07-19 10:11:19.272867+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:11:19.272723+0800 Runner[906:265995] flutter: │ 10:11:19.272 (+0:00:57.590660)
2025-07-19 10:11:19.272867+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:11:19.272965+0800 Runner[906:265995] flutter: │ 🐛 Converting TodaySummaryDto to TodaySummary entity
2025-07-19 10:11:19.273125+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.273359+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.273359+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.273359+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:11:19.273393+0800 Runner[906:265995] flutter: │ #0   new EarnedToday.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:13:12)
2025-07-19 10:11:19.273418+0800 Runner[906:265995] flutter: │ #1   new TodaySummary.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:156:49)
2025-07-19 10:11:19.273514+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.273656+0800 Runner[906:265995] flutter: │ 10:11:19.273 (+0:00:57.591604)
2025-07-19 10:11:19.273813+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.273656+0800 Runner[906:265995] flutter: │ 10:11:19.273 (+0:00:57.591604)
2025-07-19 10:11:19.273813+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.273914+0800 Runner[906:265995] flutter: │ 🐛 Converting EarnedTodayDto to EarnedToday entity
2025-07-19 10:11:19.274084+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.274405+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:11:19.274405+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.274437+0800 Runner[906:265995] flutter: │ #0   new EarnedToday.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:14:12)
2025-07-19 10:11:19.274462+0800 Runner[906:265995] flutter: │ #1   new TodaySummary.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:156:49)
2025-07-19 10:11:19.274615+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.274752+0800 Runner[906:265995] flutter: │ 10:11:19.274 (+0:00:57.592595)
2025-07-19 10:11:19.274918+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.275015+0800 Runner[906:265995] flutter: │ 🐛 Original swmt value (String): "0.00"
2025-07-19 10:11:19.275115+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.275419+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.275451+0800 Runner[906:265995] flutter: │ #0   new EarnedToday.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:15:12)
2025-07-19 10:11:19.275475+0800 Runner[906:265995] flutter: │ #1   new TodaySummary.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:156:49)
2025-07-19 10:11:19.275566+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.275661+0800 Runner[906:265995] flutter: │ 10:11:19.275 (+0:00:57.593645)
2025-07-19 10:11:19.275769+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.275903+0800 Runner[906:265995] flutter: │ 🐛 Original exp value (int): 0
2025-07-19 10:11:19.276109+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.275903+0800 Runner[906:265995] flutter: │ 🐛 Original exp value (int): 0
2025-07-19 10:11:19.276109+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.276621+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.275903+0800 Runner[906:265995] flutter: │ 🐛 Original exp value (int): 0
2025-07-19 10:11:19.276109+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.276621+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.275903+0800 Runner[906:265995] flutter: │ 🐛 Original exp value (int): 0
2025-07-19 10:11:19.276109+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.276621+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:11:19.276655+0800 Runner[906:265995] flutter: │ #0   new EarnedToday.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:37:16)
2025-07-19 10:11:19.276678+0800 Runner[906:265995] flutter: │ #1   new TodaySummary.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:156:49)
2025-07-19 10:11:19.276705+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.276936+0800 Runner[906:265995] flutter: │ 10:11:19.276 (+0:00:57.594873)
2025-07-19 10:11:19.276965+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.276988+0800 Runner[906:265995] flutter: │ 🐛 Cleaned swmt string: "0.00"
2025-07-19 10:11:19.277015+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.277254+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.277289+0800 Runner[906:265995] flutter: │ #0   new EarnedToday.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:45:12)
2025-07-19 10:11:19.277315+0800 Runner[906:265995] flutter: │ #1   new TodaySummary.fromDto (package:sweatmint/features/tasks/domain/entities/today_summary.dart:156:49)
2025-07-19 10:11:19.277415+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.277574+0800 Runner[906:265995] flutter: │ 10:11:19.277 (+0:00:57.595507)
2025-07-19 10:11:19.277727+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.277841+0800 Runner[906:265995] flutter: │ 🐛 Final parsed values: swmt=0.0, exp=0
2025-07-19 10:11:19.277946+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.277841+0800 Runner[906:265995] flutter: │ 🐛 Final parsed values: swmt=0.0, exp=0
2025-07-19 10:11:19.277946+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.278237+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.278283+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:249:18)
2025-07-19 10:11:19.278312+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.278471+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.278535+0800 Runner[906:265995] flutter: │ 10:11:19.278 (+0:00:57.596495)
2025-07-19 10:11:19.278627+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.278750+0800 Runner[906:265995] flutter: │ 🐛 已解析todaySummary，SWMT: 0.0，EXP: 0
2025-07-19 10:11:19.278848+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.279574+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.279608+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:282:18)
2025-07-19 10:11:19.279635+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.279661+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.279886+0800 Runner[906:265995] flutter: │ 10:11:19.279 (+0:00:57.597768)
2025-07-19 10:11:19.279913+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.279938+0800 Runner[906:265995] flutter: │ 🐛 已解析dailyTaskList，完成任务: 0/4
2025-07-19 10:11:19.280025+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.280318+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.280367+0800 Runner[906:265995] flutter: │ #0   new AddonTaskListDataDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:191:14)
2025-07-19 10:11:19.280488+0800 Runner[906:265995] flutter: │ #1   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:294:61)
2025-07-19 10:11:19.280517+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.280621+0800 Runner[906:265995] flutter: │ 10:11:19.280 (+0:00:57.598474)
2025-07-19 10:11:19.280700+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.280732+0800 Runner[906:265995] flutter: │ 🐛 AddonTaskListDataDto.fromJson - 原始数据: (active_addon_tasks, completed_addon_tasks, max_addon_tasks, ..., current_member_level, addon_tasks)
2025-07-19 10:11:19.280773+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.281077+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.281110+0800 Runner[906:265995] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:76:12)
2025-07-19 10:11:19.281135+0800 Runner[906:265995] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-19 10:11:19.281161+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.281750+0800 Runner[906:265995] flutter: │ 10:11:19.280 (+0:00:57.599280)
2025-07-19 10:11:19.281796+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.281825+0800 Runner[906:265995] flutter: │ 🐛 AddonTaskItemDto.fromJson - 处理原始数据
2025-07-19 10:11:19.281856+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.282041+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.282090+0800 Runner[906:265995] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:77:12)
2025-07-19 10:11:19.282121+0800 Runner[906:265995] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-19 10:11:19.282150+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.282174+0800 Runner[906:265995] flutter: │ 10:11:19.281 (+0:00:57.600269)
2025-07-19 10:11:19.282199+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.282295+0800 Runner[906:265995] flutter: │ 🐛 id: 142 (类型: int)
2025-07-19 10:11:19.282402+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.282696+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.282732+0800 Runner[906:265995] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:115:14)
2025-07-19 10:11:19.282809+0800 Runner[906:265995] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-19 10:11:19.282931+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.283045+0800 Runner[906:265995] flutter: │ 10:11:19.282 (+0:00:57.600915)
2025-07-19 10:11:19.283141+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.283045+0800 Runner[906:265995] flutter: │ 10:11:19.282 (+0:00:57.600915)
2025-07-19 10:11:19.283141+0800 Runner[906:265995] flutter: ├\342
2025-07-19 10:11:19.283260+0800 Runner[906:265995] flutter: │ 🐛 🔧 解析字段: id=142 (int)
2025-07-19 10:11:19.283364+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.283664+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.283697+0800 Runner[906:265995] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:116:14)
2025-07-19 10:11:19.283764+0800 Runner[906:265995] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-19 10:11:19.283870+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.284240+0800 Runner[906:265995] flutter: │ 10:11:19.283 (+0:00:57.601879)
2025-07-19 10:11:19.284271+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.284240+0800 Runner[906:265995] flutter: │ 10:11:19.283 (+0:00:57.601879)
2025-07-19 10:11:19.284271+0800 Runner[906:265995] flutter: ├\342
2025-07-19 10:11:19.284296+0800 Runner[906:265995] flutter: │ 🐛 🔧 解析字段: unlock_requirements=Refer 1 new user(s) to unlock this task. (String)
2025-07-19 10:11:19.284448+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.284764+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.284798+0800 Runner[906:265995] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:76:12)
2025-07-19 10:11:19.284823+0800 Runner[906:265995] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-19 10:11:19.284865+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.285130+0800 Runner[906:265995] flutter: │ 10:11:19.284 (+0:00:57.602986)
2025-07-19 10:11:19.285160+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.285130+0800 Runner[906:265995] flutter: │ 10:11:19.284 (+0:00:57.602986)
2025-07-19 10:11:19.285160+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:11:19.285130+0800 Runner[906:265995] flutter: │ 10:11:19.284 (+0:00:57.602986)
2025-07-19 10:11:19.285160+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:11:19.285268+0800 Runner[906:265995] flutter: │ 🐛 AddonTaskItemDto.fromJson - 处理原始数据
2025-07-19 10:11:19.285423+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.285731+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:11:19.285764+0800 Runner[906:265995] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:77:12)
2025-07-19 10:11:19.285790+0800 Runner[906:265995] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-19 10:11:19.285914+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.285985+0800 Runner[906:265995] flutter: │ 10:11:19.285 (+0:00:57.603917)
2025-07-19 10:11:19.286086+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.286257+0800 Runner[906:265995] flutter: │ 🐛 id: 139 (类型: int)
2025-07-19 10:11:19.286368+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.286727+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.286766+0800 Runner[906:265995] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:115:14)
2025-07-19 10:11:19.286794+0800 Runner[906:265995] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-19 10:11:19.286863+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.287333+0800 Runner[906:265995] flutter: │ 10:11:19.286 (+0:00:57.604911)
2025-07-19 10:11:19.287362+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.287387+0800 Runner[906:265995] flutter: │ 🐛 🔧 解析字段: id=139 (int)
2025-07-19 10:11:19.287413+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.287782+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.287814+0800 Runner[906:265995] flutter: │ #0   new AddonTaskItemDto.fromJson (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:116:14)
2025-07-19 10:11:19.287894+0800 Runner[906:265995] flutter: │ #1   new AddonTaskListDataDto.fromJson.<anonymous closure> (package:sweatmint/features/tasks/data/dto/addon_task_list_dto.dart:203:47)
2025-07-19 10:11:19.287928+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.287954+0800 Runner[906:265995] flutter: │ 10:11:19.287 (+0:00:57.605967)
2025-07-19 10:11:19.287981+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.288006+0800 Runner[906:265995] flutter: │ 🐛 🔧 解析字段: unlock_requirements=Refer 2 new user(s) to unlock this task. (String)
2025-07-19 10:11:19.288107+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.288335+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.288367+0800 Runner[906:265995] flutter: │ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:301:18)
2025-07-19 10:11:19.288394+0800 Runner[906:265995] flutter: │ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)
2025-07-19 10:11:19.288422+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.288519+0800 Runner[906:265995] flutter: │ 10:11:19.288 (+0:00:57.606553)
2025-07-19 10:11:19.288590+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.288778+0800 Runner[906:265995] flutter: │ 🐛 已解析addonTaskList，活跃任务: 0/2
2025-07-19 10:11:19.288850+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.289229+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224
2025-07-19 10:11:19.289229+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:11:19.289266+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   new HomeDashboardData.fromDto (package:sweatmint/features/dashboard/data/dto/dashboard_dto.dart:314:14)<…>
2025-07-19 10:11:19.289292+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:316:40)<…>
2025-07-19 10:11:19.289371+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\224<…>
2025-07-19 10:11:19.289502+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:11:19.289 (+0:00:57.607470)<…>
2025-07-19 10:11:19.289628+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.289713+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 HomeDashboardData解析完成<…>
2025-07-19 10:11:19.289816+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.289713+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 HomeDashboardData解析完成<…>
2025-07-19 10:11:19.289816+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342<…>
2025-07-19 10:11:19.289713+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 HomeDashboardData解析完成<…>
2025-07-19 10:11:19.289816+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\342\224<…>
2025-07-19 10:11:19.290058+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.289713+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 HomeDashboardData解析完成<…>
2025-07-19 10:11:19.289816+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\342\224<…>
2025-07-19 10:11:19.290058+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.290091+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:353:18)<…>
2025-07-19 10:11:19.290176+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.290290+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.290381+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:11:19.289 (+0:00:57.608312)<…>
2025-07-19 10:11:19.290478+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\224<…>
2025-07-19 10:11:19.290588+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔄 开始更新首页状态 - homeData可用性检查<…>
2025-07-19 10:11:19.290722+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.290937+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:11:19.290970+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:354:18)
2025-07-19 10:11:19.291108+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.291236+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.291332+0800 Runner[906:265995] flutter: │ 10:11:19.290 (+0:00:57.609212)
2025-07-19 10:11:19.291496+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.291533+0800 Runner[906:265995] flutter: │ 🐛 homeData.userProfile: 可用
2025-07-19 10:11:19.291650+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.291533+0800 Runner[906:265995] flutter: │ 🐛 homeData.userProfile: 可用
2025-07-19 10:11:19.291650+0800 Runner[906:265995] flutter: └\342\342\224
2025-07-19 10:11:19.291533+0800 Runner[906:265995] flutter: │ 🐛 homeData.userProfile: 可用
2025-07-19 10:11:19.291650+0800 Runner[906:265995] flutter: └\342\342\224
2025-07-19 10:11:19.291897+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:11:19.291533+0800 Runner[906:265995] flutter: │ 🐛 homeData.userProfile: 可用
2025-07-19 10:11:19.291650+0800 Runner[906:265995] flutter: └\342\342\224
2025-07-19 10:11:19.291897+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.291931+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:355:18)
2025-07-19 10:11:19.291955+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.292068+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.292168+0800 Runner[906:265995] flutter: │ 10:11:19.291 (+0:00:57.610171)
2025-07-19 10:11:19.292267+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.292364+0800 Runner[906:265995] flutter: │ 🐛 homeData.vipStatus: 可用
2025-07-19 10:11:19.292475+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.292688+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.292364+0800 Runner[906:265995] flutter: │ 🐛 homeData.vipStatus: 可用
2025-07-19 10:11:19.292475+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.292688+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:11:19.292722+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:356:18)
2025-07-19 10:11:19.292752+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.292870+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.292963+0800 Runner[906:265995] flutter: │ 10:11:19.292 (+0:00:57.610963)
2025-07-19 10:11:19.293116+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.293232+0800 Runner[906:265995] flutter: │ 🐛 homeData.todaySummary: 可用
2025-07-19 10:11:19.293342+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.293631+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.293681+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:372:20)<…>
2025-07-19 10:11:19.293712+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.293789+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.293915+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:11:19.293 (+0:00:57.611895)<…>
2025-07-19 10:11:19.293950+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.294045+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 用户档案数据已更新: test_user2, exp=11586<…>
2025-07-19 10:11:19.294170+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.294440+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.294440+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:11:19.294479+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:378:20)<…>
2025-07-19 10:11:19.294504+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.294609+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.294926+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:11:19.294 (+0:00:57.612709)<…>
2025-07-19 10:11:19.295036+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.295149+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ VIP状态数据已更新: hasVip=true<…>
2025-07-19 10:11:19.295291+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.295149+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ VIP状态数据已更新: hasVip=true<…>
2025-07-19 10:11:19.295291+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:19.295517+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.295149+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ VIP状态数据已更新: hasVip=true<…>
2025-07-19 10:11:19.295291+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:19.295517+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>

2025-07-19 10:11:19.295550+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:384:20)<…>
2025-07-19 10:11:19.295577+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.295696+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>

2025-07-19 10:11:19.295796+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:11:19.295 (+0:00:57.613768)<…>
2025-07-19 10:11:19.295960+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\224<…>
2025-07-19 10:11:19.296069+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 今日摘要数据已更新: SWMT=0.0, EXP=0<…>
2025-07-19 10:11:19.296158+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:19.296069+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 今日摘要数据已更新: SWMT=0.0, EXP=0<…>
2025-07-19 10:11:19.296158+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\342\224<…>
2025-07-19 10:11:19.296069+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 今日摘要数据已更新: SWMT=0.0, EXP=0<…>
2025-07-19 10:11:19.296158+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\342\224<…>
2025-07-19 10:11:19.296418+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.296069+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 今日摘要数据已更新: SWMT=0.0, EXP=0<…>
2025-07-19 10:11:19.296158+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\342\224<…>
2025-07-19 10:11:19.296418+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.296451+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:390:20)<…>
2025-07-19 10:11:19.296543+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.296652+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.296789+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:11:19.296 (+0:00:57.614673)<…>
2025-07-19 10:11:19.296913+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\224<…>
2025-07-19 10:11:19.297045+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 每日任务数据已更新: 0/4<…>
2025-07-19 10:11:19.297185+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.297431+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.297431+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.297475+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:396:20)<…>
2025-07-19 10:11:19.297505+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.297532+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.297879+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:11:19.297 (+0:00:57.615698)<…>
2025-07-19 10:11:19.297912+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\224<…>
2025-07-19 10:11:19.297937+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 附加任务数据已更新: 0/2<…>
2025-07-19 10:11:19.297964+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.298199+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.298199+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.298234+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:399:18)<…>
2025-07-19 10:11:19.298258+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.298363+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.298495+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 10:11:19.298 (+0:00:57.616450)<…>
2025-07-19 10:11:19.298613+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\224<…>
2025-07-19 10:11:19.298694+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🎉 首页聚合数据状态更新成功 - 所有状态已原子性更新并通知UI<…>
2025-07-19 10:11:19.298721+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.298957+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.298991+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:422:16)
2025-07-19 10:11:19.299100+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.299185+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.299271+0800 Runner[906:265995] flutter: │ 10:11:19.298 (+0:00:57.617173)
2025-07-19 10:11:19.299361+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.299271+0800 Runner[906:265995] flutter: │ 10:11:19.298 (+0:00:57.617173)
2025-07-19 10:11:19.299361+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:11:19.299271+0800 Runner[906:265995] flutter: │ 10:11:19.298 (+0:00:57.617173)
2025-07-19 10:11:19.299361+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:11:19.299481+0800 Runner[906:265995] flutter: │ 🐛 ✅ Dashboard数据加载完成，健康数据由独立Health API处理
2025-07-19 10:11:19.299567+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.299271+0800 Runner[906:265995] flutter: │ 10:11:19.298 (+0:00:57.617173)
2025-07-19 10:11:19.299361+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:11:19.299481+0800 Runner[906:265995] flutter: │ 🐛 ✅ Dashboard数据加载完成，健康数据由独立Health API处理
2025-07-19 10:11:19.299567+0800 Runner[906:265995] flutter: └\342
2025-07-19 10:11:19.299815+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.299848+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:427:18)
2025-07-19 10:11:19.299872+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.299983+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.300090+0800 Runner[906:265995] flutter: │ 10:11:19.299 (+0:00:57.618070)
2025-07-19 10:11:19.300188+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.300283+0800 Runner[906:265995] flutter: │ 🐛 ----- 转换后的Entity数据详情 -----
2025-07-19 10:11:19.300401+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.300599+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:11:19.300599+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.300639+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:428:18)
2025-07-19 10:11:19.300663+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.300789+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.300894+0800 Runner[906:265995] flutter: │ 10:11:19.300 (+0:00:57.618869)
2025-07-19 10:11:19.301016+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.301112+0800 Runner[906:265995] flutter: │ 🐛 用户名: test_user2
2025-07-19 10:11:19.301290+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.301112+0800 Runner[906:265995] flutter: │ 🐛 用户名: test_user2
2025-07-19 10:11:19.301290+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.301507+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.301112+0800 Runner[906:265995] flutter: │ 🐛 用户名: test_user2
2025-07-19 10:11:19.301290+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.301507+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.301112+0800 Runner[906:265995] flutter: │ 🐛 用户名: test_user2
2025-07-19 10:11:19.301290+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.301507+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:11:19.301542+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:429:18)
2025-07-19 10:11:19.301567+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.301709+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.301860+0800 Runner[906:265995] flutter: │ 10:11:19.301 (+0:00:57.619760)
2025-07-19 10:11:19.302003+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.302100+0800 Runner[906:265995] flutter: │ 🐛 VIP状态: true
2025-07-19 10:11:19.302953+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.303163+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.303196+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:430:18)
2025-07-19 10:11:19.303466+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.303509+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.303538+0800 Runner[906:265995] flutter: │ 10:11:19.302 (+0:00:57.621381)
2025-07-19 10:11:19.303565+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.303589+0800 Runner[906:265995] flutter: │ 🐛 今日SWMT (原值): 0.0 (类型: double)
2025-07-19 10:11:19.303614+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.303793+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.303836+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:431:18)
2025-07-19 10:11:19.303863+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.303891+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.303960+0800 Runner[906:265995] flutter: │ 10:11:19.303 (+0:00:57.622061)
2025-07-19 10:11:19.304055+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.304174+0800 Runner[906:265995] flutter: │ 🐛 今日XP (原值): 0 (类型: int)
2025-07-19 10:11:19.304278+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.304590+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.304635+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:432:18)
2025-07-19 10:11:19.304710+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.304781+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.304928+0800 Runner[906:265995] flutter: │ 10:11:19.304 (+0:00:57.622813)
2025-07-19 10:11:19.305099+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.305181+0800 Runner[906:265995] flutter: │ 🐛 当前XP总量: 11586
2025-07-19 10:11:19.305335+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.305546+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.305592+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:435:20)
2025-07-19 10:11:19.305618+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.305726+0800 Runner[906:265995] flutter: ├

2025-07-19 10:11:19.305911+0800 Runner[906:265995] flutter: │ 10:11:19.305 (+0:00:57.623814)
2025-07-19 10:11:19.306055+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.306159+0800 Runner[906:265995] flutter: │ 🐛 会员等级: 3
2025-07-19 10:11:19.306275+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.306159+0800 Runner[906:265995] flutter: │ 🐛 会员等级: 3
2025-07-19 10:11:19.306275+0800 Runner[906:265995] flutter: └\342
2025-07-19 10:11:19.306159+0800 Runner[906:265995] flutter: │ 🐛 会员等级: 3
2025-07-19 10:11:19.306275+0800 Runner[906:265995] flutter: └\342\342\224
2025-07-19 10:11:19.306538+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.306159+0800 Runner[906:265995] flutter: │ 🐛 会员等级: 3
2025-07-19 10:11:19.306275+0800 Runner[906:265995] flutter: └\342\342\224
2025-07-19 10:11:19.306538+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.306571+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:436:20)
2025-07-19 10:11:19.306596+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.306698+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.306812+0800 Runner[906:265995] flutter: │ 10:11:19.306 (+0:00:57.624800)
2025-07-19 10:11:19.306898+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.306812+0800 Runner[906:265995] flutter: │ 10:11:19.306 (+0:00:57.624800)
2025-07-19 10:11:19.306898+0800 Runner[906:265995] flutter: ├\342
2025-07-19 10:11:19.307029+0800 Runner[906:265995] flutter: │ 🐛 会员等级最小经验: 10000
2025-07-19 10:11:19.307168+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.307433+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.307467+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:437:20)
2025-07-19 10:11:19.307533+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.307633+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.307807+0800 Runner[906:265995] flutter: │ 10:11:19.307 (+0:00:57.625702)
2025-07-19 10:11:19.307834+0800 Runner[906:265995] flutter: ├

2025-07-19 10:11:19.307963+0800 Runner[906:265995] flutter: │ 🐛 会员等级最大经验: 99999
2025-07-19 10:11:19.308128+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.308340+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.308373+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:440:20)
2025-07-19 10:11:19.308508+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.308638+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.308741+0800 Runner[906:265995] flutter: │ 10:11:19.308 (+0:00:57.626607)
2025-07-19 10:11:19.308896+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.309015+0800 Runner[906:265995] flutter: │ 🐛 当前经验进度: 1.8%
2025-07-19 10:11:19.309136+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.309414+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.309450+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:447:20)
2025-07-19 10:11:19.309492+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.309630+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.309776+0800 Runner[906:265995] flutter: │ 10:11:19.309 (+0:00:57.627664)
2025-07-19 10:11:19.309805+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.309830+0800 Runner[906:265995] flutter: │ 🐛 每日任务完成情况: 0/4
2025-07-19 10:11:19.309935+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.309830+0800 Runner[906:265995] flutter: │ 🐛 每日任务完成情况: 0/4
2025-07-19 10:11:19.309935+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.310170+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.310205+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:450:20)
2025-07-19 10:11:19.310228+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.310371+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.310469+0800 Runner[906:265995] flutter: │ 10:11:19.310 (+0:00:57.628432)
2025-07-19 10:11:19.310594+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.310726+0800 Runner[906:265995] flutter: │ 🐛 附加任务情况: 已完成 0/2, 已激活 0
2025-07-19 10:11:19.310829+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.311143+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.311143+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.311143+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:11:19.311178+0800 Runner[906:265995] flutter: │ #0   HomeProvider.loadHomeData.<anonymous closure> (package:sweatmint/features/home/<USER>/providers/home_provider.dart:453:18)
2025-07-19 10:11:19.311200+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.311289+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.311374+0800 Runner[906:265995] flutter: │ 10:11:19.310 (+0:00:57.629345)
2025-07-19 10:11:19.311525+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:19.311374+0800 Runner[906:265995] flutter: │ 10:11:19.310 (+0:00:57.629345)
2025-07-19 10:11:19.311525+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:11:19.311374+0800 Runner[906:265995] flutter: │ 10:11:19.310 (+0:00:57.629345)
2025-07-19 10:11:19.311525+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:11:19.311662+0800 Runner[906:265995] flutter: │ 🐛 ----- 转换后的Entity数据详情结束 -----
2025-07-19 10:11:19.311786+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.311374+0800 Runner[906:265995] flutter: │ 10:11:19.310 (+0:00:57.629345)
2025-07-19 10:11:19.311525+0800 Runner[906:265995] flutter: ├\342\342\224
2025-07-19 10:11:19.311662+0800 Runner[906:265995] flutter: │ 🐛 ----- 转换后的Entity数据详情结束 -----
2025-07-19 10:11:19.311786+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:19.311917+0800 Runner[906:265995] flutter: 💡 HomeDashboardData operation: Succeeded.
2025-07-19 10:11:19.312130+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:19.312165+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._preloadHomePageData (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:425:17)<…>
2025-07-19 10:11:19.312249+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.312375+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\224<…>
2025-07-19 10:11:19.312508+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 首页数据预加载完成 - userProfile: ✅, todaySummary: ✅<…>
2025-07-19 10:11:19.312584+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.312762+0800 Runner[906:265995] flutter: ┌<…>
2025-07-19 10:11:19.312762+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:11:19.312795+0800 Runner[906:265995] flutter: │ #0   AuthProvider._preloadHomePageData (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:426:17)
2025-07-19 10:11:19.312854+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.312978+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.313073+0800 Runner[906:265995] flutter: │ 🐛    预加载用户: test_user2
2025-07-19 10:11:19.313207+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.313353+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.313385+0800 Runner[906:265995] flutter: │ #0   AuthProvider._preloadHomePageData (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:427:17)
2025-07-19 10:11:19.313408+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:19.313528+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.313623+0800 Runner[906:265995] flutter: │ 🐛    预加载今日SWMT: 0.0
2025-07-19 10:11:19.313722+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.313943+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.313984+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:398:15)<…>
2025-07-19 10:11:19.314009+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.314101+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\224<…>
2025-07-19 10:11:19.314227+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 阶段2.3: 业务数据同步完成<…>
2025-07-19 10:11:19.314353+0800 Runner[906:265995] flutter: \^[[38;5;12m\342\224<…>
2025-07-19 10:11:19.314227+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 阶段2.3: 业务数据同步完成<…>
2025-07-19 10:11:19.314353+0800 Runner[906:265995] flutter: \^[[38;5;12m\342└\342\224<…>
2025-07-19 10:11:19.314227+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 阶段2.3: 业务数据同步完成<…>
2025-07-19 10:11:19.314353+0800 Runner[906:265995] flutter: \^[[38;5;12m\342└\342\224<…>
2025-07-19 10:11:19.314850+0800 Runner[906:265995] flutter: ┌\342\224<…>
2025-07-19 10:11:19.314227+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 阶段2.3: 业务数据同步完成<…>
2025-07-19 10:11:19.314353+0800 Runner[906:265995] flutter: \^[[38;5;12m\342└\342\224<…>
2025-07-19 10:11:19.314850+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:11:19.314884+0800 Runner[906:265995] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:198:19)
2025-07-19 10:11:19.314911+0800 Runner[906:265995] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-19 10:11:19.314940+0800 Runner[906:265995] flutter: ├

2025-07-19 10:11:19.314968+0800 Runner[906:265995] flutter: │ 🐛 Redirect: At splash screen with authenticated user, allowing navigation to home.
2025-07-19 10:11:19.314996+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.315275+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.315313+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._performStage23BusinessDataSync (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:400:15)<…>
2025-07-19 10:11:19.315341+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.315372+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.315459+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: UI状态更新通知已发送<…>
2025-07-19 10:11:19.315526+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.315862+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.315896+0800 Runner[906:265995] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:198:19)
2025-07-19 10:11:19.315922+0800 Runner[906:265995] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-19 10:11:19.315949+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.316082+0800 Runner[906:265995] flutter: │ 🐛 Redirect: At splash screen with authenticated user, allowing navigation to home.
2025-07-19 10:11:19.316172+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.316486+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.316521+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:178:15)<…>
2025-07-19 10:11:19.316618+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:19.316793+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.316983+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 阶段2 Loading数据准备完成<…>
2025-07-19 10:11:19.317071+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.317311+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.317357+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._ensureMinimumLoadingDuration (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:487:15)<…>
2025-07-19 10:11:19.317704+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:181:13)<…>
2025-07-19 10:11:19.317731+0800 Runner[906:265995] flutter: \^[[38;5;12m├<<\342\200…>
2025-07-19 10:11:19.317760+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ⏱️ 等待最小加载时长: 1419ms<…>
2025-07-19 10:11:19.317806+0800 Runner[906:265995] flutter: \^[[38;5;12m└<\342\200…>
2025-07-19 10:11:19.317760+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ⏱️ 等待最小加载时长: 1419ms<…>
2025-07-19 10:11:19.317806+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:19.321030+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:19.321110+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   MyApp.build.<anonymous closure> (package:sweatmint/main.dart:308:17)<…>
2025-07-19 10:11:19.321147+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   Consumer.buildWithChild (package:provider/src/consumer.dart:179:19)<…>
2025-07-19 10:11:19.321191+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:19.321221+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 阶段1：显示基础MaterialApp界面 - 认证状态: AuthStatus.authenticated<…>
2025-07-19 10:11:19.321251+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:19.327999+0800 Runner[906:265995] flutter: ┌<…>
2025-07-19 10:11:19.327999+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.328117+0800 Runner[906:265995] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:198:19)
2025-07-19 10:11:19.328146+0800 Runner[906:265995] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-19 10:11:19.328264+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.328310+0800 Runner[906:265995] flutter: │ 🐛 Redirect: At splash screen with authenticated user, allowing navigation to home.
2025-07-19 10:11:19.328339+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:19.332771+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:19.332771+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:19.332854+0800 Runner[906:265995] flutter: │ #0   _SplashScreenState.build.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:408:17)
2025-07-19 10:11:19.332886+0800 Runner[906:265995] flutter: │ #1   _Selector0State.buildWithChild.<anonymous closure> (package:provider/src/selector.dart:86:45)
2025-07-19 10:11:19.332916+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:19.332944+0800 Runner[906:265995] flutter: │ 🐛 🔄 SplashScreen: Provider状态更新 - AuthStatus.authenticated, Stage: LoginStage.stage3Complete, 业务逻辑完成: false
2025-07-19 10:11:19.332970+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.703949+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.704277+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState.initState.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:43:17)<…>
2025-07-19 10:11:20.704392+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   Timer._createTimer.<anonymous closure> (dart:async-patch/timer_patch.dart:18:15)<…>
2025-07-19 10:11:20.704500+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.704603+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ⏰ SplashScreen: 最小显示时间已到<…>
2025-07-19 10:11:20.704698+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.704603+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ⏰ SplashScreen: 最小显示时间已到<…>
2025-07-19 10:11:20.704698+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342<…>
2025-07-19 10:11:20.705227+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.705362+0800 Runner[906:265995] flutter: │ #0   _SplashScreenState.initState.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:47:17)
2025-07-19 10:11:20.705457+0800 Runner[906:265995] flutter: │ #1   Timer._createTimer.<anonymous closure> (dart:async-patch/timer_patch.dart:18:15)
2025-07-19 10:11:20.706090+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:20.706231+0800 Runner[906:265995] flutter: │ 🐛 ⏳ SplashScreen: 最小时间已满足，等待业务逻辑完成
2025-07-19 10:11:20.706334+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:20.706231+0800 Runner[906:265995] flutter: │ 🐛 ⏳ SplashScreen: 最小时间已满足，等待业务逻辑完成
2025-07-19 10:11:20.706334+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:20.741021+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.741377+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider._ensureMinimumLoadingDuration (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:490:13)<…>
2025-07-19 10:11:20.741569+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:20.741666+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.741750+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 最小加载时长已满足<…>
2025-07-19 10:11:20.741844+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.742446+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.742562+0800 Runner[906:265995] flutter: \^[[38;5;12m<…>
2025-07-19 10:11:20.742562+0800 Runner[906:265995] flutter: \^[[38;5;12m\342│ #0   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:185:15)<…>
2025-07-19 10:11:20.742651+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:20.742737+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.743657+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AuthProvider: 完整业务逻辑初始化完成，允许SplashScreen跳转<…>
2025-07-19 10:11:20.743807+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.745211+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.745367+0800 Runner[906:265995] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:198:19)
2025-07-19 10:11:20.745480+0800 Runner[906:265995] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-19 10:11:20.745571+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.745973+0800 Runner[906:265995] flutter: │ 🐛 Redirect: At splash screen with authenticated user, allowing navigation to home.
2025-07-19 10:11:20.746115+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.747184+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.747328+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AuthProvider.initializeBusinessLogic (package:sweatmint/features/auth/presentation/providers/auth_provider.dart:206:15)<…>
2025-07-19 10:11:20.747454+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:20.747550+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.747643+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔧 AuthProvider: 业务逻辑初始化过程结束<…>
2025-07-19 10:11:20.747759+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.750232+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.750622+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:311:17)<…>
2025-07-19 10:11:20.750870+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:20.751168+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.753200+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ SplashScreen: AuthProvider认证检查完成<…>
2025-07-19 10:11:20.753384+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.753962+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.754122+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:318:15)<…>
2025-07-19 10:11:20.754504+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:20.754629+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.754762+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔐 SplashScreen: 用户认证状态 - AuthStatus.authenticated<…>
2025-07-19 10:11:20.754859+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.756281+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.756435+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:321:17)<…>
2025-07-19 10:11:20.756555+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:20.756669+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.757833+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ SplashScreen: 用户已认证，开始执行健康数据流步骤1-4<…>
2025-07-19 10:11:20.758017+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.758878+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.757833+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ SplashScreen: 用户已认证，开始执行健康数据流步骤1-4<…>
2025-07-19 10:11:20.758017+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.758878+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:11:20.759247+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:331:19)<…>
2025-07-19 10:11:20.759439+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:20.759665+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.759884+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🚀 SplashScreen: 开始执行v14.1健康数据流步骤1-4（登录场景）<…>
2025-07-19 10:11:20.760174+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.760995+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.761153+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   HealthDataFlowService.executeSteps1to4Only (package:sweatmint/core/services/health_data_flow_service.dart:346:13)<…>
2025-07-19 10:11:20.761955+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:334:64)<…>
2025-07-19 10:11:20.762149+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.762311+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔥 HealthDataFlowService: SplashScreen期间执行v14.1步骤1-4（阶段门控制）<…>
2025-07-19 10:11:20.762484+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.764219+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.764438+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   PhaseGateController.resetAllPhases (package:sweatmint/core/controllers/phase_gate_controller.dart:298:13)<…>
2025-07-19 10:11:20.765350+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   HealthDataFlowService.executeSteps1to4Only (package:sweatmint/core/services/health_data_flow_service.dart:349:26)<…>
2025-07-19 10:11:20.765513+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.765676+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔄 重置所有阶段状态<…>
2025-07-19 10:11:20.765784+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.766994+0800 Runner[906:265995] flutter: \^[[38;5;208m┌<…>
2025-07-19 10:11:20.767153+0800 Runner[906:265995] flutter: \^[[38;5;208m│ #0   HealthDataFlowService.executeSteps1to4Only (package:sweatmint/core/services/health_data_flow_service.dart:353:15)<…>
2025-07-19 10:11:20.767267+0800 Runner[906:265995] flutter: \^[[38;5;208m│ #1   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:334:64)<…>
2025-07-19 10:11:20.769010+0800 Runner[906:265995] flutter: \^[[38;5;208m├<…>
2025-07-19 10:11:20.769200+0800 Runner[906:265995] flutter: \^[[38;5;208m│ ⚠️ ⚠️ 步骤1-4已完成，跳过重复执行<…>
2025-07-19 10:11:20.769667+0800 Runner[906:265995] flutter: \^[[38;5;208m└<…>
2025-07-19 10:11:20.771098+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.771492+0800 Runner[906:265995] flutter: \^[[38;5;12m<…>
2025-07-19 10:11:20.771492+0800 Runner[906:265995] flutter: \^[[38;5;12m\342│ #0   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:350:19)<…>
2025-07-19 10:11:20.771740+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:20.772013+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.773585+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 📊 SplashScreen: 健康数据流完成<…>
2025-07-19 10:11:20.773720+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.774796+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.775024+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:351:19)<…>
2025-07-19 10:11:20.775133+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:20.775271+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.775374+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡   ✅ 成功: true<…>
2025-07-19 10:11:20.776318+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.776854+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.777014+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:352:19)<…>
2025-07-19 10:11:20.777870+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:20.778006+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.778114+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡   ⏱️ 耗时: 12ms<…>
2025-07-19 10:11:20.778192+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.779143+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.779287+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:359:19)<…>
2025-07-19 10:11:20.779423+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:20.779511+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.779743+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🎯 SplashScreen: 健康数据流处理完成，继续启动流程<…>
2025-07-19 10:11:20.779857+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.780294+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.781092+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:373:15)<…>
2025-07-19 10:11:20.781222+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:20.781331+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.781424+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ SplashScreen: 阶段2完成，总耗时: 1543ms<…>
2025-07-19 10:11:20.781531+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.782650+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.782810+0800 Runner[906:265995] flutter: │ #0   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:70:13)
2025-07-19 10:11:20.782928+0800 Runner[906:265995] flutter: │ #1   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:376:7)
2025-07-19 10:11:20.783025+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.783743+0800 Runner[906:265995] flutter: │ 🐛 🔍 SplashScreen: 检查导航条件
2025-07-19 10:11:20.783846+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.784267+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.785606+0800 Runner[906:265995] flutter: │ #0   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:71:13)
2025-07-19 10:11:20.785744+0800 Runner[906:265995] flutter: │ #1   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:376:7)
2025-07-19 10:11:20.785886+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.786031+0800 Runner[906:265995] flutter: │ 🐛    - 最小时间完成: true
2025-07-19 10:11:20.786902+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.787370+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.787370+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:11:20.787500+0800 Runner[906:265995] flutter: │ #0   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:72:13)
2025-07-19 10:11:20.788402+0800 Runner[906:265995] flutter: │ #1   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:376:7)
2025-07-19 10:11:20.788515+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.788616+0800 Runner[906:265995] flutter: │ 🐛    - 认证状态: AuthStatus.authenticated
2025-07-19 10:11:20.788709+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.789518+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.789655+0800 Runner[906:265995] flutter: │ #0   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:73:13)
2025-07-19 10:11:20.789747+0800 Runner[906:265995] flutter: │ #1   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:376:7)
2025-07-19 10:11:20.789901+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.790288+0800 Runner[906:265995] flutter: │ 🐛    - 已导航: false
2025-07-19 10:11:20.790656+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.791259+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.791400+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:85:17)<…>
2025-07-19 10:11:20.791505+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _SplashScreenState._executeStage2WithHealthFlow (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:376:7)<…>
2025-07-19 10:11:20.791607+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.792630+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🚀 SplashScreen: 检测到已认证状态，准备导航到首页<…>
2025-07-19 10:11:20.792786+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.793274+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.793804+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._navigateToHome (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:112:13)<…>
2025-07-19 10:11:20.793937+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _SplashScreenState._checkNavigationConditions (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:86:9)<…>
2025-07-19 10:11:20.794031+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.794128+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🏠 SplashScreen: v14.1架构合规导航到首页<…>
2025-07-19 10:11:20.794227+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.796679+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.796818+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   MyApp.build.<anonymous closure> (package:sweatmint/main.dart:308:17)<…>
2025-07-19 10:11:20.796898+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   Consumer.buildWithChild (package:provider/src/consumer.dart:179:19)<…>
2025-07-19 10:11:20.796971+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.797043+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ 阶段1：显示基础MaterialApp界面 - 认证状态: AuthStatus.authenticated<…>
2025-07-19 10:11:20.797157+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.805641+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.805808+0800 Runner[906:265995] flutter: │ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:198:19)
2025-07-19 10:11:20.805868+0800 Runner[906:265995] flutter: │ #1   RouteConfiguration.redirect.processRedirect (package:go_router/src/configuration.dart:414:80)
2025-07-19 10:11:20.805923+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.805967+0800 Runner[906:265995] flutter: │ 🐛 Redirect: At splash screen with authenticated user, allowing navigation to home.
2025-07-19 10:11:20.806022+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.810637+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.810755+0800 Runner[906:265995] flutter: │ #0   _SplashScreenState.build.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:408:17)
2025-07-19 10:11:20.810802+0800 Runner[906:265995] flutter: │ #1   _Selector0State.buildWithChild.<anonymous closure> (package:provider/src/selector.dart:86:45)
2025-07-19 10:11:20.810847+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.810890+0800 Runner[906:265995] flutter: │ 🐛 🔄 SplashScreen: Provider状态更新 - AuthStatus.authenticated, Stage: LoginStage.stage3Complete, 业务逻辑完成: true
2025-07-19 10:11:20.810940+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.814653+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.814757+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._navigateToHome.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:118:17)<…>
2025-07-19 10:11:20.814805+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)<…>
2025-07-19 10:11:20.814849+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>

2025-07-19 10:11:20.814889+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🚀 SplashScreen: 健康数据流程已完成，直接执行导航<…>
2025-07-19 10:11:20.814932+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.815450+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.815450+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:20.815509+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._navigateToHome.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:122:17)<…>
2025-07-19 10:11:20.815549+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)<…>
2025-07-19 10:11:20.815594+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.816119+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ SplashScreen: 成功导航到首页<…>
2025-07-19 10:11:20.816166+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.816320+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.816320+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342<…>
2025-07-19 10:11:20.816320+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-19 10:11:20.816399+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _SplashScreenState._navigateToHome.<anonymous closure> (package:sweatmint/features/splash/presentation/screens/splash_screen.dart:126:17)<…>
2025-07-19 10:11:20.816569+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)<…>
2025-07-19 10:11:20.816613+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.816649+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🎯 SplashScreen: 导航状态已更新<…>
2025-07-19 10:11:20.816752+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.826986+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.827135+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:308:21)<…>
2025-07-19 10:11:20.827185+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _CustomNavigatorState._buildPageForGoRoute.<anonymous closure> (package:go_router/src/builder.dart:260:21)<…>
2025-07-19 10:11:20.827233+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.827281+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🏠 AppRoutes: 构建MainLayoutScreen - 2025-07-19 10:11:20.824830<…>
2025-07-19 10:11:20.827329+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.829110+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.829153+0800 Runner[906:265995] flutter: \^[[38;5;12m<…>
2025-07-19 10:11:20.829153+0800 Runner[906:265995] flutter: \^[[38;5;12m\342│ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:309:21)<…>
2025-07-19 10:11:20.829207+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _CustomNavigatorState._buildPageForGoRoute.<anonymous closure> (package:go_router/src/builder.dart:260:21)<…>
2025-07-19 10:11:20.829243+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.829153+0800 Runner[906:265995] flutter: \^[[38;5;12m\342│ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:309:21)<…>
2025-07-19 10:11:20.829207+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _CustomNavigatorState._buildPageForGoRoute.<anonymous closure> (package:go_router/src/builder.dart:260:21)<…>
2025-07-19 10:11:20.829243+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342<…>
2025-07-19 10:11:20.829278+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔍 AppRoutes: 路由状态 - location: /home, fullPath: /home<…>
2025-07-19 10:11:20.829311+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.830744+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.830785+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   AppRoutes.createRouter.<anonymous closure> (package:sweatmint/config/app_routes.dart:312:23)<…>
2025-07-19 10:11:20.830817+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _CustomNavigatorState._buildPageForGoRoute.<anonymous closure> (package:go_router/src/builder.dart:260:21)<…>
2025-07-19 10:11:20.830852+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.830884+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ AppRoutes: MainLayoutScreen构建成功<…>
2025-07-19 10:11:20.830915+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.832608+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.832659+0800 Runner[906:265995] flutter: │ #0   _MainLayoutScreenState.build (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:371:13)
2025-07-19 10:11:20.832690+0800 Runner[906:265995] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-19 10:11:20.832728+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.832769+0800 Runner[906:265995] flutter: │ 🐛 Building MainLayoutScreen with selected index: 0
2025-07-19 10:11:20.832802+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.844354+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.844497+0800 Runner[906:265995] flutter: │ #0   AppBottomNavigationBar.build (package:sweatmint/features/main_layout/presentation/widgets/app_bottom_navigation_bar.dart:48:13)
2025-07-19 10:11:20.844534+0800 Runner[906:265995] flutter: │ #1   StatelessElement.build (package:flutter/src/widgets/framework.dart:5781:49)
2025-07-19 10:11:20.844571+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.844601+0800 Runner[906:265995] flutter: │ 🐛 Building AppBottomNavigationBar with index: 0
2025-07-19 10:11:20.844636+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.846560+0800 Runner[906:265995] flutter: \^[[38;5;244m┌<…>
2025-07-19 10:11:20.846606+0800 Runner[906:265995] flutter: \^[[38;5;244m│ #0   AppBottomNavigationBar.build.<anonymous closure> (package:sweatmint/features/main_layout/presentation/widgets/app_bottom_navigation_bar.dart:74:21)<…>
2025-07-19 10:11:20.846635+0800 Runner[906:265995] flutter: \^[[38;5;244m│ #1   new _GrowableList.generate (dart:core-patch/growable_array.dart:140:28)<…>
2025-07-19 10:11:20.846663+0800 Runner[906:265995] flutter: \^[[38;5;244m├<…>
2025-07-19 10:11:20.846734+0800 Runner[906:265995] flutter: \^[[38;5;244m│  Item 0: isActive=true, path=assets/images/icons/navigation/ic_home_active.svg<…>
2025-07-19 10:11:20.846776+0800 Runner[906:265995] flutter: \^[[38;5;244m└<…>
2025-07-19 10:11:20.848664+0800 Runner[906:265995] flutter: \^[[38;5;244m┌<…>
2025-07-19 10:11:20.848708+0800 Runner[906:265995] flutter: \^[[38;5;244m│ #0   AppBottomNavigationBar.build.<anonymous closure> (package:sweatmint/features/main_layout/presentation/widgets/app_bottom_navigation_bar.dart:74:21)<…>
2025-07-19 10:11:20.848735+0800 Runner[906:265995] flutter: \^[[38;5;244m│ #1   new _GrowableList.generate (dart:core-patch/growable_array.dart:140:28)<…>
2025-07-19 10:11:20.848762+0800 Runner[906:265995] flutter: \^[[38;5;244m├<…>
2025-07-19 10:11:20.848836+0800 Runner[906:265995] flutter: \^[[38;5;244m│  Item 1: isActive=false, path=assets/images/icons/navigation/ic_tasks_inactive.svg<…>
2025-07-19 10:11:20.848867+0800 Runner[906:265995] flutter: \^[[38;5;244m└<…>
2025-07-19 10:11:20.850686+0800 Runner[906:265995] flutter: \^[[38;5;244m┌<…>
2025-07-19 10:11:20.850727+0800 Runner[906:265995] flutter: \^[[38;5;244m│ #0   AppBottomNavigationBar.build.<anonymous closure> (package:sweatmint/features/main_layout/presentation/widgets/app_bottom_navigation_bar.dart:74:21)<…>
2025-07-19 10:11:20.850757+0800 Runner[906:265995] flutter: \^[[38;5;244m│ #1   new _GrowableList.generate (dart:core-patch/growable_array.dart:140:28)<…>
2025-07-19 10:11:20.850786+0800 Runner[906:265995] flutter: \^[[38;5;244m├<…>
2025-07-19 10:11:20.850817+0800 Runner[906:265995] flutter: \^[[38;5;244m│  Item 2: isActive=false, path=assets/images/icons/navigation/ic_rewards_inactive.svg<…>
2025-07-19 10:11:20.850847+0800 Runner[906:265995] flutter: \^[[38;5;244m└<…>
2025-07-19 10:11:20.850817+0800 Runner[906:265995] flutter: \^[[38;5;244m│  Item 2: isActive=false, path=assets/images/icons/navigation/ic_rewards_inactive.svg<…>
2025-07-19 10:11:20.850847+0800 Runner[906:265995] flutter: \^[[38;5;244m└\342<…>
2025-07-19 10:11:20.852702+0800 Runner[906:265995] flutter: \^[[38;5;244m┌<…>
2025-07-19 10:11:20.852742+0800 Runner[906:265995] flutter: \^[[38;5;244m│ #0   AppBottomNavigationBar.build.<anonymous closure> (package:sweatmint/features/main_layout/presentation/widgets/app_bottom_navigation_bar.dart:74:21)<…>
2025-07-19 10:11:20.852769+0800 Runner[906:265995] flutter: \^[[38;5;244m│ #1   new _GrowableList.generate (dart:core-patch/growable_array.dart:140:28)<…>
2025-07-19 10:11:20.852800+0800 Runner[906:265995] flutter: \^[[38;5;244m├<…>
2025-07-19 10:11:20.852827+0800 Runner[906:265995] flutter: \^[[38;5;244m│  Item 3: isActive=false, path=assets/images/icons/navigation/ic_social_inactive.svg<…>
2025-07-19 10:11:20.852857+0800 Runner[906:265995] flutter: \^[[38;5;244m└<…>
2025-07-19 10:11:20.854573+0800 Runner[906:265995] flutter: \^[[38;5;244m┌<…>
2025-07-19 10:11:20.854611+0800 Runner[906:265995] flutter: \^[[38;5;244m│ #0   AppBottomNavigationBar.build.<anonymous closure> (package:sweatmint/features/main_layout/presentation/widgets/app_bottom_navigation_bar.dart:74:21)<…>
2025-07-19 10:11:20.854636+0800 Runner[906:265995] flutter: \^[[38;5;244m│ #1   new _GrowableList.generate (dart:core-patch/growable_array.dart:140:28)<…>
2025-07-19 10:11:20.854662+0800 Runner[906:265995] flutter: \^[[38;5;244m├<…>
2025-07-19 10:11:20.854729+0800 Runner[906:265995] flutter: \^[[38;5;244m│  Item 4: isActive=false, path=assets/images/icons/navigation/ic_profile_inactive.svg<…>
2025-07-19 10:11:20.854756+0800 Runner[906:265995] flutter: \^[[38;5;244m└<…>
2025-07-19 10:11:20.854729+0800 Runner[906:265995] flutter: \^[[38;5;244m│  Item 4: isActive=false, path=assets/images/icons/navigation/ic_profile_inactive.svg<…>
2025-07-19 10:11:20.854756+0800 Runner[906:265995] flutter: \^[[38;5;244m└\342<…>
2025-07-19 10:11:20.862547+0800 Runner[906:265995] flutter: 🔍 [UserAvatarWidget] 原始头像URL: avatar_14.png
2025-07-19 10:11:20.862762+0800 Runner[906:265995] flutter: 🔗 [UserAvatarWidget] 相对路径转换为完整URL: avatar_14.png -> http://192.168.100.232:8000/media/avatars/avatar_14.png
2025-07-19 10:11:20.871958+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.872108+0800 Runner[906:265995] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:283:12)
2025-07-19 10:11:20.872144+0800 Runner[906:265995] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-19 10:11:20.872176+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.872200+0800 Runner[906:265995] flutter: │ 10:11:20.871 (+0:00:59.189464)
2025-07-19 10:11:20.872226+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.872254+0800 Runner[906:265995] flutter: │ 🐛 EarnedCard 构建参数: swmt=0.0, exp=11586, levelMinExp=10000, levelMaxExp=99999, todayXpIncrease=0
2025-07-19 10:11:20.872283+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.873140+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.873177+0800 Runner[906:265995] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:304:12)
2025-07-19 10:11:20.873203+0800 Runner[906:265995] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-19 10:11:20.873228+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.873271+0800 Runner[906:265995] flutter: │ 10:11:20.872 (+0:00:59.190879)
2025-07-19 10:11:20.873297+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.873319+0800 Runner[906:265995] flutter: │ 🐛 🎯 EarnedCard进度条详细计算:
2025-07-19 10:11:20.873343+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.873994+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.874029+0800 Runner[906:265995] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:305:12)
2025-07-19 10:11:20.874053+0800 Runner[906:265995] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-19 10:11:20.874079+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.874100+0800 Runner[906:265995] flutter: │ 10:11:20.873 (+0:00:59.191751)
2025-07-19 10:11:20.874123+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.874267+0800 Runner[906:265995] flutter: │ 🐛   - 用户总经验: 11586
2025-07-19 10:11:20.874302+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:20.875016+0800 Runner[906:265995] flutter: ┌\342\224
2025-07-19 10:11:20.875016+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:20.875052+0800 Runner[906:265995] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:306:12)
2025-07-19 10:11:20.875085+0800 Runner[906:265995] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-19 10:11:20.875111+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.875135+0800 Runner[906:265995] flutter: │ 10:11:20.874 (+0:00:59.192714)
2025-07-19 10:11:20.875158+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.875182+0800 Runner[906:265995] flutter: │ 🐛   - 当前等级范围: 10000 - 99999
2025-07-19 10:11:20.875206+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.875182+0800 Runner[906:265995] flutter: │ 🐛   - 当前等级范围: 10000 - 99999
2025-07-19 10:11:20.875206+0800 Runner[906:265995] flutter: └\342
2025-07-19 10:11:20.875869+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.875901+0800 Runner[906:265995] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:307:12)
2025-07-19 10:11:20.875926+0800 Runner[906:265995] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-19 10:11:20.875952+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.875974+0800 Runner[906:265995] flutter: │ 10:11:20.875 (+0:00:59.193615)
2025-07-19 10:11:20.876000+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.876022+0800 Runner[906:265995] flutter: │ 🐛   - 等级经验范围: 89999.0
2025-07-19 10:11:20.876045+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.876721+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.876759+0800 Runner[906:265995] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:308:12)
2025-07-19 10:11:20.876785+0800 Runner[906:265995] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-19 10:11:20.876811+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.876835+0800 Runner[906:265995] flutter: │ 10:11:20.876 (+0:00:59.194452)
2025-07-19 10:11:20.876858+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.876881+0800 Runner[906:265995] flutter: │ 🐛   - 在当前等级中的经验: 1586.0
2025-07-19 10:11:20.876904+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.877546+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.877576+0800 Runner[906:265995] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:309:12)
2025-07-19 10:11:20.877598+0800 Runner[906:265995] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-19 10:11:20.877624+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.877706+0800 Runner[906:265995] flutter: │ 10:11:20.876 (+0:00:59.195311)
2025-07-19 10:11:20.877731+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.877753+0800 Runner[906:265995] flutter: │ 🐛   - 进度百分比: 1.76%
2025-07-19 10:11:20.877776+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.878656+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.878687+0800 Runner[906:265995] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:310:12)
2025-07-19 10:11:20.878708+0800 Runner[906:265995] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-19 10:11:20.878732+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.878878+0800 Runner[906:265995] flutter: │ 10:11:20.878 (+0:00:59.196393)
2025-07-19 10:11:20.878904+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.878926+0800 Runner[906:265995] flutter: │ 🐛   - 显示格式: 11,586 / 99,999
2025-07-19 10:11:20.878949+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.880144+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.880175+0800 Runner[906:265995] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:312:12)
2025-07-19 10:11:20.880201+0800 Runner[906:265995] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-19 10:11:20.880224+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.880245+0800 Runner[906:265995] flutter: │ 10:11:20.879 (+0:00:59.197891)
2025-07-19 10:11:20.880270+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.880296+0800 Runner[906:265995] flutter: │ 🐛 经验值显示修复: 总经验=11,586, 下级上限=99,999, 当前等级进度=1.8%
2025-07-19 10:11:20.880320+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.881264+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.881298+0800 Runner[906:265995] flutter: │ #0   EarnedCardState.build (package:sweatmint/features/home/<USER>/widgets/earned_card.dart:316:12)
2025-07-19 10:11:20.881325+0800 Runner[906:265995] flutter: │ #1   StatefulElement.build (package:flutter/src/widgets/framework.dart:5823:27)
2025-07-19 10:11:20.881349+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.881370+0800 Runner[906:265995] flutter: │ 10:11:20.880 (+0:00:59.199022)
2025-07-19 10:11:20.881394+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.881447+0800 Runner[906:265995] flutter: │ 🐛 XP增长显示: hasXpIncrease=false, todayXpIncrease=EarnedCard.todayXpIncrease, 进度=0.017622418026866964
2025-07-19 10:11:20.881473+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.892120+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.892234+0800 Runner[906:265995] flutter: \^[[38;5;12m<…>
2025-07-19 10:11:20.892234+0800 Runner[906:265995] flutter: \^[[38;5;12m\342│ #0   _OverviewCardState.initState (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:62:13)<…>
2025-07-19 10:11:20.892269+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5842:55)<…>
2025-07-19 10:11:20.892306+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.892355+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🎯 OverviewCard: Phase 3优化 - 完全依赖HealthProvider，消除重复API调用<…>
2025-07-19 10:11:20.892389+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:20.892696+0800 Runner[906:265995] flutter: 🎯 Overview进度条详细计算:
2025-07-19 10:11:20.892746+0800 Runner[906:265995] flutter:   - 用户总经验: 11586
2025-07-19 10:11:20.892785+0800 Runner[906:265995] flutter:   - 当前等级范围: 10000 - 99999
2025-07-19 10:11:20.892820+0800 Runner[906:265995] flutter:   - 等级经验范围: 89999.0
2025-07-19 10:11:20.892851+0800 Runner[906:265995] flutter:   - 在当前等级中的经验: 1586.0
2025-07-19 10:11:20.892885+0800 Runner[906:265995] flutter:   - 进度百分比: 1.76%
2025-07-19 10:11:20.892972+0800 Runner[906:265995] flutter:   - 显示格式: 11,586 / 99,999 EXP
2025-07-19 10:11:20.893935+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.893972+0800 Runner[906:265995] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:271:15)
2025-07-19 10:11:20.894004+0800 Runner[906:265995] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:108:28)
2025-07-19 10:11:20.894032+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.894056+0800 Runner[906:265995] flutter: │ 🐛 📱 Overview使用HealthProvider数据
2025-07-19 10:11:20.894085+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.894869+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.894905+0800 Runner[906:265995] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:272:15)
2025-07-19 10:11:20.894934+0800 Runner[906:265995] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:108:28)
2025-07-19 10:11:20.894968+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.894996+0800 Runner[906:265995] flutter: │ 🐛    权限状态 - 步数:true, 距离:false, 卡路里:false
2025-07-19 10:11:20.895024+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.895827+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.895860+0800 Runner[906:265995] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:280:15)
2025-07-19 10:11:20.895886+0800 Runner[906:265995] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:108:28)
2025-07-19 10:11:20.895913+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.895860+0800 Runner[906:265995] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:280:15)
2025-07-19 10:11:20.895886+0800 Runner[906:265995] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:108:28)
2025-07-19 10:11:20.895913+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:20.896247+0800 Runner[906:265995] flutter: │ 🐛 📱 Overview使用HealthProvider数据 - 职责分离
2025-07-19 10:11:20.896277+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.897039+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.897072+0800 Runner[906:265995] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:281:15)
2025-07-19 10:11:20.897101+0800 Runner[906:265995] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:108:28)
2025-07-19 10:11:20.897127+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.898699+0800 Runner[906:265995] flutter: │ 🐛    权限状态 - 步数:true, 距离:false, 卡路里:false
2025-07-19 10:11:20.898729+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.899500+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.899533+0800 Runner[906:265995] flutter: │ #0   _OverviewCardState._buildRightSection (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:282:15)
2025-07-19 10:11:20.899561+0800 Runner[906:265995] flutter: │ #1   _OverviewCardState.build.<anonymous closure> (package:sweatmint/features/home/<USER>/widgets/overview_card.dart:108:28)
2025-07-19 10:11:20.899588+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.899843+0800 Runner[906:265995] flutter: │ 🐛    显示数据 - 步数:0, 距离:0.0, 卡路里:0
2025-07-19 10:11:20.899873+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.920422+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.920507+0800 Runner[906:265995] flutter: \^[[38;5;12m<…>
2025-07-19 10:11:20.920507+0800 Runner[906:265995] flutter: \^[[38;5;12m\342│ #0   _MainLayoutScreenState.initState.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:74:17)<…>
2025-07-19 10:11:20.920553+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)<…>
2025-07-19 10:11:20.920622+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.920507+0800 Runner[906:265995] flutter: \^[[38;5;12m\342│ #0   _MainLayoutScreenState.initState.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:74:17)<…>
2025-07-19 10:11:20.920553+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)<…>
2025-07-19 10:11:20.920622+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342<…>
2025-07-19 10:11:20.920507+0800 Runner[906:265995] flutter: \^[[38;5;12m\342│ #0   _MainLayoutScreenState.initState.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:74:17)<…>
2025-07-19 10:11:20.920553+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)<…>
2025-07-19 10:11:20.920622+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\342\224<…>
2025-07-19 10:11:20.920653+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ MainLayoutScreen: EventTriggeredSyncService已全局初始化，跳过<…>
2025-07-19 10:11:20.920749+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.920931+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.920971+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _MainLayoutScreenState.initState.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:79:17)<…>
2025-07-19 10:11:20.921002+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)<…>
2025-07-19 10:11:20.921031+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.921140+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🎯 MainLayoutScreen: 开始本地初始化和阶段门设置<…>
2025-07-19 10:11:20.921355+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.921825+0800 Runner[906:265995] flutter: \^[[38;5;12m<…>
2025-07-19 10:11:20.921825+0800 Runner[906:265995] flutter: \^[[38;5;12m\342\224┌<…>
2025-07-19 10:11:20.921860+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _MainLayoutScreenState.initState.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:80:17)<…>
2025-07-19 10:11:20.921901+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)<…>
2025-07-19 10:11:20.922060+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.923171+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 📍 MainLayoutScreen: 当前时间 2025-07-19 10:11:20.921607<…>
2025-07-19 10:11:20.923331+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:20.923572+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:20.923572+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:20.923840+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _MainLayoutScreenState.initState.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:84:17)<…>
2025-07-19 10:11:20.923870+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)<…>
2025-07-19 10:11:20.923898+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.923923+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🎯 MainLayoutScreen: 设置阶段门监听器，等待UI加载完成信号<…>
2025-07-19 10:11:20.923950+0800 Runner[906:265995] flutter: \^[[38;5;12m└└\342\224<…>
2025-07-19 10:11:20.924620+0800 Runner[906:265995] flutter: \^[[38;5;12m┌└\342\224<…>
2025-07-19 10:11:20.924620+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:20.924656+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _MainLayoutScreenState._setupPhaseGateListener (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:103:13)<…>
2025-07-19 10:11:20.924686+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState.initState.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:85:9)<…>
2025-07-19 10:11:20.924713+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.924739+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🎯 MainLayoutScreen: 开始设置阶段门监听器<…>
2025-07-19 10:11:20.924766+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.924886+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.924886+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:20.924919+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _MainLayoutScreenState._setupPhaseGateListener (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:145:13)<…>
2025-07-19 10:11:20.924949+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState.initState.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:85:9)<…>
2025-07-19 10:11:20.924978+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.925003+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 🔍 MainLayoutScreen: 当前步骤4状态检查: NOT_STARTED<…>
2025-07-19 10:11:20.925029+0800 Runner[906:265995] flutter: \^[[38;5;12m└└\342\224<…>
2025-07-19 10:11:20.925122+0800 Runner[906:265995] flutter: \^[[38;5;12m┌└\342\224<…>
2025-07-19 10:11:20.925122+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:20.925154+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _MainLayoutScreenState._setupPhaseGateListener (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:154:15)<…>
2025-07-19 10:11:20.925181+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   _MainLayoutScreenState.initState.<anonymous closure> (package:sweatmint/features/main_layout/presentation/screens/main_layout_screen.dart:85:9)<…>
2025-07-19 10:11:20.925210+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.925237+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ⏳ MainLayoutScreen: 步骤4尚未完成，等待阶段门通知<…>
2025-07-19 10:11:20.925264+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.925932+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.925978+0800 Runner[906:265995] flutter: │ #0   _HomeScreenState.initState.<anonymous closure> (package:sweatmint/features/home/<USER>/screens/home_screen.dart:42:17)
2025-07-19 10:11:20.926009+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:20.926035+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.926062+0800 Runner[906:265995] flutter: │ 🐛 ✅ HomeScreen: HealthProvider初始化完成
2025-07-19 10:11:20.926089+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.926176+0800 Runner[906:265995] flutter: \^[[38;5;12m┌<…>
2025-07-19 10:11:20.926209+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _HomeScreenState.initState.<anonymous closure> (package:sweatmint/features/home/<USER>/screens/home_screen.dart:63:19)<…>
2025-07-19 10:11:20.926234+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:20.926261+0800 Runner[906:265995] flutter: \^[[38;5;12m├\342\224<…>
2025-07-19 10:11:20.926286+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ HomeScreen: AuthProvider业务逻辑已完成<…>
2025-07-19 10:11:20.926313+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342\224<…>
2025-07-19 10:11:20.926286+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ HomeScreen: AuthProvider业务逻辑已完成<…>
2025-07-19 10:11:20.926313+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342<…>
2025-07-19 10:11:20.926405+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\224<…>
2025-07-19 10:11:20.926286+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ HomeScreen: AuthProvider业务逻辑已完成<…>
2025-07-19 10:11:20.926313+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342<…>
2025-07-19 10:11:20.926405+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-19 10:11:20.926286+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ HomeScreen: AuthProvider业务逻辑已完成<…>
2025-07-19 10:11:20.926313+0800 Runner[906:265995] flutter: \^[[38;5;12m└\342<…>
2025-07-19 10:11:20.926405+0800 Runner[906:265995] flutter: \^[[38;5;12m┌\342\342\224<…>
2025-07-19 10:11:20.926444+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #0   _HomeScreenState.initState.<anonymous closure> (package:sweatmint/features/home/<USER>/screens/home_screen.dart:110:17)<…>
2025-07-19 10:11:20.926469+0800 Runner[906:265995] flutter: \^[[38;5;12m│ #1   <asynchronous suspension><…>
2025-07-19 10:11:20.926495+0800 Runner[906:265995] flutter: \^[[38;5;12m├<…>
2025-07-19 10:11:20.926523+0800 Runner[906:265995] flutter: \^[[38;5;12m│ 💡 ✅ HomeScreen: 检测到完整且有效的预加载数据，跳过重复加载<…>
2025-07-19 10:11:20.926552+0800 Runner[906:265995] flutter: \^[[38;5;12m└<…>
2025-07-19 10:11:20.926620+0800 Runner[906:265995] flutter: ┌<…>
2025-07-19 10:11:20.926620+0800 Runner[906:265995] flutter: ┌\342<…>
2025-07-19 10:11:20.926620+0800 Runner[906:265995] flutter: ┌\342\342\224
2025-07-19 10:11:20.926650+0800 Runner[906:265995] flutter: │ #0   _HomeScreenState.initState.<anonymous closure> (package:sweatmint/features/home/<USER>/screens/home_screen.dart:111:17)
2025-07-19 10:11:20.926673+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:20.926699+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.926722+0800 Runner[906:265995] flutter: │ 🐛    用户: test_user2
2025-07-19 10:11:20.926748+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.926821+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.926851+0800 Runner[906:265995] flutter: │ #0   _HomeScreenState.initState.<anonymous closure> (package:sweatmint/features/home/<USER>/screens/home_screen.dart:112:17)
2025-07-19 10:11:20.926874+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:20.926901+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.926925+0800 Runner[906:265995] flutter: │ 🐛    今日收益: SWMT 0.0, EXP 0
2025-07-19 10:11:20.926953+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.927024+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.927024+0800 Runner[906:265995] flutter: ┌\342
2025-07-19 10:11:20.927052+0800 Runner[906:265995] flutter: │ #0   _HomeScreenState.initState.<anonymous closure> (package:sweatmint/features/home/<USER>/screens/home_screen.dart:113:17)
2025-07-19 10:11:20.927075+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:20.927100+0800 Runner[906:265995] flutter: ├
2025-07-19 10:11:20.927124+0800 Runner[906:265995] flutter: │ 🐛    任务状态: 0/4
2025-07-19 10:11:20.927150+0800 Runner[906:265995] flutter: └
2025-07-19 10:11:20.927218+0800 Runner[906:265995] flutter: ┌
2025-07-19 10:11:20.927247+0800 Runner[906:265995] flutter: │ #0   _HomeScreenState.initState.<anonymous closure> (package:sweatmint/features/home/<USER>/screens/home_screen.dart:114:17)
2025-07-19 10:11:20.927270+0800 Runner[906:265995] flutter: │ #1   <asynchronous suspension>
2025-07-19 10:11:20.927295+0800 Runner[906:265995] flutter: ├\342\224
2025-07-19 10:11:20.927319+0800 Runner[906:265995] flutter: │ 🐛    VIP状态: true
2025-07-19 10:11:20.927347+0800 Runner[906:265995] flutter: └\342\224
2025-07-19 10:11:20.927319+0800 Runner[906:265995] flutter: │ 🐛    VIP状态: true
2025-07-19 10:11:20.927347+0800 Runner[906:265995] flutter: └\342\224
