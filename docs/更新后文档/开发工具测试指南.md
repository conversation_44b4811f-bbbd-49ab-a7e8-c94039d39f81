# SweatMint 开发工具（DebugToolbox）测试指南

**版本**: v3.0  
**作者**: AI 助手  
**发布日期**: 2025-01-19  
**更新日期**: 2025-01-19

---

## 1. 文档目的
本指南面向 **测试人员 / 研发人员**，说明新加入的 **DebugToolbox 调试模块** 的使用方法、典型测试用例，以及在应用准备上线(Release 构建)时**如何一次性移除全部调试代码**。

> ⚠️ 生产版本默认采用 `kDebugMode` 条件编译，DebugToolbox 在 Release 构建中 **不会编译进 APK / IPA**。如果需要彻底删除（例如安全审计要求），请参考第 6 章。

---

## 2. 调试功能总览
| 功能分类 | 功能 | 入口 | 说明 |
|---------|------|------|------|
| **基础调试** | DebugToolbox 卡片 | 首页底部显式展示（仅 Debug 构建） | 含版本号 Label、演示按钮、隐藏菜单手势 |
| **基础调试** | 隐藏 DebugMenu | 连续点击版本号 5 次 *或* 长按 2 秒 | 以 BottomSheet 形式弹出调试菜单 |
| **健康数据** | syncDelta() 手动触发 | DebugMenu 第 1 行 | 立即调用 `HealthProvider.syncDeltaNow()` → `/api/app/v1/health/sync/` API |
| **健康数据** | baselineReset() 手动触发 | DebugMenu 第 2 行 | 立即调用 `HealthProvider.baselineResetNow()` → `/api/app/v1/health/baseline-reset/` API |
| **🔥 权限检查** | 检查所有权限状态 | DebugMenu 第 3 行 | 显示步数、距离、卡路里的详细权限状态（iOS原生返回值） |
| **🔥 权限检查** | 检查步数权限 | DebugMenu 第 4 行 | 单独检查步数权限状态（authorized/denied/notDetermined） |
| **🔥 权限检查** | 检查距离权限 | DebugMenu 第 5 行 | 单独检查距离权限状态（authorized/denied/notDetermined） |
| **🔥 权限检查** | 检查卡路里权限 | DebugMenu 第 6 行 | 单独检查卡路里权限状态（authorized/denied/notDetermined） |
| **🔥 权限检查** | 强制重新检查权限 | DebugMenu 第 7 行 | 忽略缓存，直接调用iOS原生API重新检查所有权限 |
| **任务演示** | Extra Tasks Demo | DebugToolbox 内按钮 | 跳转到任务演示页面（现有示例，不影响主流程） |

---

## 3. 使用步骤
1. **开启 Debug 构建**  
   ```bash
   flutter run --debug   # 或 VS Code/Android Studio 选择 Debug 运行
   ```
2. **打开首页** – 页面底部可见 "🛠️ 开发工具" 卡片。  
3. **触发隐藏菜单**  
   - 方法 A：快速 **点击版本号文本 5 次**；  
   - 方法 B：**长按版本号 2 秒**。  
4. **执行调试功能**  
   - **健康数据同步**: 点击对应条目，Xcode/Logcat 控制台将看到 `🧪 手动触发 syncDeltaNow()` 或 `baselineResetNow()` 日志；  
   - **🔥 权限检查**: 点击权限检查按钮，弹窗显示iOS HealthKit的原始权限状态；
   - 后端应返回 200，验证 API 路径：`/api/app/v1/health/sync/` 或 `/api/app/v1/health/baseline-reset/`。  
5. **查看 UI & 日志** – 同步成功会通过 `ScaffoldMessenger` Toast 提示 `syncDelta ✓` 或 `baselineReset ✓`；权限检查会弹窗显示详细状态。

---

## 4. 典型测试用例
| ID | 场景 | 预期结果 |
|----|------|----------|
| T-01 | 连续点击版本号 5 次 | BottomSheet 弹出 DebugMenu |
| T-02 | 长按版本号 2 秒 | 同上 |
| T-03 | 执行 *syncDelta()* | • API 调用成功 • Provider 更新 • Toast "syncDelta ✓"|
| T-04 | 执行 *baselineReset()* | • API 调用成功 • Toast "baselineReset ✓" |
| **🔥 T-05** | **检查所有权限状态** | **弹窗显示步数、距离、卡路里的详细权限状态** |
| **🔥 T-06** | **检查步数权限** | **弹窗显示步数权限状态（authorized/denied/notDetermined）** |
| **🔥 T-07** | **检查距离权限** | **弹窗显示距离权限状态（authorized/denied/notDetermined）** |
| **🔥 T-08** | **检查卡路里权限** | **弹窗显示卡路里权限状态（authorized/denied/notDetermined）** |
| **🔥 T-09** | **强制重新检查权限** | **忽略缓存，显示最新的权限状态，解决权限状态时机问题** |
| T-10 | Release 构建 (`flutter build apk --release`) | 页面 **不显示** 开发工具卡片； `grep -R "DebugToolbox" build/` 返回 0 |

---

## 5. API 接口详情

### 5.1 syncDelta() 接口
- **路径**: `/api/app/v1/health/sync/`
- **方法**: POST
- **功能**: 同步健康数据增量，更新任务进度
- **请求格式**:
  ```json
  {
    "current_totals": {
      "steps": 5000,
      "distance": 3.2,
      "calories": 250
    },
    "device_id": "iOS_12345",
    "platform": "ios",
    "health_source": "apple_health"
  }
  ```
- **响应格式**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "net_increment": {"steps": 100, "distance": 0.1, "calories": 10},
      "updated_tasks": [...],
      "message": "健康数据同步成功"
    }
  }
  ```

### 5.2 baselineReset() 接口
- **路径**: `/api/app/v1/health/baseline-reset/`
- **方法**: POST
- **功能**: 重置健康数据基线，处理跨天场景
- **请求格式**:
  ```json
  {
    "new_totals": {
      "steps": 1000,
      "distance": 0.8,
      "calories": 50
    },
    "device_id": "iOS_12345",
    "platform": "ios",
    "health_source": "apple_health"
  }
  ```
- **响应格式**:
  ```json
  {
    "code": 200,
    "message": "操作成功", 
    "data": {
      "baseline_steps": 1000,
      "reset_date": "2025-01-19T10:30:00Z",
      "message": "健康数据基线重置成功"
    }
  }
  ```

### 🔥 5.3 健康权限检查功能
- **实现方式**: 直接调用iOS原生HealthKit API
- **权限类型**: 
  - **步数**: `HKQuantityType.stepCount`
  - **距离**: `HKQuantityType.distanceWalkingRunning` 
  - **卡路里**: `HKQuantityType.activeEnergyBurned`
- **权限状态**:
  - `authorized`: 已授权
  - `denied`: 已拒绝
  - `notDetermined`: 未确定（首次询问）
- **功能说明**:
  - 检查所有权限：显示3个权限的完整状态
  - 单独检查：针对特定权限的详细检查
  - 强制重新检查：忽略缓存，获取最新状态
  - **解决问题**: 修复权限请求完成后立即检查状态的时机问题

---

## 6. 🔥 健康权限检查使用指南

### 6.1 使用场景
- **权限状态调试**: 当用户反馈权限问题时，快速查看iOS原生返回的权限状态
- **时机问题排查**: 解决权限请求完成后立即检查状态可能存在的延迟问题
- **权限不一致问题**: 验证手机设置中的权限状态与应用检测到的状态是否一致

### 6.2 操作步骤
1. **打开开发工具**: 连续点击版本号5次或长按2秒
2. **选择权限检查功能**:
   - 🛡️ **检查所有权限状态**: 一次性查看3个权限的状态
   - 🧡 **检查步数权限**: 单独查看步数权限
   - 🔵 **检查距离权限**: 单独查看距离权限  
   - 🔴 **检查卡路里权限**: 单独查看卡路里权限
   - 🟣 **强制重新检查权限**: 忽略缓存强制检查
3. **查看结果**: 弹窗显示详细的权限状态信息

### 6.3 权限状态说明
| 状态 | 含义 | 解决方案 |
|------|------|----------|
| `authorized` | ✅ 已授权 | 正常状态，可以读取健康数据 |
| `denied` | ❌ 已拒绝 | 用户拒绝了权限，需要引导用户到设置中手动开启 |
| `notDetermined` | ⚠️ 未确定 | 首次询问或权限被重置，需要重新请求权限 |

### 6.4 常见问题排查
| 问题 | 现象 | 排查方法 |
|------|------|----------|
| **权限状态不一致** | 手机设置显示已授权，但应用检测为denied | 使用"强制重新检查权限"功能 |
| **权限请求无效** | 权限弹窗显示但状态仍为notDetermined | 检查权限请求完成后是否有延迟检查 |
| **部分权限未授权** | 只授权了步数，距离和卡路里为denied | 使用单独检查功能确认每个权限的具体状态 |

---

## 7. 上线前删除调试模块
调试代码已通过 `kDebugMode` 保护，Release 构建自动剔除；若需**物理删除**：

1. **移除调试目录** (仅 1 条命令)：
   ```bash
   git rm -r lib/features/debug
   ```
2. **编辑 `lib/features/home/<USER>/screens/home_screen.dart`**
   - 删除 `import 'package:sweatmint/features/debug/...';`
   - 删掉 `if (kDebugMode) ... const DebugToolbox(),` 这段代码块（已使用条件编译，留空亦不会影响 Release）。
3. **全局搜索确保无残留**
   ```bash
   grep -R "DebugToolbox" lib/ || echo "✅ 无残留引用"
   ```
4. **重新构建 Release**，确保体积未包含调试资源。

> **建议**：保留 `kDebugMode` 条件编译实现，日后回归开发分支时可快速恢复调试能力。

---

## 8. 常见问题 FAQ
1. **Q: 为什么 Release 仍显示开发工具？**  
   A: 请确认是否使用 `--release` 或 `--profile` 构建，并检查 `assert(kDebugMode)` 是否被错误修改。

2. **Q: 手动同步按钮无效？**  
   A: 检查设备是否已授健康权限；无权限时 `_healthService.syncHealthDataWithBaseline()` 会抛异常并在 Toast 中提示。

3. **Q: baselineReset 导致数据清零？**  
   A: baselineReset 会重新设定基线为当前 totals，测试时请勿在正式账号频繁调用。

4. **Q: API 调用失败显示 404？**  
   A: 确认后端已部署统一健康API模块 (`api.health`)，检查路径是否为 `/api/app/v1/health/*`。

5. **Q: 同步成功但数据未更新？**  
   A: 检查后端日志确认增量计算是否正确，验证任务更新逻辑是否正常执行。

6. **🔥 Q: 权限检查显示denied但手机设置中已授权？**  
   A: 使用"强制重新检查权限"功能，可能存在权限状态缓存或时机问题。

7. **🔥 Q: 权限请求完成后状态仍为notDetermined？**  
   A: iOS权限请求完成后可能存在延迟，已在原生代码中添加0.5秒延迟重新检查。

8. **🔥 Q: 如何确认iOS原生返回的真实权限状态？**  
   A: 使用开发工具中的权限检查功能，直接显示iOS HealthKit API的原始返回值。

---

## 9. 技术实现要点

### 9.1 前端实现
- **状态管理**: 使用 `ViewModelMixin` 的 `executeAsyncAction` 包装异步操作
- **错误处理**: 统一异常处理和Toast反馈机制
- **权限检查**: 调用前确保健康数据权限已授权
- **日志记录**: 详细的调试日志便于问题排查
- **🔥 权限调试**: 新增5个权限检查按钮，直接调用iOS原生API

### 9.2 后端实现
- **API统一**: 所有健康数据API统一到 `/api/app/v1/health/` 路径
- **增量计算**: 后端中心化计算 `delta = new_totals - baseline`
- **数据验证**: 验证增量数据合理性，防止异常数据
- **任务更新**: 自动更新相关任务进度并发放奖励

### 9.3 🔥 iOS原生层修复
- **权限时机修复**: 在权限请求回调中添加0.5秒延迟，确保权限状态更新
- **详细日志**: 权限请求前后都记录每个权限的详细状态
- **强制检查**: 提供忽略缓存的权限状态检查方法

### 9.4 安全考虑
- **条件编译**: 使用 `kDebugMode` 确保生产环境不包含调试代码
- **权限检查**: 调试功能也需要通过正常的权限验证
- **数据保护**: 避免在生产数据上执行频繁的基线重置操作
- **🔥 权限安全**: 权限检查功能仅在开发环境可用，不会泄露用户权限信息

---

### 结束语
DebugToolbox 旨在 **提高测试效率** 与 **故障排查速度**；严格遵守 `kDebugMode` 约束，线上环境安全无忧。🔥 新增的健康权限检查功能特别适用于排查权限状态不一致问题，帮助开发者快速定位iOS HealthKit权限的真实状态。如在测试过程中发现问题，请在 Issue 中附加 **日志、操作步骤、截图**，便于定位。 