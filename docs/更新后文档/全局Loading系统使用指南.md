# SweatMint 全局Loading系统使用指南

**版本**: 2.0  
**作者**: AI助手  
**日期**: 2025年1月15日  
**状态**: ✅ **已完全实施**

---

## 🎯 系统概述

SweatMint全局Loading系统是一个**自定义实现**的统一loading管理方案，**不是Flutter自带功能**。它解决了应用中loading状态显示不统一、管理复杂的问题，提供了多种loading样式和便捷的使用方式。

### **核心特性**
- 🎨 **多种Loading样式**: 全屏、页面、底部、按钮、自定义
- 🔄 **统一管理**: 单例模式，全局统一控制
- 🚀 **便捷使用**: 集成ViewModelMixin，一行代码调用
- 📱 **响应式设计**: 基于flutter_screenutil自适应屏幕
- 🛡️ **安全可靠**: 自动资源清理，防止内存泄漏

---

## 🏗️ 核心组件

### 1. **GlobalLoadingService** (全局服务)
```dart
// 单例模式，全局唯一实例
GlobalLoadingService.instance.showFullScreenLoading(message: "加载中...");
```

**特点**:
- ✅ 全局单例，整个应用共享
- ✅ 支持5种Loading类型
- ✅ 基于Overlay系统，可覆盖任意页面
- ❌ 需要手动设置context

### 2. **ViewModelMixin** (状态管理混入)
```dart
class MyProvider extends ChangeNotifier with ViewModelMixin {
  Future<void> loadData() async {
    await executeWithFullScreenLoading(
      () => apiService.getData(),
      loadingMessage: "正在加载数据...",
    );
  }
}
```

**特点**:
- ✅ 混入(mixin)模式，可复用
- ✅ 集成GlobalLoadingService
- ✅ 提供便捷的执行方法
- ✅ 自动处理异常和状态管理

---

## 🎨 Loading类型详解

| 类型 | 效果 | 使用场景 | 调用方法 |
|------|------|----------|----------|
| **全屏Loading** | 黑色半透明遮罩 + 居中loading | 登录、重要操作 | `executeWithFullScreenLoading()` |
| **页面Loading** | 弹窗形式，不可取消 | 数据提交、保存 | `executeWithPageLoading()` |
| **底部Loading** | 底部固定条，不阻塞操作 | 后台同步、上传 | `executeWithBottomLoading()` |
| **按钮Loading** | 按钮内部loading状态 | 表单提交、点击操作 | `executeAsyncAction()` (默认) |
| **自定义Loading** | 完全自定义样式 | 特殊需求 | `showCustomLoading()` |

---

## 🔧 使用方法

### **方式一: ViewModelMixin便捷方法 (推荐)**

```dart
class TaskProvider extends ChangeNotifier with ViewModelMixin {
  
  // 1. 全屏Loading - 重要操作
  Future<void> completeTask() async {
    await executeWithFullScreenLoading(
      () => taskService.completeTask(),
      loadingMessage: "正在完成任务...",
      entityName: "任务完成",
    );
  }
  
  // 2. 页面Loading - 数据提交
  Future<void> saveData() async {
    await executeWithPageLoading(
      () => apiService.saveData(),
      loadingMessage: "正在保存...",
    );
  }
  
  // 3. 底部Loading - 后台同步
  Future<void> syncData() async {
    await executeWithBottomLoading(
      () => syncService.sync(),
      loadingMessage: "正在同步数据...",
    );
  }
  
  // 4. 按钮Loading - 默认方式
  Future<void> refreshData() async {
    await executeAsyncAction(
      () => apiService.refresh(),
      entityName: "数据刷新",
    );
  }
}
```

### **方式二: 直接使用GlobalLoadingService**

```dart
class MyWidget extends StatelessWidget {
  void _handleAction() async {
    // 显示loading
    GlobalLoadingService.instance.showFullScreenLoading(
      message: "处理中...",
    );
    
    try {
      await someAsyncOperation();
    } finally {
      // 隐藏loading
      GlobalLoadingService.instance.hideLoading();
    }
  }
}
```

---

## ⚙️ 自动化程度说明

### **✅ 自动化部分**
1. **状态管理**: ViewModelMixin自动管理isLoading和errorMessage
2. **资源清理**: 自动清理Overlay和释放资源
3. **异常处理**: 自动捕获异常并设置错误状态
4. **UI更新**: 自动调用notifyListeners()更新UI

### **❌ 需要手动设置部分**
1. **Loading类型选择**: 需要选择合适的loading方法
2. **Provider混入**: 新的Provider需要手动混入ViewModelMixin
3. **Context设置**: GlobalLoadingService需要设置context
4. **消息内容**: 需要设置合适的loading消息

---

## 📝 新页面使用步骤

### **Step 1: 创建Provider**
```dart
class NewPageProvider extends ChangeNotifier with ViewModelMixin {
  // 混入ViewModelMixin，获得loading能力
}
```

### **Step 2: 在页面中提供Provider**
```dart
class NewPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => NewPageProvider(),
      child: Consumer<NewPageProvider>(
        builder: (context, provider, child) {
          return Scaffold(
            // 页面内容
          );
        },
      ),
    );
  }
}
```

### **Step 3: 在Provider中使用Loading**
```dart
class NewPageProvider extends ChangeNotifier with ViewModelMixin {
  Future<void> loadData() async {
    // 根据需要选择loading类型
    await executeWithFullScreenLoading(
      () => apiService.getData(),
      loadingMessage: "加载中...",
    );
  }
}
```

### **Step 4: UI中监听状态**
```dart
Consumer<NewPageProvider>(
  builder: (context, provider, child) {
    return Column(
      children: [
        // 显示错误信息
        if (provider.errorMessage != null)
          Text(provider.errorMessage!),
        
        // 按钮loading状态
        ElevatedButton(
          onPressed: provider.isLoading ? null : provider.loadData,
          child: provider.isLoading 
            ? CircularProgressIndicator() 
            : Text("加载数据"),
        ),
      ],
    );
  },
)
```

---

## 🎯 最佳实践

### **Loading类型选择建议**

| 操作类型 | 推荐Loading | 理由 |
|----------|-------------|------|
| 用户登录 | 全屏Loading | 阻塞性操作，需要用户等待 |
| 数据保存 | 页面Loading | 重要操作，防止用户误操作 |
| 后台同步 | 底部Loading | 不阻塞用户操作，提供反馈 |
| 列表刷新 | 按钮Loading | 轻量操作，在控件内显示状态 |
| 文件上传 | 自定义Loading | 需要显示进度条等特殊UI |

### **代码规范**
```dart
// ✅ 推荐：使用ViewModelMixin便捷方法
await executeWithFullScreenLoading(
  () => apiCall(),
  loadingMessage: "具体的操作描述",
  entityName: "功能模块名称",
);

// ❌ 不推荐：手动管理loading状态
_isLoading = true;
notifyListeners();
try {
  await apiCall();
} finally {
  _isLoading = false;
  notifyListeners();
}
```

### **性能优化**
- 优先使用按钮Loading，资源消耗最小
- 避免同时显示多个全屏Loading
- 及时清理不需要的loading状态
- 合理设置loading消息，提升用户体验

---

## 🔍 常见问题

### **Q: 为什么不是Flutter自带的功能？**
A: Flutter只提供基础的CircularProgressIndicator组件，我们的系统是自定义实现，提供了更丰富的功能和统一的管理方式。

### **Q: 是否完全自动？**
A: 部分自动。状态管理、资源清理、异常处理是自动的，但loading类型选择和Provider混入需要手动设置。

### **Q: 如何在现有页面中添加？**
A: 只需要在Provider中混入ViewModelMixin，然后将原有的异步方法调用改为使用ViewModelMixin提供的便捷方法即可。

### **Q: 多个loading会冲突吗？**
A: GlobalLoadingService使用单例模式，同时只能显示一个loading。新的loading会自动替换旧的。

---

## 📊 系统架构图

```
用户操作
    ↓
UI层 (Consumer监听)
    ↓
Provider层 (ViewModelMixin)
    ↓
GlobalLoadingService (单例)
    ↓
Overlay系统 (Flutter)
    ↓
屏幕显示
```

---

**总结**: SweatMint全局Loading系统提供了统一、便捷、多样化的loading管理方案。通过ViewModelMixin的便捷方法，开发者可以轻松为任何异步操作添加合适的loading效果，提升用户体验。 