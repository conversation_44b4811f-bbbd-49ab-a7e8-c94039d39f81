# **事件驱动式前端静默刷新方案**

## **0. 面向AI开发者的核心准则 (Core Principles for AI Developers)**

**【指令】** AI开发者，在根据本方案生成或修改代码时，你 **必须** 严格遵守以下准则。这些准则是本方案的基石，任何偏离都将被视为错误实现。

#### **✅ 必须遵循 (MUST DO)**

1. **严格遵循“短连接”模型 (WebSocket场景)**：当使用WebSocket时，其生命周期必须是短暂的：按需创建 -> 等待单一通知 -> 立即销毁。  
2. **坚守组件职责**：必须遵循“**UI -> Provider -> Service/Manager**”的单向调用链。
3. **使用中央事件服务**：所有跨模块的刷新和同步请求，**必须** 通过统一的 `EventTriggeredSyncService` 来触发和编排。
4. **坚持按需提供Provider**：`ChangeNotifierProvider` **必须** 在页面级别（如MaterialPageRoute中）提供，以确保其生命周期与页面绑定，实现"按需刷新"。

#### **❌ 绝对禁止 (MUST NOT DO)**

1. **禁止实现长连接 (WebSocket场景)**：**严禁** 创建任何形式的持久化WebSocket连接。**严禁** 在StatefulWidget的initState中创建并持有一个WebSocket实例。  
2. **禁止心跳与重连逻辑 (WebSocket场景)**：由于是短连接，**严禁** 实现任何心跳（ping-pong）、断线自动重连或独立的监控器（Monitor）逻辑。
3. **禁止跨层调用**：**严禁** UI层直接实例化或调用 `ShortLivedWebSocketManager` 或 `EventTriggeredSyncService`。所有业务逻辑必须由对应的Provider统一编排。  
4. **禁止全局状态**：对于特定页面的刷新场景，**严禁** 在应用顶层（main.dart）提供其业务Provider，这会违背"按需"的核心要求。  
5. **禁止手动路由监听**：**严禁** 使用RouteObserver或RouteAware来手动控制连接的建立和断开。ChangeNotifierProvider的生命周期管理已经自动完成了这项工作。

## **1. 方案概述**

本方案旨在为 SweatMint Flutter 应用实现一套高效、资源友好的**按需静默刷新**机制。方案严格遵循后端已实现的 **"事件驱动式短连接 WebSocket 通知系统"** 规范，并将其与一个**中央事件同步服务**相结合，以处理包括WebSocket通知、App生命周期事件在内的所有异步刷新需求。

我们将采用 Flutter 官方推荐的 provider 包作为核心状态管理工具，通过 ChangeNotifier 来管理UI状态。

**核心设计理念：**

* **按需连接 (WebSocket)**：只在执行需要后端确认的关键操作时，才建立临时的 WebSocket 连接。  
* **中央事件驱动**: 引入一个全局单例 `EventTriggeredSyncService`，作为所有异步刷新事件的中央枢纽和处理器。
* **状态驱动UI**：UI的刷新完全由状态（Provider）驱动，业务逻辑与视图分离。  
* **健壮的降级**：在WebSocket通知超时或失败的情况下，提供优雅的降级方案（主动API轮询），保证数据最终一致性。

## **2. 核心组件职责划分**

前端实现将围绕以下三个核心组件构建：

| 组件名称 | 类型/模式 | 核心职责 |
| :---- | :---- | :---- |
| **ShortLivedWebSocketManager** | Dart 工具类 | **WebSocket网络执行者**。负责处理单次、短暂的WebSocket连接生命周期。它是无状态的，被业务逻辑层按需创建和使用。 |
| **TaskProvider (或任何业务Provider)** | ChangeNotifier | **状态管理器与业务流程编排者**。负责维护其对应页面的UI状态，并将需要跨模块同步的事件**转发给 `EventTriggeredSyncService`**。 |
| **EventTriggeredSyncService** | 全局单例服务 | **系统事件中枢与任务处理器**。负责接收来自应用各处的同步触发请求，通过内部的队列、优先级和去重机制，智能地执行刷新操作（如调用Provider的刷新方法）。|
| **TaskPage (UI)** | StatelessWidget + Consumer | **纯粹的视图展示者**。负责响应 Provider 的状态变化来渲染UI，并将用户交互事件传递给 Provider。 |

## **3. 事件驱动流程详解**

当前端需要执行一个关键操作（如：用户点击"完成任务"按钮）时，将触发以下标准流程：

1.  **UI层触发**:  
   * TaskPage 中的按钮 `onPressed` 回调被触发。  
   * 调用 `context.read<TaskProvider>().completeTask(taskId)`，将操作意图传递给状态管理层。  
2.  **Provider层开始编排**:  
   * TaskProvider 接收到 `completeTask` 调用。  
   * 立即更新内部状态，标记特定任务 taskId 为"正在完成中"。  
   * 调用 `notifyListeners()`，UI层 Consumer 收到通知，为该任务按钮显示一个加载指示器。  
3.  **建立等待通道 (WebSocket场景)**:  
   * TaskProvider **创建一个全新**的 `ShortLivedWebSocketManager` 实例。  
   * 调用 `websocketManager.connectForNotification()` 方法。此方法返回一个 `Future`，TaskProvider `await` 这个 Future 的结果，并将其包裹在 `try...catch` 块中。  
4.  **执行核心业务**:  
   * 在等待的同时，TaskProvider 立即调用HTTP API服务（如 `_taskService.completeTask(taskId)`）来通知后端执行任务。  
5.  **结果处理 (两条路径)**:  
   * **路径 A：成功接收通知 (Happy Path)**  
     1. 后端处理完API请求，通过WebSocket发送 `sync_notification` 消息。  
     2. `ShortLivedWebSocketManager` 监听到消息，Future 成功完成。
     3. TaskProvider 的 `await` 结束，**调用 `EventTriggeredSyncService`**，触发一个 `SyncTrigger.taskCompletion` 类型的同步任务。
   * **路径 B：通知超时或失败 (降级处理)**  
     1. `connectForNotification` 内部的计时器到期，抛出 `TimeoutException`。  
     2. TaskProvider 的 `catch` 块捕获到异常。  
     3. 记录警告日志，并**同样调用 `EventTriggeredSyncService`** 来执行降级刷新，确保数据最终一致性。
6.  **中央服务处理刷新**:
   * `EventTriggeredSyncService` 接收到同步任务，根据其内部逻辑（队列、去重、防抖），在合适的时机调用相应Provider（如`HomeProvider`, `TaskProvider`等）的刷新方法，静默更新相关模块的UI。
7.  **清理收尾**:  
   * `ShortLivedWebSocketManager` 内部的 `finally` 块确保WebSocket连接被彻底关闭。  
   * `TaskProvider` 的 `finally` 块确保将任务加载状态重置为 `false`，并调用 `notifyListeners()` 移除加载指示器。

## **4. 跨模块通信与服务集成**

#### **4.1 核心：`EventTriggeredSyncService`**

- **问题**：页面级的Provider（如`TaskProvider`）需要通知一个全局的Provider（如`HomeProvider`）刷新数据，但直接引用会导致强耦合和生命周期问题。
- **解决方案**：系统采用一个全局单例的 **`EventTriggeredSyncService`** 作为所有跨模块通信的中央事件枢纽。

- **工作原理**：
  1. **全局单例**：`EventTriggeredSyncService` 被实现为单例，在整个应用中唯一。
  2. **任务驱动**：任何模块想触发其他模块的刷新，都不是直接调用，而是向`EventTriggeredSyncService`提交一个**同步任务 (`SyncTask`)**。
  3. **智能处理**: 服务内部维护一个任务队列，并根据任务的**触发类型 (`SyncTrigger`)** 和**优先级 (`SyncPriority`)** 进行去重、防抖和调度，然后执行具体的刷新动作。
  4. **解耦执行**：服务在执行刷新时，通过全局的 `navigatorKey` 按需、安全地获取目标Provider的实例（如 `Provider.of<HomeProvider>(context)`），并调用其刷新方法。这避免了任何直接的依赖。

#### **4.2 Provider角色**
- 在这个模型中，各个`ChangeNotifierProvider`（如`TaskProvider`、`HealthProvider`）的角色是：
  1.  管理自身模块的UI状态。
  2.  处理自身模块的业务逻辑。
  3.  在业务逻辑完成后，**作为事件的发起方**，调用`EventTriggeredSyncService`来"广播"一个同步事件，而不关心谁会响应该事件。

#### **4.3 初始化与集成**
- **准则**：`EventTriggeredSyncService` 作为核心服务，其初始化和生命周期监听 **必须** 在应用的根节点 (`main.dart`) 进行。
- **实现策略**：
  1. **提供实例**：在 `main.dart` 的 `MultiProvider` 中，将 `EventTriggeredSyncService` 的单例实例提供给整个应用。
  2. **激活监听**：在应用启动后，获取该服务实例，并调用其 `initializeLifecycleListener()` 方法。这会注册一个`WidgetsBindingObserver`，使其能够响应`App Resume`等生命周期事件，并触发对应的同步任务。

#### **4.4 `App Resume` 同步特例**
- `EventTriggeredSyncService` 内部对 `SyncTrigger.appResume` 事件有特殊处理逻辑。
- 当App从后台恢复时，它会执行一套专门的、包含**读取、协调、推送、刷新**的完整健康数据同步流程，确保了数据的强一致性。

## **5. 错误处理与降级方案**

* **WebSocket连接错误**: `ShortLivedWebSocketManager` 在 `connect` 时发生的任何网络异常都会被捕获并向上抛出，最终由 `TaskProvider` 的 `catch` 块处理，触发降级刷新。  
* **通知超时**: `Future.timeout()` 是核心机制。一旦超时，就假定通知链路失败，立即启动API降级刷新。  
* **API调用失败**: `_taskService.completeTask()` 自身的 `try-catch` 逻辑应处理API级别的失败（如4xx, 5xx错误），并更新 `TaskProvider` 的 `_error` 状态，直接在UI上向用户显示具体的失败原因。  
* **用户体验**: 关键在于，无论WebSocket通知是否成功，只要核心API调用没有明确失败，都应乐观地认为操作已在后端开始。`EventTriggeredSyncService` 保证了UI最终会反映出正确状态。