# SweatMint 双 Token 认证实现说明

> 版本：v1.0  作者：AI 助手  发布日期：2025-06-20
>
> 本文档面向 **后端 / 前端开发人员与审计人员**，系统性说明 SweatMint 目前已落地的 **Access-Token + Refresh-Token** 双 Token 机制。所有描述均基于 2025-06-20 主干代码，确保与线上实现一一对应。

---
设备ID现在完全统一：所有模块都使用DeviceIdManager
## 1. 方案概览

| 项 | 内容 |
|---|---|
| **Token 架构** | 短效 **Access-Token**（15 min）＋长效 **Refresh-Token**（14 day） |
| **刷新方式** | 前端检测剩余过期时间→调用 `/token/refresh/` → 后端返回 *全新* Access & Refresh |
| **轮换与黑名单** | 后端启用 `ROTATE_REFRESH_TOKENS = True`、`BLACKLIST_AFTER_ROTATION = True`，旧 Refresh-Token 立即加入黑名单 |
| **单设备策略** | 设备指纹 `device_id` 必填；刷新端点对跨设备刷新返回 **409 Device Conflict** 并强退旧设备 |
| **存储介质** | 前端：`flutter_secure_storage` (Keychain / EncryptedSharedPreferences)<br/>后端：`OutstandingToken / BlacklistedToken`（SimpleJWT 提供） |

---

## 2. 后端实现要点（Django DRF + SimpleJWT）

### 2.1 关键配置（`settings.py`）
简化摘录：
```python
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=15),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=14),
    'ROTATE_REFRESH_TOKENS': True,   # 刷新即轮换
    'BLACKLIST_AFTER_ROTATION': True,
}
```

### 2.2 主要端点
| 端点 | 视图 | 说明 |
|------|------|------|
| `/authentication/token/obtain/` | `AppLoginView` | 登录成功后返回 Access / Refresh |
| `/authentication/token/refresh/` | `AppTokenRefreshView` | 前端持 Refresh 换新对；设备指纹不符 ⇒ 409 |
| `/authentication/token/verify/` | DRF 默认 | 可选：安全排查 |

*`AppTokenRefreshView` 已扩展 **设备冲突检测**、**黑名单写入** 与异常日志。*

### 2.3 日志脱敏
后端所有日志仅打印 Access-Token **前 8 位**＋ `hash(token)`，绝不输出完整值。实现见 `views_app.py` 第 📄 298 行。 

---

## 3. 前端实现要点（Flutter）

### 3.1 TokenManager 核心状态
- `_access_token` / `_refresh_token` / `_token_expiry_exp`（秒级 Epoch）
- 智能后台刷新定时器（默认 5 min，根据剩余时长动态缩短）
- 设备冲突、速率限制、指数退避等防护逻辑

### 3.2 刷新触发
```
1. 每次发请求前→调用 TokenManager.getAccessToken()
2. 若剩余时间 ≤ 原生命周期 × 50 %（∵15 min ⇒ 7.5 min）→ 主动刷新
3. App 前台定时器 + 生命周期恢复也会触发检测
```

### 3.3 失败分支
| 场景 | 处理 |
|------|------|
| 网络超时 / 429 | 指数退避，后台定时器延后 |
| 401（Refresh 失效） | 清空本地 Token → 跳转登录页 |
| 409（设备冲突） | 调用 `GlobalAuthService.handleDeviceConflict()` → 弹窗提醒并登出 |

---

## 4. 安全加固点
1. **轮换链追踪**：每次刷新返回全新 JTI，旧 JTI 立即黑名单，杜绝重放。
2. **Refresh 频控**：同一 Refresh-Token 15 min 内只接受一次刷新（中间件 + Redis 限流）。
3. **指纹校验**：后端校验 `device_id`；不符立即 409。
4. **日志脱敏**：前后端均不输出完整 Token，满足合规要求。
5. **HTTPS 强制**：Nginx TLS1.2+，HSTS 180 day。

---

## 5. 当前落地状态
- ✅ **前端**：`TokenManager` 全面切换双 Token；背景刷新、冲突检测已上线。
- ✅ **后端**：`AppLoginView / AppTokenRefreshView` 按双 Token 逻辑返回 & 轮换；黑名单表启用。
- ✅ **文档**：登录流程、迁移报告、TokenManager 代码注释均同步更新。
- 🚧 **后续事项**：Android Health Connect 实现完成后，需要在刷新请求头加上 `platform: android` 以便后端统计。

---

## 6. 常见问答
**Q1 : 为什么 Access-Token 只活 15 分钟？**  
短效 Access 减少泄漏风险；Refresh-Token 轮换链可审计，符合 OWASP Token 最佳实践。  

**Q2 : 若用户 14 天内未打开 App，会怎么样？**  
Refresh-Token 将过期；再次打开时需重新登录。  

**Q3 : 多端登录需求？**  
SweatMint 业务要求 **单设备强登录**。如需开放多端，可在刷新端点放宽设备冲突校验并改为多设备会话表。  

---

*文档完* 