# SweatMint 会话管理与基线系统技术规范 v13.2.1

**文档版本**: v13.2.1  
**创建日期**: 2025-07-05  
**最后更新**: 2025-07-06  
**适用范围**: iOS/Android 健康数据系统  
**依赖规范**: v13.0.1登录流程规范、基线确定.md、OFFLINE_PROCESSING_STANDARDS.md  
**重要更新**: 完善与v13.0.1的集成，补充业务场景示例，强化会话周期完整性要求

---

## 0. 与v13.0.1登录流程规范的集成关系

### 0.1 文档职责分工
- **v13.0.1**: 定义SweatMint **5步骤标准流程**和业务逻辑规范
- **v13.2.1**: 提供**会话管理和应用重启检测**的技术实现细节
- **集成目标**: 确保完整的应用生命周期管理和数据一致性

### 0.2 关键集成点

#### **步骤3集成: 会话连续性检查**
```
v13.0.1定义:
🔥 步骤3: 检查跨天检查和基线重置
- 应用重启检测优先于4小时会话连续性规则
- 第一优先级: 应用重启检测 → 强制创建新会话

v13.2.1实现:
🔥 应用重启检测系统 (第2章)
- AppRestartDetector提供精确重启检测
- 7种重启类型判断逻辑
- 与会话管理系统的自动集成
```

#### **API调用时机集成**
```
v13.0.1规范:
🔗 API调用顺序与时机规范
- check-continuity → init → start → sync 严格顺序
- 应用重启优先原则

v13.2.1实现:
🔥 API端点设计 (第4章)
- 增强版check-continuity支持is_app_restart参数
- session/init处理restart_info
- 完整的前后端交互流程
```

#### **时区处理集成**
```
v13.0.1规范:
🌏 统一时区规范: 所有时间处理统一使用新加坡时区（UTC+8）

v13.2.1实现:
🔥 会话生命周期管理 (第6章)
- 前端ISO 8601格式，包含时区信息
- 后端自动转换为新加坡时区
- 数据库UTC存储，查询时转换
```

### 0.3 集成架构图
```
┌─────────────────────┐    ┌──────────────────────┐
│   v13.0.1流程规范    │    │   v13.2.1技术实现     │
├─────────────────────┤    ├──────────────────────┤
│ 📋 5步骤标准流程     │◄──►│ 🔧 AppRestartDetector │
│ 🔥 会话连续性判断     │◄──►│ 🔄 SessionManager     │
│ 🔗 API调用顺序       │◄──►│ 📡 API端点层          │
│ 🌏 时区处理规范      │◄──►│ 🕒 时区管理器         │
│ 🎛️ 前端状态管理      │◄──►│ 📊 生命周期监控       │
└─────────────────────┘    └──────────────────────┘
             ▲                          ▲
             └──────────────────────────┘
                    统一的用户体验
```

### 0.4 关键优势
1. **零冗余**: 应用重启检测确保没有重复会话创建
2. **智能判断**: 精确区分应用重启vs后台恢复
3. **数据一致**: 统一时区处理确保前后端时间一致性
4. **用户体验**: 解决"两次loading页面"等UI问题
5. **系统稳定**: 完整的会话生命周期管理和错误恢复

---

## 1. 系统架构概览

### 1.1 核心组件
- **会话管理器 (SessionManager)**: 管理用户会话生命周期
- **基线管理器 (BaselineManager)**: 处理健康数据基线计算
- **应用重启检测器 (AppRestartDetector)**: 检测应用重启vs后台恢复
- **会话连续性服务 (SessionContinuityService)**: 处理会话连续性检查
- **API端点层**: 提供统一的前后端交互接口
- **权限管理器**: 处理HealthKit权限状态

### 1.2 设计原则
- **权限独立性**: 已授权权限有基线，未授权权限为null
- **会话驱动**: 所有基线计算基于会话开始时间
- **应用重启感知**: 精确区分应用重启和后台恢复
- **后端中心化**: 增量计算在后端完成
- **状态一致性**: 前后端状态严格同步
- **🔥 会话周期完整性**: login_time、session_start_time、logout_time三个时间字段必须完整记录

### 1.3 前端服务架构

#### 核心服务组件
```
AppRestartDetector          // 应用重启检测
    ↓
SessionContinuityService    // 会话连续性检查
    ↓
BaselineService            // 统一基线管理
    ↓
HealthService              // 健康数据同步
```

#### 服务职责分工
- **AppRestartDetector**: 检测应用启动类型（重启/恢复）
- **SessionContinuityService**: 4小时会话连续性检查
- **BaselineService**: 统一基线初始化和管理
- **EventTriggeredSyncService**: 事件驱动的数据同步

---

## 2. 应用重启检测系统

### 2.1 重启检测原理

#### 检测算法
```
应用重启判断逻辑:
1. 首次启动 → 重启
2. 应用版本更新 → 重启
3. 构建号更新 → 重启
4. 长时间间隔(>1分钟) → 重启
5. 异常退出 → 重启
6. 短时间快速重启(<30秒) → 重启
7. 正常后台恢复(<1分钟) → 非重启
```

#### 重启类型定义
- **firstLaunch**: 首次启动或数据清除
- **appUpdate**: 应用版本更新
- **buildUpdate**: 构建版本更新
- **longTimeInterval**: 长时间间隔后重启
- **abnormalExit**: 异常退出后重启
- **quickRestart**: 快速重启（系统内存不足）
- **backgroundResume**: 正常后台恢复（非重启）

### 2.2 前端重启检测实现

#### AppRestartDetector服务
```dart
// 核心检测方法
Future<AppStartInfo> detectAppStart({
  String? appVersion,
  String? buildNumber,
}) async {
  // 分析启动类型，返回重启信息
}

// 应用生命周期管理
Future<void> markAppBackground() async;
Future<void> markAppExit({String reason}) async;
```

#### 检测数据存储
- `last_exit_time`: 上次退出时间
- `app_session_id`: 应用会话ID
- `app_state`: 应用状态（active/background_normal/exit_*）
- `app_version`: 应用版本号
- `app_build_number`: 构建号

### 2.3 重启检测与会话管理集成

#### 集成流程
```
1. AppRestartDetector.detectAppStart()
2. 如果isRestart=true → 传递重启信息
3. SessionContinuityService.checkSessionContinuity(isAppRestart=true)
4. 后端强制创建新会话
5. BaselineService.initializeBaseline(isAppRestart=true)
```

---

## 3. 会话管理系统

### 3.1 会话生命周期

#### 会话创建条件
1. **用户登录**: 认证成功后创建新会话
2. **应用重启**: 已登录状态重启应用（新增）
3. **跨天处理**: 新的一天开始时
4. **4小时超时**: 超过4小时无活动
5. **手动强制**: 开发/测试需要

#### 会话结束条件
1. **用户登出**: 主动退出登录
2. **应用重启**: 检测到应用重启事件（新增）
3. **应用关闭**: 应用被系统或用户关闭
4. **Token过期**: 认证token失效
5. **系统异常**: 严重错误导致会话中断

### 3.2 会话数据模型 (UnifiedUserSession)

```
核心字段:
- user: 用户关联
- device_id: 设备唯一标识
- login_time: 登录时间
- session_start_time: 会话开始时间 (用于基线计算)
- baseline_date: 基线日期 (新加坡时间0:00)
- logout_time: 登出时间 (修正字段名)
- logout_reason: 登出原因 (app_restart/session_timeout/user_logout等)
- is_active: 会话活跃状态
- session_baseline_steps: 步数基线 (可为null)
- session_baseline_distance: 距离基线 (可为null)
- session_baseline_calories: 卡路里基线 (可为null)
```

### 3.3 会话状态管理

#### 活跃会话规则
- 每个用户在每个设备上只能有一个活跃会话
- 新会话创建时，自动结束旧会话
- 应用重启时，强制结束旧会话并创建新会话
- 会话超时时间: 24小时无活动自动过期

#### 会话连续性检查
- 检查上次会话结束时间
- 检查应用重启标识
- 判断是否需要创建新会话
- 处理会话间隙的数据恢复

---

## 4. API端点设计

### 4.1 会话管理端点

#### GET `/api/app/v1/health/session/check-continuity/`
**功能**: 检查会话连续性（增强版）  
**调用时机**: 应用恢复后，数据同步前  
**请求参数**:
```json
{
  "is_app_restart": "true|false",      // 🔥 新增：是否为应用重启
  "restart_reason": "string",          // 🔥 新增：重启原因
  "timestamp": "ISO8601时间戳"         // 🔥 新增：检测时间戳
}
```
**核心逻辑**:
- 🔥 应用重启检测：如果is_app_restart=true，直接创建新会话
- 检查会话间隙时间
- 判断是否需要数据恢复
- 返回会话状态建议

**响应格式**:
```json
{
  "code": 200,
  "message": "会话连续性检查完成",
  "data": {
    "need_new_session": true,
    "session_age_hours": 999.0,
    "last_activity_hours": 999.0,
    "reason": "应用重启检测: app_update",
    "current_session_id": null,
    "last_sync_time": null,
    "is_app_restart": true,              // 🔥 新增：重启标识
    "restart_reason": "app_update",      // 🔥 新增：重启原因
    "previous_sessions_closed": 1        // 🔥 新增：关闭的会话数
  }
}
```

#### POST `/api/app/v1/health/session/init/`
**功能**: 初始化健康数据会话基线  
**调用时机**: 用户登录后、应用启动时  
**请求参数增强**:
```json
{
  "totals": {...},
  "device_id": "string",
  "permissions": {...},
  "is_app_restart": false,             // 🔥 新增：应用重启标识
  "restart_info": {                    // 🔥 新增：重启详情
    "restart_type": "appUpdate",
    "restart_reason": "版本更新",
    "time_since_last_exit": 3600
  }
}
```
**核心逻辑**: 
- 验证设备和权限信息
- 处理应用重启场景
- 调用 BaselineManager.initialize_user_baseline
- 返回会话ID和基线信息

#### POST `/api/app/v1/health/session/start/`
**功能**: 标记会话开始时间  
**调用时机**: 前端准备开始健康数据监听时  
**请求参数增强**:
```json
{
  "reason": "string",
  "metadata": {
    "device_id": "string",
    "permissions": {...},
    "is_app_restart": false,           // 🔥 新增：重启标识
    "restart_reason": "string"         // 🔥 新增：重启原因
  }
}
```
**核心逻辑**:
- 记录精确的会话开始时间
- 处理应用重启基线初始化
- 触发基线初始化（如有权限信息）
- 用于后续基线计算的 endDate

#### POST `/api/app/v1/health/force-new-session/`
**功能**: 强制创建新会话  
**调用时机**: 应用重启检测、异常恢复  
**请求参数**:
```json
{
  "device_id": "string",
  "reason": "app_restart",
  "platform": "ios",
  "health_source": "real"
}
```
**核心逻辑**:
- 强制结束所有活跃会话
- 创建新会话
- 返回会话状态

#### POST `/api/app/v1/health/offline/process-queue/`
**功能**: 处理离线数据队列  
**调用时机**: 网络恢复后，离线数据处理  
**核心逻辑**:
- 处理离线期间的操作
- 批量数据同步
- 任务状态更新

### 4.2 数据同步端点

#### POST `/api/app/v1/health/sync/`
**功能**: 同步健康数据增量  
**调用时机**: 定期数据同步  
**核心逻辑**:
- 计算增量 = 新总量 - 基线
- 更新任务进度
- 发放奖励

#### GET `/api/app/v1/health/status/`
**功能**: 获取当天累计运动增量  
**调用时机**: 前端 Overview 显示  
**核心逻辑**:
- 返回今日累计增量
- 不接受客户端数据
- 纯后端计算结果

### 4.3 基线管理端点

#### POST `/api/app/v1/health/baseline-reset/`
**功能**: 跨天基线重置  
**调用时机**: 检测到跨天事件  
**核心逻辑**:
- 归档前一天数据
- 重置基线为新累计值
- 处理跨天任务结算

---

## 5. 前后端交互流程

### 5.1 登录初始化流程（增强版）

```
1. 用户登录 → 认证成功
2. 🔥 AppRestartDetector.detectAppStart() → 检测启动类型
3. 前端调用 session/check-continuity
   - 传递: is_app_restart, restart_reason, timestamp
   - 后端: 检测重启 + 会话连续性检查
4. 如果需要新会话：
   前端调用 session/init
   - 传递: totals, device_id, permissions, restart_info
   - 后端: 创建会话 + 设置基线
5. 前端调用 session/start  
   - 传递: reason, metadata (含重启信息)
   - 后端: 记录会话开始时间
6. 开始健康数据监听
```

### 5.2 应用重启处理流程（新增）

```
1. 应用启动 → AppRestartDetector检测
2. 如果检测到重启：
   - 收集重启信息（类型、原因、时间间隔）
   - 调用 session/check-continuity (is_app_restart=true)
   - 后端强制结束旧会话，标记logout_reason='app_restart'
3. 创建新会话：
   - 调用 session/init (含重启信息)
   - 重新确定基线
   - 开始新的会话周期
4. 恢复数据同步
```

### 5.3 数据同步流程

```
1. HealthKit数据变更
2. 前端累积数据 → 定期调用 sync
   - 传递: new_totals, device_id
   - 后端: 计算增量 + 更新任务
3. 前端更新UI显示
```

### 5.4 跨天处理流程

```
1. 检测到跨天事件
2. 前端调用 baseline-reset
   - 传递: new_totals (新一天数据)
   - 后端: 归档 + 重置基线
3. 重新开始数据监听
```

### 5.5 错误处理流程

```
网络错误 → 数据缓存到本地队列
网络恢复 → 调用 offline/process-queue
会话过期 → 重新登录 → session/init
权限变更 → 调用 sync (含新权限状态)
应用重启 → 重启检测 → 强制新会话
```

---

## 6. 🔥 会话生命周期管理

### 6.1 概述

会话生命周期管理是SweatMint系统的核心组件，负责：
- **精确跟踪用户会话的开始和结束时间**
- **确保logout_time在app真正关闭时立即设置**
- **处理网络不可用时的本地缓存机制**
- **统一时区管理（新加坡时区UTC+8）**
- **防止"几天跨度会话"的问题**

### 6.2 核心要求

**🔥 会话周期完整性问题**：
- **问题**: logout_time为空导致会话时间计算异常
- **要求**: 应用关闭时立即设置logout_time，不能等到下次登录
- **机制**: GlobalAppLifecycleManager监控应用状态，自动调用session/logout API
- **异常处理**: 网络不可用时本地缓存，下次启动时同步

### 6.3 会话状态定义

```python
# 会话状态枚举
class SessionState:
    ACTIVE = True      # 活跃会话
    INACTIVE = False   # 已结束会话

# 登出原因枚举
class LogoutReason:
    USER_LOGOUT = 'user_logout'           # 用户主动登出
    APP_BACKGROUND = 'app_background'     # app进入后台
    APP_CLOSE = 'app_close'              # app完全关闭
    APP_RESTART = 'app_restart'          # app重启
    SESSION_TIMEOUT = 'session_timeout'   # 会话超时（4小时）
    SESSION_EXPIRED = 'session_expired'   # 会话过期（24小时）
    DEVICE_CONFLICT = 'device_conflict'   # 设备冲突
    FORCE_NEW = 'force_new_session'      # 强制创建新会话
```

### 6.4 架构设计

#### 6.4.1 前端组件
- **GlobalAppLifecycleManager**: 监控应用生命周期状态变化
- **SessionContinuityService**: 处理会话连续性检查
- **本地缓存机制**: SharedPreferences存储logout_time（网络不可用时）
- **网络状态检测**: 监控网络连接状态

#### 6.4.2 后端API端点
- `/api/app/v1/health/session/start/` - 会话开始标记
- `/api/app/v1/health/session/end/` - 🔥 会话结束标记（核心API）
- `/api/app/v1/health/session/check-continuity/` - 会话连续性检查
- `/api/app/v1/health/session/force-new/` - 强制创建新会话

#### 6.4.3 关键流程
1. **正常关闭**: AppLifecycleState.detached → 立即调用session/end
2. **网络异常**: 本地缓存logout_time → 下次启动时同步
3. **时区统一**: 所有时间使用新加坡时区（UTC+8）处理

### 6.5 生命周期流程

#### 6.5.1 正常会话周期
1. **App启动**: 初始化GlobalAppLifecycleManager → 用户登录 → 创建会话（is_active=True）
2. **App使用**: 定期健康数据同步 → 更新last_sync_time
3. **App关闭**: 检测AppLifecycleState.detached → 立即调用session/end API → 设置logout_time

#### 6.5.2 网络异常处理
1. **关闭时无网络**: 本地缓存logout_time到SharedPreferences
2. **下次启动有网络**: 读取缓存时间 → 调用session/end API → 清除本地缓存
3. **时间准确性**: 使用缓存的实际关闭时间，而非API调用时间

### 6.6 时区管理规范

**统一时区原则（新加坡时区 UTC+8）**：
- **前端**: 发送ISO 8601格式时间戳，包含时区信息
- **后端**: 自动转换为UTC存储，业务计算使用新加坡时区
- **数据库**: UTC存储，查询时转换为新加坡时区进行日期比较

### 6.7 错误处理与恢复

#### 6.7.1 常见错误场景

| 错误场景 | 处理策略 | 恢复机制 |
|---------|---------|---------|
| 网络不可用 | 本地缓存logout_time | 下次启动时同步 |
| API调用失败 | 重试3次，失败后缓存 | 后台任务同步 |
| 时间解析错误 | 使用当前时间作为fallback | 记录错误日志 |
| 会话不存在 | 返回404，前端处理 | 忽略错误，不影响用户 |

#### 6.7.2 故障排除指南

**问题1：会话持续时间异常短（几毫秒）**
```bash
# 检查logout_time设置时机
SELECT id, login_time, session_start_time, logout_time, logout_reason,
       EXTRACT(EPOCH FROM (logout_time - session_start_time)) as duration_seconds
FROM users_unifiedusersession 
WHERE user_id = ? AND logout_time IS NOT NULL
ORDER BY id DESC LIMIT 5;
```

**解决方案**：确保GlobalAppLifecycleManager正确监控app生命周期

**问题2：缓存的logout_time未同步**
```bash
# 检查SharedPreferences
flutter logs | grep "pending_logout_time"
```

**解决方案**：检查网络连接和API权限

**问题3：时区不一致**
```sql
# 检查时区设置
SELECT NOW() as server_time, 
       NOW() AT TIME ZONE 'Asia/Singapore' as singapore_time;
```

### 6.8 性能监控

#### 6.8.1 关键指标

- **会话创建成功率**: > 99%
- **logout_time设置延迟**: < 1秒
- **缓存同步成功率**: > 95%
- **API响应时间**: < 500ms

#### 6.8.2 监控查询

```sql
-- 会话持续时间分布
SELECT 
    CASE 
        WHEN EXTRACT(EPOCH FROM (logout_time - session_start_time)) < 60 THEN '< 1分钟'
        WHEN EXTRACT(EPOCH FROM (logout_time - session_start_time)) < 3600 THEN '1分钟-1小时'
        WHEN EXTRACT(EPOCH FROM (logout_time - session_start_time)) < 86400 THEN '1小时-1天'
        ELSE '> 1天'
    END as duration_range,
    COUNT(*) as session_count
FROM users_unifiedusersession 
WHERE logout_time IS NOT NULL AND session_start_time IS NOT NULL
GROUP BY duration_range;

-- 异常短会话检测
SELECT id, user_id, login_time, logout_time, logout_reason,
       EXTRACT(EPOCH FROM (logout_time - session_start_time)) as duration_seconds
FROM users_unifiedusersession 
WHERE logout_time IS NOT NULL 
  AND session_start_time IS NOT NULL
  AND EXTRACT(EPOCH FROM (logout_time - session_start_time)) < 1
ORDER BY id DESC;
```

### 6.9 测试验证

#### 6.9.1 单元测试

```dart
// 前端测试
testWidgets('GlobalAppLifecycleManager should call session_end on app close', (tester) async {
  final manager = GlobalAppLifecycleManager.instance;
  await manager.initialize();
  
  // 模拟app关闭
  manager.didChangeAppLifecycleState(AppLifecycleState.detached);
  
  // 验证session_end被调用
  verify(mockSessionService.markSessionEnd(reason: 'app_close')).called(1);
});
```

```python
# 后端测试
def test_session_end_with_cached_logout_time(self):
    """测试缓存的logout_time处理"""
    cached_time = timezone.now() - timedelta(hours=1)
    
    response = self.client.post('/api/app/v1/health/session/end/', {
        'reason': 'app_close',
        'metadata': {
            'cached_logout_time': cached_time.isoformat()
        }
    })
    
    self.assertEqual(response.status_code, 200)
    session = UnifiedUserSession.objects.get(id=self.session.id)
    self.assertAlmostEqual(session.logout_time, cached_time, delta=timedelta(seconds=1))
```

#### 6.9.2 集成测试

1. **完整生命周期测试**：模拟用户从登录到关闭app的完整流程
2. **网络断开测试**：验证离线缓存和同步机制
3. **时区一致性测试**：确保前后端时间处理一致
4. **并发测试**：多设备同时登录的会话管理

### 6.10 部署检查清单

- [ ] GlobalAppLifecycleManager在main.dart中正确初始化
- [ ] 后端session_start/session_end API部署
- [ ] 数据库索引优化（user_id, is_active, logout_time）
- [ ] 监控告警配置（异常短会话、API错误率）
- [ ] 日志级别配置（生产环境ERROR，开发环境DEBUG）

---

## 7. 异常处理与恢复

### 7.1 会话冲突处理

当检测到以下情况时，系统会自动处理会话冲突：

#### 7.1.1 同用户多设备登录
```python
# 强制登出其他设备上的会话
conflicting_user_sessions = UnifiedUserSession.objects.filter(
    user=user, 
    is_active=True
).exclude(device_id=current_device_id)

for session in conflicting_user_sessions:
    session.force_logout(reason='multi_device_login')
```

#### 7.1.2 同设备多用户登录
```python
# 强制登出当前设备上的其他用户会话
conflicting_device_sessions = UnifiedUserSession.objects.filter(
    device_id=device_id, 
    is_active=True
).exclude(user=current_user)

for session in conflicting_device_sessions:
    session.force_logout(reason='device_conflict')
```

### 7.2 应用重启检测与处理

#### 7.2.1 重启检测逻辑
```python
if is_app_restart:
    # 强制结束所有活跃会话
    current_sessions = UnifiedUserSession.objects.filter(
        user=user,
        is_active=True
    )
    
    current_sessions.update(
        is_active=False,
        logout_time=timezone.now(),
        logout_reason='app_restart'
    )
```

#### 7.2.2 新会话创建
```python
# 创建新会话
new_session = UnifiedUserSession.objects.create(
    user=user,
    device_id=device_id,
    session_start_time=timezone.now(),
    baseline_date=singapore_today_start,
    is_active=True
)
```

---

## 8. 性能与监控

### 8.1 性能指标

#### 8.1.1 API响应时间要求
- **会话连续性检查**: < 1000ms
- **会话开始标记**: < 500ms
- **会话结束标记**: < 500ms
- **基线初始化**: < 2000ms

#### 8.1.2 数据库性能
- **活跃会话查询**: < 100ms
- **会话创建**: < 200ms
- **会话更新**: < 100ms

### 8.2 监控与告警

#### 8.2.1 业务监控
- **活跃会话数量**: 实时监控
- **会话创建频率**: 每分钟创建数量
- **异常会话比例**: 持续时间异常的会话
- **设备冲突频率**: 设备冲突检测频率

#### 8.2.2 技术监控
- **API错误率**: < 1%
- **数据库连接数**: 监控连接池使用情况
- **内存使用率**: 监控会话对象内存占用

---

## 9. API接口规范

### 9.1 会话管理API

#### 9.1.1 检查会话连续性
```
GET /api/app/v1/health/session/check-continuity/
Parameters:
  - is_app_restart: boolean (是否为应用重启)
  - restart_reason: string (重启原因)
  - timestamp: string (检查时间戳)

Response:
{
  "code": 200,
  "data": {
    "need_new_session": boolean,
    "session_age_hours": float,
    "last_activity_hours": float,
    "reason": string,
    "current_session_id": int,
    "is_app_restart": boolean
  }
}
```

#### 9.1.2 会话开始标记
```
POST /api/app/v1/health/session/start/
Body:
{
  "reason": "session_start",
  "timestamp": "2025-07-06T12:55:51.732017+08:00",
  "metadata": {}
}

Response:
{
  "code": 200,
  "message": "会话开始标记成功",
  "data": {
    "session_id": 99,
    "session_start_time": "2025-07-06T12:55:51.732017+08:00"
  }
}
```

#### 9.1.3 会话结束标记
```
POST /api/app/v1/health/session/end/
Body:
{
  "reason": "app_background",
  "timestamp": "2025-07-06T15:30:25.123456+08:00",
  "health_data": {
    "steps": 1500,
    "distance": 1.2,
    "calories": 85
  },
  "metadata": {
    "cached_logout_time": "2025-07-06T15:30:25.123456+08:00",  // 可选，用于缓存同步
    "sync_type": "cached_logout_sync"  // 可选，标识同步类型
  }
}

Response:
{
  "code": 200,
  "message": "会话结束标记成功",
  "data": {
    "session_id": 99,
    "session_end_time": "2025-07-06T15:30:25.123456+08:00",
    "reason": "app_background",
    "session_duration_hours": 2.58,
    "snapshot_created": true,
    "is_cached_logout": false
  }
}
```

### 9.2 错误码定义

```python
# 会话管理错误码
SESSION_ERRORS = {
    404: "没有活跃会话可结束",
    400: "缺少必填参数",
    500: "会话操作失败"
}
```

---

## 10. 实际业务场景：v13.0.1 + v13.2.1协同工作

### 10.1 完整的应用重启场景

#### **场景描述**: 用户iOS应用更新后重启
```
用户行为:
1. 用户收到SweatMint应用更新通知
2. 用户更新应用到v2.1.0版本
3. 用户打开更新后的应用
4. 系统检测到应用重启和版本更新
```

#### **v13.0.1 + v13.2.1协同处理流程**
```
🔧 v13.2.1技术层处理:
Step A: AppRestartDetector检测
       - 检测到app_version从v2.0.9 → v2.1.0
       - 重启类型: appUpdate
       - 重启原因: "应用版本更新"
       - is_app_restart = true

📋 v13.0.1业务层处理:
Step 1: 检查认证状态
       - AuthProvider检查本地Token
       - 认证状态: authenticated (用户已登录)

Step 2: 检查健康数据权限状态
       - HKStatisticsQuery检查三种权限
       - 权限状态: 步数✅ 距离✅ 卡路里❌

Step 3: 🔥 会话连续性检查 (重启优先)
       - 🔥 第一优先级: 应用重启检测
       - is_app_restart=true → 强制创建新会话
       - 忽略时间间隔(可能只有2分钟)
       
🔧 v13.2.1技术层实现:
Step 3a: POST /session/check-continuity/
        {
          "is_app_restart": true,
          "restart_reason": "appUpdate",
          "timestamp": "2025-07-06T15:30:00+08:00"
        }
        返回: {"need_new_session": true, "reason": "应用重启检测: appUpdate"}

Step 3b: POST /session/init/
        {
          "totals": {"steps": 5000, "distance": 3.2, "calories": null},
          "permissions": {"steps": true, "distance": true, "calories": false},
          "restart_info": {
            "restart_type": "appUpdate",
            "restart_reason": "应用版本更新",
            "time_since_last_exit": 120
          }
        }
        - 后端强制结束旧会话 (logout_reason='app_restart')
        - 创建新会话，设置基线:
          - 步数基线: 5000步
          - 距离基线: 3.2km
          - 卡路里基线: null (未授权)

Step 3c: POST /session/start/
        {
          "reason": "app_restart_complete",
          "metadata": {"restart_type": "appUpdate"}
        }
        - 精确标记会话开始时间: 15:30:00

📋 v13.0.1业务层继续:
Step 4: 健康数据进行同步
       - 同步已授权权限数据
       - 步数增量 = 5000 - 5000 = 0步 (重启时立即同步)
       - 距离增量 = 3.2 - 3.2 = 0.0km
       - 卡路里: 跳过 (未授权)

Step 5: 首页数据UI加载
       - 显示: 步数:0, 距离:0.0, 卡路里:"--"
       - 显示健康权限弹窗 (卡路里未授权)
```

#### **关键优势体现**
- **零重复**: 应用重启优先级确保不会因为时间间隔短而延续会话
- **数据一致**: 强制重新确定基线，避免版本更新导致的数据异常
- **用户体验**: 一次完整的Loading过程，没有"二次加载"

### 10.2 4小时后恢复场景对比

#### **场景描述**: 用户早上8点登录，下午13:30恢复应用
```
时间线:
- 08:00: 用户登录，开始使用app
- 08:30: 用户关闭app去上班
- 13:30: 用户午休时重新打开app (间隔5小时)
```

#### **v13.0.1 + v13.2.1协同处理**
```
🔧 v13.2.1技术层检测:
- AppRestartDetector分析: 
  - 时间间隔: 5小时 > 1分钟
  - 但应用版本未变化
  - 判断: backgroundResume (不是重启)
  - is_app_restart = false

📋 v13.0.1业务层判断:
Step 3: 🔥 会话连续性检查
       - 🔥 第一优先级: 应用重启检测 → false (跳过)
       - **第三优先级**: 时间间隔 > 4小时 → 创建新会话
       
🔧 v13.2.1技术层实现:
- POST /session/check-continuity/
  {"is_app_restart": false, "timestamp": "..."}
  返回: {"need_new_session": true, "reason": "会话超时: 5.0小时"}
  
- 创建新会话，重新确定基线
- 新基线 = 当天0:00到13:30的健康数据总量
```

#### **对比分析**
| 场景 | 时间间隔 | 重启检测 | 会话决策 | 决策依据 |
|------|---------|---------|---------|---------|
| 应用更新重启 | 2分钟 | ✅ true | 新会话 | **第一优先级**: 重启检测 |
| 4小时后恢复 | 5小时 | ❌ false | 新会话 | **第三优先级**: 时间超限 |
| 2小时后恢复 | 2小时 | ❌ false | 延续会话 | **延续条件**: 非重启 + 时间内 |

### 10.3 前端状态管理完整示例

#### **SplashScreen智能等待**
```dart
// v13.0.1规范的状态管理实现
class _SplashScreenState extends State<SplashScreen> {
  @override
  Widget build(BuildContext context) {
    return Selector2<AuthProvider, AuthProvider, 
        ({AuthStatus auth, bool bizComplete})>(
      selector: (_, authProvider) => (
        auth: authProvider.authStatus,
        bizComplete: authProvider.isBusinessLogicCompleted
      ),
      builder: (context, state, child) {
        // 🔥 关键：等待完整业务逻辑完成
        if (state.auth == AuthStatus.authenticated && 
            state.bizComplete) {
          // v13.0.1步骤1-5全部完成
          // v13.2.1会话管理全部就绪
          _navigateToHome();
        }
        
        return LoadingScreen(
          message: _getLoadingMessage(state),
        );
      },
    );
  }
}
```

#### **AuthProvider协同执行**
```dart
// v13.0.1 + v13.2.1协同的AuthProvider实现
class AuthProvider with ChangeNotifier, ViewModelMixin {
  Future<void> initializeBusinessLogic() async {
    await executeAsyncAction(() async {
      // v13.0.1步骤1: 认证检查
      await _performStage1AuthCheck();
      
      if (_authStatus == AuthStatus.authenticated) {
        // v13.2.1: 应用重启检测
        final restartInfo = await _appRestartDetector.detectAppStart();
        
        // v13.0.1步骤2: 权限检查
        final permissions = await _checkHealthPermissions();
        
        // v13.0.1步骤3: 会话连续性 (集成重启检测)
        await _checkSessionContinuity(
          isAppRestart: restartInfo.isRestart,
          restartReason: restartInfo.reason,
        );
        
        // v13.0.1步骤4: 数据同步
        await _syncHealthData();
        
        // 🔥 标记业务逻辑完成
        _isBusinessLogicCompleted = true;
      }
    });
  }
}
```

### 10.4 关键成功指标

#### **技术指标**
- **会话创建准确率**: 100% (重启检测 + 时间判断)
- **基线设置正确率**: 100% (权限独立 + 时间精确)
- **UI等待时间**: 2-5秒 (完整业务逻辑)
- **重复操作避免率**: 100% (状态管理优化)

#### **用户体验指标**
- **Loading页面出现次数**: 1次 (杜绝二次加载)
- **数据显示准确性**: 100% (基于完整的业务逻辑)
- **权限弹窗及时性**: 立即显示 (步骤5UI加载)
- **应用启动流畅度**: 优秀 (智能状态管理)

通过v13.0.1和v13.2.1的完美协同，SweatMint实现了业界领先的应用生命周期管理和健康数据处理体验。

---

## 总结

SweatMint会话管理与基线系统技术规范v13.2.1与v13.0.1登录流程规范协同工作，提供了完整的应用生命周期管理解决方案：

### **核心技术成就**
1. **🔥 会话周期完整性**: 确保每个会话有完整的login_time、session_start_time、logout_time
2. **生命周期监控**: GlobalAppLifecycleManager在应用关闭时立即设置logout_time
3. **网络异常处理**: 本地缓存机制确保logout_time准确记录
4. **统一时区管理**: 新加坡时区(UTC+8)统一处理所有时间计算
5. **应用重启检测**: 精确区分应用重启和后台恢复场景

### **与v13.0.1协同优势**
1. **🔥 应用重启优先级**: 完美实现v13.0.1定义的重启检测优先于4小时规则
2. **API调用顺序**: 严格按照v13.0.1规范的check-continuity → init → start → sync流程
3. **前端状态管理**: 解决v13.0.1中"两次loading页面"等UI问题
4. **业务逻辑完整性**: 确保5步骤标准流程的完整执行
5. **数据一致性**: 统一的时区处理和权限独立管理

### **关键问题解决**
- **🔥 会话时间完整性**: logout_time在应用关闭时立即设置，杜绝"几天跨度会话"
- **应用生命周期管理**: 精确监控应用状态，确保会话周期完整记录
- **网络异常容错**: 本地缓存机制保证logout_time准确性
- **前端状态优化**: 避免"二次加载"，提升用户体验

通过v13.0.1业务规范 + v13.2.1技术实现的完美协同，SweatMint达成了业界领先的健康数据应用生命周期管理标准，为用户提供流畅、准确、可靠的运动数据体验。 