# SweatMint 健康数据系统修复与统一规范 v14.0

**文档版本**: v14.0  
**创建日期**: 2025-07-08  
**制定者**: 项目技术总监 & AI Agent  
**适用范围**: 后端健康数据系统、数据库模型、API接口  
**依赖规范**: v13.0.1登录流程规范, v13.2.1会话管理规范

---

## 1. 问题根本原因分析 (The "11步变143步"问题)

经过对7月7日 `<EMAIL>` 数据的彻查，问题的根源已完全定位。

### 1.1. 核心错误: `BaselineManager` 基线计算逻辑缺陷

**问题代码**: `running/api/health/baseline_manager.py` 的 `initialize_user_baseline` 方法。

```python
# 错误的硬编码逻辑
if permissions_status.get('steps', False):
    # 问题点：无论何时创建新会话，基线都被强制设为0
    # 这导致每次应用重启后，增量计算都从一个错误的起点开始
    session_data['session_baseline_steps'] = 0 
```

### 1.2. 错误场景重现

1.  **首次会话**: 用户实际产生`11`步。系统正确或错误地将第一个会话的基线设置为`11`。会话增量 = `11 - 11 = 0`。
2.  **应用重启 (第1次)**: 应用重启，`BaselineManager` 创建新会话。**错误发生**：新会话的基线被硬编码为`0`。
3.  **增量计算**: `sync`接口用总量减去错误基线。会话增量 = `11 - 0 = 11`步。
4.  **多次重启 (11次)**: 用户当天重启了11次应用，创建了11个基线为`0`的会话。
5.  **错误累加**: 系统将这11个会话的增量（`11 * 11 = 121`步）与其它可能的错误数据相加，最终在`DailyHealthSnapshot`中记录了`143`步的错误数据。

### 1.3. 根本影响

-   **数据完全失真**: 用户实际只走了11步，系统却记录了143步。
-   **系统逻辑混乱**: 违反了"基线是会话增量起点"的核心原则。
-   **用户信任丧失**: 错误的数据会严重影响用户对产品的信任。

---

## 2. 全新数据库设计：强健、无冗余

为了彻底解决问题并满足您的要求，我将对数据库进行优化，引入新模型来精确记录每一次会话的增量。

### 2.1. 新增 `UserSessionLog` 模型

**目的**: 精确记录**每一次独立会话**产生的健康数据增量。这是实现准确统计和审计的基础。

**模型定义** (`running/tasks/models.py`):

```python
class UserSessionLog(models.Model):
    """
    用户健康数据会话日志 - v14.0新增
    - 记录每一次独立会话产生的增量，确保数据准确性和可追溯性
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='session_logs', verbose_name="用户")
    session = models.ForeignKey(UnifiedUserSession, on_delete=models.CASCADE, related_name='logs', verbose_name="关联会话")
    
    session_start_time = models.DateTimeField(verbose_name="会话开始时间")
    session_end_time = models.DateTimeField(verbose_name="会话结束时间")
    duration_seconds = models.PositiveIntegerField(verbose_name="会话持续秒数")

    steps_increment = models.PositiveIntegerField(default=0, verbose_name="步数增量")
    distance_increment = models.DecimalField(max_digits=18, decimal_places=6, default=Decimal('0.0'), verbose_name="距离增量 (米)")
    calories_increment = models.PositiveIntegerField(default=0, verbose_name="卡路里增量")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "用户会话增量日志"
        verbose_name_plural = verbose_name
        ordering = ['-session_start_time']

    def __str__(self):
        return f"{self.user.email} - {self.session_start_time.strftime('%Y-%m-%d %H:%M')} - 步数: {self.steps_increment}"

```

### 2.2. 优化 `DailyHealthSnapshot` 模型

**目的**: 明确其作为**每日总增量快照**的角色，其数据应由当天的所有 `UserSessionLog` 汇总计算得出。

**角色定位**:
-   `snapshot_steps`, `snapshot_distance`, `snapshot_calories` 字段的值 **必须** 是当天所有 `UserSessionLog` 对应增量字段的总和。
-   它是一个聚合结果，用于快速查询每日汇总，而不是直接写入。

### 2.3. 优化 `UnifiedUserSession` 模型

**目的**: 回归其核心职责——**会话生命周期管理**，并移除冗余字段。

**核心字段**:
-   `id`, `user`, `device_id`, `is_active`
-   `session_start_time` (会话首次创建时间)
-   `login_time` (应用启动/认证时间)
-   `logout_time` (应用关闭/会话结束时间)
-   `baseline_date` (基线所属日期)
-   `session_baseline_steps`, `session_baseline_distance`, `session_baseline_calories` (会话基线值)

**待移除冗余字段**: `device_fingerprint`, `ip_address`, `fcm_token`, `platform`, `app_version`, `os_version`, etc. (这些信息更适合记录在登录历史或设备管理表中)。

---

## 3. 核心逻辑修复与重构

### 3.1. 修复 `BaselineManager.initialize_user_baseline`

**新算法**:

1.  **获取当天日期**: 确定当前新加坡时区的日期。
2.  **查询当天累计增量**: 从 `DailyHealthSnapshot` 查询指定日期已记录的**总增量**。如果快照不存在，则当天累计增量为0。
3.  **设置新基线**: 将查询到的**当天累计增量**作为新会话的基线值。
4.  **权限处理**: 只为已授权的权限设置基线，未授权的保持 `null`。

**伪代码**:

```python
def initialize_user_baseline(user, device_id, permissions):
    # ...
    # 强制结束旧的活跃会话
    
    # 1. 获取当天日期（新加坡时区）
    today_sg = get_singapore_today_date()
    
    # 2. 从快照查询当天已累计的总增量
    today_snapshot = DailyHealthSnapshot.objects.filter(user=user, snapshot_date=today_sg).first()
    
    current_day_total_steps = today_snapshot.snapshot_steps if today_snapshot else 0
    # ... (distance, calories同理)

    # 3. 创建新会话，并使用当天累计增量作为新基线
    new_session = UnifiedUserSession.objects.create(
        # ...,
        session_baseline_steps = current_day_total_steps if permissions.get('steps') else None,
        # ...
    )
    return new_session
```

### 3.2. 新增 `HealthDataService` 核心方法

为了实现新逻辑，`HealthDataService` 需要重构。

**`process_health_data_sync(user, device_id, new_totals)`**:

1.  获取当前活跃会话 `active_session`。
2.  计算**会话内增量**: `session_increment = new_totals - active_session.baseline`。
3.  获取上一次的会话日志 `last_log`。
4.  计算本次同步与上次同步之间的**真实增量**: `real_increment = session_increment - last_log.total_increment`。
5.  创建新的 `UserSessionLog` 记录 `real_increment`。
6.  调用 `update_daily_snapshot` 更新每日快照。

**`update_daily_snapshot(user, date)`**:

1.  获取指定日期的所有 `UserSessionLog` 记录。
2.  **求和**: `total_increment = SUM(log.increment for log in logs)`。
3.  更新或创建当天的 `DailyHealthSnapshot`，写入 `total_increment`。

### 3.3. 统一 `health/sync` API

-   前端所有的数据同步请求都应发送到 `POST /api/app/v1/health/sync/`。
-   后端在该API内部完成上述所有逻辑：计算会话增量 -> 记录会话日志 -> 更新每日快照。

---

## 4. 统一健康数据API设计

### 4.1. `POST /api/app/v1/health/sync/`
-   **功能**: 前端同步健康数据的**唯一入口**。
-   **请求体**: `{ "steps": 1011, "distance": 800.0, "calories": 50 }` (当天从0点开始的累计总量)。
-   **后端逻辑**:
    1.  调用 `HealthDataService.process_health_data_sync`。
    2.  返回处理结果，包括本次产生的增量。
-   **响应**: `{ "code": 200, "data": { "steps_increment": 11, ... } }`

### 4.2. `GET /api/app/v1/health/status/`
-   **功能**: 获取用户当天的健康数据总览（用于首页显示）。
-   **后端逻辑**: 直接从 `DailyHealthSnapshot` 读取当天的总增量。
-   **响应**: `{ "code": 200, "data": { "daily_increment": {"steps": 11, ...}, "permissions": ... } }`

### 4.3. `GET /api/app/v1/health/sessions/{date}/`
-   **功能**: (新) 获取指定日期的所有会话增量记录。
-   **后端逻辑**: 从 `UserSessionLog` 查询并返回列表。
-   **响应**: `{ "code": 200, "data": [ { "start_time": ..., "end_time": ..., "steps_increment": 11, ... }, ... ] }`

---

## 5. 实施计划

1.  **数据库迁移**:
    -   在 `tasks/models.py` 中添加 `UserSessionLog` 模型。
    -   运行 `makemigrations` 和 `migrate` 创建新表。
    -   编写数据迁移脚本，将 `HealthDataVerificationLog` 中的历史数据转换为新的 `UserSessionLog` 格式。

2.  **后端逻辑重构**:
    -   **[高优先级]** 修复 `BaselineManager.initialize_user_baseline` 的基线计算逻辑。
    -   重构 `HealthDataService`，实现 `process_health_data_sync` 和 `update_daily_snapshot` 方法。
    -   重构 `HealthViewSet`，使 `sync` API 调用新的服务逻辑。

3.  **API调整**:
    -   实现新的 `/health/sessions/{date}/` API。
    -   确保 `/health/status/` 和 `/health/sync/` 的行为符合新规范。

4.  **测试验证**:
    -   编写单元测试，覆盖新的服务逻辑和模型。
    -   进行集成测试，模拟多次重启和跨天场景，验证数据准确性。

5.  **冗余数据清理**:
    -   在确认新系统稳定后，制定计划清理 `UnifiedUserSession` 中的冗余字段。

**BOSS，这份方案旨在从根本上解决当前的数据准确性问题，并为未来的功能扩展打下坚实的基础。我将严格按照此方案执行。** 