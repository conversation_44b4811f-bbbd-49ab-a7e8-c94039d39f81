# SweatMint 离线处理与数据补齐标准规范 v1.0

**版本**: v1.0  
**创建时间**: 2025年7月4日  
**审核状态**: ✅ BOSS审核通过  
**优先级**: 🔴 **P0 - 最高优先级**  

---

## 📋 核心问题解决方案

**基于Xcode输出日志分析，本规范系统性解决以下关键问题：**

1. **会话时间字段缺失**：会话开始时间和基线日期显示为None
2. **会话管理时间不一致**：APP重启后8小时间隔未正确创建新会话
3. **离线处理机制缺失**：缺乏完整的离线状态定义和数据补齐机制

---

## 🎯 1. 离线状态标准化定义

### 1.1 三级离线状态体系

#### **应用级离线 (App-Level Offline)**
- **定义**: APP被用户主动关闭或后台运行超过2小时
- **检测条件**: 
  - APP完全退出（用户手动关闭）
  - 后台运行时间 > 2小时
  - 系统内存不足导致APP被强制终止
- **业务影响**: 需要重新初始化健康数据会话
- **恢复策略**: 执行完整的5步骤标准流程

#### **网络级离线 (Network-Level Offline)**
- **定义**: 网络连接中断或API连续失败
- **检测条件**:
  - 网络连接状态：无WiFi、无蜂窝数据
  - API连续失败次数 ≥ 3次
  - 网络中断时间 > 5分钟
- **业务影响**: 数据无法同步到后端
- **恢复策略**: 自动重试 + 离线队列处理

#### **会话级离线 (Session-Level Offline)**
- **定义**: 用户登出、Token过期或会话超时
- **检测条件**:
  - 用户主动登出
  - Token过期（>24小时）
  - 会话无活动时间 > 4小时
- **业务影响**: 需要重新登录和会话初始化
- **恢复策略**: 重新登录 + 强制创建新会话

### 1.2 用户场景下的离线判定

**BOSS关键问题：用户授权后缩小APP到后台，直到会话结束都没有唤醒APP算离线吗？**

**答案：不算离线，属于正常后台运行**

- **< 2小时后台**：正常状态，继续原会话
- **2-4小时后台**：应用级离线，但会话延续
- **> 4小时后台**：会话级离线，需要创建新会话

---

## 🌅 2. 跨天场景处理标准

### 2.1 经典场景：23:00登录→次日1:00唤醒

**BOSS核心问题：前一天23:00-23:59的数据需要补充吗？**

**答案：需要补充，且必须自动处理**

#### **处理流程：**

1. **检测跨天状态**
   ```python
   last_sync_time = "2025-07-03 23:00:00"
   current_time = "2025-07-04 01:00:00"
   is_cross_day = last_sync_time.date() != current_time.date()
   ```

2. **补齐前一天数据**
   - 查询iOS HealthKit：2025-07-03 23:00 → 23:59
   - 获取完整的步数、距离、卡路里数据
   - 计算前一天最终增量：`final_data - baseline_data`

3. **归档前一天数据**
   ```python
   DailyHealthSnapshot.objects.create(
       user=user,
       snapshot_date=date(2025, 7, 3),
       snapshot_steps=yesterday_final_steps,
       snapshot_distance=yesterday_final_distance,
       snapshot_calories=yesterday_final_calories,
       final_increment_steps=yesterday_increment_steps,
       is_final=True,
       finalized_at=timezone.now()
   )
   ```

4. **更新前一天任务状态**
   - 基于最终增量更新任务完成度
   - 发放应得的代币奖励
   - 处理等级提升等逻辑

5. **重置新一天基线**
   - 结束旧会话（标记为inactive）
   - 创建新会话，当前时间作为基线
   - 重新开始增量计算

### 2.2 利用iOS HealthKit历史查询特性

**关键优势：不依赖后台服务**

- iOS HealthKit支持查询任意时间段的历史数据
- APP唤醒时可以直接查询缺失时段的数据
- 无需依赖后台常驻服务或推送通知

```swift
// 示例：查询前一天23:00-23:59的数据
let calendar = Calendar.current
let yesterday = calendar.date(byAdding: .day, value: -1, to: Date())!
let startOfDay = calendar.startOfDay(for: yesterday)
let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!

// 查询历史步数数据
healthStore.execute(stepsQuery) { query, result, error in
    // 处理查询结果
}
```

---

## 📊 3. 每日任务离线处理机制

### 3.1 任务状态缓存策略

**BOSS关键问题：每日任务需要健康数据，如何处理离线期间的数据？**

#### **智能UI反馈机制**
```dart
// Flutter前端处理
class TaskProgressWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<TaskProvider>(
      builder: (context, taskProvider, child) {
        if (taskProvider.isOffline) {
          return _buildOfflineIndicator();  // 显示离线状态
        }
        
        if (taskProvider.lastSyncTime != null) {
          final hoursSinceSync = DateTime.now()
              .difference(taskProvider.lastSyncTime!)
              .inHours;
          
          if (hoursSinceSync > 2) {
            return _buildStaleDataWarning();  // 显示数据可能过时
          }
        }
        
        return _buildNormalTaskProgress();
      },
    );
  }
}
```

#### **离线状态显示标准**
- **绿色图标**: 数据实时同步（< 10分钟）
- **黄色图标**: 数据可能延迟（10分钟 - 2小时）
- **红色图标**: 离线状态（> 2小时）
- **刷新按钮**: 用户可手动触发数据同步

### 3.2 任务恢复机制

**APP唤醒后自动执行：**

1. **数据完整性检查**
   ```python
   gaps = HealthDataRecovery.identify_data_gaps(
       last_sync_time=last_sync_time,
       current_time=timezone.now()
   )
   ```

2. **分类处理数据缺口**
   - **同天缺口**: 直接补齐并同步
   - **跨天缺口**: 执行跨天归档流程
   - **多天缺口**: 逐天处理，确保数据完整性

3. **任务状态重算**
   ```python
   for gap in gaps:
       recovered_data = recover_gap_data(gap)
       update_task_progress(gap.date, recovered_data)
   ```

---

## 🔧 4. 技术实现架构

### 4.1 前端Flutter组件

#### **OfflineDataManager**
```dart
class OfflineDataManager {
  // 离线状态检测
  static Future<OfflineStatus> checkOfflineStatus();
  
  // 离线操作队列
  static Future<void> addPendingOperation(OfflineOperation operation);
  
  // 恢复处理
  static Future<void> processPendingOperations();
}
```

#### **HealthDataRecovery**
```dart
class HealthDataRecovery {
  // 数据缺口识别
  static List<DataGap> identifyDataGaps(DateTime lastSync, DateTime current);
  
  // 跨天数据恢复
  static Future<RecoveryResult> recoverCrossDayData(DataGap gap);
  
  // HealthKit历史查询
  static Future<HealthData?> queryHealthKitHistoricalData({
    required DateTime startTime,
    required DateTime endTime,
  });
}
```

### 4.2 后端Django服务

#### **OfflineDataService**
```python
class OfflineDataService:
    @staticmethod
    def process_offline_queue(user_id: int) -> Dict
    
    @staticmethod
    def process_cross_day_data(user: User, data: Dict) -> Dict
    
    @staticmethod
    def add_offline_operation(user: User, operation_type: str, data: Dict)
```

#### **DataIntegrityChecker**
```python
class DataIntegrityChecker:
    @staticmethod
    def check_session_integrity(user: User, start_date, end_date) -> Dict
    
    @staticmethod
    def detect_negative_baselines(user: User) -> List[Dict]
    
    @staticmethod
    def find_duplicate_sessions(user: User) -> List[Dict]
```

#### **AuditLogger**
```python
class AuditLogger:
    @staticmethod
    def log_session_operation(user, operation, session_data)
    
    @staticmethod
    def log_data_recovery(user, recovery_data, gaps_found)
    
    @staticmethod
    def detect_anomaly_patterns(user) -> List[Dict]
```

---

## 🚀 5. 新增API接口

### 5.1 离线处理接口

```http
POST /api/app/v1/health/offline/process-queue/
Content-Type: application/json

{
  "device_id": "iOS_12345",
  "last_sync_time": "2025-07-03T23:00:00Z"
}
```

### 5.2 数据恢复接口

```http
POST /api/app/v1/health/offline/data-recovery/
Content-Type: application/json

{
  "last_sync_time": "2025-07-03T23:00:00Z",
  "current_time": "2025-07-04T01:00:00Z",
  "permissions": {
    "steps": true,
    "distance": true,
    "calories": true
  }
}
```

### 5.3 会话连续性检查接口

```http
GET /api/app/v1/health/session/check-continuity/
Headers:
  Device-ID: iOS_12345

Response:
{
  "need_new_session": false,
  "session_age_hours": 8.5,
  "last_activity_hours": 8.2,
  "reason": "超过4小时无活动"
}
```

---

## 📈 6. 数据安全保障

### 6.1 事务保护

```python
@transaction.atomic
def process_cross_day_transition(user, yesterday_data, today_baseline):
    # 归档昨天数据
    archive_previous_day_data(user, yesterday_data)
    
    # 更新昨天任务
    update_yesterday_tasks(user, yesterday_data)
    
    # 重置今天基线
    reset_today_baseline(user, today_baseline)
```

### 6.2 完整性检查

```python
class DataIntegrityChecker:
    def check_baseline_consistency(self, session):
        issues = []
        
        # 检查负基线
        if session.session_baseline_steps < 0:
            issues.append("negative_baseline_steps")
        
        # 检查基线合理性
        if session.session_baseline_steps > 100000:
            issues.append("unrealistic_baseline_steps")
        
        return issues
```

### 6.3 审计日志

```python
# 记录所有关键操作
AuditLogger.log_operation(
    user=user,
    operation="cross_day_processing",
    before_state=yesterday_final_data,
    after_state=today_baseline_data,
    metadata={
        "cross_date": "2025-07-03",
        "gaps_found": data_gaps,
        "recovery_method": "healthkit_historical_query"
    }
)
```

---

## ✅ 7. 解决方案优势

### 7.1 技术优势
- **不依赖后台服务**: 利用iOS HealthKit历史查询特性
- **完整数据补齐**: 自动处理跨天场景，确保任务计算准确
- **系统性架构**: 前后端配合，离线在线无缝切换
- **严格审计**: 所有操作可追溯，数据完整性有保障

### 7.2 业务优势
- **用户体验优化**: 智能离线状态提示，手动刷新选项
- **数据准确性**: 确保任务奖励计算基于完整数据
- **异常处理**: 自动识别和修复常见数据问题
- **可扩展性**: 支持未来更复杂的离线场景

### 7.3 运维优势
- **自动恢复**: 系统自动处理大部分离线恢复场景
- **完整监控**: 详细的审计日志和异常检测
- **性能优化**: 智能队列处理，避免系统过载
- **故障隔离**: 离线处理不影响在线用户体验

---

## 🎯 8. 实施计划

### 阶段1：核心修复（已完成）
- ✅ 修复会话时间字段缺失问题
- ✅ 增强会话检测逻辑
- ✅ 创建数据修复工具

### 阶段2：离线处理实现
- 🔄 实现OfflineDataManager和HealthDataRecovery
- 🔄 创建离线处理API接口
- 🔄 实现数据完整性检查

### 阶段3：审计和监控
- ⏳ 完善审计日志系统
- ⏳ 实现异常检测和告警
- ⏳ 性能优化和压力测试

---

**本规范确保SweatMint健康数据在任何离线场景下都能保持完整性、一致性和业务连续性。** 